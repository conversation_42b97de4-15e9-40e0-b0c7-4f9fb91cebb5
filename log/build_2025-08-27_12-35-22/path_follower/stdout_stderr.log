running egg_info
writing ../../build/path_follower/path_follower.egg-info/PKG-INFO
writing dependency_links to ../../build/path_follower/path_follower.egg-info/dependency_links.txt
writing entry points to ../../build/path_follower/path_follower.egg-info/entry_points.txt
writing requirements to ../../build/path_follower/path_follower.egg-info/requires.txt
writing top-level names to ../../build/path_follower/path_follower.egg-info/top_level.txt
reading manifest file '../../build/path_follower/path_follower.egg-info/SOURCES.txt'
writing manifest file '../../build/path_follower/path_follower.egg-info/SOURCES.txt'
running build
running build_py
running install
running install_lib
running install_data
running install_egg_info
removing '/Users/<USER>/Work/drill2/onboard/install/path_follower/lib/python3.11/site-packages/path_follower-0.0.0-py3.11.egg-info' (and everything under it)
Copying ../../build/path_follower/path_follower.egg-info to /Users/<USER>/Work/drill2/onboard/install/path_follower/lib/python3.11/site-packages/path_follower-0.0.0-py3.11.egg-info
running install_scripts
Installing path_follower script to /Users/<USER>/Work/drill2/onboard/install/path_follower/lib/path_follower
Installing path_follower_node script to /Users/<USER>/Work/drill2/onboard/install/path_follower/lib/path_follower
writing list of installed files to '/Users/<USER>/Work/drill2/onboard/build/path_follower/install.log'
