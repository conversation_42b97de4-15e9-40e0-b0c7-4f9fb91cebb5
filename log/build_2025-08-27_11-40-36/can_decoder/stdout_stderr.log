running develop
running egg_info
writing can_decoder.egg-info/PKG-INFO
writing dependency_links to can_decoder.egg-info/dependency_links.txt
writing entry points to can_decoder.egg-info/entry_points.txt
writing requirements to can_decoder.egg-info/requires.txt
writing top-level names to can_decoder.egg-info/top_level.txt
reading manifest file 'can_decoder.egg-info/SOURCES.txt'
writing manifest file 'can_decoder.egg-info/SOURCES.txt'
running build_ext
Creating /Users/<USER>/Work/drill2/onboard/install/can_decoder/lib/python3.11/site-packages/can-decoder.egg-link (link to .)
Installing can_decoder script to /Users/<USER>/Work/drill2/onboard/install/can_decoder/lib/can_decoder

Installed /Users/<USER>/Work/drill2/onboard/build/can_decoder
running symlink_data
symbolically linking /Users/<USER>/Work/drill2/onboard/build/can_decoder/resource/can_decoder -> /Users/<USER>/Work/drill2/onboard/install/can_decoder/share/ament_index/resource_index/packages
symbolically linking /Users/<USER>/Work/drill2/onboard/build/can_decoder/package.xml -> /Users/<USER>/Work/drill2/onboard/install/can_decoder/share/can_decoder
symbolically linking /Users/<USER>/Work/drill2/onboard/build/can_decoder/can_decoder/protocols/vep1.yaml -> /Users/<USER>/Work/drill2/onboard/install/can_decoder/share/can_decoder/protocols
symbolically linking /Users/<USER>/Work/drill2/onboard/build/can_decoder/can_decoder/protocols/ssi2.yaml -> /Users/<USER>/Work/drill2/onboard/install/can_decoder/share/can_decoder/protocols
symbolically linking /Users/<USER>/Work/drill2/onboard/build/can_decoder/can_decoder/protocols/eflp1.yaml -> /Users/<USER>/Work/drill2/onboard/install/can_decoder/share/can_decoder/protocols
symbolically linking /Users/<USER>/Work/drill2/onboard/build/can_decoder/can_decoder/protocols/inc1.yaml -> /Users/<USER>/Work/drill2/onboard/install/can_decoder/share/can_decoder/protocols
symbolically linking /Users/<USER>/Work/drill2/onboard/build/can_decoder/can_decoder/protocols/x90JacksData.yaml -> /Users/<USER>/Work/drill2/onboard/install/can_decoder/share/can_decoder/protocols
symbolically linking /Users/<USER>/Work/drill2/onboard/build/can_decoder/can_decoder/protocols/x90Encoders.yaml -> /Users/<USER>/Work/drill2/onboard/install/can_decoder/share/can_decoder/protocols
symbolically linking /Users/<USER>/Work/drill2/onboard/build/can_decoder/can_decoder/protocols/lfc.yaml -> /Users/<USER>/Work/drill2/onboard/install/can_decoder/share/can_decoder/protocols
symbolically linking /Users/<USER>/Work/drill2/onboard/build/can_decoder/can_decoder/protocols/eec1.yaml -> /Users/<USER>/Work/drill2/onboard/install/can_decoder/share/can_decoder/protocols
symbolically linking /Users/<USER>/Work/drill2/onboard/build/can_decoder/can_decoder/protocols/x90ArmData.yaml -> /Users/<USER>/Work/drill2/onboard/install/can_decoder/share/can_decoder/protocols
symbolically linking /Users/<USER>/Work/drill2/onboard/build/can_decoder/can_decoder/protocols/x90BarData.yaml -> /Users/<USER>/Work/drill2/onboard/install/can_decoder/share/can_decoder/protocols
symbolically linking /Users/<USER>/Work/drill2/onboard/build/can_decoder/can_decoder/protocols/ardp.yaml -> /Users/<USER>/Work/drill2/onboard/install/can_decoder/share/can_decoder/protocols
symbolically linking /Users/<USER>/Work/drill2/onboard/build/can_decoder/can_decoder/protocols/eec2.yaml -> /Users/<USER>/Work/drill2/onboard/install/can_decoder/share/can_decoder/protocols
symbolically linking /Users/<USER>/Work/drill2/onboard/build/can_decoder/can_decoder/protocols/x90MastLvl.yaml -> /Users/<USER>/Work/drill2/onboard/install/can_decoder/share/can_decoder/protocols
symbolically linking /Users/<USER>/Work/drill2/onboard/build/can_decoder/can_decoder/protocols/et1.yaml -> /Users/<USER>/Work/drill2/onboard/install/can_decoder/share/can_decoder/protocols
symbolically linking /Users/<USER>/Work/drill2/onboard/build/can_decoder/can_decoder/protocols/x90Prssr.yaml -> /Users/<USER>/Work/drill2/onboard/install/can_decoder/share/can_decoder/protocols
symbolically linking /Users/<USER>/Work/drill2/onboard/build/can_decoder/can_decoder/protocols/inc2.yaml -> /Users/<USER>/Work/drill2/onboard/install/can_decoder/share/can_decoder/protocols
symbolically linking /Users/<USER>/Work/drill2/onboard/build/can_decoder/can_decoder/protocols/mdp.yaml -> /Users/<USER>/Work/drill2/onboard/install/can_decoder/share/can_decoder/protocols
symbolically linking /Users/<USER>/Work/drill2/onboard/build/can_decoder/can_decoder/protocols/adp.yaml -> /Users/<USER>/Work/drill2/onboard/install/can_decoder/share/can_decoder/protocols
symbolically linking /Users/<USER>/Work/drill2/onboard/build/can_decoder/can_decoder/protocols/eec3.yaml -> /Users/<USER>/Work/drill2/onboard/install/can_decoder/share/can_decoder/protocols
symbolically linking /Users/<USER>/Work/drill2/onboard/build/can_decoder/can_decoder/protocols/hours.yaml -> /Users/<USER>/Work/drill2/onboard/install/can_decoder/share/can_decoder/protocols
symbolically linking /Users/<USER>/Work/drill2/onboard/build/can_decoder/can_decoder/protocols/x90WaterData.yaml -> /Users/<USER>/Work/drill2/onboard/install/can_decoder/share/can_decoder/protocols
symbolically linking /Users/<USER>/Work/drill2/onboard/build/can_decoder/can_decoder/protocols/x90PinsData.yaml -> /Users/<USER>/Work/drill2/onboard/install/can_decoder/share/can_decoder/protocols
symbolically linking /Users/<USER>/Work/drill2/onboard/build/can_decoder/can_decoder/protocols/x90DustFlaps.yaml -> /Users/<USER>/Work/drill2/onboard/install/can_decoder/share/can_decoder/protocols
symbolically linking /Users/<USER>/Work/drill2/onboard/build/can_decoder/can_decoder/protocols/x90ForkData.yaml -> /Users/<USER>/Work/drill2/onboard/install/can_decoder/share/can_decoder/protocols
symbolically linking /Users/<USER>/Work/drill2/onboard/build/can_decoder/can_decoder/protocols/lfe1.yaml -> /Users/<USER>/Work/drill2/onboard/install/can_decoder/share/can_decoder/protocols
symbolically linking /Users/<USER>/Work/drill2/onboard/build/can_decoder/can_decoder/protocols/x90UlsJacks.yaml -> /Users/<USER>/Work/drill2/onboard/install/can_decoder/share/can_decoder/protocols
symbolically linking /Users/<USER>/Work/drill2/onboard/build/can_decoder/can_decoder/protocols/x90WrenchData.yaml -> /Users/<USER>/Work/drill2/onboard/install/can_decoder/share/can_decoder/protocols
symbolically linking /Users/<USER>/Work/drill2/onboard/build/can_decoder/can_decoder/protocols/x90CarouselData.yaml -> /Users/<USER>/Work/drill2/onboard/install/can_decoder/share/can_decoder/protocols
