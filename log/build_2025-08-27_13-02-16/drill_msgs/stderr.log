CMake Deprecation Warning at CMakeLists.txt:1 (cmake_minimum_required):
  Compatibility with CMake < 3.10 will be removed from a future version of
  CMake.

  Update the VERSION argument <min> value.  Or, use the <min>...<max> syntax
  to tell CMake that the project requires at least <min> but has been updated
  to work with policies introduced by <max> or earlier.


failed to create symbolic link '/Users/<USER>/Work/drill2/onboard/build/drill_msgs/ament_cmake_python/drill_msgs/drill_msgs' because existing path cannot be removed: Operation not permitted
make[2]: *** [CMakeFiles/ament_cmake_python_symlink_drill_msgs] Error 1
make[1]: *** [CMakeFiles/ament_cmake_python_symlink_drill_msgs.dir/all] Error 2
make[1]: *** Waiting for unfinished jobs....
make: *** [all] Error 2
