[0.221s] DEBUG:colcon:Command line arguments: ['/Users/<USER>/.ros2_venv/bin/colcon', 'build', '--packages-select', 'drill_msgs', '--symlink-install']
[0.221s] DEBUG:colcon:Parsed command line arguments: Namespace(log_base=None, log_level=None, verb_name='build', build_base='build', install_base='install', merge_install=False, symlink_install=True, test_result_base=None, continue_on_error=False, executor='parallel', parallel_workers=8, event_handlers=None, ignore_user_meta=False, metas=['./colcon.meta'], base_paths=['.'], packages_ignore=None, packages_ignore_regex=None, paths=None, packages_up_to=None, packages_up_to_regex=None, packages_above=None, packages_above_and_dependencies=None, packages_above_depth=None, packages_select_by_dep=None, packages_skip_by_dep=None, packages_skip_up_to=None, packages_select_build_failed=False, packages_skip_build_finished=False, packages_select_test_failures=False, packages_skip_test_passed=False, packages_select=['drill_msgs'], packages_skip=None, packages_select_regex=None, packages_skip_regex=None, packages_start=None, packages_end=None, cmake_args=None, cmake_target=None, cmake_target_skip_unavailable=False, cmake_clean_cache=False, cmake_clean_first=False, cmake_force_configure=False, ament_cmake_args=None, catkin_cmake_args=None, catkin_skip_building_tests=False, verb_parser=<colcon_defaults.argument_parser.defaults.DefaultArgumentsDecorator object at 0x102652850>, verb_extension=<colcon_core.verb.build.BuildVerb object at 0x1020e8e90>, main=<bound method BuildVerb.main of <colcon_core.verb.build.BuildVerb object at 0x1020e8e90>>)
[0.367s] Level 1:colcon.colcon_core.package_discovery:discover_packages(colcon_meta) check parameters
[0.367s] Level 1:colcon.colcon_core.package_discovery:discover_packages(recursive) check parameters
[0.367s] Level 1:colcon.colcon_core.package_discovery:discover_packages(ignore) check parameters
[0.367s] Level 1:colcon.colcon_core.package_discovery:discover_packages(path) check parameters
[0.367s] Level 1:colcon.colcon_core.package_discovery:discover_packages(colcon_meta) discover
[0.367s] Level 1:colcon.colcon_core.package_discovery:discover_packages(recursive) discover
[0.367s] INFO:colcon.colcon_core.package_discovery:Crawling recursively for packages in '/Users/<USER>/Work/drill2/onboard'
[0.367s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extensions ['ignore', 'ignore_ament_install']
[0.367s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'ignore'
[0.367s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'ignore_ament_install'
[0.367s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extensions ['colcon_pkg']
[0.367s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'colcon_pkg'
[0.367s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extensions ['colcon_meta']
[0.367s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'colcon_meta'
[0.367s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extensions ['ros']
[0.367s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'ros'
[0.377s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extensions ['cmake', 'python']
[0.377s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'cmake'
[0.377s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'python'
[0.377s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extensions ['python_setup_py']
[0.377s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'python_setup_py'
[0.377s] Level 1:colcon.colcon_core.package_identification:_identify(build) by extensions ['ignore', 'ignore_ament_install']
[0.377s] Level 1:colcon.colcon_core.package_identification:_identify(build) by extension 'ignore'
[0.377s] Level 1:colcon.colcon_core.package_identification:_identify(build) ignored
[0.377s] Level 1:colcon.colcon_core.package_identification:_identify(install) by extensions ['ignore', 'ignore_ament_install']
[0.377s] Level 1:colcon.colcon_core.package_identification:_identify(install) by extension 'ignore'
[0.377s] Level 1:colcon.colcon_core.package_identification:_identify(install) ignored
[0.377s] Level 1:colcon.colcon_core.package_identification:_identify(log) by extensions ['ignore', 'ignore_ament_install']
[0.377s] Level 1:colcon.colcon_core.package_identification:_identify(log) by extension 'ignore'
[0.377s] Level 1:colcon.colcon_core.package_identification:_identify(log) ignored
[0.377s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extensions ['ignore', 'ignore_ament_install']
[0.377s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extension 'ignore'
[0.377s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extension 'ignore_ament_install'
[0.377s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extensions ['colcon_pkg']
[0.377s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extension 'colcon_pkg'
[0.377s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extensions ['colcon_meta']
[0.377s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extension 'colcon_meta'
[0.377s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extensions ['ros']
[0.377s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extension 'ros'
[0.377s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extensions ['cmake', 'python']
[0.377s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extension 'cmake'
[0.377s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extension 'python'
[0.377s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extensions ['python_setup_py']
[0.377s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extension 'python_setup_py'
[0.377s] Level 1:colcon.colcon_core.package_identification:_identify(src/base_node) by extensions ['ignore', 'ignore_ament_install']
[0.377s] Level 1:colcon.colcon_core.package_identification:_identify(src/base_node) by extension 'ignore'
[0.378s] Level 1:colcon.colcon_core.package_identification:_identify(src/base_node) by extension 'ignore_ament_install'
[0.378s] Level 1:colcon.colcon_core.package_identification:_identify(src/base_node) by extensions ['colcon_pkg']
[0.378s] Level 1:colcon.colcon_core.package_identification:_identify(src/base_node) by extension 'colcon_pkg'
[0.378s] Level 1:colcon.colcon_core.package_identification:_identify(src/base_node) by extensions ['colcon_meta']
[0.378s] Level 1:colcon.colcon_core.package_identification:_identify(src/base_node) by extension 'colcon_meta'
[0.378s] Level 1:colcon.colcon_core.package_identification:_identify(src/base_node) by extensions ['ros']
[0.378s] Level 1:colcon.colcon_core.package_identification:_identify(src/base_node) by extension 'ros'
[0.379s] DEBUG:colcon.colcon_core.package_identification:Package 'src/base_node' with type 'ros.ament_python' and name 'base_node'
[0.379s] Level 1:colcon.colcon_core.package_identification:_identify(src/can_decoder) by extensions ['ignore', 'ignore_ament_install']
[0.379s] Level 1:colcon.colcon_core.package_identification:_identify(src/can_decoder) by extension 'ignore'
[0.379s] Level 1:colcon.colcon_core.package_identification:_identify(src/can_decoder) by extension 'ignore_ament_install'
[0.379s] Level 1:colcon.colcon_core.package_identification:_identify(src/can_decoder) by extensions ['colcon_pkg']
[0.379s] Level 1:colcon.colcon_core.package_identification:_identify(src/can_decoder) by extension 'colcon_pkg'
[0.379s] Level 1:colcon.colcon_core.package_identification:_identify(src/can_decoder) by extensions ['colcon_meta']
[0.379s] Level 1:colcon.colcon_core.package_identification:_identify(src/can_decoder) by extension 'colcon_meta'
[0.380s] Level 1:colcon.colcon_core.package_identification:_identify(src/can_decoder) by extensions ['ros']
[0.380s] Level 1:colcon.colcon_core.package_identification:_identify(src/can_decoder) by extension 'ros'
[0.380s] DEBUG:colcon.colcon_core.package_identification:Package 'src/can_decoder' with type 'ros.ament_python' and name 'can_decoder'
[0.380s] Level 1:colcon.colcon_core.package_identification:_identify(src/can_encoder) by extensions ['ignore', 'ignore_ament_install']
[0.380s] Level 1:colcon.colcon_core.package_identification:_identify(src/can_encoder) by extension 'ignore'
[0.380s] Level 1:colcon.colcon_core.package_identification:_identify(src/can_encoder) by extension 'ignore_ament_install'
[0.380s] Level 1:colcon.colcon_core.package_identification:_identify(src/can_encoder) by extensions ['colcon_pkg']
[0.380s] Level 1:colcon.colcon_core.package_identification:_identify(src/can_encoder) by extension 'colcon_pkg'
[0.380s] Level 1:colcon.colcon_core.package_identification:_identify(src/can_encoder) by extensions ['colcon_meta']
[0.380s] Level 1:colcon.colcon_core.package_identification:_identify(src/can_encoder) by extension 'colcon_meta'
[0.380s] Level 1:colcon.colcon_core.package_identification:_identify(src/can_encoder) by extensions ['ros']
[0.380s] Level 1:colcon.colcon_core.package_identification:_identify(src/can_encoder) by extension 'ros'
[0.381s] DEBUG:colcon.colcon_core.package_identification:Package 'src/can_encoder' with type 'ros.ament_python' and name 'can_encoder'
[0.381s] Level 1:colcon.colcon_core.package_identification:_identify(src/can_msgs) by extensions ['ignore', 'ignore_ament_install']
[0.381s] Level 1:colcon.colcon_core.package_identification:_identify(src/can_msgs) by extension 'ignore'
[0.381s] Level 1:colcon.colcon_core.package_identification:_identify(src/can_msgs) by extension 'ignore_ament_install'
[0.381s] Level 1:colcon.colcon_core.package_identification:_identify(src/can_msgs) by extensions ['colcon_pkg']
[0.381s] Level 1:colcon.colcon_core.package_identification:_identify(src/can_msgs) by extension 'colcon_pkg'
[0.381s] Level 1:colcon.colcon_core.package_identification:_identify(src/can_msgs) by extensions ['colcon_meta']
[0.381s] Level 1:colcon.colcon_core.package_identification:_identify(src/can_msgs) by extension 'colcon_meta'
[0.381s] Level 1:colcon.colcon_core.package_identification:_identify(src/can_msgs) by extensions ['ros']
[0.381s] Level 1:colcon.colcon_core.package_identification:_identify(src/can_msgs) by extension 'ros'
[0.381s] DEBUG:colcon.colcon_core.package_identification:Package 'src/can_msgs' with type 'ros.ament_cmake' and name 'can_msgs'
[0.381s] Level 1:colcon.colcon_core.package_identification:_identify(src/depth_tracker) by extensions ['ignore', 'ignore_ament_install']
[0.381s] Level 1:colcon.colcon_core.package_identification:_identify(src/depth_tracker) by extension 'ignore'
[0.382s] Level 1:colcon.colcon_core.package_identification:_identify(src/depth_tracker) by extension 'ignore_ament_install'
[0.382s] Level 1:colcon.colcon_core.package_identification:_identify(src/depth_tracker) by extensions ['colcon_pkg']
[0.382s] Level 1:colcon.colcon_core.package_identification:_identify(src/depth_tracker) by extension 'colcon_pkg'
[0.382s] Level 1:colcon.colcon_core.package_identification:_identify(src/depth_tracker) by extensions ['colcon_meta']
[0.382s] Level 1:colcon.colcon_core.package_identification:_identify(src/depth_tracker) by extension 'colcon_meta'
[0.382s] Level 1:colcon.colcon_core.package_identification:_identify(src/depth_tracker) by extensions ['ros']
[0.382s] Level 1:colcon.colcon_core.package_identification:_identify(src/depth_tracker) by extension 'ros'
[0.382s] DEBUG:colcon.colcon_core.package_identification:Package 'src/depth_tracker' with type 'ros.ament_python' and name 'depth_tracker'
[0.382s] Level 1:colcon.colcon_core.package_identification:_identify(src/docker) by extensions ['ignore', 'ignore_ament_install']
[0.382s] Level 1:colcon.colcon_core.package_identification:_identify(src/docker) by extension 'ignore'
[0.382s] Level 1:colcon.colcon_core.package_identification:_identify(src/docker) by extension 'ignore_ament_install'
[0.382s] Level 1:colcon.colcon_core.package_identification:_identify(src/docker) by extensions ['colcon_pkg']
[0.382s] Level 1:colcon.colcon_core.package_identification:_identify(src/docker) by extension 'colcon_pkg'
[0.382s] Level 1:colcon.colcon_core.package_identification:_identify(src/docker) by extensions ['colcon_meta']
[0.382s] Level 1:colcon.colcon_core.package_identification:_identify(src/docker) by extension 'colcon_meta'
[0.382s] Level 1:colcon.colcon_core.package_identification:_identify(src/docker) by extensions ['ros']
[0.382s] Level 1:colcon.colcon_core.package_identification:_identify(src/docker) by extension 'ros'
[0.382s] Level 1:colcon.colcon_core.package_identification:_identify(src/docker) by extensions ['cmake', 'python']
[0.382s] Level 1:colcon.colcon_core.package_identification:_identify(src/docker) by extension 'cmake'
[0.382s] Level 1:colcon.colcon_core.package_identification:_identify(src/docker) by extension 'python'
[0.382s] Level 1:colcon.colcon_core.package_identification:_identify(src/docker) by extensions ['python_setup_py']
[0.382s] Level 1:colcon.colcon_core.package_identification:_identify(src/docker) by extension 'python_setup_py'
[0.382s] Level 1:colcon.colcon_core.package_identification:_identify(src/drill_msgs) by extensions ['ignore', 'ignore_ament_install']
[0.382s] Level 1:colcon.colcon_core.package_identification:_identify(src/drill_msgs) by extension 'ignore'
[0.382s] Level 1:colcon.colcon_core.package_identification:_identify(src/drill_msgs) by extension 'ignore_ament_install'
[0.382s] Level 1:colcon.colcon_core.package_identification:_identify(src/drill_msgs) by extensions ['colcon_pkg']
[0.382s] Level 1:colcon.colcon_core.package_identification:_identify(src/drill_msgs) by extension 'colcon_pkg'
[0.382s] Level 1:colcon.colcon_core.package_identification:_identify(src/drill_msgs) by extensions ['colcon_meta']
[0.382s] Level 1:colcon.colcon_core.package_identification:_identify(src/drill_msgs) by extension 'colcon_meta'
[0.383s] Level 1:colcon.colcon_core.package_identification:_identify(src/drill_msgs) by extensions ['ros']
[0.383s] Level 1:colcon.colcon_core.package_identification:_identify(src/drill_msgs) by extension 'ros'
[0.383s] DEBUG:colcon.colcon_core.package_identification:Package 'src/drill_msgs' with type 'ros.ament_cmake' and name 'drill_msgs'
[0.383s] Level 1:colcon.colcon_core.package_identification:_identify(src/driver_example) by extensions ['ignore', 'ignore_ament_install']
[0.383s] Level 1:colcon.colcon_core.package_identification:_identify(src/driver_example) by extension 'ignore'
[0.383s] Level 1:colcon.colcon_core.package_identification:_identify(src/driver_example) by extension 'ignore_ament_install'
[0.383s] Level 1:colcon.colcon_core.package_identification:_identify(src/driver_example) by extensions ['colcon_pkg']
[0.383s] Level 1:colcon.colcon_core.package_identification:_identify(src/driver_example) by extension 'colcon_pkg'
[0.383s] Level 1:colcon.colcon_core.package_identification:_identify(src/driver_example) by extensions ['colcon_meta']
[0.383s] Level 1:colcon.colcon_core.package_identification:_identify(src/driver_example) by extension 'colcon_meta'
[0.383s] Level 1:colcon.colcon_core.package_identification:_identify(src/driver_example) by extensions ['ros']
[0.383s] Level 1:colcon.colcon_core.package_identification:_identify(src/driver_example) by extension 'ros'
[0.383s] Level 1:colcon.colcon_core.package_identification:_identify(src/driver_example) by extensions ['cmake', 'python']
[0.383s] Level 1:colcon.colcon_core.package_identification:_identify(src/driver_example) by extension 'cmake'
[0.383s] Level 1:colcon.colcon_core.package_identification:_identify(src/driver_example) by extension 'python'
[0.383s] Level 1:colcon.colcon_core.package_identification:_identify(src/driver_example) by extensions ['python_setup_py']
[0.383s] Level 1:colcon.colcon_core.package_identification:_identify(src/driver_example) by extension 'python_setup_py'
[0.383s] Level 1:colcon.colcon_core.package_identification:_identify(src/external) by extensions ['ignore', 'ignore_ament_install']
[0.383s] Level 1:colcon.colcon_core.package_identification:_identify(src/external) by extension 'ignore'
[0.383s] Level 1:colcon.colcon_core.package_identification:_identify(src/external) by extension 'ignore_ament_install'
[0.383s] Level 1:colcon.colcon_core.package_identification:_identify(src/external) by extensions ['colcon_pkg']
[0.383s] Level 1:colcon.colcon_core.package_identification:_identify(src/external) by extension 'colcon_pkg'
[0.383s] Level 1:colcon.colcon_core.package_identification:_identify(src/external) by extensions ['colcon_meta']
[0.383s] Level 1:colcon.colcon_core.package_identification:_identify(src/external) by extension 'colcon_meta'
[0.383s] Level 1:colcon.colcon_core.package_identification:_identify(src/external) by extensions ['ros']
[0.383s] Level 1:colcon.colcon_core.package_identification:_identify(src/external) by extension 'ros'
[0.383s] Level 1:colcon.colcon_core.package_identification:_identify(src/external) by extensions ['cmake', 'python']
[0.383s] Level 1:colcon.colcon_core.package_identification:_identify(src/external) by extension 'cmake'
[0.383s] Level 1:colcon.colcon_core.package_identification:_identify(src/external) by extension 'python'
[0.383s] Level 1:colcon.colcon_core.package_identification:_identify(src/external) by extensions ['python_setup_py']
[0.383s] Level 1:colcon.colcon_core.package_identification:_identify(src/external) by extension 'python_setup_py'
[0.384s] Level 1:colcon.colcon_core.package_identification:_identify(src/external/log) by extensions ['ignore', 'ignore_ament_install']
[0.384s] Level 1:colcon.colcon_core.package_identification:_identify(src/external/log) by extension 'ignore'
[0.384s] Level 1:colcon.colcon_core.package_identification:_identify(src/external/log) ignored
[0.384s] Level 1:colcon.colcon_core.package_identification:_identify(src/external/maneuver-builder-cpp) by extensions ['ignore', 'ignore_ament_install']
[0.384s] Level 1:colcon.colcon_core.package_identification:_identify(src/external/maneuver-builder-cpp) by extension 'ignore'
[0.384s] Level 1:colcon.colcon_core.package_identification:_identify(src/external/maneuver-builder-cpp) by extension 'ignore_ament_install'
[0.384s] Level 1:colcon.colcon_core.package_identification:_identify(src/external/maneuver-builder-cpp) by extensions ['colcon_pkg']
[0.384s] Level 1:colcon.colcon_core.package_identification:_identify(src/external/maneuver-builder-cpp) by extension 'colcon_pkg'
[0.384s] Level 1:colcon.colcon_core.package_identification:_identify(src/external/maneuver-builder-cpp) by extensions ['colcon_meta']
[0.384s] Level 1:colcon.colcon_core.package_identification:_identify(src/external/maneuver-builder-cpp) by extension 'colcon_meta'
[0.384s] Level 1:colcon.colcon_core.package_identification:_identify(src/external/maneuver-builder-cpp) by extensions ['ros']
[0.384s] Level 1:colcon.colcon_core.package_identification:_identify(src/external/maneuver-builder-cpp) by extension 'ros'
[0.384s] DEBUG:colcon.colcon_core.package_identification:Package 'src/external/maneuver-builder-cpp' with type 'ros.ament_cmake' and name 'maneuver_builder_cpp'
[0.384s] Level 1:colcon.colcon_core.package_identification:_identify(src/external/pydubins) by extensions ['ignore', 'ignore_ament_install']
[0.384s] Level 1:colcon.colcon_core.package_identification:_identify(src/external/pydubins) by extension 'ignore'
[0.384s] Level 1:colcon.colcon_core.package_identification:_identify(src/external/pydubins) by extension 'ignore_ament_install'
[0.384s] Level 1:colcon.colcon_core.package_identification:_identify(src/external/pydubins) by extensions ['colcon_pkg']
[0.384s] Level 1:colcon.colcon_core.package_identification:_identify(src/external/pydubins) by extension 'colcon_pkg'
[0.384s] Level 1:colcon.colcon_core.package_identification:_identify(src/external/pydubins) by extensions ['colcon_meta']
[0.384s] Level 1:colcon.colcon_core.package_identification:_identify(src/external/pydubins) by extension 'colcon_meta'
[0.384s] Level 1:colcon.colcon_core.package_identification:_identify(src/external/pydubins) by extensions ['ros']
[0.384s] Level 1:colcon.colcon_core.package_identification:_identify(src/external/pydubins) by extension 'ros'
[0.385s] DEBUG:colcon.colcon_core.package_identification:Package 'src/external/pydubins' with type 'ros.ament_python' and name 'pydubins'
[0.385s] Level 1:colcon.colcon_core.package_identification:_identify(src/hal_connector) by extensions ['ignore', 'ignore_ament_install']
[0.385s] Level 1:colcon.colcon_core.package_identification:_identify(src/hal_connector) by extension 'ignore'
[0.385s] Level 1:colcon.colcon_core.package_identification:_identify(src/hal_connector) by extension 'ignore_ament_install'
[0.385s] Level 1:colcon.colcon_core.package_identification:_identify(src/hal_connector) by extensions ['colcon_pkg']
[0.385s] Level 1:colcon.colcon_core.package_identification:_identify(src/hal_connector) by extension 'colcon_pkg'
[0.385s] Level 1:colcon.colcon_core.package_identification:_identify(src/hal_connector) by extensions ['colcon_meta']
[0.385s] Level 1:colcon.colcon_core.package_identification:_identify(src/hal_connector) by extension 'colcon_meta'
[0.385s] Level 1:colcon.colcon_core.package_identification:_identify(src/hal_connector) by extensions ['ros']
[0.385s] Level 1:colcon.colcon_core.package_identification:_identify(src/hal_connector) by extension 'ros'
[0.386s] DEBUG:colcon.colcon_core.package_identification:Package 'src/hal_connector' with type 'ros.ament_python' and name 'hal_connector'
[0.386s] Level 1:colcon.colcon_core.package_identification:_identify(src/launchpack) by extensions ['ignore', 'ignore_ament_install']
[0.386s] Level 1:colcon.colcon_core.package_identification:_identify(src/launchpack) by extension 'ignore'
[0.386s] Level 1:colcon.colcon_core.package_identification:_identify(src/launchpack) by extension 'ignore_ament_install'
[0.386s] Level 1:colcon.colcon_core.package_identification:_identify(src/launchpack) by extensions ['colcon_pkg']
[0.386s] Level 1:colcon.colcon_core.package_identification:_identify(src/launchpack) by extension 'colcon_pkg'
[0.386s] Level 1:colcon.colcon_core.package_identification:_identify(src/launchpack) by extensions ['colcon_meta']
[0.386s] Level 1:colcon.colcon_core.package_identification:_identify(src/launchpack) by extension 'colcon_meta'
[0.386s] Level 1:colcon.colcon_core.package_identification:_identify(src/launchpack) by extensions ['ros']
[0.386s] Level 1:colcon.colcon_core.package_identification:_identify(src/launchpack) by extension 'ros'
[0.386s] DEBUG:colcon.colcon_core.package_identification:Package 'src/launchpack' with type 'ros.ament_python' and name 'launchpack'
[0.386s] Level 1:colcon.colcon_core.package_identification:_identify(src/leveler) by extensions ['ignore', 'ignore_ament_install']
[0.386s] Level 1:colcon.colcon_core.package_identification:_identify(src/leveler) by extension 'ignore'
[0.386s] Level 1:colcon.colcon_core.package_identification:_identify(src/leveler) by extension 'ignore_ament_install'
[0.386s] Level 1:colcon.colcon_core.package_identification:_identify(src/leveler) by extensions ['colcon_pkg']
[0.386s] Level 1:colcon.colcon_core.package_identification:_identify(src/leveler) by extension 'colcon_pkg'
[0.386s] Level 1:colcon.colcon_core.package_identification:_identify(src/leveler) by extensions ['colcon_meta']
[0.386s] Level 1:colcon.colcon_core.package_identification:_identify(src/leveler) by extension 'colcon_meta'
[0.386s] Level 1:colcon.colcon_core.package_identification:_identify(src/leveler) by extensions ['ros']
[0.386s] Level 1:colcon.colcon_core.package_identification:_identify(src/leveler) by extension 'ros'
[0.387s] DEBUG:colcon.colcon_core.package_identification:Package 'src/leveler' with type 'ros.ament_python' and name 'leveler'
[0.387s] Level 1:colcon.colcon_core.package_identification:_identify(src/log) by extensions ['ignore', 'ignore_ament_install']
[0.387s] Level 1:colcon.colcon_core.package_identification:_identify(src/log) by extension 'ignore'
[0.387s] Level 1:colcon.colcon_core.package_identification:_identify(src/log) ignored
[0.387s] Level 1:colcon.colcon_core.package_identification:_identify(src/main_state_machine) by extensions ['ignore', 'ignore_ament_install']
[0.387s] Level 1:colcon.colcon_core.package_identification:_identify(src/main_state_machine) by extension 'ignore'
[0.387s] Level 1:colcon.colcon_core.package_identification:_identify(src/main_state_machine) by extension 'ignore_ament_install'
[0.387s] Level 1:colcon.colcon_core.package_identification:_identify(src/main_state_machine) by extensions ['colcon_pkg']
[0.387s] Level 1:colcon.colcon_core.package_identification:_identify(src/main_state_machine) by extension 'colcon_pkg'
[0.387s] Level 1:colcon.colcon_core.package_identification:_identify(src/main_state_machine) by extensions ['colcon_meta']
[0.387s] Level 1:colcon.colcon_core.package_identification:_identify(src/main_state_machine) by extension 'colcon_meta'
[0.387s] Level 1:colcon.colcon_core.package_identification:_identify(src/main_state_machine) by extensions ['ros']
[0.387s] Level 1:colcon.colcon_core.package_identification:_identify(src/main_state_machine) by extension 'ros'
[0.387s] DEBUG:colcon.colcon_core.package_identification:Package 'src/main_state_machine' with type 'ros.ament_python' and name 'main_state_machine'
[0.388s] Level 1:colcon.colcon_core.package_identification:_identify(src/modbus_node) by extensions ['ignore', 'ignore_ament_install']
[0.388s] Level 1:colcon.colcon_core.package_identification:_identify(src/modbus_node) by extension 'ignore'
[0.388s] Level 1:colcon.colcon_core.package_identification:_identify(src/modbus_node) by extension 'ignore_ament_install'
[0.388s] Level 1:colcon.colcon_core.package_identification:_identify(src/modbus_node) by extensions ['colcon_pkg']
[0.388s] Level 1:colcon.colcon_core.package_identification:_identify(src/modbus_node) by extension 'colcon_pkg'
[0.388s] Level 1:colcon.colcon_core.package_identification:_identify(src/modbus_node) by extensions ['colcon_meta']
[0.388s] Level 1:colcon.colcon_core.package_identification:_identify(src/modbus_node) by extension 'colcon_meta'
[0.388s] Level 1:colcon.colcon_core.package_identification:_identify(src/modbus_node) by extensions ['ros']
[0.388s] Level 1:colcon.colcon_core.package_identification:_identify(src/modbus_node) by extension 'ros'
[0.388s] DEBUG:colcon.colcon_core.package_identification:Package 'src/modbus_node' with type 'ros.ament_python' and name 'modbus_node'
[0.388s] Level 1:colcon.colcon_core.package_identification:_identify(src/params_server) by extensions ['ignore', 'ignore_ament_install']
[0.388s] Level 1:colcon.colcon_core.package_identification:_identify(src/params_server) by extension 'ignore'
[0.388s] Level 1:colcon.colcon_core.package_identification:_identify(src/params_server) by extension 'ignore_ament_install'
[0.388s] Level 1:colcon.colcon_core.package_identification:_identify(src/params_server) by extensions ['colcon_pkg']
[0.388s] Level 1:colcon.colcon_core.package_identification:_identify(src/params_server) by extension 'colcon_pkg'
[0.389s] Level 1:colcon.colcon_core.package_identification:_identify(src/params_server) by extensions ['colcon_meta']
[0.389s] Level 1:colcon.colcon_core.package_identification:_identify(src/params_server) by extension 'colcon_meta'
[0.389s] Level 1:colcon.colcon_core.package_identification:_identify(src/params_server) by extensions ['ros']
[0.389s] Level 1:colcon.colcon_core.package_identification:_identify(src/params_server) by extension 'ros'
[0.389s] DEBUG:colcon.colcon_core.package_identification:Package 'src/params_server' with type 'ros.ament_python' and name 'params_server'
[0.389s] Level 1:colcon.colcon_core.package_identification:_identify(src/path_follower) by extensions ['ignore', 'ignore_ament_install']
[0.389s] Level 1:colcon.colcon_core.package_identification:_identify(src/path_follower) by extension 'ignore'
[0.389s] Level 1:colcon.colcon_core.package_identification:_identify(src/path_follower) by extension 'ignore_ament_install'
[0.389s] Level 1:colcon.colcon_core.package_identification:_identify(src/path_follower) by extensions ['colcon_pkg']
[0.389s] Level 1:colcon.colcon_core.package_identification:_identify(src/path_follower) by extension 'colcon_pkg'
[0.389s] Level 1:colcon.colcon_core.package_identification:_identify(src/path_follower) by extensions ['colcon_meta']
[0.389s] Level 1:colcon.colcon_core.package_identification:_identify(src/path_follower) by extension 'colcon_meta'
[0.389s] Level 1:colcon.colcon_core.package_identification:_identify(src/path_follower) by extensions ['ros']
[0.389s] Level 1:colcon.colcon_core.package_identification:_identify(src/path_follower) by extension 'ros'
[0.390s] DEBUG:colcon.colcon_core.package_identification:Package 'src/path_follower' with type 'ros.ament_python' and name 'path_follower'
[0.390s] Level 1:colcon.colcon_core.package_identification:_identify(src/remote_connector) by extensions ['ignore', 'ignore_ament_install']
[0.390s] Level 1:colcon.colcon_core.package_identification:_identify(src/remote_connector) by extension 'ignore'
[0.390s] Level 1:colcon.colcon_core.package_identification:_identify(src/remote_connector) by extension 'ignore_ament_install'
[0.390s] Level 1:colcon.colcon_core.package_identification:_identify(src/remote_connector) by extensions ['colcon_pkg']
[0.390s] Level 1:colcon.colcon_core.package_identification:_identify(src/remote_connector) by extension 'colcon_pkg'
[0.390s] Level 1:colcon.colcon_core.package_identification:_identify(src/remote_connector) by extensions ['colcon_meta']
[0.390s] Level 1:colcon.colcon_core.package_identification:_identify(src/remote_connector) by extension 'colcon_meta'
[0.390s] Level 1:colcon.colcon_core.package_identification:_identify(src/remote_connector) by extensions ['ros']
[0.390s] Level 1:colcon.colcon_core.package_identification:_identify(src/remote_connector) by extension 'ros'
[0.391s] DEBUG:colcon.colcon_core.package_identification:Package 'src/remote_connector' with type 'ros.ament_python' and name 'remote_connector'
[0.391s] Level 1:colcon.colcon_core.package_identification:_identify(src/ros-foxglove-bridge) by extensions ['ignore', 'ignore_ament_install']
[0.391s] Level 1:colcon.colcon_core.package_identification:_identify(src/ros-foxglove-bridge) by extension 'ignore'
[0.391s] Level 1:colcon.colcon_core.package_identification:_identify(src/ros-foxglove-bridge) by extension 'ignore_ament_install'
[0.391s] Level 1:colcon.colcon_core.package_identification:_identify(src/ros-foxglove-bridge) by extensions ['colcon_pkg']
[0.391s] Level 1:colcon.colcon_core.package_identification:_identify(src/ros-foxglove-bridge) by extension 'colcon_pkg'
[0.391s] Level 1:colcon.colcon_core.package_identification:_identify(src/ros-foxglove-bridge) by extensions ['colcon_meta']
[0.391s] Level 1:colcon.colcon_core.package_identification:_identify(src/ros-foxglove-bridge) by extension 'colcon_meta'
[0.391s] Level 1:colcon.colcon_core.package_identification:_identify(src/ros-foxglove-bridge) by extensions ['ros']
[0.391s] Level 1:colcon.colcon_core.package_identification:_identify(src/ros-foxglove-bridge) by extension 'ros'
[0.396s] DEBUG:colcon.colcon_core.package_identification:Package 'src/ros-foxglove-bridge' with type 'ros.ament_cmake' and name 'foxglove_bridge'
[0.396s] Level 1:colcon.colcon_core.package_identification:_identify(src/rosx_introspection) by extensions ['ignore', 'ignore_ament_install']
[0.396s] Level 1:colcon.colcon_core.package_identification:_identify(src/rosx_introspection) by extension 'ignore'
[0.396s] Level 1:colcon.colcon_core.package_identification:_identify(src/rosx_introspection) by extension 'ignore_ament_install'
[0.396s] Level 1:colcon.colcon_core.package_identification:_identify(src/rosx_introspection) by extensions ['colcon_pkg']
[0.396s] Level 1:colcon.colcon_core.package_identification:_identify(src/rosx_introspection) by extension 'colcon_pkg'
[0.396s] Level 1:colcon.colcon_core.package_identification:_identify(src/rosx_introspection) by extensions ['colcon_meta']
[0.396s] Level 1:colcon.colcon_core.package_identification:_identify(src/rosx_introspection) by extension 'colcon_meta'
[0.396s] Level 1:colcon.colcon_core.package_identification:_identify(src/rosx_introspection) by extensions ['ros']
[0.396s] Level 1:colcon.colcon_core.package_identification:_identify(src/rosx_introspection) by extension 'ros'
[0.404s] DEBUG:colcon.colcon_core.package_identification:Package 'src/rosx_introspection' with type 'ros.ament_cmake' and name 'rosx_introspection'
[0.404s] Level 1:colcon.colcon_core.package_identification:_identify(src/rtk_connector) by extensions ['ignore', 'ignore_ament_install']
[0.404s] Level 1:colcon.colcon_core.package_identification:_identify(src/rtk_connector) by extension 'ignore'
[0.404s] Level 1:colcon.colcon_core.package_identification:_identify(src/rtk_connector) by extension 'ignore_ament_install'
[0.404s] Level 1:colcon.colcon_core.package_identification:_identify(src/rtk_connector) by extensions ['colcon_pkg']
[0.404s] Level 1:colcon.colcon_core.package_identification:_identify(src/rtk_connector) by extension 'colcon_pkg'
[0.404s] Level 1:colcon.colcon_core.package_identification:_identify(src/rtk_connector) by extensions ['colcon_meta']
[0.404s] Level 1:colcon.colcon_core.package_identification:_identify(src/rtk_connector) by extension 'colcon_meta'
[0.404s] Level 1:colcon.colcon_core.package_identification:_identify(src/rtk_connector) by extensions ['ros']
[0.404s] Level 1:colcon.colcon_core.package_identification:_identify(src/rtk_connector) by extension 'ros'
[0.405s] DEBUG:colcon.colcon_core.package_identification:Package 'src/rtk_connector' with type 'ros.ament_python' and name 'rtk_connector'
[0.405s] Level 1:colcon.colcon_core.package_identification:_identify(src/scripts) by extensions ['ignore', 'ignore_ament_install']
[0.405s] Level 1:colcon.colcon_core.package_identification:_identify(src/scripts) by extension 'ignore'
[0.405s] Level 1:colcon.colcon_core.package_identification:_identify(src/scripts) by extension 'ignore_ament_install'
[0.405s] Level 1:colcon.colcon_core.package_identification:_identify(src/scripts) by extensions ['colcon_pkg']
[0.405s] Level 1:colcon.colcon_core.package_identification:_identify(src/scripts) by extension 'colcon_pkg'
[0.405s] Level 1:colcon.colcon_core.package_identification:_identify(src/scripts) by extensions ['colcon_meta']
[0.405s] Level 1:colcon.colcon_core.package_identification:_identify(src/scripts) by extension 'colcon_meta'
[0.405s] Level 1:colcon.colcon_core.package_identification:_identify(src/scripts) by extensions ['ros']
[0.405s] Level 1:colcon.colcon_core.package_identification:_identify(src/scripts) by extension 'ros'
[0.405s] Level 1:colcon.colcon_core.package_identification:_identify(src/scripts) by extensions ['cmake', 'python']
[0.405s] Level 1:colcon.colcon_core.package_identification:_identify(src/scripts) by extension 'cmake'
[0.405s] Level 1:colcon.colcon_core.package_identification:_identify(src/scripts) by extension 'python'
[0.405s] Level 1:colcon.colcon_core.package_identification:_identify(src/scripts) by extensions ['python_setup_py']
[0.405s] Level 1:colcon.colcon_core.package_identification:_identify(src/scripts) by extension 'python_setup_py'
[0.405s] Level 1:colcon.colcon_core.package_identification:_identify(src/state_tracker) by extensions ['ignore', 'ignore_ament_install']
[0.405s] Level 1:colcon.colcon_core.package_identification:_identify(src/state_tracker) by extension 'ignore'
[0.405s] Level 1:colcon.colcon_core.package_identification:_identify(src/state_tracker) by extension 'ignore_ament_install'
[0.405s] Level 1:colcon.colcon_core.package_identification:_identify(src/state_tracker) by extensions ['colcon_pkg']
[0.406s] Level 1:colcon.colcon_core.package_identification:_identify(src/state_tracker) by extension 'colcon_pkg'
[0.406s] Level 1:colcon.colcon_core.package_identification:_identify(src/state_tracker) by extensions ['colcon_meta']
[0.406s] Level 1:colcon.colcon_core.package_identification:_identify(src/state_tracker) by extension 'colcon_meta'
[0.406s] Level 1:colcon.colcon_core.package_identification:_identify(src/state_tracker) by extensions ['ros']
[0.406s] Level 1:colcon.colcon_core.package_identification:_identify(src/state_tracker) by extension 'ros'
[0.406s] DEBUG:colcon.colcon_core.package_identification:Package 'src/state_tracker' with type 'ros.ament_python' and name 'state_tracker'
[0.406s] Level 1:colcon.colcon_core.package_identification:_identify(src/tracks_regulator) by extensions ['ignore', 'ignore_ament_install']
[0.406s] Level 1:colcon.colcon_core.package_identification:_identify(src/tracks_regulator) by extension 'ignore'
[0.406s] Level 1:colcon.colcon_core.package_identification:_identify(src/tracks_regulator) by extension 'ignore_ament_install'
[0.406s] Level 1:colcon.colcon_core.package_identification:_identify(src/tracks_regulator) by extensions ['colcon_pkg']
[0.406s] Level 1:colcon.colcon_core.package_identification:_identify(src/tracks_regulator) by extension 'colcon_pkg'
[0.406s] Level 1:colcon.colcon_core.package_identification:_identify(src/tracks_regulator) by extensions ['colcon_meta']
[0.406s] Level 1:colcon.colcon_core.package_identification:_identify(src/tracks_regulator) by extension 'colcon_meta'
[0.406s] Level 1:colcon.colcon_core.package_identification:_identify(src/tracks_regulator) by extensions ['ros']
[0.406s] Level 1:colcon.colcon_core.package_identification:_identify(src/tracks_regulator) by extension 'ros'
[0.407s] DEBUG:colcon.colcon_core.package_identification:Package 'src/tracks_regulator' with type 'ros.ament_python' and name 'tracks_regulator'
[0.407s] Level 1:colcon.colcon_core.package_identification:_identify(src/zenoh) by extensions ['ignore', 'ignore_ament_install']
[0.407s] Level 1:colcon.colcon_core.package_identification:_identify(src/zenoh) by extension 'ignore'
[0.407s] Level 1:colcon.colcon_core.package_identification:_identify(src/zenoh) by extension 'ignore_ament_install'
[0.407s] Level 1:colcon.colcon_core.package_identification:_identify(src/zenoh) by extensions ['colcon_pkg']
[0.407s] Level 1:colcon.colcon_core.package_identification:_identify(src/zenoh) by extension 'colcon_pkg'
[0.407s] Level 1:colcon.colcon_core.package_identification:_identify(src/zenoh) by extensions ['colcon_meta']
[0.407s] Level 1:colcon.colcon_core.package_identification:_identify(src/zenoh) by extension 'colcon_meta'
[0.407s] Level 1:colcon.colcon_core.package_identification:_identify(src/zenoh) by extensions ['ros']
[0.407s] Level 1:colcon.colcon_core.package_identification:_identify(src/zenoh) by extension 'ros'
[0.407s] Level 1:colcon.colcon_core.package_identification:_identify(src/zenoh) by extensions ['cmake', 'python']
[0.407s] Level 1:colcon.colcon_core.package_identification:_identify(src/zenoh) by extension 'cmake'
[0.407s] Level 1:colcon.colcon_core.package_identification:_identify(src/zenoh) by extension 'python'
[0.407s] Level 1:colcon.colcon_core.package_identification:_identify(src/zenoh) by extensions ['python_setup_py']
[0.407s] Level 1:colcon.colcon_core.package_identification:_identify(src/zenoh) by extension 'python_setup_py'
[0.407s] Level 1:colcon.colcon_core.package_identification:_identify(zenoh-plugin-ros2dds) by extensions ['ignore', 'ignore_ament_install']
[0.407s] Level 1:colcon.colcon_core.package_identification:_identify(zenoh-plugin-ros2dds) by extension 'ignore'
[0.407s] Level 1:colcon.colcon_core.package_identification:_identify(zenoh-plugin-ros2dds) by extension 'ignore_ament_install'
[0.408s] Level 1:colcon.colcon_core.package_identification:_identify(zenoh-plugin-ros2dds) by extensions ['colcon_pkg']
[0.408s] Level 1:colcon.colcon_core.package_identification:_identify(zenoh-plugin-ros2dds) by extension 'colcon_pkg'
[0.408s] Level 1:colcon.colcon_core.package_identification:_identify(zenoh-plugin-ros2dds) by extensions ['colcon_meta']
[0.408s] Level 1:colcon.colcon_core.package_identification:_identify(zenoh-plugin-ros2dds) by extension 'colcon_meta'
[0.408s] Level 1:colcon.colcon_core.package_identification:_identify(zenoh-plugin-ros2dds) by extensions ['ros']
[0.408s] Level 1:colcon.colcon_core.package_identification:_identify(zenoh-plugin-ros2dds) by extension 'ros'
[0.408s] Level 1:colcon.colcon_core.package_identification:_identify(zenoh-plugin-ros2dds) by extensions ['cmake', 'python']
[0.408s] Level 1:colcon.colcon_core.package_identification:_identify(zenoh-plugin-ros2dds) by extension 'cmake'
[0.408s] Level 1:colcon.colcon_core.package_identification:_identify(zenoh-plugin-ros2dds) by extension 'python'
[0.408s] Level 1:colcon.colcon_core.package_identification:_identify(zenoh-plugin-ros2dds) by extensions ['python_setup_py']
[0.408s] Level 1:colcon.colcon_core.package_identification:_identify(zenoh-plugin-ros2dds) by extension 'python_setup_py'
[0.408s] Level 1:colcon.colcon_core.package_identification:_identify(zenoh-plugin-ros2dds/build) by extensions ['ignore', 'ignore_ament_install']
[0.408s] Level 1:colcon.colcon_core.package_identification:_identify(zenoh-plugin-ros2dds/build) by extension 'ignore'
[0.408s] Level 1:colcon.colcon_core.package_identification:_identify(zenoh-plugin-ros2dds/build) ignored
[0.408s] Level 1:colcon.colcon_core.package_identification:_identify(zenoh-plugin-ros2dds/install) by extensions ['ignore', 'ignore_ament_install']
[0.408s] Level 1:colcon.colcon_core.package_identification:_identify(zenoh-plugin-ros2dds/install) by extension 'ignore'
[0.408s] Level 1:colcon.colcon_core.package_identification:_identify(zenoh-plugin-ros2dds/install) ignored
[0.408s] Level 1:colcon.colcon_core.package_identification:_identify(zenoh-plugin-ros2dds/log) by extensions ['ignore', 'ignore_ament_install']
[0.408s] Level 1:colcon.colcon_core.package_identification:_identify(zenoh-plugin-ros2dds/log) by extension 'ignore'
[0.408s] Level 1:colcon.colcon_core.package_identification:_identify(zenoh-plugin-ros2dds/log) ignored
[0.408s] Level 1:colcon.colcon_core.package_discovery:discover_packages(recursive) using defaults
[0.408s] Level 1:colcon.colcon_core.package_discovery:discover_packages(ignore) discover
[0.408s] Level 1:colcon.colcon_core.package_discovery:discover_packages(ignore) using defaults
[0.408s] Level 1:colcon.colcon_core.package_discovery:discover_packages(path) discover
[0.408s] Level 1:colcon.colcon_core.package_discovery:discover_packages(path) using defaults
[0.444s] INFO:colcon.colcon_core.package_selection:Skipping not selected package 'can_msgs' in 'src/can_msgs'
[0.444s] INFO:colcon.colcon_core.package_selection:Skipping not selected package 'launchpack' in 'src/launchpack'
[0.444s] INFO:colcon.colcon_core.package_selection:Skipping not selected package 'maneuver_builder_cpp' in 'src/external/maneuver-builder-cpp'
[0.444s] INFO:colcon.colcon_core.package_selection:Skipping not selected package 'pydubins' in 'src/external/pydubins'
[0.444s] INFO:colcon.colcon_core.package_selection:Skipping not selected package 'rosx_introspection' in 'src/rosx_introspection'
[0.444s] INFO:colcon.colcon_core.package_selection:Skipping not selected package 'base_node' in 'src/base_node'
[0.444s] INFO:colcon.colcon_core.package_selection:Skipping not selected package 'can_decoder' in 'src/can_decoder'
[0.444s] INFO:colcon.colcon_core.package_selection:Skipping not selected package 'can_encoder' in 'src/can_encoder'
[0.444s] INFO:colcon.colcon_core.package_selection:Skipping not selected package 'foxglove_bridge' in 'src/ros-foxglove-bridge'
[0.444s] INFO:colcon.colcon_core.package_selection:Skipping not selected package 'main_state_machine' in 'src/main_state_machine'
[0.444s] INFO:colcon.colcon_core.package_selection:Skipping not selected package 'params_server' in 'src/params_server'
[0.444s] INFO:colcon.colcon_core.package_selection:Skipping not selected package 'remote_connector' in 'src/remote_connector'
[0.444s] INFO:colcon.colcon_core.package_selection:Skipping not selected package 'rtk_connector' in 'src/rtk_connector'
[0.444s] INFO:colcon.colcon_core.package_selection:Skipping not selected package 'state_tracker' in 'src/state_tracker'
[0.444s] INFO:colcon.colcon_core.package_selection:Skipping not selected package 'depth_tracker' in 'src/depth_tracker'
[0.444s] INFO:colcon.colcon_core.package_selection:Skipping not selected package 'hal_connector' in 'src/hal_connector'
[0.444s] INFO:colcon.colcon_core.package_selection:Skipping not selected package 'leveler' in 'src/leveler'
[0.444s] INFO:colcon.colcon_core.package_selection:Skipping not selected package 'modbus_node' in 'src/modbus_node'
[0.444s] INFO:colcon.colcon_core.package_selection:Skipping not selected package 'path_follower' in 'src/path_follower'
[0.444s] INFO:colcon.colcon_core.package_selection:Skipping not selected package 'tracks_regulator' in 'src/tracks_regulator'
[0.444s] Level 5:colcon.colcon_core.verb:set package 'drill_msgs' build argument 'cmake_args' from command line to 'None'
[0.444s] Level 5:colcon.colcon_core.verb:set package 'drill_msgs' build argument 'cmake_target' from command line to 'None'
[0.444s] Level 5:colcon.colcon_core.verb:set package 'drill_msgs' build argument 'cmake_target_skip_unavailable' from command line to 'False'
[0.444s] Level 5:colcon.colcon_core.verb:set package 'drill_msgs' build argument 'cmake_clean_cache' from command line to 'False'
[0.444s] Level 5:colcon.colcon_core.verb:set package 'drill_msgs' build argument 'cmake_clean_first' from command line to 'False'
[0.444s] Level 5:colcon.colcon_core.verb:set package 'drill_msgs' build argument 'cmake_force_configure' from command line to 'False'
[0.444s] Level 5:colcon.colcon_core.verb:set package 'drill_msgs' build argument 'ament_cmake_args' from command line to 'None'
[0.444s] Level 5:colcon.colcon_core.verb:set package 'drill_msgs' build argument 'catkin_cmake_args' from command line to 'None'
[0.444s] Level 5:colcon.colcon_core.verb:set package 'drill_msgs' build argument 'catkin_skip_building_tests' from command line to 'False'
[0.444s] DEBUG:colcon.colcon_core.verb:Building package 'drill_msgs' with the following arguments: {'ament_cmake_args': None, 'build_base': '/Users/<USER>/Work/drill2/onboard/build/drill_msgs', 'catkin_cmake_args': None, 'catkin_skip_building_tests': False, 'cmake_args': None, 'cmake_clean_cache': False, 'cmake_clean_first': False, 'cmake_force_configure': False, 'cmake_target': None, 'cmake_target_skip_unavailable': False, 'install_base': '/Users/<USER>/Work/drill2/onboard/install/drill_msgs', 'merge_install': False, 'path': '/Users/<USER>/Work/drill2/onboard/src/drill_msgs', 'symlink_install': True, 'test_result_base': None}
[0.444s] INFO:colcon.colcon_core.executor:Executing jobs using 'parallel' executor
[0.445s] DEBUG:colcon.colcon_parallel_executor.executor.parallel:run_until_complete
[0.445s] INFO:colcon.colcon_ros.task.ament_cmake.build:Building ROS package in '/Users/<USER>/Work/drill2/onboard/src/drill_msgs' with build type 'ament_cmake'
[0.445s] INFO:colcon.colcon_cmake.task.cmake.build:Building CMake package in '/Users/<USER>/Work/drill2/onboard/src/drill_msgs'
[0.449s] INFO:colcon.colcon_core.plugin_system:Skipping extension 'colcon_core.shell.bat': Not used on non-Windows systems
[0.450s] INFO:colcon.colcon_core.shell:Skip shell extension 'powershell' for command environment: Not usable outside of PowerShell
[0.450s] DEBUG:colcon.colcon_core.shell:Skip shell extension 'dsv' for command environment
[0.575s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoking command in '/Users/<USER>/Work/drill2/onboard/build/drill_msgs': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/Users/<USER>/ros2_jazzy/install/rosbag2_examples_py:/Users/<USER>/ros2_jazzy/install/launch_testing_examples:/Users/<USER>/ros2_jazzy/install/ros2bag:/Users/<USER>/ros2_jazzy/install/tracetools_test:/Users/<USER>/ros2_jazzy/install/tracetools_launch:/Users/<USER>/ros2_jazzy/install/topic_monitor:/Users/<USER>/ros2_jazzy/install/tf2_tools:/Users/<USER>/ros2_jazzy/install/examples_tf2_py:/Users/<USER>/ros2_jazzy/install/tf2_ros_py:/Users/<USER>/ros2_jazzy/install/examples_rclpy_pointcloud_publisher:/Users/<USER>/ros2_jazzy/install/sensor_msgs_py:/Users/<USER>/ros2_jazzy/install/quality_of_service_demo_py:/Users/<USER>/ros2_jazzy/install/camera_info_manager_py:/Users/<USER>/ros2_jazzy/install/ros2doctor:/Users/<USER>/ros2_jazzy/install/lifecycle_py:/Users/<USER>/ros2_jazzy/install/examples_rclpy_minimal_subscriber:/Users/<USER>/ros2_jazzy/install/examples_rclpy_minimal_service:/Users/<USER>/ros2_jazzy/install/examples_rclpy_minimal_publisher:/Users/<USER>/ros2_jazzy/install/examples_rclpy_minimal_client:/Users/<USER>/ros2_jazzy/install/examples_rclpy_executors:/Users/<USER>/ros2_jazzy/install/demo_nodes_py:/Users/<USER>/ros2_jazzy/install/ros2component:/Users/<USER>/ros2_jazzy/install/sros2:/Users/<USER>/ros2_jazzy/install/ros2trace:/Users/<USER>/ros2_jazzy/install/ros2topic:/Users/<USER>/ros2_jazzy/install/ros2test:/Users/<USER>/ros2_jazzy/install/ros2param:/Users/<USER>/ros2_jazzy/install/ros2lifecycle:/Users/<USER>/ros2_jazzy/install/ros2service:/Users/<USER>/ros2_jazzy/install/ros2run:/Users/<USER>/ros2_jazzy/install/ros2launch:/Users/<USER>/ros2_jazzy/install/ros2pkg:/Users/<USER>/ros2_jazzy/install/ros2node:/Users/<USER>/ros2_jazzy/install/ros2multicast:/Users/<USER>/ros2_jazzy/install/ros2interface:/Users/<USER>/ros2_jazzy/install/ros2action:/Users/<USER>/ros2_jazzy/install/ros2cli:/Users/<USER>/ros2_jazzy/install/launch_testing_ros:/Users/<USER>/ros2_jazzy/install/launch_ros:/Users/<USER>/ros2_jazzy/install/examples_rclpy_minimal_action_server:/Users/<USER>/ros2_jazzy/install/examples_rclpy_minimal_action_client:/Users/<USER>/ros2_jazzy/install/examples_rclpy_guard_conditions:/Users/<USER>/ros2_jazzy/install/action_tutorials_py:/Users/<USER>/ros2_jazzy/install/ament_uncrustify:/Users/<USER>/ros2_jazzy/install/tracetools_trace:/Users/<USER>/ros2_jazzy/install/tracetools_read:/Users/<USER>/ros2_jazzy/install/test_tracetools_launch:/Users/<USER>/ros2_jazzy/install/test_ros2trace:/Users/<USER>/ros2_jazzy/install/test_launch_ros:/Users/<USER>/ros2_jazzy/install/rpyutils:/Users/<USER>/ros2_jazzy/install/rosidl_runtime_py:/Users/<USER>/ros2_jazzy/install/rosidl_pycommon:/Users/<USER>/ros2_jazzy/install/rosidl_cli:/Users/<USER>/ros2_jazzy/install/launch_pytest:/Users/<USER>/ros2_jazzy/install/launch_testing:/Users/<USER>/ros2_jazzy/install/launch_yaml:/Users/<USER>/ros2_jazzy/install/launch_xml:/Users/<USER>/ros2_jazzy/install/launch:/Users/<USER>/ros2_jazzy/install/osrf_pycommon:/Users/<USER>/ros2_jazzy/install/domain_coordinator:/Users/<USER>/ros2_jazzy/install/ament_xmllint:/Users/<USER>/ros2_jazzy/install/ament_pyflakes:/Users/<USER>/ros2_jazzy/install/ament_pycodestyle:/Users/<USER>/ros2_jazzy/install/ament_pep257:/Users/<USER>/ros2_jazzy/install/ament_pclint:/Users/<USER>/ros2_jazzy/install/ament_package:/Users/<USER>/ros2_jazzy/install/ament_mypy:/Users/<USER>/ros2_jazzy/install/ament_lint_cmake:/Users/<USER>/ros2_jazzy/install/ament_flake8:/Users/<USER>/ros2_jazzy/install/ament_copyright:/Users/<USER>/ros2_jazzy/install/ament_lint:/Users/<USER>/ros2_jazzy/install/ament_index_python:/Users/<USER>/ros2_jazzy/install/ament_cpplint:/Users/<USER>/ros2_jazzy/install/ament_cppcheck:/Users/<USER>/ros2_jazzy/install/ament_clang_tidy:/Users/<USER>/ros2_jazzy/install/ament_clang_format:/Users/<USER>/Work/drill2/onboard/install/tracks_regulator:/Users/<USER>/Work/drill2/onboard/install/state_tracker:/Users/<USER>/Work/drill2/onboard/install/rtk_connector:/Users/<USER>/Work/drill2/onboard/install/remote_connector:/Users/<USER>/Work/drill2/onboard/install/pydubins:/Users/<USER>/Work/drill2/onboard/install/path_follower:/Users/<USER>/Work/drill2/onboard/install/params_server:/Users/<USER>/Work/drill2/onboard/install/modbus_node:/Users/<USER>/Work/drill2/onboard/install/main_state_machine:/Users/<USER>/Work/drill2/onboard/install/leveler:/Users/<USER>/Work/drill2/onboard/install/launchpack:/Users/<USER>/Work/drill2/onboard/install/depth_tracker:/Users/<USER>/Work/drill2/onboard/install/can_encoder:/Users/<USER>/Work/drill2/onboard/install/can_decoder:/Users/<USER>/Work/drill2/onboard/install/base_node SHLVL=2 VIRTUAL_ENV_PROMPT=(.ros2_venv) _=/usr/bin/env /opt/homebrew/bin/cmake /Users/<USER>/Work/drill2/onboard/src/drill_msgs -DAMENT_CMAKE_SYMLINK_INSTALL=1 -DCMAKE_INSTALL_PREFIX=/Users/<USER>/Work/drill2/onboard/install/drill_msgs
[12.388s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoked command in '/Users/<USER>/Work/drill2/onboard/build/drill_msgs' returned '0': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/Users/<USER>/ros2_jazzy/install/rosbag2_examples_py:/Users/<USER>/ros2_jazzy/install/launch_testing_examples:/Users/<USER>/ros2_jazzy/install/ros2bag:/Users/<USER>/ros2_jazzy/install/tracetools_test:/Users/<USER>/ros2_jazzy/install/tracetools_launch:/Users/<USER>/ros2_jazzy/install/topic_monitor:/Users/<USER>/ros2_jazzy/install/tf2_tools:/Users/<USER>/ros2_jazzy/install/examples_tf2_py:/Users/<USER>/ros2_jazzy/install/tf2_ros_py:/Users/<USER>/ros2_jazzy/install/examples_rclpy_pointcloud_publisher:/Users/<USER>/ros2_jazzy/install/sensor_msgs_py:/Users/<USER>/ros2_jazzy/install/quality_of_service_demo_py:/Users/<USER>/ros2_jazzy/install/camera_info_manager_py:/Users/<USER>/ros2_jazzy/install/ros2doctor:/Users/<USER>/ros2_jazzy/install/lifecycle_py:/Users/<USER>/ros2_jazzy/install/examples_rclpy_minimal_subscriber:/Users/<USER>/ros2_jazzy/install/examples_rclpy_minimal_service:/Users/<USER>/ros2_jazzy/install/examples_rclpy_minimal_publisher:/Users/<USER>/ros2_jazzy/install/examples_rclpy_minimal_client:/Users/<USER>/ros2_jazzy/install/examples_rclpy_executors:/Users/<USER>/ros2_jazzy/install/demo_nodes_py:/Users/<USER>/ros2_jazzy/install/ros2component:/Users/<USER>/ros2_jazzy/install/sros2:/Users/<USER>/ros2_jazzy/install/ros2trace:/Users/<USER>/ros2_jazzy/install/ros2topic:/Users/<USER>/ros2_jazzy/install/ros2test:/Users/<USER>/ros2_jazzy/install/ros2param:/Users/<USER>/ros2_jazzy/install/ros2lifecycle:/Users/<USER>/ros2_jazzy/install/ros2service:/Users/<USER>/ros2_jazzy/install/ros2run:/Users/<USER>/ros2_jazzy/install/ros2launch:/Users/<USER>/ros2_jazzy/install/ros2pkg:/Users/<USER>/ros2_jazzy/install/ros2node:/Users/<USER>/ros2_jazzy/install/ros2multicast:/Users/<USER>/ros2_jazzy/install/ros2interface:/Users/<USER>/ros2_jazzy/install/ros2action:/Users/<USER>/ros2_jazzy/install/ros2cli:/Users/<USER>/ros2_jazzy/install/launch_testing_ros:/Users/<USER>/ros2_jazzy/install/launch_ros:/Users/<USER>/ros2_jazzy/install/examples_rclpy_minimal_action_server:/Users/<USER>/ros2_jazzy/install/examples_rclpy_minimal_action_client:/Users/<USER>/ros2_jazzy/install/examples_rclpy_guard_conditions:/Users/<USER>/ros2_jazzy/install/action_tutorials_py:/Users/<USER>/ros2_jazzy/install/ament_uncrustify:/Users/<USER>/ros2_jazzy/install/tracetools_trace:/Users/<USER>/ros2_jazzy/install/tracetools_read:/Users/<USER>/ros2_jazzy/install/test_tracetools_launch:/Users/<USER>/ros2_jazzy/install/test_ros2trace:/Users/<USER>/ros2_jazzy/install/test_launch_ros:/Users/<USER>/ros2_jazzy/install/rpyutils:/Users/<USER>/ros2_jazzy/install/rosidl_runtime_py:/Users/<USER>/ros2_jazzy/install/rosidl_pycommon:/Users/<USER>/ros2_jazzy/install/rosidl_cli:/Users/<USER>/ros2_jazzy/install/launch_pytest:/Users/<USER>/ros2_jazzy/install/launch_testing:/Users/<USER>/ros2_jazzy/install/launch_yaml:/Users/<USER>/ros2_jazzy/install/launch_xml:/Users/<USER>/ros2_jazzy/install/launch:/Users/<USER>/ros2_jazzy/install/osrf_pycommon:/Users/<USER>/ros2_jazzy/install/domain_coordinator:/Users/<USER>/ros2_jazzy/install/ament_xmllint:/Users/<USER>/ros2_jazzy/install/ament_pyflakes:/Users/<USER>/ros2_jazzy/install/ament_pycodestyle:/Users/<USER>/ros2_jazzy/install/ament_pep257:/Users/<USER>/ros2_jazzy/install/ament_pclint:/Users/<USER>/ros2_jazzy/install/ament_package:/Users/<USER>/ros2_jazzy/install/ament_mypy:/Users/<USER>/ros2_jazzy/install/ament_lint_cmake:/Users/<USER>/ros2_jazzy/install/ament_flake8:/Users/<USER>/ros2_jazzy/install/ament_copyright:/Users/<USER>/ros2_jazzy/install/ament_lint:/Users/<USER>/ros2_jazzy/install/ament_index_python:/Users/<USER>/ros2_jazzy/install/ament_cpplint:/Users/<USER>/ros2_jazzy/install/ament_cppcheck:/Users/<USER>/ros2_jazzy/install/ament_clang_tidy:/Users/<USER>/ros2_jazzy/install/ament_clang_format:/Users/<USER>/Work/drill2/onboard/install/tracks_regulator:/Users/<USER>/Work/drill2/onboard/install/state_tracker:/Users/<USER>/Work/drill2/onboard/install/rtk_connector:/Users/<USER>/Work/drill2/onboard/install/remote_connector:/Users/<USER>/Work/drill2/onboard/install/pydubins:/Users/<USER>/Work/drill2/onboard/install/path_follower:/Users/<USER>/Work/drill2/onboard/install/params_server:/Users/<USER>/Work/drill2/onboard/install/modbus_node:/Users/<USER>/Work/drill2/onboard/install/main_state_machine:/Users/<USER>/Work/drill2/onboard/install/leveler:/Users/<USER>/Work/drill2/onboard/install/launchpack:/Users/<USER>/Work/drill2/onboard/install/depth_tracker:/Users/<USER>/Work/drill2/onboard/install/can_encoder:/Users/<USER>/Work/drill2/onboard/install/can_decoder:/Users/<USER>/Work/drill2/onboard/install/base_node SHLVL=2 VIRTUAL_ENV_PROMPT=(.ros2_venv) _=/usr/bin/env /opt/homebrew/bin/cmake /Users/<USER>/Work/drill2/onboard/src/drill_msgs -DAMENT_CMAKE_SYMLINK_INSTALL=1 -DCMAKE_INSTALL_PREFIX=/Users/<USER>/Work/drill2/onboard/install/drill_msgs
[12.391s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoking command in '/Users/<USER>/Work/drill2/onboard/build/drill_msgs': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/Users/<USER>/ros2_jazzy/install/rosbag2_examples_py:/Users/<USER>/ros2_jazzy/install/launch_testing_examples:/Users/<USER>/ros2_jazzy/install/ros2bag:/Users/<USER>/ros2_jazzy/install/tracetools_test:/Users/<USER>/ros2_jazzy/install/tracetools_launch:/Users/<USER>/ros2_jazzy/install/topic_monitor:/Users/<USER>/ros2_jazzy/install/tf2_tools:/Users/<USER>/ros2_jazzy/install/examples_tf2_py:/Users/<USER>/ros2_jazzy/install/tf2_ros_py:/Users/<USER>/ros2_jazzy/install/examples_rclpy_pointcloud_publisher:/Users/<USER>/ros2_jazzy/install/sensor_msgs_py:/Users/<USER>/ros2_jazzy/install/quality_of_service_demo_py:/Users/<USER>/ros2_jazzy/install/camera_info_manager_py:/Users/<USER>/ros2_jazzy/install/ros2doctor:/Users/<USER>/ros2_jazzy/install/lifecycle_py:/Users/<USER>/ros2_jazzy/install/examples_rclpy_minimal_subscriber:/Users/<USER>/ros2_jazzy/install/examples_rclpy_minimal_service:/Users/<USER>/ros2_jazzy/install/examples_rclpy_minimal_publisher:/Users/<USER>/ros2_jazzy/install/examples_rclpy_minimal_client:/Users/<USER>/ros2_jazzy/install/examples_rclpy_executors:/Users/<USER>/ros2_jazzy/install/demo_nodes_py:/Users/<USER>/ros2_jazzy/install/ros2component:/Users/<USER>/ros2_jazzy/install/sros2:/Users/<USER>/ros2_jazzy/install/ros2trace:/Users/<USER>/ros2_jazzy/install/ros2topic:/Users/<USER>/ros2_jazzy/install/ros2test:/Users/<USER>/ros2_jazzy/install/ros2param:/Users/<USER>/ros2_jazzy/install/ros2lifecycle:/Users/<USER>/ros2_jazzy/install/ros2service:/Users/<USER>/ros2_jazzy/install/ros2run:/Users/<USER>/ros2_jazzy/install/ros2launch:/Users/<USER>/ros2_jazzy/install/ros2pkg:/Users/<USER>/ros2_jazzy/install/ros2node:/Users/<USER>/ros2_jazzy/install/ros2multicast:/Users/<USER>/ros2_jazzy/install/ros2interface:/Users/<USER>/ros2_jazzy/install/ros2action:/Users/<USER>/ros2_jazzy/install/ros2cli:/Users/<USER>/ros2_jazzy/install/launch_testing_ros:/Users/<USER>/ros2_jazzy/install/launch_ros:/Users/<USER>/ros2_jazzy/install/examples_rclpy_minimal_action_server:/Users/<USER>/ros2_jazzy/install/examples_rclpy_minimal_action_client:/Users/<USER>/ros2_jazzy/install/examples_rclpy_guard_conditions:/Users/<USER>/ros2_jazzy/install/action_tutorials_py:/Users/<USER>/ros2_jazzy/install/ament_uncrustify:/Users/<USER>/ros2_jazzy/install/tracetools_trace:/Users/<USER>/ros2_jazzy/install/tracetools_read:/Users/<USER>/ros2_jazzy/install/test_tracetools_launch:/Users/<USER>/ros2_jazzy/install/test_ros2trace:/Users/<USER>/ros2_jazzy/install/test_launch_ros:/Users/<USER>/ros2_jazzy/install/rpyutils:/Users/<USER>/ros2_jazzy/install/rosidl_runtime_py:/Users/<USER>/ros2_jazzy/install/rosidl_pycommon:/Users/<USER>/ros2_jazzy/install/rosidl_cli:/Users/<USER>/ros2_jazzy/install/launch_pytest:/Users/<USER>/ros2_jazzy/install/launch_testing:/Users/<USER>/ros2_jazzy/install/launch_yaml:/Users/<USER>/ros2_jazzy/install/launch_xml:/Users/<USER>/ros2_jazzy/install/launch:/Users/<USER>/ros2_jazzy/install/osrf_pycommon:/Users/<USER>/ros2_jazzy/install/domain_coordinator:/Users/<USER>/ros2_jazzy/install/ament_xmllint:/Users/<USER>/ros2_jazzy/install/ament_pyflakes:/Users/<USER>/ros2_jazzy/install/ament_pycodestyle:/Users/<USER>/ros2_jazzy/install/ament_pep257:/Users/<USER>/ros2_jazzy/install/ament_pclint:/Users/<USER>/ros2_jazzy/install/ament_package:/Users/<USER>/ros2_jazzy/install/ament_mypy:/Users/<USER>/ros2_jazzy/install/ament_lint_cmake:/Users/<USER>/ros2_jazzy/install/ament_flake8:/Users/<USER>/ros2_jazzy/install/ament_copyright:/Users/<USER>/ros2_jazzy/install/ament_lint:/Users/<USER>/ros2_jazzy/install/ament_index_python:/Users/<USER>/ros2_jazzy/install/ament_cpplint:/Users/<USER>/ros2_jazzy/install/ament_cppcheck:/Users/<USER>/ros2_jazzy/install/ament_clang_tidy:/Users/<USER>/ros2_jazzy/install/ament_clang_format:/Users/<USER>/Work/drill2/onboard/install/tracks_regulator:/Users/<USER>/Work/drill2/onboard/install/state_tracker:/Users/<USER>/Work/drill2/onboard/install/rtk_connector:/Users/<USER>/Work/drill2/onboard/install/remote_connector:/Users/<USER>/Work/drill2/onboard/install/pydubins:/Users/<USER>/Work/drill2/onboard/install/path_follower:/Users/<USER>/Work/drill2/onboard/install/params_server:/Users/<USER>/Work/drill2/onboard/install/modbus_node:/Users/<USER>/Work/drill2/onboard/install/main_state_machine:/Users/<USER>/Work/drill2/onboard/install/leveler:/Users/<USER>/Work/drill2/onboard/install/launchpack:/Users/<USER>/Work/drill2/onboard/install/depth_tracker:/Users/<USER>/Work/drill2/onboard/install/can_encoder:/Users/<USER>/Work/drill2/onboard/install/can_decoder:/Users/<USER>/Work/drill2/onboard/install/base_node SHLVL=2 VIRTUAL_ENV_PROMPT=(.ros2_venv) _=/usr/bin/env /opt/homebrew/bin/cmake --build /Users/<USER>/Work/drill2/onboard/build/drill_msgs -- -j8 -l8
[12.899s] Level 1:colcon.colcon_core.environment:create_environment_scripts_only(drill_msgs)
[12.899s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoked command in '/Users/<USER>/Work/drill2/onboard/build/drill_msgs' returned '2': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/Users/<USER>/ros2_jazzy/install/rosbag2_examples_py:/Users/<USER>/ros2_jazzy/install/launch_testing_examples:/Users/<USER>/ros2_jazzy/install/ros2bag:/Users/<USER>/ros2_jazzy/install/tracetools_test:/Users/<USER>/ros2_jazzy/install/tracetools_launch:/Users/<USER>/ros2_jazzy/install/topic_monitor:/Users/<USER>/ros2_jazzy/install/tf2_tools:/Users/<USER>/ros2_jazzy/install/examples_tf2_py:/Users/<USER>/ros2_jazzy/install/tf2_ros_py:/Users/<USER>/ros2_jazzy/install/examples_rclpy_pointcloud_publisher:/Users/<USER>/ros2_jazzy/install/sensor_msgs_py:/Users/<USER>/ros2_jazzy/install/quality_of_service_demo_py:/Users/<USER>/ros2_jazzy/install/camera_info_manager_py:/Users/<USER>/ros2_jazzy/install/ros2doctor:/Users/<USER>/ros2_jazzy/install/lifecycle_py:/Users/<USER>/ros2_jazzy/install/examples_rclpy_minimal_subscriber:/Users/<USER>/ros2_jazzy/install/examples_rclpy_minimal_service:/Users/<USER>/ros2_jazzy/install/examples_rclpy_minimal_publisher:/Users/<USER>/ros2_jazzy/install/examples_rclpy_minimal_client:/Users/<USER>/ros2_jazzy/install/examples_rclpy_executors:/Users/<USER>/ros2_jazzy/install/demo_nodes_py:/Users/<USER>/ros2_jazzy/install/ros2component:/Users/<USER>/ros2_jazzy/install/sros2:/Users/<USER>/ros2_jazzy/install/ros2trace:/Users/<USER>/ros2_jazzy/install/ros2topic:/Users/<USER>/ros2_jazzy/install/ros2test:/Users/<USER>/ros2_jazzy/install/ros2param:/Users/<USER>/ros2_jazzy/install/ros2lifecycle:/Users/<USER>/ros2_jazzy/install/ros2service:/Users/<USER>/ros2_jazzy/install/ros2run:/Users/<USER>/ros2_jazzy/install/ros2launch:/Users/<USER>/ros2_jazzy/install/ros2pkg:/Users/<USER>/ros2_jazzy/install/ros2node:/Users/<USER>/ros2_jazzy/install/ros2multicast:/Users/<USER>/ros2_jazzy/install/ros2interface:/Users/<USER>/ros2_jazzy/install/ros2action:/Users/<USER>/ros2_jazzy/install/ros2cli:/Users/<USER>/ros2_jazzy/install/launch_testing_ros:/Users/<USER>/ros2_jazzy/install/launch_ros:/Users/<USER>/ros2_jazzy/install/examples_rclpy_minimal_action_server:/Users/<USER>/ros2_jazzy/install/examples_rclpy_minimal_action_client:/Users/<USER>/ros2_jazzy/install/examples_rclpy_guard_conditions:/Users/<USER>/ros2_jazzy/install/action_tutorials_py:/Users/<USER>/ros2_jazzy/install/ament_uncrustify:/Users/<USER>/ros2_jazzy/install/tracetools_trace:/Users/<USER>/ros2_jazzy/install/tracetools_read:/Users/<USER>/ros2_jazzy/install/test_tracetools_launch:/Users/<USER>/ros2_jazzy/install/test_ros2trace:/Users/<USER>/ros2_jazzy/install/test_launch_ros:/Users/<USER>/ros2_jazzy/install/rpyutils:/Users/<USER>/ros2_jazzy/install/rosidl_runtime_py:/Users/<USER>/ros2_jazzy/install/rosidl_pycommon:/Users/<USER>/ros2_jazzy/install/rosidl_cli:/Users/<USER>/ros2_jazzy/install/launch_pytest:/Users/<USER>/ros2_jazzy/install/launch_testing:/Users/<USER>/ros2_jazzy/install/launch_yaml:/Users/<USER>/ros2_jazzy/install/launch_xml:/Users/<USER>/ros2_jazzy/install/launch:/Users/<USER>/ros2_jazzy/install/osrf_pycommon:/Users/<USER>/ros2_jazzy/install/domain_coordinator:/Users/<USER>/ros2_jazzy/install/ament_xmllint:/Users/<USER>/ros2_jazzy/install/ament_pyflakes:/Users/<USER>/ros2_jazzy/install/ament_pycodestyle:/Users/<USER>/ros2_jazzy/install/ament_pep257:/Users/<USER>/ros2_jazzy/install/ament_pclint:/Users/<USER>/ros2_jazzy/install/ament_package:/Users/<USER>/ros2_jazzy/install/ament_mypy:/Users/<USER>/ros2_jazzy/install/ament_lint_cmake:/Users/<USER>/ros2_jazzy/install/ament_flake8:/Users/<USER>/ros2_jazzy/install/ament_copyright:/Users/<USER>/ros2_jazzy/install/ament_lint:/Users/<USER>/ros2_jazzy/install/ament_index_python:/Users/<USER>/ros2_jazzy/install/ament_cpplint:/Users/<USER>/ros2_jazzy/install/ament_cppcheck:/Users/<USER>/ros2_jazzy/install/ament_clang_tidy:/Users/<USER>/ros2_jazzy/install/ament_clang_format:/Users/<USER>/Work/drill2/onboard/install/tracks_regulator:/Users/<USER>/Work/drill2/onboard/install/state_tracker:/Users/<USER>/Work/drill2/onboard/install/rtk_connector:/Users/<USER>/Work/drill2/onboard/install/remote_connector:/Users/<USER>/Work/drill2/onboard/install/pydubins:/Users/<USER>/Work/drill2/onboard/install/path_follower:/Users/<USER>/Work/drill2/onboard/install/params_server:/Users/<USER>/Work/drill2/onboard/install/modbus_node:/Users/<USER>/Work/drill2/onboard/install/main_state_machine:/Users/<USER>/Work/drill2/onboard/install/leveler:/Users/<USER>/Work/drill2/onboard/install/launchpack:/Users/<USER>/Work/drill2/onboard/install/depth_tracker:/Users/<USER>/Work/drill2/onboard/install/can_encoder:/Users/<USER>/Work/drill2/onboard/install/can_decoder:/Users/<USER>/Work/drill2/onboard/install/base_node SHLVL=2 VIRTUAL_ENV_PROMPT=(.ros2_venv) _=/usr/bin/env /opt/homebrew/bin/cmake --build /Users/<USER>/Work/drill2/onboard/build/drill_msgs -- -j8 -l8
[12.904s] Level 1:colcon.colcon_core.environment:checking '/Users/<USER>/Work/drill2/onboard/install/drill_msgs' for CMake module files
[12.905s] Level 1:colcon.colcon_core.environment:checking '/Users/<USER>/Work/drill2/onboard/install/drill_msgs' for CMake config files
[12.907s] Level 1:colcon.colcon_core.shell:create_environment_hook('drill_msgs', 'cmake_prefix_path')
[12.907s] INFO:colcon.colcon_core.shell:Creating environment hook '/Users/<USER>/Work/drill2/onboard/install/drill_msgs/share/drill_msgs/hook/cmake_prefix_path.ps1'
[12.907s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/Users/<USER>/Work/drill2/onboard/install/drill_msgs/share/drill_msgs/hook/cmake_prefix_path.dsv'
[12.908s] INFO:colcon.colcon_core.shell:Creating environment hook '/Users/<USER>/Work/drill2/onboard/install/drill_msgs/share/drill_msgs/hook/cmake_prefix_path.sh'
[12.908s] Level 1:colcon.colcon_core.environment:checking '/Users/<USER>/Work/drill2/onboard/install/drill_msgs/lib'
[12.908s] Level 1:colcon.colcon_core.shell:create_environment_hook('drill_msgs', 'dyld_library_path')
[12.908s] INFO:colcon.colcon_core.shell:Creating environment hook '/Users/<USER>/Work/drill2/onboard/install/drill_msgs/share/drill_msgs/hook/dyld_library_path.ps1'
[12.908s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/Users/<USER>/Work/drill2/onboard/install/drill_msgs/share/drill_msgs/hook/dyld_library_path.dsv'
[12.908s] INFO:colcon.colcon_core.shell:Creating environment hook '/Users/<USER>/Work/drill2/onboard/install/drill_msgs/share/drill_msgs/hook/dyld_library_path.sh'
[12.909s] Level 1:colcon.colcon_core.environment:checking '/Users/<USER>/Work/drill2/onboard/install/drill_msgs/bin'
[12.909s] Level 1:colcon.colcon_core.environment:checking '/Users/<USER>/Work/drill2/onboard/install/drill_msgs/lib/pkgconfig/drill_msgs.pc'
[12.909s] Level 1:colcon.colcon_core.environment:checking '/Users/<USER>/Work/drill2/onboard/install/drill_msgs/lib/python3.11/site-packages'
[12.909s] Level 1:colcon.colcon_core.shell:create_environment_hook('drill_msgs', 'pythonpath')
[12.909s] INFO:colcon.colcon_core.shell:Creating environment hook '/Users/<USER>/Work/drill2/onboard/install/drill_msgs/share/drill_msgs/hook/pythonpath.ps1'
[12.909s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/Users/<USER>/Work/drill2/onboard/install/drill_msgs/share/drill_msgs/hook/pythonpath.dsv'
[12.909s] INFO:colcon.colcon_core.shell:Creating environment hook '/Users/<USER>/Work/drill2/onboard/install/drill_msgs/share/drill_msgs/hook/pythonpath.sh'
[12.910s] Level 1:colcon.colcon_core.environment:checking '/Users/<USER>/Work/drill2/onboard/install/drill_msgs/bin'
[12.910s] INFO:colcon.colcon_core.shell:Creating package script '/Users/<USER>/Work/drill2/onboard/install/drill_msgs/share/drill_msgs/package.ps1'
[12.910s] INFO:colcon.colcon_core.shell:Creating package descriptor '/Users/<USER>/Work/drill2/onboard/install/drill_msgs/share/drill_msgs/package.dsv'
[12.910s] INFO:colcon.colcon_core.shell:Creating package script '/Users/<USER>/Work/drill2/onboard/install/drill_msgs/share/drill_msgs/package.sh'
[12.911s] INFO:colcon.colcon_core.shell:Creating package script '/Users/<USER>/Work/drill2/onboard/install/drill_msgs/share/drill_msgs/package.bash'
[12.911s] INFO:colcon.colcon_core.shell:Creating package script '/Users/<USER>/Work/drill2/onboard/install/drill_msgs/share/drill_msgs/package.zsh'
[12.912s] Level 1:colcon.colcon_core.environment:create_file_with_runtime_dependencies(/Users/<USER>/Work/drill2/onboard/install/drill_msgs/share/colcon-core/packages/drill_msgs)
[12.912s] DEBUG:colcon.colcon_parallel_executor.executor.parallel:closing loop
[12.912s] DEBUG:colcon.colcon_parallel_executor.executor.parallel:loop closed
[12.912s] DEBUG:colcon.colcon_parallel_executor.executor.parallel:run_until_complete finished with '2'
[12.912s] DEBUG:colcon.colcon_core.event_reactor:joining thread
[12.915s] INFO:colcon.colcon_core.plugin_system:Skipping extension 'colcon_notification.desktop_notification.notify2': 'notify2' not found
[12.915s] INFO:colcon.colcon_core.plugin_system:Skipping extension 'colcon_notification.desktop_notification.notify_send': Not used on non-Linux systems
[12.915s] INFO:colcon.colcon_core.plugin_system:Skipping extension 'colcon_notification.desktop_notification.win32': Not used on non-Windows systems
[12.915s] INFO:colcon.colcon_notification.desktop_notification:Sending desktop notification using 'terminal_notifier'
[12.990s] DEBUG:colcon.colcon_core.event_reactor:joined thread
[12.992s] INFO:colcon.colcon_core.shell:Creating prefix script '/Users/<USER>/Work/drill2/onboard/install/local_setup.ps1'
[12.994s] INFO:colcon.colcon_core.shell:Creating prefix util module '/Users/<USER>/Work/drill2/onboard/install/_local_setup_util_ps1.py'
[13.015s] INFO:colcon.colcon_core.shell:Creating prefix chain script '/Users/<USER>/Work/drill2/onboard/install/setup.ps1'
[13.030s] INFO:colcon.colcon_core.shell:Creating prefix script '/Users/<USER>/Work/drill2/onboard/install/local_setup.sh'
[13.031s] INFO:colcon.colcon_core.shell:Creating prefix util module '/Users/<USER>/Work/drill2/onboard/install/_local_setup_util_sh.py'
[13.031s] INFO:colcon.colcon_core.shell:Creating prefix chain script '/Users/<USER>/Work/drill2/onboard/install/setup.sh'
[13.043s] INFO:colcon.colcon_core.shell:Creating prefix script '/Users/<USER>/Work/drill2/onboard/install/local_setup.bash'
[13.044s] INFO:colcon.colcon_core.shell:Creating prefix chain script '/Users/<USER>/Work/drill2/onboard/install/setup.bash'
[13.054s] INFO:colcon.colcon_core.shell:Creating prefix script '/Users/<USER>/Work/drill2/onboard/install/local_setup.zsh'
[13.055s] INFO:colcon.colcon_core.shell:Creating prefix chain script '/Users/<USER>/Work/drill2/onboard/install/setup.zsh'
