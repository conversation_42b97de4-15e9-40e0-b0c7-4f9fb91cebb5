running egg_info
writing ../../build/leveler/leveler.egg-info/PKG-INFO
writing dependency_links to ../../build/leveler/leveler.egg-info/dependency_links.txt
writing entry points to ../../build/leveler/leveler.egg-info/entry_points.txt
writing requirements to ../../build/leveler/leveler.egg-info/requires.txt
writing top-level names to ../../build/leveler/leveler.egg-info/top_level.txt
reading manifest file '../../build/leveler/leveler.egg-info/SOURCES.txt'
writing manifest file '../../build/leveler/leveler.egg-info/SOURCES.txt'
running build
running build_py
copying leveler/leveler_states.py -> /Users/<USER>/Work/drill2/onboard/build/leveler/build/lib/leveler
running install
running install_lib
copying /Users/<USER>/Work/drill2/onboard/build/leveler/build/lib/leveler/leveler_states.py -> /Users/<USER>/Work/drill2/onboard/install/leveler/lib/python3.11/site-packages/leveler
byte-compiling /Users/<USER>/Work/drill2/onboard/install/leveler/lib/python3.11/site-packages/leveler/leveler_states.py to leveler_states.cpython-311.pyc
running install_data
running install_egg_info
removing '/Users/<USER>/Work/drill2/onboard/install/leveler/lib/python3.11/site-packages/leveler-0.0.0-py3.11.egg-info' (and everything under it)
Copying ../../build/leveler/leveler.egg-info to /Users/<USER>/Work/drill2/onboard/install/leveler/lib/python3.11/site-packages/leveler-0.0.0-py3.11.egg-info
running install_scripts
Installing leveler_node script to /Users/<USER>/Work/drill2/onboard/install/leveler/lib/leveler
writing list of installed files to '/Users/<USER>/Work/drill2/onboard/build/leveler/install.log'
