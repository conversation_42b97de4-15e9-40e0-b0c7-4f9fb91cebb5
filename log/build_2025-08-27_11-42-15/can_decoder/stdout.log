running develop
Removing /Users/<USER>/Work/drill2/onboard/install/can_decoder/lib/python3.11/site-packages/can-decoder.egg-link (link to .)
running egg_info
writing ../../build/can_decoder/can_decoder.egg-info/PKG-INFO
writing dependency_links to ../../build/can_decoder/can_decoder.egg-info/dependency_links.txt
writing entry points to ../../build/can_decoder/can_decoder.egg-info/entry_points.txt
writing requirements to ../../build/can_decoder/can_decoder.egg-info/requires.txt
writing top-level names to ../../build/can_decoder/can_decoder.egg-info/top_level.txt
reading manifest file '../../build/can_decoder/can_decoder.egg-info/SOURCES.txt'
writing manifest file '../../build/can_decoder/can_decoder.egg-info/SOURCES.txt'
running build
running build_py
copying can_decoder/can_decoder.py -> /Users/<USER>/Work/drill2/onboard/build/can_decoder/build/lib/can_decoder
running install
running install_lib
creating /Users/<USER>/Work/drill2/onboard/install/can_decoder/lib/python3.11/site-packages/can_decoder
copying /Users/<USER>/Work/drill2/onboard/build/can_decoder/build/lib/can_decoder/__init__.py -> /Users/<USER>/Work/drill2/onboard/install/can_decoder/lib/python3.11/site-packages/can_decoder
copying /Users/<USER>/Work/drill2/onboard/build/can_decoder/build/lib/can_decoder/can_decoder.py -> /Users/<USER>/Work/drill2/onboard/install/can_decoder/lib/python3.11/site-packages/can_decoder
byte-compiling /Users/<USER>/Work/drill2/onboard/install/can_decoder/lib/python3.11/site-packages/can_decoder/__init__.py to __init__.cpython-311.pyc
byte-compiling /Users/<USER>/Work/drill2/onboard/install/can_decoder/lib/python3.11/site-packages/can_decoder/can_decoder.py to can_decoder.cpython-311.pyc
running install_data
copying resource/can_decoder -> /Users/<USER>/Work/drill2/onboard/install/can_decoder/share/ament_index/resource_index/packages
copying package.xml -> /Users/<USER>/Work/drill2/onboard/install/can_decoder/share/can_decoder
copying can_decoder/protocols/vep1.yaml -> /Users/<USER>/Work/drill2/onboard/install/can_decoder/share/can_decoder/protocols
copying can_decoder/protocols/ssi2.yaml -> /Users/<USER>/Work/drill2/onboard/install/can_decoder/share/can_decoder/protocols
copying can_decoder/protocols/eflp1.yaml -> /Users/<USER>/Work/drill2/onboard/install/can_decoder/share/can_decoder/protocols
copying can_decoder/protocols/inc1.yaml -> /Users/<USER>/Work/drill2/onboard/install/can_decoder/share/can_decoder/protocols
copying can_decoder/protocols/x90JacksData.yaml -> /Users/<USER>/Work/drill2/onboard/install/can_decoder/share/can_decoder/protocols
copying can_decoder/protocols/x90Encoders.yaml -> /Users/<USER>/Work/drill2/onboard/install/can_decoder/share/can_decoder/protocols
copying can_decoder/protocols/lfc.yaml -> /Users/<USER>/Work/drill2/onboard/install/can_decoder/share/can_decoder/protocols
copying can_decoder/protocols/eec1.yaml -> /Users/<USER>/Work/drill2/onboard/install/can_decoder/share/can_decoder/protocols
copying can_decoder/protocols/x90ArmData.yaml -> /Users/<USER>/Work/drill2/onboard/install/can_decoder/share/can_decoder/protocols
copying can_decoder/protocols/x90BarData.yaml -> /Users/<USER>/Work/drill2/onboard/install/can_decoder/share/can_decoder/protocols
copying can_decoder/protocols/ardp.yaml -> /Users/<USER>/Work/drill2/onboard/install/can_decoder/share/can_decoder/protocols
copying can_decoder/protocols/eec2.yaml -> /Users/<USER>/Work/drill2/onboard/install/can_decoder/share/can_decoder/protocols
copying can_decoder/protocols/x90MastLvl.yaml -> /Users/<USER>/Work/drill2/onboard/install/can_decoder/share/can_decoder/protocols
copying can_decoder/protocols/et1.yaml -> /Users/<USER>/Work/drill2/onboard/install/can_decoder/share/can_decoder/protocols
copying can_decoder/protocols/x90Prssr.yaml -> /Users/<USER>/Work/drill2/onboard/install/can_decoder/share/can_decoder/protocols
copying can_decoder/protocols/inc2.yaml -> /Users/<USER>/Work/drill2/onboard/install/can_decoder/share/can_decoder/protocols
copying can_decoder/protocols/mdp.yaml -> /Users/<USER>/Work/drill2/onboard/install/can_decoder/share/can_decoder/protocols
copying can_decoder/protocols/adp.yaml -> /Users/<USER>/Work/drill2/onboard/install/can_decoder/share/can_decoder/protocols
copying can_decoder/protocols/eec3.yaml -> /Users/<USER>/Work/drill2/onboard/install/can_decoder/share/can_decoder/protocols
copying can_decoder/protocols/hours.yaml -> /Users/<USER>/Work/drill2/onboard/install/can_decoder/share/can_decoder/protocols
copying can_decoder/protocols/x90WaterData.yaml -> /Users/<USER>/Work/drill2/onboard/install/can_decoder/share/can_decoder/protocols
copying can_decoder/protocols/x90PinsData.yaml -> /Users/<USER>/Work/drill2/onboard/install/can_decoder/share/can_decoder/protocols
copying can_decoder/protocols/x90DustFlaps.yaml -> /Users/<USER>/Work/drill2/onboard/install/can_decoder/share/can_decoder/protocols
copying can_decoder/protocols/x90ForkData.yaml -> /Users/<USER>/Work/drill2/onboard/install/can_decoder/share/can_decoder/protocols
copying can_decoder/protocols/lfe1.yaml -> /Users/<USER>/Work/drill2/onboard/install/can_decoder/share/can_decoder/protocols
copying can_decoder/protocols/x90UlsJacks.yaml -> /Users/<USER>/Work/drill2/onboard/install/can_decoder/share/can_decoder/protocols
copying can_decoder/protocols/x90WrenchData.yaml -> /Users/<USER>/Work/drill2/onboard/install/can_decoder/share/can_decoder/protocols
copying can_decoder/protocols/x90CarouselData.yaml -> /Users/<USER>/Work/drill2/onboard/install/can_decoder/share/can_decoder/protocols
running install_egg_info
Copying ../../build/can_decoder/can_decoder.egg-info to /Users/<USER>/Work/drill2/onboard/install/can_decoder/lib/python3.11/site-packages/can_decoder-0.0.0-py3.11.egg-info
running install_scripts
Installing can_decoder script to /Users/<USER>/Work/drill2/onboard/install/can_decoder/lib/can_decoder
writing list of installed files to '/Users/<USER>/Work/drill2/onboard/build/can_decoder/install.log'
