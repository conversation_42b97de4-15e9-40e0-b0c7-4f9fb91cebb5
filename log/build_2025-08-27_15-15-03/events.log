[0.000000] (-) TimerEvent: {}
[0.000622] (-) JobUnselected: {'identifier': 'base_node'}
[0.000734] (-) JobUnselected: {'identifier': 'can_decoder'}
[0.000777] (-) JobUnselected: {'identifier': 'can_encoder'}
[0.000832] (-) JobUnselected: {'identifier': 'can_msgs'}
[0.000900] (-) JobUnselected: {'identifier': 'depth_tracker'}
[0.000922] (-) JobUnselected: {'identifier': 'foxglove_bridge'}
[0.000945] (-) JobUnselected: {'identifier': 'hal_connector'}
[0.000986] (-) JobUnselected: {'identifier': 'launchpack'}
[0.001004] (-) JobUnselected: {'identifier': 'main_state_machine'}
[0.001020] (-) JobUnselected: {'identifier': 'maneuver_builder_cpp'}
[0.001034] (-) JobUnselected: {'identifier': 'modbus_node'}
[0.001048] (-) JobUnselected: {'identifier': 'params_server'}
[0.001062] (-) JobUnselected: {'identifier': 'path_follower'}
[0.001076] (-) JobUnselected: {'identifier': 'pydubins'}
[0.001089] (-) JobUnselected: {'identifier': 'remote_connector'}
[0.001102] (-) JobUnselected: {'identifier': 'rosx_introspection'}
[0.001115] (-) JobUnselected: {'identifier': 'rtk_connector'}
[0.001129] (-) JobUnselected: {'identifier': 'state_tracker'}
[0.001142] (-) JobUnselected: {'identifier': 'tracks_regulator'}
[0.001158] (drill_msgs) JobQueued: {'identifier': 'drill_msgs', 'dependencies': OrderedDict()}
[0.001174] (leveler) JobQueued: {'identifier': 'leveler', 'dependencies': OrderedDict([('drill_msgs', '/Users/<USER>/Work/drill2/onboard/install/drill_msgs'), ('base_node', '/Users/<USER>/Work/drill2/onboard/install/base_node')])}
[0.001190] (drill_msgs) JobStarted: {'identifier': 'drill_msgs'}
[0.102159] (-) TimerEvent: {}
[0.186847] (drill_msgs) JobProgress: {'identifier': 'drill_msgs', 'progress': 'cmake'}
[0.187951] (drill_msgs) JobProgress: {'identifier': 'drill_msgs', 'progress': 'build'}
[0.190203] (drill_msgs) Command: {'cmd': ['/opt/homebrew/bin/cmake', '--build', '/Users/<USER>/Work/drill2/onboard/build/drill_msgs', '--', '-j8', '-l8'], 'cwd': '/Users/<USER>/Work/drill2/onboard/build/drill_msgs', 'env': OrderedDict([('NVM_INC', '/Users/<USER>/.nvm/versions/node/v22.14.0/include/node'), ('TERM_PROGRAM', 'iTerm.app'), ('NVM_CD_FLAGS', '-q'), ('TERM', 'xterm-256color'), ('SHELL', '/bin/zsh'), ('HOMEBREW_REPOSITORY', '/opt/homebrew'), ('TMPDIR', '/var/folders/6b/9kvsjxmn2wg9n27c78lfw18r0000gn/T/'), ('TERM_PROGRAM_VERSION', '3.5.15beta1'), ('ROS_PYTHON_VERSION', '3'), ('RCUTILS_LOGGING_SEVERITY', 'DEBUG'), ('ROS_VERSION', '2'), ('COLCON_PREFIX_PATH', '/Users/<USER>/ros2_jazzy/install:/Users/<USER>/ros2_jazzy/install:/Users/<USER>/Work/drill2/onboard/install'), ('TERM_SESSION_ID', 'w0t3p0:22583761-CC84-481F-B886-691DB3AE381C'), ('AMENT_PREFIX_PATH', '/Users/<USER>/Work/drill2/onboard/install/depth_tracker:/Users/<USER>/ros2_jazzy/install/rosbag2:/Users/<USER>/ros2_jazzy/install/rosbag2_storage_default_plugins:/Users/<USER>/ros2_jazzy/install/rosbag2_storage_mcap:/Users/<USER>/ros2_jazzy/install/rosbag2_compression_zstd:/Users/<USER>/ros2_jazzy/install/mcap_vendor:/Users/<USER>/ros2_jazzy/install/zstd_vendor:/Users/<USER>/ros2_jazzy/install/rviz_visual_testing_framework:/Users/<USER>/ros2_jazzy/install/rviz2:/Users/<USER>/ros2_jazzy/install/rviz_default_plugins:/Users/<USER>/ros2_jazzy/install/rviz_common:/Users/<USER>/ros2_jazzy/install/rosbag2_examples_py:/Users/<USER>/ros2_jazzy/install/launch_testing_examples:/Users/<USER>/ros2_jazzy/install/ros2bag:/Users/<USER>/ros2_jazzy/install/rosbag2_py:/Users/<USER>/ros2_jazzy/install/rosbag2_transport:/Users/<USER>/ros2_jazzy/install/rosbag2_storage_sqlite3:/Users/<USER>/ros2_jazzy/install/rosbag2_examples_cpp:/Users/<USER>/ros2_jazzy/install/rosbag2_compression:/Users/<USER>/ros2_jazzy/install/rosbag2_cpp:/Users/<USER>/ros2_jazzy/install/rosbag2_storage:/Users/<USER>/ros2_jazzy/install/image_common:/Users/<USER>/ros2_jazzy/install/camera_info_manager:/Users/<USER>/ros2_jazzy/install/camera_calibration_parsers:/Users/<USER>/ros2_jazzy/install/yaml_cpp_vendor:/Users/<USER>/ros2_jazzy/install/interactive_markers:/Users/<USER>/ros2_jazzy/install/common_interfaces:/Users/<USER>/ros2_jazzy/install/visualization_msgs:/Users/<USER>/ros2_jazzy/install/dummy_robot_bringup:/Users/<USER>/ros2_jazzy/install/robot_state_publisher:/Users/<USER>/ros2_jazzy/install/kdl_parser:/Users/<USER>/ros2_jazzy/install/urdf:/Users/<USER>/ros2_jazzy/install/urdf_parser_plugin:/Users/<USER>/ros2_jazzy/install/turtlesim:/Users/<USER>/ros2_jazzy/install/trajectory_msgs:/Users/<USER>/ros2_jazzy/install/tracetools_test:/Users/<USER>/ros2_jazzy/install/tracetools_launch:/Users/<USER>/ros2_jazzy/install/topic_statistics_demo:/Users/<USER>/ros2_jazzy/install/topic_monitor:/Users/<USER>/ros2_jazzy/install/geometry2:/Users/<USER>/ros2_jazzy/install/tf2_tools:/Users/<USER>/ros2_jazzy/install/tf2_sensor_msgs:/Users/<USER>/ros2_jazzy/install/test_tf2:/Users/<USER>/ros2_jazzy/install/tf2_kdl:/Users/<USER>/ros2_jazzy/install/tf2_geometry_msgs:/Users/<USER>/ros2_jazzy/install/examples_tf2_py:/Users/<USER>/ros2_jazzy/install/tf2_ros_py:/Users/<USER>/ros2_jazzy/install/tf2_eigen:/Users/<USER>/ros2_jazzy/install/tf2_bullet:/Users/<USER>/ros2_jazzy/install/tf2_ros:/Users/<USER>/ros2_jazzy/install/tf2_py:/Users/<USER>/ros2_jazzy/install/tf2_msgs:/Users/<USER>/ros2_jazzy/install/tf2_eigen_kdl:/Users/<USER>/ros2_jazzy/install/laser_geometry:/Users/<USER>/ros2_jazzy/install/tf2:/Users/<USER>/ros2_jazzy/install/test_tracetools:/Users/<USER>/ros2_jazzy/install/rosbag2_test_common:/Users/<USER>/ros2_jazzy/install/test_msgs:/Users/<USER>/ros2_jazzy/install/test_communication:/Users/<USER>/ros2_jazzy/install/stereo_msgs:/Users/<USER>/ros2_jazzy/install/std_srvs:/Users/<USER>/ros2_jazzy/install/shape_msgs:/Users/<USER>/ros2_jazzy/install/examples_rclpy_pointcloud_publisher:/Users/<USER>/ros2_jazzy/install/sensor_msgs_py:/Users/<USER>/ros2_jazzy/install/quality_of_service_demo_py:/Users/<USER>/ros2_jazzy/install/quality_of_service_demo_cpp:/Users/<USER>/ros2_jazzy/install/point_cloud_transport_py:/Users/<USER>/ros2_jazzy/install/point_cloud_transport:/Users/<USER>/ros2_jazzy/install/map_msgs:/Users/<USER>/ros2_jazzy/install/intra_process_demo:/Users/<USER>/ros2_jazzy/install/image_transport:/Users/<USER>/ros2_jazzy/install/image_tools:/Users/<USER>/ros2_jazzy/install/dummy_sensors:/Users/<USER>/ros2_jazzy/install/camera_info_manager_py:/Users/<USER>/ros2_jazzy/install/sensor_msgs:/Users/<USER>/ros2_jazzy/install/ros2cli_common_extensions:/Users/<USER>/ros2_jazzy/install/ros2doctor:/Users/<USER>/ros2_jazzy/install/dummy_map_server:/Users/<USER>/ros2_jazzy/install/nav_msgs:/Users/<USER>/ros2_jazzy/install/message_filters:/Users/<USER>/ros2_jazzy/install/logging_demo:/Users/<USER>/ros2_jazzy/install/lifecycle_py:/Users/<USER>/ros2_jazzy/install/lifecycle:/Users/<USER>/ros2_jazzy/install/diagnostic_msgs:/Users/<USER>/ros2_jazzy/install/geometry_msgs:/Users/<USER>/ros2_jazzy/install/examples_rclpy_minimal_subscriber:/Users/<USER>/ros2_jazzy/install/examples_rclpy_minimal_service:/Users/<USER>/ros2_jazzy/install/examples_rclpy_minimal_publisher:/Users/<USER>/ros2_jazzy/install/examples_rclpy_minimal_client:/Users/<USER>/ros2_jazzy/install/examples_rclpy_executors:/Users/<USER>/ros2_jazzy/install/examples_rclcpp_wait_set:/Users/<USER>/ros2_jazzy/install/examples_rclcpp_multithreaded_executor:/Users/<USER>/ros2_jazzy/install/examples_rclcpp_minimal_subscriber:/Users/<USER>/ros2_jazzy/install/examples_rclcpp_minimal_publisher:/Users/<USER>/ros2_jazzy/install/examples_rclcpp_minimal_composition:/Users/<USER>/ros2_jazzy/install/examples_rclcpp_cbg_executor:/Users/<USER>/ros2_jazzy/install/demo_nodes_py:/Users/<USER>/ros2_jazzy/install/demo_nodes_cpp_native:/Users/<USER>/ros2_jazzy/install/demo_nodes_cpp:/Users/<USER>/ros2_jazzy/install/composition:/Users/<USER>/ros2_jazzy/install/actionlib_msgs:/Users/<USER>/ros2_jazzy/install/std_msgs:/Users/<USER>/ros2_jazzy/install/ros2lifecycle_test_fixtures:/Users/<USER>/ros2_jazzy/install/ros2component:/Users/<USER>/ros2_jazzy/install/rclcpp_lifecycle:/Users/<USER>/ros2_jazzy/install/action_tutorials_cpp:/Users/<USER>/ros2_jazzy/install/rclcpp_components:/Users/<USER>/ros2_jazzy/install/examples_rclcpp_minimal_action_server:/Users/<USER>/ros2_jazzy/install/examples_rclcpp_minimal_action_client:/Users/<USER>/ros2_jazzy/install/rclcpp_action:/Users/<USER>/ros2_jazzy/install/examples_rclcpp_minimal_timer:/Users/<USER>/ros2_jazzy/install/examples_rclcpp_minimal_service:/Users/<USER>/ros2_jazzy/install/examples_rclcpp_minimal_client:/Users/<USER>/ros2_jazzy/install/examples_rclcpp_async_client:/Users/<USER>/ros2_jazzy/install/rclcpp:/Users/<USER>/ros2_jazzy/install/libstatistics_collector:/Users/<USER>/ros2_jazzy/install/statistics_msgs:/Users/<USER>/ros2_jazzy/install/sros2_cmake:/Users/<USER>/ros2_jazzy/install/sros2:/Users/<USER>/ros2_jazzy/install/ros_testing:/Users/<USER>/ros2_jazzy/install/ros2trace:/Users/<USER>/ros2_jazzy/install/ros2topic:/Users/<USER>/ros2_jazzy/install/ros2test:/Users/<USER>/ros2_jazzy/install/ros2param:/Users/<USER>/ros2_jazzy/install/ros2lifecycle:/Users/<USER>/ros2_jazzy/install/ros2service:/Users/<USER>/ros2_jazzy/install/ros2run:/Users/<USER>/ros2_jazzy/install/ros2launch:/Users/<USER>/ros2_jazzy/install/ros2pkg:/Users/<USER>/ros2_jazzy/install/ros2node:/Users/<USER>/ros2_jazzy/install/ros2multicast:/Users/<USER>/ros2_jazzy/install/ros2interface:/Users/<USER>/ros2_jazzy/install/ros2action:/Users/<USER>/ros2_jazzy/install/ros2cli:/Users/<USER>/ros2_jazzy/install/launch_testing_ros:/Users/<USER>/ros2_jazzy/install/launch_ros:/Users/<USER>/ros2_jazzy/install/examples_rclpy_minimal_action_server:/Users/<USER>/ros2_jazzy/install/examples_rclpy_minimal_action_client:/Users/<USER>/ros2_jazzy/install/examples_rclpy_guard_conditions:/Users/<USER>/ros2_jazzy/install/action_tutorials_py:/Users/<USER>/ros2_jazzy/install/rclpy:/Users/<USER>/ros2_jazzy/install/rosgraph_msgs:/Users/<USER>/ros2_jazzy/install/rosbag2_interfaces:/Users/<USER>/ros2_jazzy/install/ros2cli_test_interfaces:/Users/<USER>/ros2_jazzy/install/rcl_lifecycle:/Users/<USER>/ros2_jazzy/install/rcl_action:/Users/<USER>/ros2_jazzy/install/rcl:/Users/<USER>/ros2_jazzy/install/rmw_implementation:/Users/<USER>/ros2_jazzy/install/rmw_fastrtps_dynamic_cpp:/Users/<USER>/ros2_jazzy/install/rmw_fastrtps_cpp:/Users/<USER>/ros2_jazzy/install/rmw_fastrtps_shared_cpp:/Users/<USER>/ros2_jazzy/install/rmw_cyclonedds_cpp:/Users/<USER>/ros2_jazzy/install/rmw_connextddsmicro:/Users/<USER>/ros2_jazzy/install/rmw_connextdds:/Users/<USER>/ros2_jazzy/install/rmw_connextdds_common:/Users/<USER>/ros2_jazzy/install/rmw_dds_common:/Users/<USER>/ros2_jazzy/install/composition_interfaces:/Users/<USER>/ros2_jazzy/install/rcl_interfaces:/Users/<USER>/ros2_jazzy/install/pendulum_msgs:/Users/<USER>/ros2_jazzy/install/lifecycle_msgs:/Users/<USER>/ros2_jazzy/install/example_interfaces:/Users/<USER>/ros2_jazzy/install/action_tutorials_interfaces:/Users/<USER>/ros2_jazzy/install/rosidl_default_runtime:/Users/<USER>/ros2_jazzy/install/rosidl_default_generators:/Users/<USER>/ros2_jazzy/install/action_msgs:/Users/<USER>/ros2_jazzy/install/unique_identifier_msgs:/Users/<USER>/ros2_jazzy/install/type_description_interfaces:/Users/<USER>/ros2_jazzy/install/service_msgs:/Users/<USER>/ros2_jazzy/install/builtin_interfaces:/Users/<USER>/ros2_jazzy/install/rosidl_core_runtime:/Users/<USER>/ros2_jazzy/install/rosidl_core_generators:/Users/<USER>/ros2_jazzy/install/rosidl_generator_py:/Users/<USER>/ros2_jazzy/install/ament_lint_common:/Users/<USER>/ros2_jazzy/install/ament_cmake_uncrustify:/Users/<USER>/ros2_jazzy/install/ament_uncrustify:/Users/<USER>/ros2_jazzy/install/uncrustify_vendor:/Users/<USER>/ros2_jazzy/install/tracetools_trace:/Users/<USER>/ros2_jazzy/install/tracetools_read:/Users/<USER>/ros2_jazzy/install/tracetools:/Users/<USER>/ros2_jazzy/install/pluginlib:/Users/<USER>/ros2_jazzy/install/tinyxml2_vendor:/Users/<USER>/ros2_jazzy/install/test_tracetools_launch:/Users/<USER>/ros2_jazzy/install/test_security:/Users/<USER>/ros2_jazzy/install/test_ros2trace:/Users/<USER>/ros2_jazzy/install/test_rmw_implementation:/Users/<USER>/ros2_jazzy/install/test_rclcpp:/Users/<USER>/ros2_jazzy/install/test_quality_of_service:/Users/<USER>/ros2_jazzy/install/test_launch_testing:/Users/<USER>/ros2_jazzy/install/test_launch_ros:/Users/<USER>/ros2_jazzy/install/test_interface_files:/Users/<USER>/ros2_jazzy/install/test_cli_remapping:/Users/<USER>/ros2_jazzy/install/test_cli:/Users/<USER>/ros2_jazzy/install/tango_icons_vendor:/Users/<USER>/ros2_jazzy/install/sqlite3_vendor:/Users/<USER>/ros2_jazzy/install/rcl_logging_spdlog:/Users/<USER>/ros2_jazzy/install/spdlog_vendor:/Users/<USER>/ros2_jazzy/install/shared_queues_vendor:/Users/<USER>/ros2_jazzy/install/rviz_rendering_tests:/Users/<USER>/ros2_jazzy/install/rviz_rendering:/Users/<USER>/ros2_jazzy/install/rviz_ogre_vendor:/Users/<USER>/ros2_jazzy/install/rviz_assimp_vendor:/Users/<USER>/ros2_jazzy/install/rti_connext_dds_cmake_module:/Users/<USER>/ros2_jazzy/install/lttngpy:/Users/<USER>/ros2_jazzy/install/rpyutils:/Users/<USER>/ros2_jazzy/install/rosidl_typesupport_tests:/Users/<USER>/ros2_jazzy/install/rosidl_typesupport_introspection_tests:/Users/<USER>/ros2_jazzy/install/rosidl_typesupport_cpp:/Users/<USER>/ros2_jazzy/install/rosidl_typesupport_introspection_cpp:/Users/<USER>/ros2_jazzy/install/rosidl_typesupport_c:/Users/<USER>/ros2_jazzy/install/rosidl_typesupport_introspection_c:/Users/<USER>/ros2_jazzy/install/rosidl_typesupport_fastrtps_c:/Users/<USER>/ros2_jazzy/install/rosidl_typesupport_fastrtps_cpp:/Users/<USER>/ros2_jazzy/install/rosidl_generator_cpp:/Users/<USER>/ros2_jazzy/install/rosidl_runtime_cpp:/Users/<USER>/ros2_jazzy/install/rosidl_dynamic_typesupport_fastrtps:/Users/<USER>/ros2_jazzy/install/rcl_yaml_param_parser:/Users/<USER>/ros2_jazzy/install/rmw:/Users/<USER>/ros2_jazzy/install/rosidl_dynamic_typesupport:/Users/<USER>/ros2_jazzy/install/rosidl_runtime_c:/Users/<USER>/ros2_jazzy/install/rosidl_generator_c:/Users/<USER>/ros2_jazzy/install/rosidl_typesupport_interface:/Users/<USER>/ros2_jazzy/install/rosidl_runtime_py:/Users/<USER>/ros2_jazzy/install/rosidl_generator_dds_idl:/Users/<USER>/ros2_jazzy/install/rosidl_cmake:/Users/<USER>/ros2_jazzy/install/rosidl_pycommon:/Users/<USER>/ros2_jazzy/install/rosidl_generator_type_description:/Users/<USER>/ros2_jazzy/install/rosidl_parser:/Users/<USER>/ros2_jazzy/install/rosidl_generator_tests:/Users/<USER>/ros2_jazzy/install/rosidl_adapter:/Users/<USER>/ros2_jazzy/install/rosidl_cli:/Users/<USER>/ros2_jazzy/install/rosbag2_tests:/Users/<USER>/ros2_jazzy/install/rosbag2_test_msgdefs:/Users/<USER>/ros2_jazzy/install/ros_environment:/Users/<USER>/ros2_jazzy/install/rmw_implementation_cmake:/Users/<USER>/ros2_jazzy/install/resource_retriever:/Users/<USER>/ros2_jazzy/install/class_loader:/Users/<USER>/ros2_jazzy/install/rcpputils:/Users/<USER>/ros2_jazzy/install/rcl_logging_noop:/Users/<USER>/ros2_jazzy/install/rcl_logging_interface:/Users/<USER>/ros2_jazzy/install/rcutils:/Users/<USER>/ros2_jazzy/install/python_qt_binding:/Users/<USER>/ros2_jazzy/install/python_orocos_kdl_vendor:/Users/<USER>/ros2_jazzy/install/launch_testing_ament_cmake:/Users/<USER>/ros2_jazzy/install/python_cmake_module:/Users/<USER>/ros2_jazzy/install/pybind11_vendor:/Users/<USER>/ros2_jazzy/install/performance_test_fixture:/Users/<USER>/ros2_jazzy/install/launch_pytest:/Users/<USER>/ros2_jazzy/install/launch_testing:/Users/<USER>/ros2_jazzy/install/launch_yaml:/Users/<USER>/ros2_jazzy/install/launch_xml:/Users/<USER>/ros2_jazzy/install/launch:/Users/<USER>/ros2_jazzy/install/osrf_pycommon:/Users/<USER>/ros2_jazzy/install/orocos_kdl_vendor:/Users/<USER>/ros2_jazzy/install/mimick_vendor:/Users/<USER>/ros2_jazzy/install/libyaml_vendor:/Users/<USER>/ros2_jazzy/install/liblz4_vendor:/Users/<USER>/ros2_jazzy/install/libcurl_vendor:/Users/<USER>/ros2_jazzy/install/keyboard_handler:/Users/<USER>/ros2_jazzy/install/gz_math_vendor:/Users/<USER>/ros2_jazzy/install/gz_utils_vendor:/Users/<USER>/ros2_jazzy/install/gz_cmake_vendor:/Users/<USER>/ros2_jazzy/install/ament_cmake_ros:/Users/<USER>/ros2_jazzy/install/ament_cmake_auto:/Users/<USER>/ros2_jazzy/install/ament_cmake_gmock:/Users/<USER>/ros2_jazzy/install/ament_cmake_gtest:/Users/<USER>/ros2_jazzy/install/ament_cmake_google_benchmark:/Users/<USER>/ros2_jazzy/install/fastrtps_cmake_module:/Users/<USER>/ros2_jazzy/install/eigen3_cmake_module:/Users/<USER>/ros2_jazzy/install/domain_coordinator:/Users/<USER>/ros2_jazzy/install/console_bridge_vendor:/Users/<USER>/ros2_jazzy/install/ament_cmake_xmllint:/Users/<USER>/ros2_jazzy/install/ament_xmllint:/Users/<USER>/ros2_jazzy/install/ament_cmake_pyflakes:/Users/<USER>/ros2_jazzy/install/ament_pyflakes:/Users/<USER>/ros2_jazzy/install/ament_cmake_pycodestyle:/Users/<USER>/ros2_jazzy/install/ament_pycodestyle:/Users/<USER>/ros2_jazzy/install/ament_cmake_pep257:/Users/<USER>/ros2_jazzy/install/ament_pep257:/Users/<USER>/ros2_jazzy/install/ament_cmake_pclint:/Users/<USER>/ros2_jazzy/install/ament_pclint:/Users/<USER>/ros2_jazzy/install/ament_lint_auto:/Users/<USER>/ros2_jazzy/install/ament_cmake:/Users/<USER>/ros2_jazzy/install/ament_cmake_version:/Users/<USER>/ros2_jazzy/install/ament_cmake_vendor_package:/Users/<USER>/ros2_jazzy/install/ament_cmake_pytest:/Users/<USER>/ros2_jazzy/install/ament_cmake_mypy:/Users/<USER>/ros2_jazzy/install/ament_cmake_lint_cmake:/Users/<USER>/ros2_jazzy/install/ament_cmake_flake8:/Users/<USER>/ros2_jazzy/install/ament_cmake_cpplint:/Users/<USER>/ros2_jazzy/install/ament_cmake_cppcheck:/Users/<USER>/ros2_jazzy/install/ament_cmake_copyright:/Users/<USER>/ros2_jazzy/install/ament_cmake_clang_tidy:/Users/<USER>/ros2_jazzy/install/ament_cmake_clang_format:/Users/<USER>/ros2_jazzy/install/ament_cmake_test:/Users/<USER>/ros2_jazzy/install/ament_cmake_target_dependencies:/Users/<USER>/ros2_jazzy/install/ament_cmake_python:/Users/<USER>/ros2_jazzy/install/ament_cmake_export_dependencies:/Users/<USER>/ros2_jazzy/install/ament_cmake_libraries:/Users/<USER>/ros2_jazzy/install/ament_cmake_include_directories:/Users/<USER>/ros2_jazzy/install/ament_cmake_gen_version_h:/Users/<USER>/ros2_jazzy/install/ament_cmake_export_targets:/Users/<USER>/ros2_jazzy/install/ament_cmake_export_link_flags:/Users/<USER>/ros2_jazzy/install/ament_cmake_export_interfaces:/Users/<USER>/ros2_jazzy/install/ament_cmake_export_libraries:/Users/<USER>/ros2_jazzy/install/ament_cmake_export_include_directories:/Users/<USER>/ros2_jazzy/install/ament_cmake_export_definitions:/Users/<USER>/ros2_jazzy/install/ament_cmake_core:/Users/<USER>/ros2_jazzy/install/ament_package:/Users/<USER>/ros2_jazzy/install/ament_mypy:/Users/<USER>/ros2_jazzy/install/ament_lint_cmake:/Users/<USER>/ros2_jazzy/install/ament_flake8:/Users/<USER>/ros2_jazzy/install/ament_copyright:/Users/<USER>/ros2_jazzy/install/ament_lint:/Users/<USER>/ros2_jazzy/install/ament_index_python:/Users/<USER>/ros2_jazzy/install/ament_index_cpp:/Users/<USER>/ros2_jazzy/install/ament_cpplint:/Users/<USER>/ros2_jazzy/install/ament_cppcheck:/Users/<USER>/ros2_jazzy/install/ament_clang_tidy:/Users/<USER>/ros2_jazzy/install/ament_clang_format:/Users/<USER>/Work/drill2/onboard/install/tracks_regulator:/Users/<USER>/Work/drill2/onboard/install/state_tracker:/Users/<USER>/Work/drill2/onboard/install/rtk_connector:/Users/<USER>/Work/drill2/onboard/install/remote_connector:/Users/<USER>/Work/drill2/onboard/install/pydubins:/Users/<USER>/Work/drill2/onboard/install/path_follower:/Users/<USER>/Work/drill2/onboard/install/params_server:/Users/<USER>/Work/drill2/onboard/install/modbus_node:/Users/<USER>/Work/drill2/onboard/install/main_state_machine:/Users/<USER>/Work/drill2/onboard/install/maneuver_builder_cpp:/Users/<USER>/Work/drill2/onboard/install/leveler:/Users/<USER>/Work/drill2/onboard/install/launchpack:/Users/<USER>/Work/drill2/onboard/install/can_encoder:/Users/<USER>/Work/drill2/onboard/install/can_decoder:/Users/<USER>/Work/drill2/onboard/install/base_node:/Users/<USER>/Work/drill2/onboard/install/drill_msgs:/Users/<USER>/Work/drill2/onboard/install/can_msgs'), ('NVM_DIR', '/Users/<USER>/.nvm'), ('USER', 'frontwise'), ('COMMAND_MODE', 'unix2003'), ('SSH_AUTH_SOCK', '/private/tmp/com.apple.launchd.QIsZBTGupl/Listeners'), ('__CF_USER_TEXT_ENCODING', '0x1F5:0x0:0x0'), ('TERM_FEATURES', 'T3LrMSc7UUw9Ts3BFGsSyHNoSxF'), ('VIRTUAL_ENV', '/Users/<USER>/.ros2_venv'), ('TERMINFO_DIRS', '/Applications/iTerm.app/Contents/Resources/terminfo:/usr/share/terminfo'), ('PATH', '/Users/<USER>/ros2_jazzy/install/rviz2/bin:/Users/<USER>/ros2_jazzy/install/urdfdom/bin:/Users/<USER>/ros2_jazzy/install/ros2cli/bin:/Users/<USER>/ros2_jazzy/install/ament_uncrustify/bin:/Users/<USER>/ros2_jazzy/install/uncrustify_vendor/opt/uncrustify_vendor/bin:/Users/<USER>/ros2_jazzy/install/rviz_ogre_vendor/opt/rviz_ogre_vendor/bin:/Users/<USER>/ros2_jazzy/install/rosidl_cli/bin:/Users/<USER>/ros2_jazzy/install/launch_testing/bin:/Users/<USER>/ros2_jazzy/install/iceoryx_introspection/bin:/Users/<USER>/ros2_jazzy/install/cyclonedds/bin:/Users/<USER>/ros2_jazzy/install/iceoryx_posh/bin:/Users/<USER>/ros2_jazzy/install/fastrtps/bin:/Users/<USER>/ros2_jazzy/install/foonathan_memory_vendor/bin:/Users/<USER>/ros2_jazzy/install/ament_xmllint/bin:/Users/<USER>/ros2_jazzy/install/ament_pyflakes/bin:/Users/<USER>/ros2_jazzy/install/ament_pycodestyle/bin:/Users/<USER>/ros2_jazzy/install/ament_pep257/bin:/Users/<USER>/ros2_jazzy/install/ament_pclint/bin:/Users/<USER>/ros2_jazzy/install/ament_mypy/bin:/Users/<USER>/ros2_jazzy/install/ament_lint_cmake/bin:/Users/<USER>/ros2_jazzy/install/ament_flake8/bin:/Users/<USER>/ros2_jazzy/install/ament_copyright/bin:/Users/<USER>/ros2_jazzy/install/ament_index_python/bin:/Users/<USER>/ros2_jazzy/install/ament_cpplint/bin:/Users/<USER>/ros2_jazzy/install/ament_cppcheck/bin:/Users/<USER>/ros2_jazzy/install/ament_clang_tidy/bin:/Users/<USER>/ros2_jazzy/install/ament_clang_format/bin:/Users/<USER>/.ros2_venv/bin:/Users/<USER>/.codeium/windsurf/bin:/Users/<USER>/yandex-cloud/bin:/Users/<USER>/.nvm/versions/node/v22.14.0/bin:/opt/homebrew/bin:/opt/homebrew/sbin:/usr/local/bin:/System/Cryptexes/App/usr/bin:/usr/bin:/bin:/usr/sbin:/sbin:/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/local/bin:/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/bin:/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/appleinternal/bin:/opt/X11/bin:/Library/Apple/usr/bin:/usr/local/share/dotnet:~/.dotnet/tools:/Library/Frameworks/Mono.framework/Versions/Current/Commands:/Applications/iTerm.app/Contents/Resources/utilities:/Users/<USER>/.spoofdpi/bin'), ('LaunchInstanceID', '672A663D-5DC3-4014-BF99-18B0DF3B92FC'), ('__CFBundleIdentifier', 'com.googlecode.iterm2'), ('PWD', '/Users/<USER>/Work/drill2/onboard/build/drill_msgs'), ('COLCON', '1'), ('LANG', 'ru_RU.UTF-8'), ('ITERM_PROFILE', 'Default'), ('XPC_FLAGS', '0x0'), ('XPC_SERVICE_NAME', '0'), ('OPENSSL_ROOT_DIR', '/opt/homebrew/opt/openssl@3'), ('SHLVL', '2'), ('HOME', '/Users/<USER>'), ('COLORFGBG', '15;0'), ('ROS_DISTRO', 'jazzy'), ('LC_TERMINAL_VERSION', '3.5.15beta1'), ('HOMEBREW_PREFIX', '/opt/homebrew'), ('DYLD_LIBRARY_PATH', '/Users/<USER>/ros2_jazzy/install/rosbag2_storage_mcap/lib:/Users/<USER>/ros2_jazzy/install/rosbag2_compression_zstd/lib:/Users/<USER>/ros2_jazzy/install/mcap_vendor/lib:/Users/<USER>/ros2_jazzy/install/rviz_default_plugins/lib:/Users/<USER>/ros2_jazzy/install/rviz_common/lib:/Users/<USER>/ros2_jazzy/install/rosbag2_transport/lib:/Users/<USER>/ros2_jazzy/install/rosbag2_storage_sqlite3/lib:/Users/<USER>/ros2_jazzy/install/rosbag2_compression/lib:/Users/<USER>/ros2_jazzy/install/rosbag2_cpp/lib:/Users/<USER>/ros2_jazzy/install/rosbag2_storage/lib:/Users/<USER>/ros2_jazzy/install/camera_info_manager/lib:/Users/<USER>/ros2_jazzy/install/camera_calibration_parsers/lib:/Users/<USER>/ros2_jazzy/install/interactive_markers/lib:/Users/<USER>/ros2_jazzy/install/visualization_msgs/lib:/Users/<USER>/ros2_jazzy/install/robot_state_publisher/lib:/Users/<USER>/ros2_jazzy/install/kdl_parser/lib:/Users/<USER>/ros2_jazzy/install/urdf/lib:/Users/<USER>/ros2_jazzy/install/urdfdom/lib:/Users/<USER>/ros2_jazzy/install/turtlesim/lib:/Users/<USER>/ros2_jazzy/install/trajectory_msgs/lib:/Users/<USER>/ros2_jazzy/install/tf2_ros/lib:/Users/<USER>/ros2_jazzy/install/tf2_msgs/lib:/Users/<USER>/ros2_jazzy/install/tf2_eigen_kdl/lib:/Users/<USER>/ros2_jazzy/install/laser_geometry/lib:/Users/<USER>/ros2_jazzy/install/tf2/lib:/Users/<USER>/ros2_jazzy/install/test_msgs/lib:/Users/<USER>/ros2_jazzy/install/stereo_msgs/lib:/Users/<USER>/ros2_jazzy/install/std_srvs/lib:/Users/<USER>/ros2_jazzy/install/shape_msgs/lib:/Users/<USER>/ros2_jazzy/install/quality_of_service_demo_cpp/lib:/Users/<USER>/ros2_jazzy/install/point_cloud_transport/lib:/Users/<USER>/ros2_jazzy/install/map_msgs/lib:/Users/<USER>/ros2_jazzy/install/image_transport/lib:/Users/<USER>/ros2_jazzy/install/image_tools/lib:/Users/<USER>/ros2_jazzy/install/sensor_msgs/lib:/Users/<USER>/ros2_jazzy/install/nav_msgs/lib:/Users/<USER>/ros2_jazzy/install/message_filters/lib:/Users/<USER>/ros2_jazzy/install/logging_demo/lib:/Users/<USER>/ros2_jazzy/install/diagnostic_msgs/lib:/Users/<USER>/ros2_jazzy/install/geometry_msgs/lib:/Users/<USER>/ros2_jazzy/install/examples_rclcpp_wait_set/lib:/Users/<USER>/ros2_jazzy/install/examples_rclcpp_minimal_subscriber/lib:/Users/<USER>/ros2_jazzy/install/examples_rclcpp_minimal_composition/lib:/Users/<USER>/ros2_jazzy/install/demo_nodes_cpp_native/lib:/Users/<USER>/ros2_jazzy/install/demo_nodes_cpp/lib:/Users/<USER>/ros2_jazzy/install/composition/lib:/Users/<USER>/ros2_jazzy/install/actionlib_msgs/lib:/Users/<USER>/ros2_jazzy/install/std_msgs/lib:/Users/<USER>/ros2_jazzy/install/rclcpp_lifecycle/lib:/Users/<USER>/ros2_jazzy/install/action_tutorials_cpp/lib:/Users/<USER>/ros2_jazzy/install/rclcpp_components/lib:/Users/<USER>/ros2_jazzy/install/rclcpp_action/lib:/Users/<USER>/ros2_jazzy/install/rclcpp/lib:/Users/<USER>/ros2_jazzy/install/libstatistics_collector/lib:/Users/<USER>/ros2_jazzy/install/statistics_msgs/lib:/Users/<USER>/ros2_jazzy/install/rclpy/lib:/Users/<USER>/ros2_jazzy/install/rosgraph_msgs/lib:/Users/<USER>/ros2_jazzy/install/rosbag2_interfaces/lib:/Users/<USER>/ros2_jazzy/install/ros2cli_test_interfaces/lib:/Users/<USER>/ros2_jazzy/install/rcl_lifecycle/lib:/Users/<USER>/ros2_jazzy/install/rcl_action/lib:/Users/<USER>/ros2_jazzy/install/rcl/lib:/Users/<USER>/ros2_jazzy/install/rmw_implementation/lib:/Users/<USER>/ros2_jazzy/install/rmw_fastrtps_dynamic_cpp/lib:/Users/<USER>/ros2_jazzy/install/rmw_fastrtps_cpp/lib:/Users/<USER>/ros2_jazzy/install/rmw_fastrtps_shared_cpp/lib:/Users/<USER>/ros2_jazzy/install/rmw_cyclonedds_cpp/lib:/Users/<USER>/ros2_jazzy/install/rmw_dds_common/lib:/Users/<USER>/ros2_jazzy/install/composition_interfaces/lib:/Users/<USER>/ros2_jazzy/install/rcl_interfaces/lib:/Users/<USER>/ros2_jazzy/install/pendulum_msgs/lib:/Users/<USER>/ros2_jazzy/install/lifecycle_msgs/lib:/Users/<USER>/ros2_jazzy/install/example_interfaces/lib:/Users/<USER>/ros2_jazzy/install/action_tutorials_interfaces/lib:/Users/<USER>/ros2_jazzy/install/action_msgs/lib:/Users/<USER>/ros2_jazzy/install/unique_identifier_msgs/lib:/Users/<USER>/ros2_jazzy/install/type_description_interfaces/lib:/Users/<USER>/ros2_jazzy/install/service_msgs/lib:/Users/<USER>/ros2_jazzy/install/builtin_interfaces/lib:/Users/<USER>/ros2_jazzy/install/rcl_logging_spdlog/lib:/Users/<USER>/ros2_jazzy/install/rviz_rendering/lib:/Users/<USER>/ros2_jazzy/install/rviz_ogre_vendor/opt/rviz_ogre_vendor/lib:/Users/<USER>/ros2_jazzy/install/rosidl_typesupport_cpp/lib:/Users/<USER>/ros2_jazzy/install/rosidl_typesupport_introspection_cpp/lib:/Users/<USER>/ros2_jazzy/install/rosidl_typesupport_c/lib:/Users/<USER>/ros2_jazzy/install/rosidl_typesupport_introspection_c/lib:/Users/<USER>/ros2_jazzy/install/rosidl_typesupport_fastrtps_c/lib:/Users/<USER>/ros2_jazzy/install/rosidl_typesupport_fastrtps_cpp/lib:/Users/<USER>/ros2_jazzy/install/rosidl_dynamic_typesupport_fastrtps/lib:/Users/<USER>/ros2_jazzy/install/rcl_yaml_param_parser/lib:/Users/<USER>/ros2_jazzy/install/rmw/lib:/Users/<USER>/ros2_jazzy/install/rosidl_dynamic_typesupport/lib:/Users/<USER>/ros2_jazzy/install/rosidl_runtime_c/lib:/Users/<USER>/ros2_jazzy/install/rosbag2_test_msgdefs/lib:/Users/<USER>/ros2_jazzy/install/resource_retriever/lib:/Users/<USER>/ros2_jazzy/install/class_loader/lib:/Users/<USER>/ros2_jazzy/install/rcpputils/lib:/Users/<USER>/ros2_jazzy/install/rcl_logging_noop/lib:/Users/<USER>/ros2_jazzy/install/rcl_logging_interface/lib:/Users/<USER>/ros2_jazzy/install/rcutils/lib:/Users/<USER>/ros2_jazzy/install/performance_test_fixture/lib:/Users/<USER>/ros2_jazzy/install/osrf_testing_tools_cpp/lib:/Users/<USER>/ros2_jazzy/install/mimick_vendor/opt/mimick_vendor/lib:/Users/<USER>/ros2_jazzy/install/keyboard_handler/lib:/Users/<USER>/ros2_jazzy/install/iceoryx_introspection/lib:/Users/<USER>/ros2_jazzy/install/cyclonedds/lib:/Users/<USER>/ros2_jazzy/install/iceoryx_posh/lib:/Users/<USER>/ros2_jazzy/install/iceoryx_hoofs/lib:/Users/<USER>/ros2_jazzy/install/iceoryx_binding_c/lib:/Users/<USER>/ros2_jazzy/install/gz_math_vendor/opt/gz_math_vendor/lib:/Users/<USER>/ros2_jazzy/install/gz_utils_vendor/opt/gz_utils_vendor/lib:/Users/<USER>/ros2_jazzy/install/gz_cmake_vendor/opt/gz_cmake_vendor/lib:/Users/<USER>/ros2_jazzy/install/fastrtps/lib:/Users/<USER>/ros2_jazzy/install/fastcdr/lib:/Users/<USER>/ros2_jazzy/install/google_benchmark_vendor/lib:/Users/<USER>/ros2_jazzy/install/ament_index_cpp/lib:/Users/<USER>/Work/drill2/onboard/install/drill_msgs/lib:/Users/<USER>/Work/drill2/onboard/install/can_msgs/lib'), ('ITERM_SESSION_ID', 'w0t3p0:22583761-CC84-481F-B886-691DB3AE381C'), ('PYTHONPATH', '/Users/<USER>/Work/drill2/onboard/install/depth_tracker/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/install/rosbag2_storage_mcap/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/build/rosbag2_examples_py:/Users/<USER>/ros2_jazzy/install/rosbag2_examples_py/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/build/launch_testing_examples:/Users/<USER>/ros2_jazzy/install/launch_testing_examples/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/build/ros2bag:/Users/<USER>/ros2_jazzy/install/ros2bag/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/install/rosbag2_py/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/install/rosbag2_storage_sqlite3/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/install/interactive_markers/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/install/visualization_msgs/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/install/turtlesim/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/install/trajectory_msgs/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/build/tracetools_test:/Users/<USER>/ros2_jazzy/install/tracetools_test/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/build/tracetools_launch:/Users/<USER>/ros2_jazzy/install/tracetools_launch/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/build/topic_monitor:/Users/<USER>/ros2_jazzy/install/topic_monitor/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/build/tf2_tools:/Users/<USER>/ros2_jazzy/install/tf2_tools/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/install/tf2_sensor_msgs/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/install/tf2_kdl/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/install/tf2_geometry_msgs/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/build/examples_tf2_py:/Users/<USER>/ros2_jazzy/install/examples_tf2_py/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/build/tf2_ros_py:/Users/<USER>/ros2_jazzy/install/tf2_ros_py/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/install/tf2_py/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/install/tf2_msgs/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/install/laser_geometry/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/install/rosbag2_test_common/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/install/test_msgs/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/install/stereo_msgs/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/install/std_srvs/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/install/shape_msgs/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/build/examples_rclpy_pointcloud_publisher:/Users/<USER>/ros2_jazzy/install/examples_rclpy_pointcloud_publisher/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/build/sensor_msgs_py:/Users/<USER>/ros2_jazzy/install/sensor_msgs_py/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/build/quality_of_service_demo_py:/Users/<USER>/ros2_jazzy/install/quality_of_service_demo_py/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/install/point_cloud_transport_py/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/install/map_msgs/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/build/camera_info_manager_py:/Users/<USER>/ros2_jazzy/install/camera_info_manager_py/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/install/sensor_msgs/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/build/ros2doctor:/Users/<USER>/ros2_jazzy/install/ros2doctor/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/install/nav_msgs/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/install/message_filters/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/install/logging_demo/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/build/lifecycle_py:/Users/<USER>/ros2_jazzy/install/lifecycle_py/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/install/diagnostic_msgs/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/install/geometry_msgs/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/build/examples_rclpy_minimal_subscriber:/Users/<USER>/ros2_jazzy/install/examples_rclpy_minimal_subscriber/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/build/examples_rclpy_minimal_service:/Users/<USER>/ros2_jazzy/install/examples_rclpy_minimal_service/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/build/examples_rclpy_minimal_publisher:/Users/<USER>/ros2_jazzy/install/examples_rclpy_minimal_publisher/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/build/examples_rclpy_minimal_client:/Users/<USER>/ros2_jazzy/install/examples_rclpy_minimal_client/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/build/examples_rclpy_executors:/Users/<USER>/ros2_jazzy/install/examples_rclpy_executors/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/build/demo_nodes_py:/Users/<USER>/ros2_jazzy/install/demo_nodes_py/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/install/actionlib_msgs/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/install/std_msgs/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/build/ros2component:/Users/<USER>/ros2_jazzy/install/ros2component/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/install/statistics_msgs/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/build/sros2:/Users/<USER>/ros2_jazzy/install/sros2/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/build/ros2trace:/Users/<USER>/ros2_jazzy/install/ros2trace/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/build/ros2topic:/Users/<USER>/ros2_jazzy/install/ros2topic/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/build/ros2test:/Users/<USER>/ros2_jazzy/install/ros2test/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/build/ros2param:/Users/<USER>/ros2_jazzy/install/ros2param/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/build/ros2lifecycle:/Users/<USER>/ros2_jazzy/install/ros2lifecycle/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/build/ros2service:/Users/<USER>/ros2_jazzy/install/ros2service/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/build/ros2run:/Users/<USER>/ros2_jazzy/install/ros2run/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/build/ros2launch:/Users/<USER>/ros2_jazzy/install/ros2launch/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/build/ros2pkg:/Users/<USER>/ros2_jazzy/install/ros2pkg/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/build/ros2node:/Users/<USER>/ros2_jazzy/install/ros2node/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/build/ros2multicast:/Users/<USER>/ros2_jazzy/install/ros2multicast/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/build/ros2interface:/Users/<USER>/ros2_jazzy/install/ros2interface/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/build/ros2action:/Users/<USER>/ros2_jazzy/install/ros2action/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/build/ros2cli:/Users/<USER>/ros2_jazzy/install/ros2cli/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/build/launch_testing_ros:/Users/<USER>/ros2_jazzy/install/launch_testing_ros/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/build/launch_ros:/Users/<USER>/ros2_jazzy/install/launch_ros/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/build/examples_rclpy_minimal_action_server:/Users/<USER>/ros2_jazzy/install/examples_rclpy_minimal_action_server/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/build/examples_rclpy_minimal_action_client:/Users/<USER>/ros2_jazzy/install/examples_rclpy_minimal_action_client/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/build/examples_rclpy_guard_conditions:/Users/<USER>/ros2_jazzy/install/examples_rclpy_guard_conditions/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/build/action_tutorials_py:/Users/<USER>/ros2_jazzy/install/action_tutorials_py/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/install/rclpy/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/install/rosgraph_msgs/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/install/rosbag2_interfaces/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/install/ros2cli_test_interfaces/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/install/rmw_dds_common/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/install/composition_interfaces/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/install/rcl_interfaces/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/install/pendulum_msgs/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/install/lifecycle_msgs/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/install/example_interfaces/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/install/action_tutorials_interfaces/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/install/action_msgs/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/install/unique_identifier_msgs/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/install/type_description_interfaces/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/install/service_msgs/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/install/builtin_interfaces/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/install/rosidl_generator_py/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/build/ament_uncrustify:/Users/<USER>/ros2_jazzy/install/ament_uncrustify/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/build/tracetools_trace:/Users/<USER>/ros2_jazzy/install/tracetools_trace/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/build/tracetools_read:/Users/<USER>/ros2_jazzy/install/tracetools_read/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/build/test_tracetools_launch:/Users/<USER>/ros2_jazzy/install/test_tracetools_launch/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/build/test_ros2trace:/Users/<USER>/ros2_jazzy/install/test_ros2trace/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/build/test_launch_ros:/Users/<USER>/ros2_jazzy/install/test_launch_ros/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/install/lttngpy/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/build/rpyutils:/Users/<USER>/ros2_jazzy/install/rpyutils/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/install/rosidl_typesupport_cpp/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/install/rosidl_typesupport_introspection_cpp/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/install/rosidl_typesupport_c/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/install/rosidl_typesupport_introspection_c/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/install/rosidl_typesupport_fastrtps_c/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/install/rosidl_typesupport_fastrtps_cpp/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/install/rosidl_generator_cpp/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/install/rosidl_generator_c/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/build/rosidl_runtime_py:/Users/<USER>/ros2_jazzy/install/rosidl_runtime_py/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/install/rosidl_generator_dds_idl/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/install/rosidl_cmake/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/build/rosidl_pycommon:/Users/<USER>/ros2_jazzy/install/rosidl_pycommon/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/install/rosidl_generator_type_description/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/install/rosidl_parser/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/install/rosidl_adapter/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/build/rosidl_cli:/Users/<USER>/ros2_jazzy/install/rosidl_cli/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/install/rosbag2_test_msgdefs/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/install/resource_retriever/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/install/rcutils/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/install/python_qt_binding/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/install/python_orocos_kdl_vendor/lib/python3.11/dist-packages:/Users/<USER>/ros2_jazzy/build/launch_pytest:/Users/<USER>/ros2_jazzy/install/launch_pytest/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/build/launch_testing:/Users/<USER>/ros2_jazzy/install/launch_testing/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/build/launch_yaml:/Users/<USER>/ros2_jazzy/install/launch_yaml/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/build/launch_xml:/Users/<USER>/ros2_jazzy/install/launch_xml/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/build/launch:/Users/<USER>/ros2_jazzy/install/launch/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/build/osrf_pycommon:/Users/<USER>/ros2_jazzy/install/osrf_pycommon/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/install/ament_cmake_google_benchmark/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/build/domain_coordinator:/Users/<USER>/ros2_jazzy/install/domain_coordinator/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/build/ament_xmllint:/Users/<USER>/ros2_jazzy/install/ament_xmllint/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/build/ament_pyflakes:/Users/<USER>/ros2_jazzy/install/ament_pyflakes/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/build/ament_pycodestyle:/Users/<USER>/ros2_jazzy/install/ament_pycodestyle/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/build/ament_pep257:/Users/<USER>/ros2_jazzy/install/ament_pep257/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/build/ament_pclint:/Users/<USER>/ros2_jazzy/install/ament_pclint/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/install/ament_cmake_test/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/build/ament_package:/Users/<USER>/ros2_jazzy/install/ament_package/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/build/ament_mypy:/Users/<USER>/ros2_jazzy/install/ament_mypy/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/build/ament_lint_cmake:/Users/<USER>/ros2_jazzy/install/ament_lint_cmake/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/build/ament_flake8:/Users/<USER>/ros2_jazzy/install/ament_flake8/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/build/ament_copyright:/Users/<USER>/ros2_jazzy/install/ament_copyright/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/build/ament_lint:/Users/<USER>/ros2_jazzy/install/ament_lint/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/build/ament_index_python:/Users/<USER>/ros2_jazzy/install/ament_index_python/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/build/ament_cpplint:/Users/<USER>/ros2_jazzy/install/ament_cpplint/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/build/ament_cppcheck:/Users/<USER>/ros2_jazzy/install/ament_cppcheck/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/build/ament_clang_tidy:/Users/<USER>/ros2_jazzy/install/ament_clang_tidy/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/build/ament_clang_format:/Users/<USER>/ros2_jazzy/install/ament_clang_format/lib/python3.11/site-packages:/Users/<USER>/Work/drill2/onboard/install/tracks_regulator/lib/python3.11/site-packages:/Users/<USER>/Work/drill2/onboard/install/state_tracker/lib/python3.11/site-packages:/Users/<USER>/Work/drill2/onboard/install/rtk_connector/lib/python3.11/site-packages:/Users/<USER>/Work/drill2/onboard/install/remote_connector/lib/python3.11/site-packages:/Users/<USER>/Work/drill2/onboard/install/pydubins/lib/python3.11/site-packages:/Users/<USER>/Work/drill2/onboard/install/path_follower/lib/python3.11/site-packages:/Users/<USER>/Work/drill2/onboard/install/params_server/lib/python3.11/site-packages:/Users/<USER>/Work/drill2/onboard/install/modbus_node/lib/python3.11/site-packages:/Users/<USER>/Work/drill2/onboard/install/main_state_machine/lib/python3.11/site-packages:/Users/<USER>/Work/drill2/onboard/install/maneuver_builder_cpp/lib/python3.11/site-packages:/Users/<USER>/Work/drill2/onboard/install/leveler/lib/python3.11/site-packages:/Users/<USER>/Work/drill2/onboard/install/launchpack/lib/python3.11/site-packages:/Users/<USER>/Work/drill2/onboard/install/can_encoder/lib/python3.11/site-packages:/Users/<USER>/Work/drill2/onboard/install/can_decoder/lib/python3.11/site-packages:/Users/<USER>/Work/drill2/onboard/install/base_node/lib/python3.11/site-packages:/Users/<USER>/Work/drill2/onboard/install/drill_msgs/lib/python3.11/site-packages:/Users/<USER>/Work/drill2/onboard/install/can_msgs/lib/python3.11/site-packages:/Users/<USER>/Work/drill2/onboard/install/external/maneuver_builder_cpp/lib/python3.11/site-packages:/Users/<USER>/Work/drill2/onboard/install/external/maneuver_builder_cpp/lib/python3.11/site-packages'), ('LOGNAME', 'frontwise'), ('ROS_AUTOMATIC_DISCOVERY_RANGE', 'SUBNET'), ('PKG_CONFIG_PATH', '/Users/<USER>/ros2_jazzy/install/urdfdom/lib/pkgconfig:/Users/<USER>/ros2_jazzy/install/urdfdom_headers/lib/pkgconfig:/Users/<USER>/ros2_jazzy/install/osrf_testing_tools_cpp/lib/pkgconfig:/Users/<USER>/ros2_jazzy/install/iceoryx_introspection/lib/pkgconfig:/Users/<USER>/ros2_jazzy/install/cyclonedds/lib/pkgconfig:/Users/<USER>/ros2_jazzy/install/iceoryx_posh/lib/pkgconfig:/Users/<USER>/ros2_jazzy/install/iceoryx_hoofs/lib/pkgconfig:/Users/<USER>/ros2_jazzy/install/iceoryx_binding_c/lib/pkgconfig:/Users/<USER>/ros2_jazzy/install/gmock_vendor/lib/pkgconfig:/Users/<USER>/ros2_jazzy/install/gtest_vendor/lib/pkgconfig:/Users/<USER>/ros2_jazzy/install/google_benchmark_vendor/lib/pkgconfig:/Users/<USER>/ros2_jazzy/install/foonathan_memory_vendor/lib/pkgconfig'), ('NVM_BIN', '/Users/<USER>/.nvm/versions/node/v22.14.0/bin'), ('INFOPATH', '/opt/homebrew/share/info:'), ('HOMEBREW_CELLAR', '/opt/homebrew/Cellar'), ('CMAKE_PREFIX_PATH', '/opt/homebrew/opt/qt@5/lib/cmake:/opt/homebrew/opt:/opt/homebrew/opt/qt@5/lib/cmake:/opt/homebrew/opt:/opt/homebrew/opt/qt@5/lib/cmake:/opt/homebrew/opt:/Users/<USER>/ros2_jazzy/install/rosbag2:/Users/<USER>/ros2_jazzy/install/rosbag2_storage_default_plugins:/Users/<USER>/ros2_jazzy/install/rosbag2_storage_mcap:/Users/<USER>/ros2_jazzy/install/rosbag2_compression_zstd:/Users/<USER>/ros2_jazzy/install/mcap_vendor:/Users/<USER>/ros2_jazzy/install/zstd_vendor:/Users/<USER>/ros2_jazzy/install/rviz_visual_testing_framework:/Users/<USER>/ros2_jazzy/install/rviz2:/Users/<USER>/ros2_jazzy/install/rviz_default_plugins:/Users/<USER>/ros2_jazzy/install/rviz_common:/Users/<USER>/ros2_jazzy/install/rosbag2_py:/Users/<USER>/ros2_jazzy/install/rosbag2_transport:/Users/<USER>/ros2_jazzy/install/rosbag2_storage_sqlite3:/Users/<USER>/ros2_jazzy/install/rosbag2_examples_cpp:/Users/<USER>/ros2_jazzy/install/rosbag2_compression:/Users/<USER>/ros2_jazzy/install/rosbag2_cpp:/Users/<USER>/ros2_jazzy/install/rosbag2_storage:/Users/<USER>/ros2_jazzy/install/image_common:/Users/<USER>/ros2_jazzy/install/camera_info_manager:/Users/<USER>/ros2_jazzy/install/camera_calibration_parsers:/Users/<USER>/ros2_jazzy/install/yaml_cpp_vendor:/Users/<USER>/ros2_jazzy/install/interactive_markers:/Users/<USER>/ros2_jazzy/install/common_interfaces:/Users/<USER>/ros2_jazzy/install/visualization_msgs:/Users/<USER>/ros2_jazzy/install/dummy_robot_bringup:/Users/<USER>/ros2_jazzy/install/robot_state_publisher:/Users/<USER>/ros2_jazzy/install/kdl_parser:/Users/<USER>/ros2_jazzy/install/urdf:/Users/<USER>/ros2_jazzy/install/urdfdom:/Users/<USER>/ros2_jazzy/install/urdf_parser_plugin:/Users/<USER>/ros2_jazzy/install/urdfdom_headers:/Users/<USER>/ros2_jazzy/install/turtlesim:/Users/<USER>/ros2_jazzy/install/trajectory_msgs:/Users/<USER>/ros2_jazzy/install/topic_statistics_demo:/Users/<USER>/ros2_jazzy/install/geometry2:/Users/<USER>/ros2_jazzy/install/tf2_sensor_msgs:/Users/<USER>/ros2_jazzy/install/test_tf2:/Users/<USER>/ros2_jazzy/install/tf2_kdl:/Users/<USER>/ros2_jazzy/install/tf2_geometry_msgs:/Users/<USER>/ros2_jazzy/install/tf2_eigen:/Users/<USER>/ros2_jazzy/install/tf2_bullet:/Users/<USER>/ros2_jazzy/install/tf2_ros:/Users/<USER>/ros2_jazzy/install/tf2_py:/Users/<USER>/ros2_jazzy/install/tf2_msgs:/Users/<USER>/ros2_jazzy/install/tf2_eigen_kdl:/Users/<USER>/ros2_jazzy/install/laser_geometry:/Users/<USER>/ros2_jazzy/install/tf2:/Users/<USER>/ros2_jazzy/install/test_tracetools:/Users/<USER>/ros2_jazzy/install/rosbag2_test_common:/Users/<USER>/ros2_jazzy/install/test_msgs:/Users/<USER>/ros2_jazzy/install/test_communication:/Users/<USER>/ros2_jazzy/install/stereo_msgs:/Users/<USER>/ros2_jazzy/install/std_srvs:/Users/<USER>/ros2_jazzy/install/shape_msgs:/Users/<USER>/ros2_jazzy/install/quality_of_service_demo_cpp:/Users/<USER>/ros2_jazzy/install/point_cloud_transport_py:/Users/<USER>/ros2_jazzy/install/point_cloud_transport:/Users/<USER>/ros2_jazzy/install/map_msgs:/Users/<USER>/ros2_jazzy/install/intra_process_demo:/Users/<USER>/ros2_jazzy/install/image_transport:/Users/<USER>/ros2_jazzy/install/image_tools:/Users/<USER>/ros2_jazzy/install/dummy_sensors:/Users/<USER>/ros2_jazzy/install/sensor_msgs:/Users/<USER>/ros2_jazzy/install/ros2cli_common_extensions:/Users/<USER>/ros2_jazzy/install/dummy_map_server:/Users/<USER>/ros2_jazzy/install/nav_msgs:/Users/<USER>/ros2_jazzy/install/message_filters:/Users/<USER>/ros2_jazzy/install/logging_demo:/Users/<USER>/ros2_jazzy/install/lifecycle:/Users/<USER>/ros2_jazzy/install/diagnostic_msgs:/Users/<USER>/ros2_jazzy/install/geometry_msgs:/Users/<USER>/ros2_jazzy/install/examples_rclcpp_wait_set:/Users/<USER>/ros2_jazzy/install/examples_rclcpp_multithreaded_executor:/Users/<USER>/ros2_jazzy/install/examples_rclcpp_minimal_subscriber:/Users/<USER>/ros2_jazzy/install/examples_rclcpp_minimal_publisher:/Users/<USER>/ros2_jazzy/install/examples_rclcpp_minimal_composition:/Users/<USER>/ros2_jazzy/install/examples_rclcpp_cbg_executor:/Users/<USER>/ros2_jazzy/install/demo_nodes_cpp_native:/Users/<USER>/ros2_jazzy/install/demo_nodes_cpp:/Users/<USER>/ros2_jazzy/install/composition:/Users/<USER>/ros2_jazzy/install/actionlib_msgs:/Users/<USER>/ros2_jazzy/install/std_msgs:/Users/<USER>/ros2_jazzy/install/ros2lifecycle_test_fixtures:/Users/<USER>/ros2_jazzy/install/rclcpp_lifecycle:/Users/<USER>/ros2_jazzy/install/action_tutorials_cpp:/Users/<USER>/ros2_jazzy/install/rclcpp_components:/Users/<USER>/ros2_jazzy/install/examples_rclcpp_minimal_action_server:/Users/<USER>/ros2_jazzy/install/examples_rclcpp_minimal_action_client:/Users/<USER>/ros2_jazzy/install/rclcpp_action:/Users/<USER>/ros2_jazzy/install/examples_rclcpp_minimal_timer:/Users/<USER>/ros2_jazzy/install/examples_rclcpp_minimal_service:/Users/<USER>/ros2_jazzy/install/examples_rclcpp_minimal_client:/Users/<USER>/ros2_jazzy/install/examples_rclcpp_async_client:/Users/<USER>/ros2_jazzy/install/rclcpp:/Users/<USER>/ros2_jazzy/install/libstatistics_collector:/Users/<USER>/ros2_jazzy/install/statistics_msgs:/Users/<USER>/ros2_jazzy/install/sros2_cmake:/Users/<USER>/ros2_jazzy/install/ros_testing:/Users/<USER>/ros2_jazzy/install/rclpy:/Users/<USER>/ros2_jazzy/install/rosgraph_msgs:/Users/<USER>/ros2_jazzy/install/rosbag2_interfaces:/Users/<USER>/ros2_jazzy/install/ros2cli_test_interfaces:/Users/<USER>/ros2_jazzy/install/rcl_lifecycle:/Users/<USER>/ros2_jazzy/install/rcl_action:/Users/<USER>/ros2_jazzy/install/rcl:/Users/<USER>/ros2_jazzy/install/rmw_implementation:/Users/<USER>/ros2_jazzy/install/rmw_fastrtps_dynamic_cpp:/Users/<USER>/ros2_jazzy/install/rmw_fastrtps_cpp:/Users/<USER>/ros2_jazzy/install/rmw_fastrtps_shared_cpp:/Users/<USER>/ros2_jazzy/install/rmw_cyclonedds_cpp:/Users/<USER>/ros2_jazzy/install/rmw_connextddsmicro:/Users/<USER>/ros2_jazzy/install/rmw_connextdds:/Users/<USER>/ros2_jazzy/install/rmw_connextdds_common:/Users/<USER>/ros2_jazzy/install/rmw_dds_common:/Users/<USER>/ros2_jazzy/install/composition_interfaces:/Users/<USER>/ros2_jazzy/install/rcl_interfaces:/Users/<USER>/ros2_jazzy/install/pendulum_msgs:/Users/<USER>/ros2_jazzy/install/lifecycle_msgs:/Users/<USER>/ros2_jazzy/install/example_interfaces:/Users/<USER>/ros2_jazzy/install/action_tutorials_interfaces:/Users/<USER>/ros2_jazzy/install/rosidl_default_runtime:/Users/<USER>/ros2_jazzy/install/rosidl_default_generators:/Users/<USER>/ros2_jazzy/install/action_msgs:/Users/<USER>/ros2_jazzy/install/unique_identifier_msgs:/Users/<USER>/ros2_jazzy/install/type_description_interfaces:/Users/<USER>/ros2_jazzy/install/service_msgs:/Users/<USER>/ros2_jazzy/install/builtin_interfaces:/Users/<USER>/ros2_jazzy/install/rosidl_core_runtime:/Users/<USER>/ros2_jazzy/install/rosidl_core_generators:/Users/<USER>/ros2_jazzy/install/rosidl_generator_py:/Users/<USER>/ros2_jazzy/install/ament_lint_common:/Users/<USER>/ros2_jazzy/install/ament_cmake_uncrustify:/Users/<USER>/ros2_jazzy/install/uncrustify_vendor:/Users/<USER>/ros2_jazzy/install/tracetools:/Users/<USER>/ros2_jazzy/install/pluginlib:/Users/<USER>/ros2_jazzy/install/tinyxml2_vendor:/Users/<USER>/ros2_jazzy/install/test_security:/Users/<USER>/ros2_jazzy/install/test_rmw_implementation:/Users/<USER>/ros2_jazzy/install/test_rclcpp:/Users/<USER>/ros2_jazzy/install/test_quality_of_service:/Users/<USER>/ros2_jazzy/install/test_launch_testing:/Users/<USER>/ros2_jazzy/install/test_interface_files:/Users/<USER>/ros2_jazzy/install/test_cli_remapping:/Users/<USER>/ros2_jazzy/install/test_cli:/Users/<USER>/ros2_jazzy/install/tango_icons_vendor:/Users/<USER>/ros2_jazzy/install/sqlite3_vendor:/Users/<USER>/ros2_jazzy/install/rcl_logging_spdlog:/Users/<USER>/ros2_jazzy/install/spdlog_vendor:/Users/<USER>/ros2_jazzy/install/shared_queues_vendor:/Users/<USER>/ros2_jazzy/install/rviz_rendering_tests:/Users/<USER>/ros2_jazzy/install/rviz_rendering:/Users/<USER>/ros2_jazzy/install/rviz_ogre_vendor:/Users/<USER>/ros2_jazzy/install/rviz_assimp_vendor:/Users/<USER>/ros2_jazzy/install/rti_connext_dds_cmake_module:/Users/<USER>/ros2_jazzy/install/lttngpy:/Users/<USER>/ros2_jazzy/install/rosidl_typesupport_tests:/Users/<USER>/ros2_jazzy/install/rosidl_typesupport_introspection_tests:/Users/<USER>/ros2_jazzy/install/rosidl_typesupport_cpp:/Users/<USER>/ros2_jazzy/install/rosidl_typesupport_introspection_cpp:/Users/<USER>/ros2_jazzy/install/rosidl_typesupport_c:/Users/<USER>/ros2_jazzy/install/rosidl_typesupport_introspection_c:/Users/<USER>/ros2_jazzy/install/rosidl_typesupport_fastrtps_c:/Users/<USER>/ros2_jazzy/install/rosidl_typesupport_fastrtps_cpp:/Users/<USER>/ros2_jazzy/install/rosidl_generator_cpp:/Users/<USER>/ros2_jazzy/install/rosidl_runtime_cpp:/Users/<USER>/ros2_jazzy/install/rosidl_dynamic_typesupport_fastrtps:/Users/<USER>/ros2_jazzy/install/rcl_yaml_param_parser:/Users/<USER>/ros2_jazzy/install/rmw:/Users/<USER>/ros2_jazzy/install/rosidl_dynamic_typesupport:/Users/<USER>/ros2_jazzy/install/rosidl_runtime_c:/Users/<USER>/ros2_jazzy/install/rosidl_generator_c:/Users/<USER>/ros2_jazzy/install/rosidl_typesupport_interface:/Users/<USER>/ros2_jazzy/install/rosidl_generator_dds_idl:/Users/<USER>/ros2_jazzy/install/rosidl_cmake:/Users/<USER>/ros2_jazzy/install/rosidl_generator_type_description:/Users/<USER>/ros2_jazzy/install/rosidl_parser:/Users/<USER>/ros2_jazzy/install/rosidl_generator_tests:/Users/<USER>/ros2_jazzy/install/rosidl_adapter:/Users/<USER>/ros2_jazzy/install/rosbag2_tests:/Users/<USER>/ros2_jazzy/install/rosbag2_test_msgdefs:/Users/<USER>/ros2_jazzy/install/ros_environment:/Users/<USER>/ros2_jazzy/install/rmw_implementation_cmake:/Users/<USER>/ros2_jazzy/install/resource_retriever:/Users/<USER>/ros2_jazzy/install/class_loader:/Users/<USER>/ros2_jazzy/install/rcpputils:/Users/<USER>/ros2_jazzy/install/rcl_logging_noop:/Users/<USER>/ros2_jazzy/install/rcl_logging_interface:/Users/<USER>/ros2_jazzy/install/rcutils:/Users/<USER>/ros2_jazzy/install/python_qt_binding:/Users/<USER>/ros2_jazzy/install/python_orocos_kdl_vendor:/Users/<USER>/ros2_jazzy/install/launch_testing_ament_cmake:/Users/<USER>/ros2_jazzy/install/python_cmake_module:/Users/<USER>/ros2_jazzy/install/pybind11_vendor:/Users/<USER>/ros2_jazzy/install/performance_test_fixture:/Users/<USER>/ros2_jazzy/install/osrf_testing_tools_cpp:/Users/<USER>/ros2_jazzy/install/orocos_kdl_vendor:/Users/<USER>/ros2_jazzy/install/mimick_vendor:/Users/<USER>/ros2_jazzy/install/libyaml_vendor:/Users/<USER>/ros2_jazzy/install/liblz4_vendor:/Users/<USER>/ros2_jazzy/install/libcurl_vendor:/Users/<USER>/ros2_jazzy/install/keyboard_handler:/Users/<USER>/ros2_jazzy/install/iceoryx_introspection:/Users/<USER>/ros2_jazzy/install/cyclonedds:/Users/<USER>/ros2_jazzy/install/iceoryx_posh:/Users/<USER>/ros2_jazzy/install/iceoryx_hoofs:/Users/<USER>/ros2_jazzy/install/iceoryx_binding_c:/Users/<USER>/ros2_jazzy/install/gz_math_vendor/opt/gz_math_vendor:/Users/<USER>/ros2_jazzy/install/gz_math_vendor:/Users/<USER>/ros2_jazzy/install/gz_utils_vendor/opt/gz_utils_vendor:/Users/<USER>/ros2_jazzy/install/gz_utils_vendor:/Users/<USER>/ros2_jazzy/install/gz_cmake_vendor/opt/gz_cmake_vendor:/Users/<USER>/ros2_jazzy/install/gz_cmake_vendor:/Users/<USER>/ros2_jazzy/install/ament_cmake_ros:/Users/<USER>/ros2_jazzy/install/ament_cmake_auto:/Users/<USER>/ros2_jazzy/install/ament_cmake_gmock:/Users/<USER>/ros2_jazzy/install/gmock_vendor:/Users/<USER>/ros2_jazzy/install/fastrtps:/Users/<USER>/ros2_jazzy/install/fastcdr:/Users/<USER>/ros2_jazzy/install/ament_cmake_gtest:/Users/<USER>/ros2_jazzy/install/gtest_vendor:/Users/<USER>/ros2_jazzy/install/ament_cmake_google_benchmark:/Users/<USER>/ros2_jazzy/install/google_benchmark_vendor:/Users/<USER>/ros2_jazzy/install/foonathan_memory_vendor:/Users/<USER>/ros2_jazzy/install/fastrtps_cmake_module:/Users/<USER>/ros2_jazzy/install/eigen3_cmake_module:/Users/<USER>/ros2_jazzy/install/console_bridge_vendor:/Users/<USER>/ros2_jazzy/install/ament_cmake_xmllint:/Users/<USER>/ros2_jazzy/install/ament_cmake_pyflakes:/Users/<USER>/ros2_jazzy/install/ament_cmake_pycodestyle:/Users/<USER>/ros2_jazzy/install/ament_cmake_pep257:/Users/<USER>/ros2_jazzy/install/ament_cmake_pclint:/Users/<USER>/ros2_jazzy/install/ament_lint_auto:/Users/<USER>/ros2_jazzy/install/ament_cmake:/Users/<USER>/ros2_jazzy/install/ament_cmake_version:/Users/<USER>/ros2_jazzy/install/ament_cmake_vendor_package:/Users/<USER>/ros2_jazzy/install/ament_cmake_pytest:/Users/<USER>/ros2_jazzy/install/ament_cmake_mypy:/Users/<USER>/ros2_jazzy/install/ament_cmake_lint_cmake:/Users/<USER>/ros2_jazzy/install/ament_cmake_flake8:/Users/<USER>/ros2_jazzy/install/ament_cmake_cpplint:/Users/<USER>/ros2_jazzy/install/ament_cmake_cppcheck:/Users/<USER>/ros2_jazzy/install/ament_cmake_copyright:/Users/<USER>/ros2_jazzy/install/ament_cmake_clang_tidy:/Users/<USER>/ros2_jazzy/install/ament_cmake_clang_format:/Users/<USER>/ros2_jazzy/install/ament_cmake_test:/Users/<USER>/ros2_jazzy/install/ament_cmake_target_dependencies:/Users/<USER>/ros2_jazzy/install/ament_cmake_python:/Users/<USER>/ros2_jazzy/install/ament_cmake_export_dependencies:/Users/<USER>/ros2_jazzy/install/ament_cmake_libraries:/Users/<USER>/ros2_jazzy/install/ament_cmake_include_directories:/Users/<USER>/ros2_jazzy/install/ament_cmake_gen_version_h:/Users/<USER>/ros2_jazzy/install/ament_cmake_export_targets:/Users/<USER>/ros2_jazzy/install/ament_cmake_export_link_flags:/Users/<USER>/ros2_jazzy/install/ament_cmake_export_interfaces:/Users/<USER>/ros2_jazzy/install/ament_cmake_export_libraries:/Users/<USER>/ros2_jazzy/install/ament_cmake_export_include_directories:/Users/<USER>/ros2_jazzy/install/ament_cmake_export_definitions:/Users/<USER>/ros2_jazzy/install/ament_cmake_core:/Users/<USER>/ros2_jazzy/install/ament_index_cpp:/Users/<USER>/Work/drill2/onboard/install/maneuver_builder_cpp:/Users/<USER>/Work/drill2/onboard/install/drill_msgs:/Users/<USER>/Work/drill2/onboard/install/can_msgs:/opt/homebrew/opt/qt@5/lib/cmake:/opt/homebrew/opt:/Users/<USER>/Work/drill2/onboard/install/depth_tracker:/Users/<USER>/ros2_jazzy/install/rosbag2_examples_py:/Users/<USER>/ros2_jazzy/install/launch_testing_examples:/Users/<USER>/ros2_jazzy/install/ros2bag:/Users/<USER>/ros2_jazzy/install/tracetools_test:/Users/<USER>/ros2_jazzy/install/tracetools_launch:/Users/<USER>/ros2_jazzy/install/topic_monitor:/Users/<USER>/ros2_jazzy/install/tf2_tools:/Users/<USER>/ros2_jazzy/install/examples_tf2_py:/Users/<USER>/ros2_jazzy/install/tf2_ros_py:/Users/<USER>/ros2_jazzy/install/examples_rclpy_pointcloud_publisher:/Users/<USER>/ros2_jazzy/install/sensor_msgs_py:/Users/<USER>/ros2_jazzy/install/quality_of_service_demo_py:/Users/<USER>/ros2_jazzy/install/camera_info_manager_py:/Users/<USER>/ros2_jazzy/install/ros2doctor:/Users/<USER>/ros2_jazzy/install/lifecycle_py:/Users/<USER>/ros2_jazzy/install/examples_rclpy_minimal_subscriber:/Users/<USER>/ros2_jazzy/install/examples_rclpy_minimal_service:/Users/<USER>/ros2_jazzy/install/examples_rclpy_minimal_publisher:/Users/<USER>/ros2_jazzy/install/examples_rclpy_minimal_client:/Users/<USER>/ros2_jazzy/install/examples_rclpy_executors:/Users/<USER>/ros2_jazzy/install/demo_nodes_py:/Users/<USER>/ros2_jazzy/install/ros2component:/Users/<USER>/ros2_jazzy/install/sros2:/Users/<USER>/ros2_jazzy/install/ros2trace:/Users/<USER>/ros2_jazzy/install/ros2topic:/Users/<USER>/ros2_jazzy/install/ros2test:/Users/<USER>/ros2_jazzy/install/ros2param:/Users/<USER>/ros2_jazzy/install/ros2lifecycle:/Users/<USER>/ros2_jazzy/install/ros2service:/Users/<USER>/ros2_jazzy/install/ros2run:/Users/<USER>/ros2_jazzy/install/ros2launch:/Users/<USER>/ros2_jazzy/install/ros2pkg:/Users/<USER>/ros2_jazzy/install/ros2node:/Users/<USER>/ros2_jazzy/install/ros2multicast:/Users/<USER>/ros2_jazzy/install/ros2interface:/Users/<USER>/ros2_jazzy/install/ros2action:/Users/<USER>/ros2_jazzy/install/ros2cli:/Users/<USER>/ros2_jazzy/install/launch_testing_ros:/Users/<USER>/ros2_jazzy/install/launch_ros:/Users/<USER>/ros2_jazzy/install/examples_rclpy_minimal_action_server:/Users/<USER>/ros2_jazzy/install/examples_rclpy_minimal_action_client:/Users/<USER>/ros2_jazzy/install/examples_rclpy_guard_conditions:/Users/<USER>/ros2_jazzy/install/action_tutorials_py:/Users/<USER>/ros2_jazzy/install/ament_uncrustify:/Users/<USER>/ros2_jazzy/install/tracetools_trace:/Users/<USER>/ros2_jazzy/install/tracetools_read:/Users/<USER>/ros2_jazzy/install/test_tracetools_launch:/Users/<USER>/ros2_jazzy/install/test_ros2trace:/Users/<USER>/ros2_jazzy/install/test_launch_ros:/Users/<USER>/ros2_jazzy/install/rpyutils:/Users/<USER>/ros2_jazzy/install/rosidl_runtime_py:/Users/<USER>/ros2_jazzy/install/rosidl_pycommon:/Users/<USER>/ros2_jazzy/install/rosidl_cli:/Users/<USER>/ros2_jazzy/install/launch_pytest:/Users/<USER>/ros2_jazzy/install/launch_testing:/Users/<USER>/ros2_jazzy/install/launch_yaml:/Users/<USER>/ros2_jazzy/install/launch_xml:/Users/<USER>/ros2_jazzy/install/launch:/Users/<USER>/ros2_jazzy/install/osrf_pycommon:/Users/<USER>/ros2_jazzy/install/domain_coordinator:/Users/<USER>/ros2_jazzy/install/ament_xmllint:/Users/<USER>/ros2_jazzy/install/ament_pyflakes:/Users/<USER>/ros2_jazzy/install/ament_pycodestyle:/Users/<USER>/ros2_jazzy/install/ament_pep257:/Users/<USER>/ros2_jazzy/install/ament_pclint:/Users/<USER>/ros2_jazzy/install/ament_package:/Users/<USER>/ros2_jazzy/install/ament_mypy:/Users/<USER>/ros2_jazzy/install/ament_lint_cmake:/Users/<USER>/ros2_jazzy/install/ament_flake8:/Users/<USER>/ros2_jazzy/install/ament_copyright:/Users/<USER>/ros2_jazzy/install/ament_lint:/Users/<USER>/ros2_jazzy/install/ament_index_python:/Users/<USER>/ros2_jazzy/install/ament_cpplint:/Users/<USER>/ros2_jazzy/install/ament_cppcheck:/Users/<USER>/ros2_jazzy/install/ament_clang_tidy:/Users/<USER>/ros2_jazzy/install/ament_clang_format:/Users/<USER>/Work/drill2/onboard/install/tracks_regulator:/Users/<USER>/Work/drill2/onboard/install/state_tracker:/Users/<USER>/Work/drill2/onboard/install/rtk_connector:/Users/<USER>/Work/drill2/onboard/install/remote_connector:/Users/<USER>/Work/drill2/onboard/install/pydubins:/Users/<USER>/Work/drill2/onboard/install/path_follower:/Users/<USER>/Work/drill2/onboard/install/params_server:/Users/<USER>/Work/drill2/onboard/install/modbus_node:/Users/<USER>/Work/drill2/onboard/install/main_state_machine:/Users/<USER>/Work/drill2/onboard/install/leveler:/Users/<USER>/Work/drill2/onboard/install/launchpack:/Users/<USER>/Work/drill2/onboard/install/can_encoder:/Users/<USER>/Work/drill2/onboard/install/can_decoder:/Users/<USER>/Work/drill2/onboard/install/base_node'), ('LC_TERMINAL', 'iTerm2'), ('DISPLAY', '/private/tmp/com.apple.launchd.UjTF9XIlm8/org.xquartz:0'), ('SECURITYSESSIONID', '186ab'), ('VIRTUAL_ENV_PROMPT', '(.ros2_venv)'), ('COLORTERM', 'truecolor'), ('_', '/usr/bin/env')]), 'shell': False}
[0.204213] (-) TimerEvent: {}
[0.306027] (-) TimerEvent: {}
[0.411301] (-) TimerEvent: {}
[0.513443] (-) TimerEvent: {}
[0.616754] (-) TimerEvent: {}
[0.721851] (-) TimerEvent: {}
[0.750447] (drill_msgs) StdoutLine: {'line': b'[  0%] Built target drill_msgs__rosidl_generator_type_description\n'}
[0.826738] (-) TimerEvent: {}
[0.927315] (-) TimerEvent: {}
[1.028490] (-) TimerEvent: {}
[1.029408] (drill_msgs) StdoutLine: {'line': b'[  0%] Built target ament_cmake_python_symlink_drill_msgs\n'}
[1.133084] (-) TimerEvent: {}
[1.235968] (-) TimerEvent: {}
[1.339664] (-) TimerEvent: {}
[1.372516] (drill_msgs) StdoutLine: {'line': b'[ 29%] Built target drill_msgs__rosidl_generator_c\n'}
[1.440658] (-) TimerEvent: {}
[1.545074] (-) TimerEvent: {}
[1.647961] (-) TimerEvent: {}
[1.749622] (-) TimerEvent: {}
[1.853814] (-) TimerEvent: {}
[1.958394] (-) TimerEvent: {}
[2.063850] (-) TimerEvent: {}
[2.168884] (-) TimerEvent: {}
[2.272732] (-) TimerEvent: {}
[2.376075] (-) TimerEvent: {}
[2.431429] (drill_msgs) StdoutLine: {'line': b'running egg_info\n'}
[2.432880] (drill_msgs) StdoutLine: {'line': b'writing drill_msgs.egg-info/PKG-INFO\n'}
[2.433190] (drill_msgs) StdoutLine: {'line': b'writing dependency_links to drill_msgs.egg-info/dependency_links.txt\n'}
[2.433989] (drill_msgs) StdoutLine: {'line': b'writing top-level names to drill_msgs.egg-info/top_level.txt\n'}
[2.438508] (drill_msgs) StdoutLine: {'line': b"reading manifest file 'drill_msgs.egg-info/SOURCES.txt'\n"}
[2.439842] (drill_msgs) StdoutLine: {'line': b"writing manifest file 'drill_msgs.egg-info/SOURCES.txt'\n"}
[2.485378] (-) TimerEvent: {}
[2.590308] (-) TimerEvent: {}
[2.679835] (drill_msgs) StdoutLine: {'line': b'[ 29%] Built target ament_cmake_python_build_drill_msgs_egg\n'}
[2.690417] (-) TimerEvent: {}
[2.795191] (-) TimerEvent: {}
[2.899797] (-) TimerEvent: {}
[3.001297] (-) TimerEvent: {}
[3.050331] (drill_msgs) StdoutLine: {'line': b'[ 39%] Built target drill_msgs__rosidl_typesupport_c\n'}
[3.103310] (-) TimerEvent: {}
[3.206308] (-) TimerEvent: {}
[3.310998] (-) TimerEvent: {}
[3.340546] (drill_msgs) StdoutLine: {'line': b'[ 48%] Built target drill_msgs__rosidl_typesupport_introspection_c\n'}
[3.416117] (-) TimerEvent: {}
[3.518631] (-) TimerEvent: {}
[3.623351] (-) TimerEvent: {}
[3.724092] (-) TimerEvent: {}
[3.756227] (drill_msgs) StdoutLine: {'line': b'[ 58%] Built target drill_msgs__rosidl_typesupport_fastrtps_c\n'}
[3.824462] (-) TimerEvent: {}
[3.929869] (-) TimerEvent: {}
[4.025367] (drill_msgs) StdoutLine: {'line': b'[ 58%] Built target drill_msgs__cpp\n'}
[4.030720] (-) TimerEvent: {}
[4.130925] (-) TimerEvent: {}
[4.235962] (-) TimerEvent: {}
[4.338761] (-) TimerEvent: {}
[4.443933] (-) TimerEvent: {}
[4.462054] (drill_msgs) StdoutLine: {'line': b'[ 68%] Built target drill_msgs__rosidl_typesupport_cpp\n'}
[4.548675] (-) TimerEvent: {}
[4.652149] (-) TimerEvent: {}
[4.757065] (-) TimerEvent: {}
[4.857414] (-) TimerEvent: {}
[4.862890] (drill_msgs) StdoutLine: {'line': b'[ 78%] Built target drill_msgs__rosidl_typesupport_introspection_cpp\n'}
[4.960743] (-) TimerEvent: {}
[5.065306] (-) TimerEvent: {}
[5.168567] (-) TimerEvent: {}
[5.274306] (-) TimerEvent: {}
[5.366579] (drill_msgs) StdoutLine: {'line': b'[ 88%] Built target drill_msgs__rosidl_typesupport_fastrtps_cpp\n'}
[5.375799] (-) TimerEvent: {}
[5.480822] (-) TimerEvent: {}
[5.582080] (-) TimerEvent: {}
[5.684266] (-) TimerEvent: {}
[5.691848] (drill_msgs) StdoutLine: {'line': b'[ 88%] Built target drill_msgs\n'}
[5.788906] (-) TimerEvent: {}
[5.893733] (-) TimerEvent: {}
[5.958816] (drill_msgs) StdoutLine: {'line': b'[ 88%] Built target drill_msgs__py\n'}
[5.996478] (-) TimerEvent: {}
[6.099304] (-) TimerEvent: {}
[6.202169] (-) TimerEvent: {}
[6.305864] (-) TimerEvent: {}
[6.382728] (drill_msgs) StdoutLine: {'line': b'[ 98%] Built target drill_msgs__rosidl_generator_py\n'}
[6.409935] (-) TimerEvent: {}
[6.515147] (-) TimerEvent: {}
[6.619135] (-) TimerEvent: {}
[6.653316] (drill_msgs) StdoutLine: {'line': b'[ 99%] Built target drill_msgs_s__rosidl_typesupport_introspection_c\n'}
[6.724104] (-) TimerEvent: {}
[6.827584] (-) TimerEvent: {}
[6.921898] (drill_msgs) StdoutLine: {'line': b'[100%] Built target drill_msgs_s__rosidl_typesupport_c\n'}
[6.929040] (-) TimerEvent: {}
[7.033819] (-) TimerEvent: {}
[7.138691] (-) TimerEvent: {}
[7.193082] (drill_msgs) StdoutLine: {'line': b'[100%] Built target drill_msgs_s__rosidl_typesupport_fastrtps_c\n'}
[7.241169] (-) TimerEvent: {}
[7.242571] (drill_msgs) CommandEnded: {'returncode': 0}
[7.243571] (drill_msgs) JobProgress: {'identifier': 'drill_msgs', 'progress': 'install'}
[7.280780] (drill_msgs) Command: {'cmd': ['/opt/homebrew/bin/cmake', '--install', '/Users/<USER>/Work/drill2/onboard/build/drill_msgs'], 'cwd': '/Users/<USER>/Work/drill2/onboard/build/drill_msgs', 'env': OrderedDict([('NVM_INC', '/Users/<USER>/.nvm/versions/node/v22.14.0/include/node'), ('TERM_PROGRAM', 'iTerm.app'), ('NVM_CD_FLAGS', '-q'), ('TERM', 'xterm-256color'), ('SHELL', '/bin/zsh'), ('HOMEBREW_REPOSITORY', '/opt/homebrew'), ('TMPDIR', '/var/folders/6b/9kvsjxmn2wg9n27c78lfw18r0000gn/T/'), ('TERM_PROGRAM_VERSION', '3.5.15beta1'), ('ROS_PYTHON_VERSION', '3'), ('RCUTILS_LOGGING_SEVERITY', 'DEBUG'), ('ROS_VERSION', '2'), ('COLCON_PREFIX_PATH', '/Users/<USER>/ros2_jazzy/install:/Users/<USER>/ros2_jazzy/install:/Users/<USER>/Work/drill2/onboard/install'), ('TERM_SESSION_ID', 'w0t3p0:22583761-CC84-481F-B886-691DB3AE381C'), ('AMENT_PREFIX_PATH', '/Users/<USER>/Work/drill2/onboard/install/depth_tracker:/Users/<USER>/ros2_jazzy/install/rosbag2:/Users/<USER>/ros2_jazzy/install/rosbag2_storage_default_plugins:/Users/<USER>/ros2_jazzy/install/rosbag2_storage_mcap:/Users/<USER>/ros2_jazzy/install/rosbag2_compression_zstd:/Users/<USER>/ros2_jazzy/install/mcap_vendor:/Users/<USER>/ros2_jazzy/install/zstd_vendor:/Users/<USER>/ros2_jazzy/install/rviz_visual_testing_framework:/Users/<USER>/ros2_jazzy/install/rviz2:/Users/<USER>/ros2_jazzy/install/rviz_default_plugins:/Users/<USER>/ros2_jazzy/install/rviz_common:/Users/<USER>/ros2_jazzy/install/rosbag2_examples_py:/Users/<USER>/ros2_jazzy/install/launch_testing_examples:/Users/<USER>/ros2_jazzy/install/ros2bag:/Users/<USER>/ros2_jazzy/install/rosbag2_py:/Users/<USER>/ros2_jazzy/install/rosbag2_transport:/Users/<USER>/ros2_jazzy/install/rosbag2_storage_sqlite3:/Users/<USER>/ros2_jazzy/install/rosbag2_examples_cpp:/Users/<USER>/ros2_jazzy/install/rosbag2_compression:/Users/<USER>/ros2_jazzy/install/rosbag2_cpp:/Users/<USER>/ros2_jazzy/install/rosbag2_storage:/Users/<USER>/ros2_jazzy/install/image_common:/Users/<USER>/ros2_jazzy/install/camera_info_manager:/Users/<USER>/ros2_jazzy/install/camera_calibration_parsers:/Users/<USER>/ros2_jazzy/install/yaml_cpp_vendor:/Users/<USER>/ros2_jazzy/install/interactive_markers:/Users/<USER>/ros2_jazzy/install/common_interfaces:/Users/<USER>/ros2_jazzy/install/visualization_msgs:/Users/<USER>/ros2_jazzy/install/dummy_robot_bringup:/Users/<USER>/ros2_jazzy/install/robot_state_publisher:/Users/<USER>/ros2_jazzy/install/kdl_parser:/Users/<USER>/ros2_jazzy/install/urdf:/Users/<USER>/ros2_jazzy/install/urdf_parser_plugin:/Users/<USER>/ros2_jazzy/install/turtlesim:/Users/<USER>/ros2_jazzy/install/trajectory_msgs:/Users/<USER>/ros2_jazzy/install/tracetools_test:/Users/<USER>/ros2_jazzy/install/tracetools_launch:/Users/<USER>/ros2_jazzy/install/topic_statistics_demo:/Users/<USER>/ros2_jazzy/install/topic_monitor:/Users/<USER>/ros2_jazzy/install/geometry2:/Users/<USER>/ros2_jazzy/install/tf2_tools:/Users/<USER>/ros2_jazzy/install/tf2_sensor_msgs:/Users/<USER>/ros2_jazzy/install/test_tf2:/Users/<USER>/ros2_jazzy/install/tf2_kdl:/Users/<USER>/ros2_jazzy/install/tf2_geometry_msgs:/Users/<USER>/ros2_jazzy/install/examples_tf2_py:/Users/<USER>/ros2_jazzy/install/tf2_ros_py:/Users/<USER>/ros2_jazzy/install/tf2_eigen:/Users/<USER>/ros2_jazzy/install/tf2_bullet:/Users/<USER>/ros2_jazzy/install/tf2_ros:/Users/<USER>/ros2_jazzy/install/tf2_py:/Users/<USER>/ros2_jazzy/install/tf2_msgs:/Users/<USER>/ros2_jazzy/install/tf2_eigen_kdl:/Users/<USER>/ros2_jazzy/install/laser_geometry:/Users/<USER>/ros2_jazzy/install/tf2:/Users/<USER>/ros2_jazzy/install/test_tracetools:/Users/<USER>/ros2_jazzy/install/rosbag2_test_common:/Users/<USER>/ros2_jazzy/install/test_msgs:/Users/<USER>/ros2_jazzy/install/test_communication:/Users/<USER>/ros2_jazzy/install/stereo_msgs:/Users/<USER>/ros2_jazzy/install/std_srvs:/Users/<USER>/ros2_jazzy/install/shape_msgs:/Users/<USER>/ros2_jazzy/install/examples_rclpy_pointcloud_publisher:/Users/<USER>/ros2_jazzy/install/sensor_msgs_py:/Users/<USER>/ros2_jazzy/install/quality_of_service_demo_py:/Users/<USER>/ros2_jazzy/install/quality_of_service_demo_cpp:/Users/<USER>/ros2_jazzy/install/point_cloud_transport_py:/Users/<USER>/ros2_jazzy/install/point_cloud_transport:/Users/<USER>/ros2_jazzy/install/map_msgs:/Users/<USER>/ros2_jazzy/install/intra_process_demo:/Users/<USER>/ros2_jazzy/install/image_transport:/Users/<USER>/ros2_jazzy/install/image_tools:/Users/<USER>/ros2_jazzy/install/dummy_sensors:/Users/<USER>/ros2_jazzy/install/camera_info_manager_py:/Users/<USER>/ros2_jazzy/install/sensor_msgs:/Users/<USER>/ros2_jazzy/install/ros2cli_common_extensions:/Users/<USER>/ros2_jazzy/install/ros2doctor:/Users/<USER>/ros2_jazzy/install/dummy_map_server:/Users/<USER>/ros2_jazzy/install/nav_msgs:/Users/<USER>/ros2_jazzy/install/message_filters:/Users/<USER>/ros2_jazzy/install/logging_demo:/Users/<USER>/ros2_jazzy/install/lifecycle_py:/Users/<USER>/ros2_jazzy/install/lifecycle:/Users/<USER>/ros2_jazzy/install/diagnostic_msgs:/Users/<USER>/ros2_jazzy/install/geometry_msgs:/Users/<USER>/ros2_jazzy/install/examples_rclpy_minimal_subscriber:/Users/<USER>/ros2_jazzy/install/examples_rclpy_minimal_service:/Users/<USER>/ros2_jazzy/install/examples_rclpy_minimal_publisher:/Users/<USER>/ros2_jazzy/install/examples_rclpy_minimal_client:/Users/<USER>/ros2_jazzy/install/examples_rclpy_executors:/Users/<USER>/ros2_jazzy/install/examples_rclcpp_wait_set:/Users/<USER>/ros2_jazzy/install/examples_rclcpp_multithreaded_executor:/Users/<USER>/ros2_jazzy/install/examples_rclcpp_minimal_subscriber:/Users/<USER>/ros2_jazzy/install/examples_rclcpp_minimal_publisher:/Users/<USER>/ros2_jazzy/install/examples_rclcpp_minimal_composition:/Users/<USER>/ros2_jazzy/install/examples_rclcpp_cbg_executor:/Users/<USER>/ros2_jazzy/install/demo_nodes_py:/Users/<USER>/ros2_jazzy/install/demo_nodes_cpp_native:/Users/<USER>/ros2_jazzy/install/demo_nodes_cpp:/Users/<USER>/ros2_jazzy/install/composition:/Users/<USER>/ros2_jazzy/install/actionlib_msgs:/Users/<USER>/ros2_jazzy/install/std_msgs:/Users/<USER>/ros2_jazzy/install/ros2lifecycle_test_fixtures:/Users/<USER>/ros2_jazzy/install/ros2component:/Users/<USER>/ros2_jazzy/install/rclcpp_lifecycle:/Users/<USER>/ros2_jazzy/install/action_tutorials_cpp:/Users/<USER>/ros2_jazzy/install/rclcpp_components:/Users/<USER>/ros2_jazzy/install/examples_rclcpp_minimal_action_server:/Users/<USER>/ros2_jazzy/install/examples_rclcpp_minimal_action_client:/Users/<USER>/ros2_jazzy/install/rclcpp_action:/Users/<USER>/ros2_jazzy/install/examples_rclcpp_minimal_timer:/Users/<USER>/ros2_jazzy/install/examples_rclcpp_minimal_service:/Users/<USER>/ros2_jazzy/install/examples_rclcpp_minimal_client:/Users/<USER>/ros2_jazzy/install/examples_rclcpp_async_client:/Users/<USER>/ros2_jazzy/install/rclcpp:/Users/<USER>/ros2_jazzy/install/libstatistics_collector:/Users/<USER>/ros2_jazzy/install/statistics_msgs:/Users/<USER>/ros2_jazzy/install/sros2_cmake:/Users/<USER>/ros2_jazzy/install/sros2:/Users/<USER>/ros2_jazzy/install/ros_testing:/Users/<USER>/ros2_jazzy/install/ros2trace:/Users/<USER>/ros2_jazzy/install/ros2topic:/Users/<USER>/ros2_jazzy/install/ros2test:/Users/<USER>/ros2_jazzy/install/ros2param:/Users/<USER>/ros2_jazzy/install/ros2lifecycle:/Users/<USER>/ros2_jazzy/install/ros2service:/Users/<USER>/ros2_jazzy/install/ros2run:/Users/<USER>/ros2_jazzy/install/ros2launch:/Users/<USER>/ros2_jazzy/install/ros2pkg:/Users/<USER>/ros2_jazzy/install/ros2node:/Users/<USER>/ros2_jazzy/install/ros2multicast:/Users/<USER>/ros2_jazzy/install/ros2interface:/Users/<USER>/ros2_jazzy/install/ros2action:/Users/<USER>/ros2_jazzy/install/ros2cli:/Users/<USER>/ros2_jazzy/install/launch_testing_ros:/Users/<USER>/ros2_jazzy/install/launch_ros:/Users/<USER>/ros2_jazzy/install/examples_rclpy_minimal_action_server:/Users/<USER>/ros2_jazzy/install/examples_rclpy_minimal_action_client:/Users/<USER>/ros2_jazzy/install/examples_rclpy_guard_conditions:/Users/<USER>/ros2_jazzy/install/action_tutorials_py:/Users/<USER>/ros2_jazzy/install/rclpy:/Users/<USER>/ros2_jazzy/install/rosgraph_msgs:/Users/<USER>/ros2_jazzy/install/rosbag2_interfaces:/Users/<USER>/ros2_jazzy/install/ros2cli_test_interfaces:/Users/<USER>/ros2_jazzy/install/rcl_lifecycle:/Users/<USER>/ros2_jazzy/install/rcl_action:/Users/<USER>/ros2_jazzy/install/rcl:/Users/<USER>/ros2_jazzy/install/rmw_implementation:/Users/<USER>/ros2_jazzy/install/rmw_fastrtps_dynamic_cpp:/Users/<USER>/ros2_jazzy/install/rmw_fastrtps_cpp:/Users/<USER>/ros2_jazzy/install/rmw_fastrtps_shared_cpp:/Users/<USER>/ros2_jazzy/install/rmw_cyclonedds_cpp:/Users/<USER>/ros2_jazzy/install/rmw_connextddsmicro:/Users/<USER>/ros2_jazzy/install/rmw_connextdds:/Users/<USER>/ros2_jazzy/install/rmw_connextdds_common:/Users/<USER>/ros2_jazzy/install/rmw_dds_common:/Users/<USER>/ros2_jazzy/install/composition_interfaces:/Users/<USER>/ros2_jazzy/install/rcl_interfaces:/Users/<USER>/ros2_jazzy/install/pendulum_msgs:/Users/<USER>/ros2_jazzy/install/lifecycle_msgs:/Users/<USER>/ros2_jazzy/install/example_interfaces:/Users/<USER>/ros2_jazzy/install/action_tutorials_interfaces:/Users/<USER>/ros2_jazzy/install/rosidl_default_runtime:/Users/<USER>/ros2_jazzy/install/rosidl_default_generators:/Users/<USER>/ros2_jazzy/install/action_msgs:/Users/<USER>/ros2_jazzy/install/unique_identifier_msgs:/Users/<USER>/ros2_jazzy/install/type_description_interfaces:/Users/<USER>/ros2_jazzy/install/service_msgs:/Users/<USER>/ros2_jazzy/install/builtin_interfaces:/Users/<USER>/ros2_jazzy/install/rosidl_core_runtime:/Users/<USER>/ros2_jazzy/install/rosidl_core_generators:/Users/<USER>/ros2_jazzy/install/rosidl_generator_py:/Users/<USER>/ros2_jazzy/install/ament_lint_common:/Users/<USER>/ros2_jazzy/install/ament_cmake_uncrustify:/Users/<USER>/ros2_jazzy/install/ament_uncrustify:/Users/<USER>/ros2_jazzy/install/uncrustify_vendor:/Users/<USER>/ros2_jazzy/install/tracetools_trace:/Users/<USER>/ros2_jazzy/install/tracetools_read:/Users/<USER>/ros2_jazzy/install/tracetools:/Users/<USER>/ros2_jazzy/install/pluginlib:/Users/<USER>/ros2_jazzy/install/tinyxml2_vendor:/Users/<USER>/ros2_jazzy/install/test_tracetools_launch:/Users/<USER>/ros2_jazzy/install/test_security:/Users/<USER>/ros2_jazzy/install/test_ros2trace:/Users/<USER>/ros2_jazzy/install/test_rmw_implementation:/Users/<USER>/ros2_jazzy/install/test_rclcpp:/Users/<USER>/ros2_jazzy/install/test_quality_of_service:/Users/<USER>/ros2_jazzy/install/test_launch_testing:/Users/<USER>/ros2_jazzy/install/test_launch_ros:/Users/<USER>/ros2_jazzy/install/test_interface_files:/Users/<USER>/ros2_jazzy/install/test_cli_remapping:/Users/<USER>/ros2_jazzy/install/test_cli:/Users/<USER>/ros2_jazzy/install/tango_icons_vendor:/Users/<USER>/ros2_jazzy/install/sqlite3_vendor:/Users/<USER>/ros2_jazzy/install/rcl_logging_spdlog:/Users/<USER>/ros2_jazzy/install/spdlog_vendor:/Users/<USER>/ros2_jazzy/install/shared_queues_vendor:/Users/<USER>/ros2_jazzy/install/rviz_rendering_tests:/Users/<USER>/ros2_jazzy/install/rviz_rendering:/Users/<USER>/ros2_jazzy/install/rviz_ogre_vendor:/Users/<USER>/ros2_jazzy/install/rviz_assimp_vendor:/Users/<USER>/ros2_jazzy/install/rti_connext_dds_cmake_module:/Users/<USER>/ros2_jazzy/install/lttngpy:/Users/<USER>/ros2_jazzy/install/rpyutils:/Users/<USER>/ros2_jazzy/install/rosidl_typesupport_tests:/Users/<USER>/ros2_jazzy/install/rosidl_typesupport_introspection_tests:/Users/<USER>/ros2_jazzy/install/rosidl_typesupport_cpp:/Users/<USER>/ros2_jazzy/install/rosidl_typesupport_introspection_cpp:/Users/<USER>/ros2_jazzy/install/rosidl_typesupport_c:/Users/<USER>/ros2_jazzy/install/rosidl_typesupport_introspection_c:/Users/<USER>/ros2_jazzy/install/rosidl_typesupport_fastrtps_c:/Users/<USER>/ros2_jazzy/install/rosidl_typesupport_fastrtps_cpp:/Users/<USER>/ros2_jazzy/install/rosidl_generator_cpp:/Users/<USER>/ros2_jazzy/install/rosidl_runtime_cpp:/Users/<USER>/ros2_jazzy/install/rosidl_dynamic_typesupport_fastrtps:/Users/<USER>/ros2_jazzy/install/rcl_yaml_param_parser:/Users/<USER>/ros2_jazzy/install/rmw:/Users/<USER>/ros2_jazzy/install/rosidl_dynamic_typesupport:/Users/<USER>/ros2_jazzy/install/rosidl_runtime_c:/Users/<USER>/ros2_jazzy/install/rosidl_generator_c:/Users/<USER>/ros2_jazzy/install/rosidl_typesupport_interface:/Users/<USER>/ros2_jazzy/install/rosidl_runtime_py:/Users/<USER>/ros2_jazzy/install/rosidl_generator_dds_idl:/Users/<USER>/ros2_jazzy/install/rosidl_cmake:/Users/<USER>/ros2_jazzy/install/rosidl_pycommon:/Users/<USER>/ros2_jazzy/install/rosidl_generator_type_description:/Users/<USER>/ros2_jazzy/install/rosidl_parser:/Users/<USER>/ros2_jazzy/install/rosidl_generator_tests:/Users/<USER>/ros2_jazzy/install/rosidl_adapter:/Users/<USER>/ros2_jazzy/install/rosidl_cli:/Users/<USER>/ros2_jazzy/install/rosbag2_tests:/Users/<USER>/ros2_jazzy/install/rosbag2_test_msgdefs:/Users/<USER>/ros2_jazzy/install/ros_environment:/Users/<USER>/ros2_jazzy/install/rmw_implementation_cmake:/Users/<USER>/ros2_jazzy/install/resource_retriever:/Users/<USER>/ros2_jazzy/install/class_loader:/Users/<USER>/ros2_jazzy/install/rcpputils:/Users/<USER>/ros2_jazzy/install/rcl_logging_noop:/Users/<USER>/ros2_jazzy/install/rcl_logging_interface:/Users/<USER>/ros2_jazzy/install/rcutils:/Users/<USER>/ros2_jazzy/install/python_qt_binding:/Users/<USER>/ros2_jazzy/install/python_orocos_kdl_vendor:/Users/<USER>/ros2_jazzy/install/launch_testing_ament_cmake:/Users/<USER>/ros2_jazzy/install/python_cmake_module:/Users/<USER>/ros2_jazzy/install/pybind11_vendor:/Users/<USER>/ros2_jazzy/install/performance_test_fixture:/Users/<USER>/ros2_jazzy/install/launch_pytest:/Users/<USER>/ros2_jazzy/install/launch_testing:/Users/<USER>/ros2_jazzy/install/launch_yaml:/Users/<USER>/ros2_jazzy/install/launch_xml:/Users/<USER>/ros2_jazzy/install/launch:/Users/<USER>/ros2_jazzy/install/osrf_pycommon:/Users/<USER>/ros2_jazzy/install/orocos_kdl_vendor:/Users/<USER>/ros2_jazzy/install/mimick_vendor:/Users/<USER>/ros2_jazzy/install/libyaml_vendor:/Users/<USER>/ros2_jazzy/install/liblz4_vendor:/Users/<USER>/ros2_jazzy/install/libcurl_vendor:/Users/<USER>/ros2_jazzy/install/keyboard_handler:/Users/<USER>/ros2_jazzy/install/gz_math_vendor:/Users/<USER>/ros2_jazzy/install/gz_utils_vendor:/Users/<USER>/ros2_jazzy/install/gz_cmake_vendor:/Users/<USER>/ros2_jazzy/install/ament_cmake_ros:/Users/<USER>/ros2_jazzy/install/ament_cmake_auto:/Users/<USER>/ros2_jazzy/install/ament_cmake_gmock:/Users/<USER>/ros2_jazzy/install/ament_cmake_gtest:/Users/<USER>/ros2_jazzy/install/ament_cmake_google_benchmark:/Users/<USER>/ros2_jazzy/install/fastrtps_cmake_module:/Users/<USER>/ros2_jazzy/install/eigen3_cmake_module:/Users/<USER>/ros2_jazzy/install/domain_coordinator:/Users/<USER>/ros2_jazzy/install/console_bridge_vendor:/Users/<USER>/ros2_jazzy/install/ament_cmake_xmllint:/Users/<USER>/ros2_jazzy/install/ament_xmllint:/Users/<USER>/ros2_jazzy/install/ament_cmake_pyflakes:/Users/<USER>/ros2_jazzy/install/ament_pyflakes:/Users/<USER>/ros2_jazzy/install/ament_cmake_pycodestyle:/Users/<USER>/ros2_jazzy/install/ament_pycodestyle:/Users/<USER>/ros2_jazzy/install/ament_cmake_pep257:/Users/<USER>/ros2_jazzy/install/ament_pep257:/Users/<USER>/ros2_jazzy/install/ament_cmake_pclint:/Users/<USER>/ros2_jazzy/install/ament_pclint:/Users/<USER>/ros2_jazzy/install/ament_lint_auto:/Users/<USER>/ros2_jazzy/install/ament_cmake:/Users/<USER>/ros2_jazzy/install/ament_cmake_version:/Users/<USER>/ros2_jazzy/install/ament_cmake_vendor_package:/Users/<USER>/ros2_jazzy/install/ament_cmake_pytest:/Users/<USER>/ros2_jazzy/install/ament_cmake_mypy:/Users/<USER>/ros2_jazzy/install/ament_cmake_lint_cmake:/Users/<USER>/ros2_jazzy/install/ament_cmake_flake8:/Users/<USER>/ros2_jazzy/install/ament_cmake_cpplint:/Users/<USER>/ros2_jazzy/install/ament_cmake_cppcheck:/Users/<USER>/ros2_jazzy/install/ament_cmake_copyright:/Users/<USER>/ros2_jazzy/install/ament_cmake_clang_tidy:/Users/<USER>/ros2_jazzy/install/ament_cmake_clang_format:/Users/<USER>/ros2_jazzy/install/ament_cmake_test:/Users/<USER>/ros2_jazzy/install/ament_cmake_target_dependencies:/Users/<USER>/ros2_jazzy/install/ament_cmake_python:/Users/<USER>/ros2_jazzy/install/ament_cmake_export_dependencies:/Users/<USER>/ros2_jazzy/install/ament_cmake_libraries:/Users/<USER>/ros2_jazzy/install/ament_cmake_include_directories:/Users/<USER>/ros2_jazzy/install/ament_cmake_gen_version_h:/Users/<USER>/ros2_jazzy/install/ament_cmake_export_targets:/Users/<USER>/ros2_jazzy/install/ament_cmake_export_link_flags:/Users/<USER>/ros2_jazzy/install/ament_cmake_export_interfaces:/Users/<USER>/ros2_jazzy/install/ament_cmake_export_libraries:/Users/<USER>/ros2_jazzy/install/ament_cmake_export_include_directories:/Users/<USER>/ros2_jazzy/install/ament_cmake_export_definitions:/Users/<USER>/ros2_jazzy/install/ament_cmake_core:/Users/<USER>/ros2_jazzy/install/ament_package:/Users/<USER>/ros2_jazzy/install/ament_mypy:/Users/<USER>/ros2_jazzy/install/ament_lint_cmake:/Users/<USER>/ros2_jazzy/install/ament_flake8:/Users/<USER>/ros2_jazzy/install/ament_copyright:/Users/<USER>/ros2_jazzy/install/ament_lint:/Users/<USER>/ros2_jazzy/install/ament_index_python:/Users/<USER>/ros2_jazzy/install/ament_index_cpp:/Users/<USER>/ros2_jazzy/install/ament_cpplint:/Users/<USER>/ros2_jazzy/install/ament_cppcheck:/Users/<USER>/ros2_jazzy/install/ament_clang_tidy:/Users/<USER>/ros2_jazzy/install/ament_clang_format:/Users/<USER>/Work/drill2/onboard/install/tracks_regulator:/Users/<USER>/Work/drill2/onboard/install/state_tracker:/Users/<USER>/Work/drill2/onboard/install/rtk_connector:/Users/<USER>/Work/drill2/onboard/install/remote_connector:/Users/<USER>/Work/drill2/onboard/install/pydubins:/Users/<USER>/Work/drill2/onboard/install/path_follower:/Users/<USER>/Work/drill2/onboard/install/params_server:/Users/<USER>/Work/drill2/onboard/install/modbus_node:/Users/<USER>/Work/drill2/onboard/install/main_state_machine:/Users/<USER>/Work/drill2/onboard/install/maneuver_builder_cpp:/Users/<USER>/Work/drill2/onboard/install/leveler:/Users/<USER>/Work/drill2/onboard/install/launchpack:/Users/<USER>/Work/drill2/onboard/install/can_encoder:/Users/<USER>/Work/drill2/onboard/install/can_decoder:/Users/<USER>/Work/drill2/onboard/install/base_node:/Users/<USER>/Work/drill2/onboard/install/drill_msgs:/Users/<USER>/Work/drill2/onboard/install/can_msgs'), ('NVM_DIR', '/Users/<USER>/.nvm'), ('USER', 'frontwise'), ('COMMAND_MODE', 'unix2003'), ('SSH_AUTH_SOCK', '/private/tmp/com.apple.launchd.QIsZBTGupl/Listeners'), ('__CF_USER_TEXT_ENCODING', '0x1F5:0x0:0x0'), ('TERM_FEATURES', 'T3LrMSc7UUw9Ts3BFGsSyHNoSxF'), ('VIRTUAL_ENV', '/Users/<USER>/.ros2_venv'), ('TERMINFO_DIRS', '/Applications/iTerm.app/Contents/Resources/terminfo:/usr/share/terminfo'), ('PATH', '/Users/<USER>/ros2_jazzy/install/rviz2/bin:/Users/<USER>/ros2_jazzy/install/urdfdom/bin:/Users/<USER>/ros2_jazzy/install/ros2cli/bin:/Users/<USER>/ros2_jazzy/install/ament_uncrustify/bin:/Users/<USER>/ros2_jazzy/install/uncrustify_vendor/opt/uncrustify_vendor/bin:/Users/<USER>/ros2_jazzy/install/rviz_ogre_vendor/opt/rviz_ogre_vendor/bin:/Users/<USER>/ros2_jazzy/install/rosidl_cli/bin:/Users/<USER>/ros2_jazzy/install/launch_testing/bin:/Users/<USER>/ros2_jazzy/install/iceoryx_introspection/bin:/Users/<USER>/ros2_jazzy/install/cyclonedds/bin:/Users/<USER>/ros2_jazzy/install/iceoryx_posh/bin:/Users/<USER>/ros2_jazzy/install/fastrtps/bin:/Users/<USER>/ros2_jazzy/install/foonathan_memory_vendor/bin:/Users/<USER>/ros2_jazzy/install/ament_xmllint/bin:/Users/<USER>/ros2_jazzy/install/ament_pyflakes/bin:/Users/<USER>/ros2_jazzy/install/ament_pycodestyle/bin:/Users/<USER>/ros2_jazzy/install/ament_pep257/bin:/Users/<USER>/ros2_jazzy/install/ament_pclint/bin:/Users/<USER>/ros2_jazzy/install/ament_mypy/bin:/Users/<USER>/ros2_jazzy/install/ament_lint_cmake/bin:/Users/<USER>/ros2_jazzy/install/ament_flake8/bin:/Users/<USER>/ros2_jazzy/install/ament_copyright/bin:/Users/<USER>/ros2_jazzy/install/ament_index_python/bin:/Users/<USER>/ros2_jazzy/install/ament_cpplint/bin:/Users/<USER>/ros2_jazzy/install/ament_cppcheck/bin:/Users/<USER>/ros2_jazzy/install/ament_clang_tidy/bin:/Users/<USER>/ros2_jazzy/install/ament_clang_format/bin:/Users/<USER>/.ros2_venv/bin:/Users/<USER>/.codeium/windsurf/bin:/Users/<USER>/yandex-cloud/bin:/Users/<USER>/.nvm/versions/node/v22.14.0/bin:/opt/homebrew/bin:/opt/homebrew/sbin:/usr/local/bin:/System/Cryptexes/App/usr/bin:/usr/bin:/bin:/usr/sbin:/sbin:/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/local/bin:/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/bin:/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/appleinternal/bin:/opt/X11/bin:/Library/Apple/usr/bin:/usr/local/share/dotnet:~/.dotnet/tools:/Library/Frameworks/Mono.framework/Versions/Current/Commands:/Applications/iTerm.app/Contents/Resources/utilities:/Users/<USER>/.spoofdpi/bin'), ('LaunchInstanceID', '672A663D-5DC3-4014-BF99-18B0DF3B92FC'), ('__CFBundleIdentifier', 'com.googlecode.iterm2'), ('PWD', '/Users/<USER>/Work/drill2/onboard/build/drill_msgs'), ('COLCON', '1'), ('LANG', 'ru_RU.UTF-8'), ('ITERM_PROFILE', 'Default'), ('XPC_FLAGS', '0x0'), ('XPC_SERVICE_NAME', '0'), ('OPENSSL_ROOT_DIR', '/opt/homebrew/opt/openssl@3'), ('SHLVL', '2'), ('HOME', '/Users/<USER>'), ('COLORFGBG', '15;0'), ('ROS_DISTRO', 'jazzy'), ('LC_TERMINAL_VERSION', '3.5.15beta1'), ('HOMEBREW_PREFIX', '/opt/homebrew'), ('DYLD_LIBRARY_PATH', '/Users/<USER>/ros2_jazzy/install/rosbag2_storage_mcap/lib:/Users/<USER>/ros2_jazzy/install/rosbag2_compression_zstd/lib:/Users/<USER>/ros2_jazzy/install/mcap_vendor/lib:/Users/<USER>/ros2_jazzy/install/rviz_default_plugins/lib:/Users/<USER>/ros2_jazzy/install/rviz_common/lib:/Users/<USER>/ros2_jazzy/install/rosbag2_transport/lib:/Users/<USER>/ros2_jazzy/install/rosbag2_storage_sqlite3/lib:/Users/<USER>/ros2_jazzy/install/rosbag2_compression/lib:/Users/<USER>/ros2_jazzy/install/rosbag2_cpp/lib:/Users/<USER>/ros2_jazzy/install/rosbag2_storage/lib:/Users/<USER>/ros2_jazzy/install/camera_info_manager/lib:/Users/<USER>/ros2_jazzy/install/camera_calibration_parsers/lib:/Users/<USER>/ros2_jazzy/install/interactive_markers/lib:/Users/<USER>/ros2_jazzy/install/visualization_msgs/lib:/Users/<USER>/ros2_jazzy/install/robot_state_publisher/lib:/Users/<USER>/ros2_jazzy/install/kdl_parser/lib:/Users/<USER>/ros2_jazzy/install/urdf/lib:/Users/<USER>/ros2_jazzy/install/urdfdom/lib:/Users/<USER>/ros2_jazzy/install/turtlesim/lib:/Users/<USER>/ros2_jazzy/install/trajectory_msgs/lib:/Users/<USER>/ros2_jazzy/install/tf2_ros/lib:/Users/<USER>/ros2_jazzy/install/tf2_msgs/lib:/Users/<USER>/ros2_jazzy/install/tf2_eigen_kdl/lib:/Users/<USER>/ros2_jazzy/install/laser_geometry/lib:/Users/<USER>/ros2_jazzy/install/tf2/lib:/Users/<USER>/ros2_jazzy/install/test_msgs/lib:/Users/<USER>/ros2_jazzy/install/stereo_msgs/lib:/Users/<USER>/ros2_jazzy/install/std_srvs/lib:/Users/<USER>/ros2_jazzy/install/shape_msgs/lib:/Users/<USER>/ros2_jazzy/install/quality_of_service_demo_cpp/lib:/Users/<USER>/ros2_jazzy/install/point_cloud_transport/lib:/Users/<USER>/ros2_jazzy/install/map_msgs/lib:/Users/<USER>/ros2_jazzy/install/image_transport/lib:/Users/<USER>/ros2_jazzy/install/image_tools/lib:/Users/<USER>/ros2_jazzy/install/sensor_msgs/lib:/Users/<USER>/ros2_jazzy/install/nav_msgs/lib:/Users/<USER>/ros2_jazzy/install/message_filters/lib:/Users/<USER>/ros2_jazzy/install/logging_demo/lib:/Users/<USER>/ros2_jazzy/install/diagnostic_msgs/lib:/Users/<USER>/ros2_jazzy/install/geometry_msgs/lib:/Users/<USER>/ros2_jazzy/install/examples_rclcpp_wait_set/lib:/Users/<USER>/ros2_jazzy/install/examples_rclcpp_minimal_subscriber/lib:/Users/<USER>/ros2_jazzy/install/examples_rclcpp_minimal_composition/lib:/Users/<USER>/ros2_jazzy/install/demo_nodes_cpp_native/lib:/Users/<USER>/ros2_jazzy/install/demo_nodes_cpp/lib:/Users/<USER>/ros2_jazzy/install/composition/lib:/Users/<USER>/ros2_jazzy/install/actionlib_msgs/lib:/Users/<USER>/ros2_jazzy/install/std_msgs/lib:/Users/<USER>/ros2_jazzy/install/rclcpp_lifecycle/lib:/Users/<USER>/ros2_jazzy/install/action_tutorials_cpp/lib:/Users/<USER>/ros2_jazzy/install/rclcpp_components/lib:/Users/<USER>/ros2_jazzy/install/rclcpp_action/lib:/Users/<USER>/ros2_jazzy/install/rclcpp/lib:/Users/<USER>/ros2_jazzy/install/libstatistics_collector/lib:/Users/<USER>/ros2_jazzy/install/statistics_msgs/lib:/Users/<USER>/ros2_jazzy/install/rclpy/lib:/Users/<USER>/ros2_jazzy/install/rosgraph_msgs/lib:/Users/<USER>/ros2_jazzy/install/rosbag2_interfaces/lib:/Users/<USER>/ros2_jazzy/install/ros2cli_test_interfaces/lib:/Users/<USER>/ros2_jazzy/install/rcl_lifecycle/lib:/Users/<USER>/ros2_jazzy/install/rcl_action/lib:/Users/<USER>/ros2_jazzy/install/rcl/lib:/Users/<USER>/ros2_jazzy/install/rmw_implementation/lib:/Users/<USER>/ros2_jazzy/install/rmw_fastrtps_dynamic_cpp/lib:/Users/<USER>/ros2_jazzy/install/rmw_fastrtps_cpp/lib:/Users/<USER>/ros2_jazzy/install/rmw_fastrtps_shared_cpp/lib:/Users/<USER>/ros2_jazzy/install/rmw_cyclonedds_cpp/lib:/Users/<USER>/ros2_jazzy/install/rmw_dds_common/lib:/Users/<USER>/ros2_jazzy/install/composition_interfaces/lib:/Users/<USER>/ros2_jazzy/install/rcl_interfaces/lib:/Users/<USER>/ros2_jazzy/install/pendulum_msgs/lib:/Users/<USER>/ros2_jazzy/install/lifecycle_msgs/lib:/Users/<USER>/ros2_jazzy/install/example_interfaces/lib:/Users/<USER>/ros2_jazzy/install/action_tutorials_interfaces/lib:/Users/<USER>/ros2_jazzy/install/action_msgs/lib:/Users/<USER>/ros2_jazzy/install/unique_identifier_msgs/lib:/Users/<USER>/ros2_jazzy/install/type_description_interfaces/lib:/Users/<USER>/ros2_jazzy/install/service_msgs/lib:/Users/<USER>/ros2_jazzy/install/builtin_interfaces/lib:/Users/<USER>/ros2_jazzy/install/rcl_logging_spdlog/lib:/Users/<USER>/ros2_jazzy/install/rviz_rendering/lib:/Users/<USER>/ros2_jazzy/install/rviz_ogre_vendor/opt/rviz_ogre_vendor/lib:/Users/<USER>/ros2_jazzy/install/rosidl_typesupport_cpp/lib:/Users/<USER>/ros2_jazzy/install/rosidl_typesupport_introspection_cpp/lib:/Users/<USER>/ros2_jazzy/install/rosidl_typesupport_c/lib:/Users/<USER>/ros2_jazzy/install/rosidl_typesupport_introspection_c/lib:/Users/<USER>/ros2_jazzy/install/rosidl_typesupport_fastrtps_c/lib:/Users/<USER>/ros2_jazzy/install/rosidl_typesupport_fastrtps_cpp/lib:/Users/<USER>/ros2_jazzy/install/rosidl_dynamic_typesupport_fastrtps/lib:/Users/<USER>/ros2_jazzy/install/rcl_yaml_param_parser/lib:/Users/<USER>/ros2_jazzy/install/rmw/lib:/Users/<USER>/ros2_jazzy/install/rosidl_dynamic_typesupport/lib:/Users/<USER>/ros2_jazzy/install/rosidl_runtime_c/lib:/Users/<USER>/ros2_jazzy/install/rosbag2_test_msgdefs/lib:/Users/<USER>/ros2_jazzy/install/resource_retriever/lib:/Users/<USER>/ros2_jazzy/install/class_loader/lib:/Users/<USER>/ros2_jazzy/install/rcpputils/lib:/Users/<USER>/ros2_jazzy/install/rcl_logging_noop/lib:/Users/<USER>/ros2_jazzy/install/rcl_logging_interface/lib:/Users/<USER>/ros2_jazzy/install/rcutils/lib:/Users/<USER>/ros2_jazzy/install/performance_test_fixture/lib:/Users/<USER>/ros2_jazzy/install/osrf_testing_tools_cpp/lib:/Users/<USER>/ros2_jazzy/install/mimick_vendor/opt/mimick_vendor/lib:/Users/<USER>/ros2_jazzy/install/keyboard_handler/lib:/Users/<USER>/ros2_jazzy/install/iceoryx_introspection/lib:/Users/<USER>/ros2_jazzy/install/cyclonedds/lib:/Users/<USER>/ros2_jazzy/install/iceoryx_posh/lib:/Users/<USER>/ros2_jazzy/install/iceoryx_hoofs/lib:/Users/<USER>/ros2_jazzy/install/iceoryx_binding_c/lib:/Users/<USER>/ros2_jazzy/install/gz_math_vendor/opt/gz_math_vendor/lib:/Users/<USER>/ros2_jazzy/install/gz_utils_vendor/opt/gz_utils_vendor/lib:/Users/<USER>/ros2_jazzy/install/gz_cmake_vendor/opt/gz_cmake_vendor/lib:/Users/<USER>/ros2_jazzy/install/fastrtps/lib:/Users/<USER>/ros2_jazzy/install/fastcdr/lib:/Users/<USER>/ros2_jazzy/install/google_benchmark_vendor/lib:/Users/<USER>/ros2_jazzy/install/ament_index_cpp/lib:/Users/<USER>/Work/drill2/onboard/install/drill_msgs/lib:/Users/<USER>/Work/drill2/onboard/install/can_msgs/lib'), ('ITERM_SESSION_ID', 'w0t3p0:22583761-CC84-481F-B886-691DB3AE381C'), ('PYTHONPATH', '/Users/<USER>/Work/drill2/onboard/install/depth_tracker/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/install/rosbag2_storage_mcap/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/build/rosbag2_examples_py:/Users/<USER>/ros2_jazzy/install/rosbag2_examples_py/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/build/launch_testing_examples:/Users/<USER>/ros2_jazzy/install/launch_testing_examples/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/build/ros2bag:/Users/<USER>/ros2_jazzy/install/ros2bag/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/install/rosbag2_py/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/install/rosbag2_storage_sqlite3/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/install/interactive_markers/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/install/visualization_msgs/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/install/turtlesim/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/install/trajectory_msgs/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/build/tracetools_test:/Users/<USER>/ros2_jazzy/install/tracetools_test/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/build/tracetools_launch:/Users/<USER>/ros2_jazzy/install/tracetools_launch/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/build/topic_monitor:/Users/<USER>/ros2_jazzy/install/topic_monitor/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/build/tf2_tools:/Users/<USER>/ros2_jazzy/install/tf2_tools/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/install/tf2_sensor_msgs/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/install/tf2_kdl/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/install/tf2_geometry_msgs/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/build/examples_tf2_py:/Users/<USER>/ros2_jazzy/install/examples_tf2_py/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/build/tf2_ros_py:/Users/<USER>/ros2_jazzy/install/tf2_ros_py/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/install/tf2_py/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/install/tf2_msgs/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/install/laser_geometry/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/install/rosbag2_test_common/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/install/test_msgs/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/install/stereo_msgs/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/install/std_srvs/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/install/shape_msgs/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/build/examples_rclpy_pointcloud_publisher:/Users/<USER>/ros2_jazzy/install/examples_rclpy_pointcloud_publisher/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/build/sensor_msgs_py:/Users/<USER>/ros2_jazzy/install/sensor_msgs_py/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/build/quality_of_service_demo_py:/Users/<USER>/ros2_jazzy/install/quality_of_service_demo_py/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/install/point_cloud_transport_py/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/install/map_msgs/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/build/camera_info_manager_py:/Users/<USER>/ros2_jazzy/install/camera_info_manager_py/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/install/sensor_msgs/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/build/ros2doctor:/Users/<USER>/ros2_jazzy/install/ros2doctor/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/install/nav_msgs/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/install/message_filters/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/install/logging_demo/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/build/lifecycle_py:/Users/<USER>/ros2_jazzy/install/lifecycle_py/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/install/diagnostic_msgs/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/install/geometry_msgs/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/build/examples_rclpy_minimal_subscriber:/Users/<USER>/ros2_jazzy/install/examples_rclpy_minimal_subscriber/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/build/examples_rclpy_minimal_service:/Users/<USER>/ros2_jazzy/install/examples_rclpy_minimal_service/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/build/examples_rclpy_minimal_publisher:/Users/<USER>/ros2_jazzy/install/examples_rclpy_minimal_publisher/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/build/examples_rclpy_minimal_client:/Users/<USER>/ros2_jazzy/install/examples_rclpy_minimal_client/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/build/examples_rclpy_executors:/Users/<USER>/ros2_jazzy/install/examples_rclpy_executors/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/build/demo_nodes_py:/Users/<USER>/ros2_jazzy/install/demo_nodes_py/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/install/actionlib_msgs/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/install/std_msgs/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/build/ros2component:/Users/<USER>/ros2_jazzy/install/ros2component/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/install/statistics_msgs/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/build/sros2:/Users/<USER>/ros2_jazzy/install/sros2/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/build/ros2trace:/Users/<USER>/ros2_jazzy/install/ros2trace/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/build/ros2topic:/Users/<USER>/ros2_jazzy/install/ros2topic/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/build/ros2test:/Users/<USER>/ros2_jazzy/install/ros2test/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/build/ros2param:/Users/<USER>/ros2_jazzy/install/ros2param/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/build/ros2lifecycle:/Users/<USER>/ros2_jazzy/install/ros2lifecycle/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/build/ros2service:/Users/<USER>/ros2_jazzy/install/ros2service/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/build/ros2run:/Users/<USER>/ros2_jazzy/install/ros2run/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/build/ros2launch:/Users/<USER>/ros2_jazzy/install/ros2launch/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/build/ros2pkg:/Users/<USER>/ros2_jazzy/install/ros2pkg/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/build/ros2node:/Users/<USER>/ros2_jazzy/install/ros2node/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/build/ros2multicast:/Users/<USER>/ros2_jazzy/install/ros2multicast/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/build/ros2interface:/Users/<USER>/ros2_jazzy/install/ros2interface/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/build/ros2action:/Users/<USER>/ros2_jazzy/install/ros2action/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/build/ros2cli:/Users/<USER>/ros2_jazzy/install/ros2cli/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/build/launch_testing_ros:/Users/<USER>/ros2_jazzy/install/launch_testing_ros/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/build/launch_ros:/Users/<USER>/ros2_jazzy/install/launch_ros/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/build/examples_rclpy_minimal_action_server:/Users/<USER>/ros2_jazzy/install/examples_rclpy_minimal_action_server/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/build/examples_rclpy_minimal_action_client:/Users/<USER>/ros2_jazzy/install/examples_rclpy_minimal_action_client/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/build/examples_rclpy_guard_conditions:/Users/<USER>/ros2_jazzy/install/examples_rclpy_guard_conditions/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/build/action_tutorials_py:/Users/<USER>/ros2_jazzy/install/action_tutorials_py/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/install/rclpy/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/install/rosgraph_msgs/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/install/rosbag2_interfaces/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/install/ros2cli_test_interfaces/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/install/rmw_dds_common/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/install/composition_interfaces/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/install/rcl_interfaces/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/install/pendulum_msgs/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/install/lifecycle_msgs/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/install/example_interfaces/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/install/action_tutorials_interfaces/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/install/action_msgs/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/install/unique_identifier_msgs/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/install/type_description_interfaces/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/install/service_msgs/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/install/builtin_interfaces/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/install/rosidl_generator_py/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/build/ament_uncrustify:/Users/<USER>/ros2_jazzy/install/ament_uncrustify/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/build/tracetools_trace:/Users/<USER>/ros2_jazzy/install/tracetools_trace/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/build/tracetools_read:/Users/<USER>/ros2_jazzy/install/tracetools_read/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/build/test_tracetools_launch:/Users/<USER>/ros2_jazzy/install/test_tracetools_launch/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/build/test_ros2trace:/Users/<USER>/ros2_jazzy/install/test_ros2trace/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/build/test_launch_ros:/Users/<USER>/ros2_jazzy/install/test_launch_ros/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/install/lttngpy/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/build/rpyutils:/Users/<USER>/ros2_jazzy/install/rpyutils/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/install/rosidl_typesupport_cpp/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/install/rosidl_typesupport_introspection_cpp/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/install/rosidl_typesupport_c/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/install/rosidl_typesupport_introspection_c/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/install/rosidl_typesupport_fastrtps_c/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/install/rosidl_typesupport_fastrtps_cpp/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/install/rosidl_generator_cpp/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/install/rosidl_generator_c/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/build/rosidl_runtime_py:/Users/<USER>/ros2_jazzy/install/rosidl_runtime_py/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/install/rosidl_generator_dds_idl/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/install/rosidl_cmake/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/build/rosidl_pycommon:/Users/<USER>/ros2_jazzy/install/rosidl_pycommon/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/install/rosidl_generator_type_description/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/install/rosidl_parser/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/install/rosidl_adapter/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/build/rosidl_cli:/Users/<USER>/ros2_jazzy/install/rosidl_cli/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/install/rosbag2_test_msgdefs/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/install/resource_retriever/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/install/rcutils/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/install/python_qt_binding/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/install/python_orocos_kdl_vendor/lib/python3.11/dist-packages:/Users/<USER>/ros2_jazzy/build/launch_pytest:/Users/<USER>/ros2_jazzy/install/launch_pytest/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/build/launch_testing:/Users/<USER>/ros2_jazzy/install/launch_testing/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/build/launch_yaml:/Users/<USER>/ros2_jazzy/install/launch_yaml/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/build/launch_xml:/Users/<USER>/ros2_jazzy/install/launch_xml/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/build/launch:/Users/<USER>/ros2_jazzy/install/launch/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/build/osrf_pycommon:/Users/<USER>/ros2_jazzy/install/osrf_pycommon/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/install/ament_cmake_google_benchmark/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/build/domain_coordinator:/Users/<USER>/ros2_jazzy/install/domain_coordinator/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/build/ament_xmllint:/Users/<USER>/ros2_jazzy/install/ament_xmllint/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/build/ament_pyflakes:/Users/<USER>/ros2_jazzy/install/ament_pyflakes/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/build/ament_pycodestyle:/Users/<USER>/ros2_jazzy/install/ament_pycodestyle/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/build/ament_pep257:/Users/<USER>/ros2_jazzy/install/ament_pep257/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/build/ament_pclint:/Users/<USER>/ros2_jazzy/install/ament_pclint/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/install/ament_cmake_test/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/build/ament_package:/Users/<USER>/ros2_jazzy/install/ament_package/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/build/ament_mypy:/Users/<USER>/ros2_jazzy/install/ament_mypy/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/build/ament_lint_cmake:/Users/<USER>/ros2_jazzy/install/ament_lint_cmake/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/build/ament_flake8:/Users/<USER>/ros2_jazzy/install/ament_flake8/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/build/ament_copyright:/Users/<USER>/ros2_jazzy/install/ament_copyright/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/build/ament_lint:/Users/<USER>/ros2_jazzy/install/ament_lint/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/build/ament_index_python:/Users/<USER>/ros2_jazzy/install/ament_index_python/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/build/ament_cpplint:/Users/<USER>/ros2_jazzy/install/ament_cpplint/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/build/ament_cppcheck:/Users/<USER>/ros2_jazzy/install/ament_cppcheck/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/build/ament_clang_tidy:/Users/<USER>/ros2_jazzy/install/ament_clang_tidy/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/build/ament_clang_format:/Users/<USER>/ros2_jazzy/install/ament_clang_format/lib/python3.11/site-packages:/Users/<USER>/Work/drill2/onboard/install/tracks_regulator/lib/python3.11/site-packages:/Users/<USER>/Work/drill2/onboard/install/state_tracker/lib/python3.11/site-packages:/Users/<USER>/Work/drill2/onboard/install/rtk_connector/lib/python3.11/site-packages:/Users/<USER>/Work/drill2/onboard/install/remote_connector/lib/python3.11/site-packages:/Users/<USER>/Work/drill2/onboard/install/pydubins/lib/python3.11/site-packages:/Users/<USER>/Work/drill2/onboard/install/path_follower/lib/python3.11/site-packages:/Users/<USER>/Work/drill2/onboard/install/params_server/lib/python3.11/site-packages:/Users/<USER>/Work/drill2/onboard/install/modbus_node/lib/python3.11/site-packages:/Users/<USER>/Work/drill2/onboard/install/main_state_machine/lib/python3.11/site-packages:/Users/<USER>/Work/drill2/onboard/install/maneuver_builder_cpp/lib/python3.11/site-packages:/Users/<USER>/Work/drill2/onboard/install/leveler/lib/python3.11/site-packages:/Users/<USER>/Work/drill2/onboard/install/launchpack/lib/python3.11/site-packages:/Users/<USER>/Work/drill2/onboard/install/can_encoder/lib/python3.11/site-packages:/Users/<USER>/Work/drill2/onboard/install/can_decoder/lib/python3.11/site-packages:/Users/<USER>/Work/drill2/onboard/install/base_node/lib/python3.11/site-packages:/Users/<USER>/Work/drill2/onboard/install/drill_msgs/lib/python3.11/site-packages:/Users/<USER>/Work/drill2/onboard/install/can_msgs/lib/python3.11/site-packages:/Users/<USER>/Work/drill2/onboard/install/external/maneuver_builder_cpp/lib/python3.11/site-packages:/Users/<USER>/Work/drill2/onboard/install/external/maneuver_builder_cpp/lib/python3.11/site-packages'), ('LOGNAME', 'frontwise'), ('ROS_AUTOMATIC_DISCOVERY_RANGE', 'SUBNET'), ('PKG_CONFIG_PATH', '/Users/<USER>/ros2_jazzy/install/urdfdom/lib/pkgconfig:/Users/<USER>/ros2_jazzy/install/urdfdom_headers/lib/pkgconfig:/Users/<USER>/ros2_jazzy/install/osrf_testing_tools_cpp/lib/pkgconfig:/Users/<USER>/ros2_jazzy/install/iceoryx_introspection/lib/pkgconfig:/Users/<USER>/ros2_jazzy/install/cyclonedds/lib/pkgconfig:/Users/<USER>/ros2_jazzy/install/iceoryx_posh/lib/pkgconfig:/Users/<USER>/ros2_jazzy/install/iceoryx_hoofs/lib/pkgconfig:/Users/<USER>/ros2_jazzy/install/iceoryx_binding_c/lib/pkgconfig:/Users/<USER>/ros2_jazzy/install/gmock_vendor/lib/pkgconfig:/Users/<USER>/ros2_jazzy/install/gtest_vendor/lib/pkgconfig:/Users/<USER>/ros2_jazzy/install/google_benchmark_vendor/lib/pkgconfig:/Users/<USER>/ros2_jazzy/install/foonathan_memory_vendor/lib/pkgconfig'), ('NVM_BIN', '/Users/<USER>/.nvm/versions/node/v22.14.0/bin'), ('INFOPATH', '/opt/homebrew/share/info:'), ('HOMEBREW_CELLAR', '/opt/homebrew/Cellar'), ('CMAKE_PREFIX_PATH', '/opt/homebrew/opt/qt@5/lib/cmake:/opt/homebrew/opt:/opt/homebrew/opt/qt@5/lib/cmake:/opt/homebrew/opt:/opt/homebrew/opt/qt@5/lib/cmake:/opt/homebrew/opt:/Users/<USER>/ros2_jazzy/install/rosbag2:/Users/<USER>/ros2_jazzy/install/rosbag2_storage_default_plugins:/Users/<USER>/ros2_jazzy/install/rosbag2_storage_mcap:/Users/<USER>/ros2_jazzy/install/rosbag2_compression_zstd:/Users/<USER>/ros2_jazzy/install/mcap_vendor:/Users/<USER>/ros2_jazzy/install/zstd_vendor:/Users/<USER>/ros2_jazzy/install/rviz_visual_testing_framework:/Users/<USER>/ros2_jazzy/install/rviz2:/Users/<USER>/ros2_jazzy/install/rviz_default_plugins:/Users/<USER>/ros2_jazzy/install/rviz_common:/Users/<USER>/ros2_jazzy/install/rosbag2_py:/Users/<USER>/ros2_jazzy/install/rosbag2_transport:/Users/<USER>/ros2_jazzy/install/rosbag2_storage_sqlite3:/Users/<USER>/ros2_jazzy/install/rosbag2_examples_cpp:/Users/<USER>/ros2_jazzy/install/rosbag2_compression:/Users/<USER>/ros2_jazzy/install/rosbag2_cpp:/Users/<USER>/ros2_jazzy/install/rosbag2_storage:/Users/<USER>/ros2_jazzy/install/image_common:/Users/<USER>/ros2_jazzy/install/camera_info_manager:/Users/<USER>/ros2_jazzy/install/camera_calibration_parsers:/Users/<USER>/ros2_jazzy/install/yaml_cpp_vendor:/Users/<USER>/ros2_jazzy/install/interactive_markers:/Users/<USER>/ros2_jazzy/install/common_interfaces:/Users/<USER>/ros2_jazzy/install/visualization_msgs:/Users/<USER>/ros2_jazzy/install/dummy_robot_bringup:/Users/<USER>/ros2_jazzy/install/robot_state_publisher:/Users/<USER>/ros2_jazzy/install/kdl_parser:/Users/<USER>/ros2_jazzy/install/urdf:/Users/<USER>/ros2_jazzy/install/urdfdom:/Users/<USER>/ros2_jazzy/install/urdf_parser_plugin:/Users/<USER>/ros2_jazzy/install/urdfdom_headers:/Users/<USER>/ros2_jazzy/install/turtlesim:/Users/<USER>/ros2_jazzy/install/trajectory_msgs:/Users/<USER>/ros2_jazzy/install/topic_statistics_demo:/Users/<USER>/ros2_jazzy/install/geometry2:/Users/<USER>/ros2_jazzy/install/tf2_sensor_msgs:/Users/<USER>/ros2_jazzy/install/test_tf2:/Users/<USER>/ros2_jazzy/install/tf2_kdl:/Users/<USER>/ros2_jazzy/install/tf2_geometry_msgs:/Users/<USER>/ros2_jazzy/install/tf2_eigen:/Users/<USER>/ros2_jazzy/install/tf2_bullet:/Users/<USER>/ros2_jazzy/install/tf2_ros:/Users/<USER>/ros2_jazzy/install/tf2_py:/Users/<USER>/ros2_jazzy/install/tf2_msgs:/Users/<USER>/ros2_jazzy/install/tf2_eigen_kdl:/Users/<USER>/ros2_jazzy/install/laser_geometry:/Users/<USER>/ros2_jazzy/install/tf2:/Users/<USER>/ros2_jazzy/install/test_tracetools:/Users/<USER>/ros2_jazzy/install/rosbag2_test_common:/Users/<USER>/ros2_jazzy/install/test_msgs:/Users/<USER>/ros2_jazzy/install/test_communication:/Users/<USER>/ros2_jazzy/install/stereo_msgs:/Users/<USER>/ros2_jazzy/install/std_srvs:/Users/<USER>/ros2_jazzy/install/shape_msgs:/Users/<USER>/ros2_jazzy/install/quality_of_service_demo_cpp:/Users/<USER>/ros2_jazzy/install/point_cloud_transport_py:/Users/<USER>/ros2_jazzy/install/point_cloud_transport:/Users/<USER>/ros2_jazzy/install/map_msgs:/Users/<USER>/ros2_jazzy/install/intra_process_demo:/Users/<USER>/ros2_jazzy/install/image_transport:/Users/<USER>/ros2_jazzy/install/image_tools:/Users/<USER>/ros2_jazzy/install/dummy_sensors:/Users/<USER>/ros2_jazzy/install/sensor_msgs:/Users/<USER>/ros2_jazzy/install/ros2cli_common_extensions:/Users/<USER>/ros2_jazzy/install/dummy_map_server:/Users/<USER>/ros2_jazzy/install/nav_msgs:/Users/<USER>/ros2_jazzy/install/message_filters:/Users/<USER>/ros2_jazzy/install/logging_demo:/Users/<USER>/ros2_jazzy/install/lifecycle:/Users/<USER>/ros2_jazzy/install/diagnostic_msgs:/Users/<USER>/ros2_jazzy/install/geometry_msgs:/Users/<USER>/ros2_jazzy/install/examples_rclcpp_wait_set:/Users/<USER>/ros2_jazzy/install/examples_rclcpp_multithreaded_executor:/Users/<USER>/ros2_jazzy/install/examples_rclcpp_minimal_subscriber:/Users/<USER>/ros2_jazzy/install/examples_rclcpp_minimal_publisher:/Users/<USER>/ros2_jazzy/install/examples_rclcpp_minimal_composition:/Users/<USER>/ros2_jazzy/install/examples_rclcpp_cbg_executor:/Users/<USER>/ros2_jazzy/install/demo_nodes_cpp_native:/Users/<USER>/ros2_jazzy/install/demo_nodes_cpp:/Users/<USER>/ros2_jazzy/install/composition:/Users/<USER>/ros2_jazzy/install/actionlib_msgs:/Users/<USER>/ros2_jazzy/install/std_msgs:/Users/<USER>/ros2_jazzy/install/ros2lifecycle_test_fixtures:/Users/<USER>/ros2_jazzy/install/rclcpp_lifecycle:/Users/<USER>/ros2_jazzy/install/action_tutorials_cpp:/Users/<USER>/ros2_jazzy/install/rclcpp_components:/Users/<USER>/ros2_jazzy/install/examples_rclcpp_minimal_action_server:/Users/<USER>/ros2_jazzy/install/examples_rclcpp_minimal_action_client:/Users/<USER>/ros2_jazzy/install/rclcpp_action:/Users/<USER>/ros2_jazzy/install/examples_rclcpp_minimal_timer:/Users/<USER>/ros2_jazzy/install/examples_rclcpp_minimal_service:/Users/<USER>/ros2_jazzy/install/examples_rclcpp_minimal_client:/Users/<USER>/ros2_jazzy/install/examples_rclcpp_async_client:/Users/<USER>/ros2_jazzy/install/rclcpp:/Users/<USER>/ros2_jazzy/install/libstatistics_collector:/Users/<USER>/ros2_jazzy/install/statistics_msgs:/Users/<USER>/ros2_jazzy/install/sros2_cmake:/Users/<USER>/ros2_jazzy/install/ros_testing:/Users/<USER>/ros2_jazzy/install/rclpy:/Users/<USER>/ros2_jazzy/install/rosgraph_msgs:/Users/<USER>/ros2_jazzy/install/rosbag2_interfaces:/Users/<USER>/ros2_jazzy/install/ros2cli_test_interfaces:/Users/<USER>/ros2_jazzy/install/rcl_lifecycle:/Users/<USER>/ros2_jazzy/install/rcl_action:/Users/<USER>/ros2_jazzy/install/rcl:/Users/<USER>/ros2_jazzy/install/rmw_implementation:/Users/<USER>/ros2_jazzy/install/rmw_fastrtps_dynamic_cpp:/Users/<USER>/ros2_jazzy/install/rmw_fastrtps_cpp:/Users/<USER>/ros2_jazzy/install/rmw_fastrtps_shared_cpp:/Users/<USER>/ros2_jazzy/install/rmw_cyclonedds_cpp:/Users/<USER>/ros2_jazzy/install/rmw_connextddsmicro:/Users/<USER>/ros2_jazzy/install/rmw_connextdds:/Users/<USER>/ros2_jazzy/install/rmw_connextdds_common:/Users/<USER>/ros2_jazzy/install/rmw_dds_common:/Users/<USER>/ros2_jazzy/install/composition_interfaces:/Users/<USER>/ros2_jazzy/install/rcl_interfaces:/Users/<USER>/ros2_jazzy/install/pendulum_msgs:/Users/<USER>/ros2_jazzy/install/lifecycle_msgs:/Users/<USER>/ros2_jazzy/install/example_interfaces:/Users/<USER>/ros2_jazzy/install/action_tutorials_interfaces:/Users/<USER>/ros2_jazzy/install/rosidl_default_runtime:/Users/<USER>/ros2_jazzy/install/rosidl_default_generators:/Users/<USER>/ros2_jazzy/install/action_msgs:/Users/<USER>/ros2_jazzy/install/unique_identifier_msgs:/Users/<USER>/ros2_jazzy/install/type_description_interfaces:/Users/<USER>/ros2_jazzy/install/service_msgs:/Users/<USER>/ros2_jazzy/install/builtin_interfaces:/Users/<USER>/ros2_jazzy/install/rosidl_core_runtime:/Users/<USER>/ros2_jazzy/install/rosidl_core_generators:/Users/<USER>/ros2_jazzy/install/rosidl_generator_py:/Users/<USER>/ros2_jazzy/install/ament_lint_common:/Users/<USER>/ros2_jazzy/install/ament_cmake_uncrustify:/Users/<USER>/ros2_jazzy/install/uncrustify_vendor:/Users/<USER>/ros2_jazzy/install/tracetools:/Users/<USER>/ros2_jazzy/install/pluginlib:/Users/<USER>/ros2_jazzy/install/tinyxml2_vendor:/Users/<USER>/ros2_jazzy/install/test_security:/Users/<USER>/ros2_jazzy/install/test_rmw_implementation:/Users/<USER>/ros2_jazzy/install/test_rclcpp:/Users/<USER>/ros2_jazzy/install/test_quality_of_service:/Users/<USER>/ros2_jazzy/install/test_launch_testing:/Users/<USER>/ros2_jazzy/install/test_interface_files:/Users/<USER>/ros2_jazzy/install/test_cli_remapping:/Users/<USER>/ros2_jazzy/install/test_cli:/Users/<USER>/ros2_jazzy/install/tango_icons_vendor:/Users/<USER>/ros2_jazzy/install/sqlite3_vendor:/Users/<USER>/ros2_jazzy/install/rcl_logging_spdlog:/Users/<USER>/ros2_jazzy/install/spdlog_vendor:/Users/<USER>/ros2_jazzy/install/shared_queues_vendor:/Users/<USER>/ros2_jazzy/install/rviz_rendering_tests:/Users/<USER>/ros2_jazzy/install/rviz_rendering:/Users/<USER>/ros2_jazzy/install/rviz_ogre_vendor:/Users/<USER>/ros2_jazzy/install/rviz_assimp_vendor:/Users/<USER>/ros2_jazzy/install/rti_connext_dds_cmake_module:/Users/<USER>/ros2_jazzy/install/lttngpy:/Users/<USER>/ros2_jazzy/install/rosidl_typesupport_tests:/Users/<USER>/ros2_jazzy/install/rosidl_typesupport_introspection_tests:/Users/<USER>/ros2_jazzy/install/rosidl_typesupport_cpp:/Users/<USER>/ros2_jazzy/install/rosidl_typesupport_introspection_cpp:/Users/<USER>/ros2_jazzy/install/rosidl_typesupport_c:/Users/<USER>/ros2_jazzy/install/rosidl_typesupport_introspection_c:/Users/<USER>/ros2_jazzy/install/rosidl_typesupport_fastrtps_c:/Users/<USER>/ros2_jazzy/install/rosidl_typesupport_fastrtps_cpp:/Users/<USER>/ros2_jazzy/install/rosidl_generator_cpp:/Users/<USER>/ros2_jazzy/install/rosidl_runtime_cpp:/Users/<USER>/ros2_jazzy/install/rosidl_dynamic_typesupport_fastrtps:/Users/<USER>/ros2_jazzy/install/rcl_yaml_param_parser:/Users/<USER>/ros2_jazzy/install/rmw:/Users/<USER>/ros2_jazzy/install/rosidl_dynamic_typesupport:/Users/<USER>/ros2_jazzy/install/rosidl_runtime_c:/Users/<USER>/ros2_jazzy/install/rosidl_generator_c:/Users/<USER>/ros2_jazzy/install/rosidl_typesupport_interface:/Users/<USER>/ros2_jazzy/install/rosidl_generator_dds_idl:/Users/<USER>/ros2_jazzy/install/rosidl_cmake:/Users/<USER>/ros2_jazzy/install/rosidl_generator_type_description:/Users/<USER>/ros2_jazzy/install/rosidl_parser:/Users/<USER>/ros2_jazzy/install/rosidl_generator_tests:/Users/<USER>/ros2_jazzy/install/rosidl_adapter:/Users/<USER>/ros2_jazzy/install/rosbag2_tests:/Users/<USER>/ros2_jazzy/install/rosbag2_test_msgdefs:/Users/<USER>/ros2_jazzy/install/ros_environment:/Users/<USER>/ros2_jazzy/install/rmw_implementation_cmake:/Users/<USER>/ros2_jazzy/install/resource_retriever:/Users/<USER>/ros2_jazzy/install/class_loader:/Users/<USER>/ros2_jazzy/install/rcpputils:/Users/<USER>/ros2_jazzy/install/rcl_logging_noop:/Users/<USER>/ros2_jazzy/install/rcl_logging_interface:/Users/<USER>/ros2_jazzy/install/rcutils:/Users/<USER>/ros2_jazzy/install/python_qt_binding:/Users/<USER>/ros2_jazzy/install/python_orocos_kdl_vendor:/Users/<USER>/ros2_jazzy/install/launch_testing_ament_cmake:/Users/<USER>/ros2_jazzy/install/python_cmake_module:/Users/<USER>/ros2_jazzy/install/pybind11_vendor:/Users/<USER>/ros2_jazzy/install/performance_test_fixture:/Users/<USER>/ros2_jazzy/install/osrf_testing_tools_cpp:/Users/<USER>/ros2_jazzy/install/orocos_kdl_vendor:/Users/<USER>/ros2_jazzy/install/mimick_vendor:/Users/<USER>/ros2_jazzy/install/libyaml_vendor:/Users/<USER>/ros2_jazzy/install/liblz4_vendor:/Users/<USER>/ros2_jazzy/install/libcurl_vendor:/Users/<USER>/ros2_jazzy/install/keyboard_handler:/Users/<USER>/ros2_jazzy/install/iceoryx_introspection:/Users/<USER>/ros2_jazzy/install/cyclonedds:/Users/<USER>/ros2_jazzy/install/iceoryx_posh:/Users/<USER>/ros2_jazzy/install/iceoryx_hoofs:/Users/<USER>/ros2_jazzy/install/iceoryx_binding_c:/Users/<USER>/ros2_jazzy/install/gz_math_vendor/opt/gz_math_vendor:/Users/<USER>/ros2_jazzy/install/gz_math_vendor:/Users/<USER>/ros2_jazzy/install/gz_utils_vendor/opt/gz_utils_vendor:/Users/<USER>/ros2_jazzy/install/gz_utils_vendor:/Users/<USER>/ros2_jazzy/install/gz_cmake_vendor/opt/gz_cmake_vendor:/Users/<USER>/ros2_jazzy/install/gz_cmake_vendor:/Users/<USER>/ros2_jazzy/install/ament_cmake_ros:/Users/<USER>/ros2_jazzy/install/ament_cmake_auto:/Users/<USER>/ros2_jazzy/install/ament_cmake_gmock:/Users/<USER>/ros2_jazzy/install/gmock_vendor:/Users/<USER>/ros2_jazzy/install/fastrtps:/Users/<USER>/ros2_jazzy/install/fastcdr:/Users/<USER>/ros2_jazzy/install/ament_cmake_gtest:/Users/<USER>/ros2_jazzy/install/gtest_vendor:/Users/<USER>/ros2_jazzy/install/ament_cmake_google_benchmark:/Users/<USER>/ros2_jazzy/install/google_benchmark_vendor:/Users/<USER>/ros2_jazzy/install/foonathan_memory_vendor:/Users/<USER>/ros2_jazzy/install/fastrtps_cmake_module:/Users/<USER>/ros2_jazzy/install/eigen3_cmake_module:/Users/<USER>/ros2_jazzy/install/console_bridge_vendor:/Users/<USER>/ros2_jazzy/install/ament_cmake_xmllint:/Users/<USER>/ros2_jazzy/install/ament_cmake_pyflakes:/Users/<USER>/ros2_jazzy/install/ament_cmake_pycodestyle:/Users/<USER>/ros2_jazzy/install/ament_cmake_pep257:/Users/<USER>/ros2_jazzy/install/ament_cmake_pclint:/Users/<USER>/ros2_jazzy/install/ament_lint_auto:/Users/<USER>/ros2_jazzy/install/ament_cmake:/Users/<USER>/ros2_jazzy/install/ament_cmake_version:/Users/<USER>/ros2_jazzy/install/ament_cmake_vendor_package:/Users/<USER>/ros2_jazzy/install/ament_cmake_pytest:/Users/<USER>/ros2_jazzy/install/ament_cmake_mypy:/Users/<USER>/ros2_jazzy/install/ament_cmake_lint_cmake:/Users/<USER>/ros2_jazzy/install/ament_cmake_flake8:/Users/<USER>/ros2_jazzy/install/ament_cmake_cpplint:/Users/<USER>/ros2_jazzy/install/ament_cmake_cppcheck:/Users/<USER>/ros2_jazzy/install/ament_cmake_copyright:/Users/<USER>/ros2_jazzy/install/ament_cmake_clang_tidy:/Users/<USER>/ros2_jazzy/install/ament_cmake_clang_format:/Users/<USER>/ros2_jazzy/install/ament_cmake_test:/Users/<USER>/ros2_jazzy/install/ament_cmake_target_dependencies:/Users/<USER>/ros2_jazzy/install/ament_cmake_python:/Users/<USER>/ros2_jazzy/install/ament_cmake_export_dependencies:/Users/<USER>/ros2_jazzy/install/ament_cmake_libraries:/Users/<USER>/ros2_jazzy/install/ament_cmake_include_directories:/Users/<USER>/ros2_jazzy/install/ament_cmake_gen_version_h:/Users/<USER>/ros2_jazzy/install/ament_cmake_export_targets:/Users/<USER>/ros2_jazzy/install/ament_cmake_export_link_flags:/Users/<USER>/ros2_jazzy/install/ament_cmake_export_interfaces:/Users/<USER>/ros2_jazzy/install/ament_cmake_export_libraries:/Users/<USER>/ros2_jazzy/install/ament_cmake_export_include_directories:/Users/<USER>/ros2_jazzy/install/ament_cmake_export_definitions:/Users/<USER>/ros2_jazzy/install/ament_cmake_core:/Users/<USER>/ros2_jazzy/install/ament_index_cpp:/Users/<USER>/Work/drill2/onboard/install/maneuver_builder_cpp:/Users/<USER>/Work/drill2/onboard/install/drill_msgs:/Users/<USER>/Work/drill2/onboard/install/can_msgs:/opt/homebrew/opt/qt@5/lib/cmake:/opt/homebrew/opt:/Users/<USER>/Work/drill2/onboard/install/depth_tracker:/Users/<USER>/ros2_jazzy/install/rosbag2_examples_py:/Users/<USER>/ros2_jazzy/install/launch_testing_examples:/Users/<USER>/ros2_jazzy/install/ros2bag:/Users/<USER>/ros2_jazzy/install/tracetools_test:/Users/<USER>/ros2_jazzy/install/tracetools_launch:/Users/<USER>/ros2_jazzy/install/topic_monitor:/Users/<USER>/ros2_jazzy/install/tf2_tools:/Users/<USER>/ros2_jazzy/install/examples_tf2_py:/Users/<USER>/ros2_jazzy/install/tf2_ros_py:/Users/<USER>/ros2_jazzy/install/examples_rclpy_pointcloud_publisher:/Users/<USER>/ros2_jazzy/install/sensor_msgs_py:/Users/<USER>/ros2_jazzy/install/quality_of_service_demo_py:/Users/<USER>/ros2_jazzy/install/camera_info_manager_py:/Users/<USER>/ros2_jazzy/install/ros2doctor:/Users/<USER>/ros2_jazzy/install/lifecycle_py:/Users/<USER>/ros2_jazzy/install/examples_rclpy_minimal_subscriber:/Users/<USER>/ros2_jazzy/install/examples_rclpy_minimal_service:/Users/<USER>/ros2_jazzy/install/examples_rclpy_minimal_publisher:/Users/<USER>/ros2_jazzy/install/examples_rclpy_minimal_client:/Users/<USER>/ros2_jazzy/install/examples_rclpy_executors:/Users/<USER>/ros2_jazzy/install/demo_nodes_py:/Users/<USER>/ros2_jazzy/install/ros2component:/Users/<USER>/ros2_jazzy/install/sros2:/Users/<USER>/ros2_jazzy/install/ros2trace:/Users/<USER>/ros2_jazzy/install/ros2topic:/Users/<USER>/ros2_jazzy/install/ros2test:/Users/<USER>/ros2_jazzy/install/ros2param:/Users/<USER>/ros2_jazzy/install/ros2lifecycle:/Users/<USER>/ros2_jazzy/install/ros2service:/Users/<USER>/ros2_jazzy/install/ros2run:/Users/<USER>/ros2_jazzy/install/ros2launch:/Users/<USER>/ros2_jazzy/install/ros2pkg:/Users/<USER>/ros2_jazzy/install/ros2node:/Users/<USER>/ros2_jazzy/install/ros2multicast:/Users/<USER>/ros2_jazzy/install/ros2interface:/Users/<USER>/ros2_jazzy/install/ros2action:/Users/<USER>/ros2_jazzy/install/ros2cli:/Users/<USER>/ros2_jazzy/install/launch_testing_ros:/Users/<USER>/ros2_jazzy/install/launch_ros:/Users/<USER>/ros2_jazzy/install/examples_rclpy_minimal_action_server:/Users/<USER>/ros2_jazzy/install/examples_rclpy_minimal_action_client:/Users/<USER>/ros2_jazzy/install/examples_rclpy_guard_conditions:/Users/<USER>/ros2_jazzy/install/action_tutorials_py:/Users/<USER>/ros2_jazzy/install/ament_uncrustify:/Users/<USER>/ros2_jazzy/install/tracetools_trace:/Users/<USER>/ros2_jazzy/install/tracetools_read:/Users/<USER>/ros2_jazzy/install/test_tracetools_launch:/Users/<USER>/ros2_jazzy/install/test_ros2trace:/Users/<USER>/ros2_jazzy/install/test_launch_ros:/Users/<USER>/ros2_jazzy/install/rpyutils:/Users/<USER>/ros2_jazzy/install/rosidl_runtime_py:/Users/<USER>/ros2_jazzy/install/rosidl_pycommon:/Users/<USER>/ros2_jazzy/install/rosidl_cli:/Users/<USER>/ros2_jazzy/install/launch_pytest:/Users/<USER>/ros2_jazzy/install/launch_testing:/Users/<USER>/ros2_jazzy/install/launch_yaml:/Users/<USER>/ros2_jazzy/install/launch_xml:/Users/<USER>/ros2_jazzy/install/launch:/Users/<USER>/ros2_jazzy/install/osrf_pycommon:/Users/<USER>/ros2_jazzy/install/domain_coordinator:/Users/<USER>/ros2_jazzy/install/ament_xmllint:/Users/<USER>/ros2_jazzy/install/ament_pyflakes:/Users/<USER>/ros2_jazzy/install/ament_pycodestyle:/Users/<USER>/ros2_jazzy/install/ament_pep257:/Users/<USER>/ros2_jazzy/install/ament_pclint:/Users/<USER>/ros2_jazzy/install/ament_package:/Users/<USER>/ros2_jazzy/install/ament_mypy:/Users/<USER>/ros2_jazzy/install/ament_lint_cmake:/Users/<USER>/ros2_jazzy/install/ament_flake8:/Users/<USER>/ros2_jazzy/install/ament_copyright:/Users/<USER>/ros2_jazzy/install/ament_lint:/Users/<USER>/ros2_jazzy/install/ament_index_python:/Users/<USER>/ros2_jazzy/install/ament_cpplint:/Users/<USER>/ros2_jazzy/install/ament_cppcheck:/Users/<USER>/ros2_jazzy/install/ament_clang_tidy:/Users/<USER>/ros2_jazzy/install/ament_clang_format:/Users/<USER>/Work/drill2/onboard/install/tracks_regulator:/Users/<USER>/Work/drill2/onboard/install/state_tracker:/Users/<USER>/Work/drill2/onboard/install/rtk_connector:/Users/<USER>/Work/drill2/onboard/install/remote_connector:/Users/<USER>/Work/drill2/onboard/install/pydubins:/Users/<USER>/Work/drill2/onboard/install/path_follower:/Users/<USER>/Work/drill2/onboard/install/params_server:/Users/<USER>/Work/drill2/onboard/install/modbus_node:/Users/<USER>/Work/drill2/onboard/install/main_state_machine:/Users/<USER>/Work/drill2/onboard/install/leveler:/Users/<USER>/Work/drill2/onboard/install/launchpack:/Users/<USER>/Work/drill2/onboard/install/can_encoder:/Users/<USER>/Work/drill2/onboard/install/can_decoder:/Users/<USER>/Work/drill2/onboard/install/base_node'), ('LC_TERMINAL', 'iTerm2'), ('DISPLAY', '/private/tmp/com.apple.launchd.UjTF9XIlm8/org.xquartz:0'), ('SECURITYSESSIONID', '186ab'), ('VIRTUAL_ENV_PROMPT', '(.ros2_venv)'), ('COLORTERM', 'truecolor'), ('_', '/usr/bin/env')]), 'shell': False}
[7.316826] (drill_msgs) StdoutLine: {'line': b'-- Install configuration: ""\n'}
[7.319637] (drill_msgs) StdoutLine: {'line': b'-- Execute custom install script\n'}
[7.319903] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/share/ament_index/resource_index/rosidl_interfaces/drill_msgs\n'}
[7.320073] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/share/drill_msgs/msg/Event.json\n'}
[7.320226] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/share/drill_msgs/msg/Report.json\n'}
[7.320375] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/share/drill_msgs/msg/Permission.json\n'}
[7.320529] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/share/drill_msgs/msg/ParamNotification.json\n'}
[7.320666] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/share/drill_msgs/msg/BoolStamped.json\n'}
[7.320805] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/share/drill_msgs/msg/Vector2d.json\n'}
[7.320968] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/share/drill_msgs/msg/IMU.json\n'}
[7.321085] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/share/drill_msgs/msg/EngineState.json\n'}
[7.321225] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/share/drill_msgs/msg/DrillStateRaw.json\n'}
[7.321361] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/share/drill_msgs/msg/DrillState.json\n'}
[7.321502] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/share/drill_msgs/msg/DepthInfo.json\n'}
[7.321638] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/share/drill_msgs/msg/FloatStamped.json\n'}
[7.321773] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/share/drill_msgs/msg/JacksStateRaw.json\n'}
[7.321914] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/share/drill_msgs/msg/JacksSwitchState.json\n'}
[7.322061] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/share/drill_msgs/msg/JacksSwitchStateRaw.json\n'}
[7.322206] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/share/drill_msgs/msg/PinsStateRaw.json\n'}
[7.322337] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/share/drill_msgs/msg/ArmStateRaw.json\n'}
[7.322478] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/share/drill_msgs/msg/ForkStateRaw.json\n'}
[7.322618] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/share/drill_msgs/msg/ForkState.json\n'}
[7.322760] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/share/drill_msgs/msg/CarouselStateRaw.json\n'}
[7.322934] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/share/drill_msgs/msg/WrenchStateRaw.json\n'}
[7.323046] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/share/drill_msgs/msg/DustFlapsState.json\n'}
[7.323188] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/share/drill_msgs/msg/StateMachineStatus.json\n'}
[7.323325] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/share/drill_msgs/msg/StateCommand.json\n'}
[7.323463] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/share/drill_msgs/msg/RmoHealth.json\n'}
[7.323598] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/share/drill_msgs/msg/CarouselCtrl.json\n'}
[7.323735] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/share/drill_msgs/msg/TowerCtrl.json\n'}
[7.323874] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/share/drill_msgs/msg/WrenchCtrl.json\n'}
[7.324019] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/share/drill_msgs/msg/TracksCtrl.json\n'}
[7.324151] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/share/drill_msgs/msg/FloatCtrl.json\n'}
[7.324303] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/share/drill_msgs/msg/DrillCtrl.json\n'}
[7.324441] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/share/drill_msgs/msg/DrillActuatorCtrl.json\n'}
[7.324583] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/share/drill_msgs/msg/AirCtrl.json\n'}
[7.324726] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/share/drill_msgs/msg/JacksCtrl.json\n'}
[7.324923] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/share/drill_msgs/msg/UpsStatus.json\n'}
[7.325162] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/share/drill_msgs/msg/LampCtrl.json\n'}
[7.325328] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/share/drill_msgs/msg/Level.json\n'}
[7.325499] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/share/drill_msgs/msg/GNSS.json\n'}
[7.326080] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/share/drill_msgs/msg/Position.json\n'}
[7.326321] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/share/drill_msgs/msg/SpeedState.json\n'}
[7.326376] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/share/drill_msgs/msg/TracksState.json\n'}
[7.326403] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/share/drill_msgs/msg/ModeCtrl.json\n'}
[7.326428] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/share/drill_msgs/msg/Point2d.json\n'}
[7.326547] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/share/drill_msgs/msg/Path.json\n'}
[7.326636] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/share/drill_msgs/msg/PathPoint.json\n'}
[7.326845] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/share/drill_msgs/msg/MainAction.json\n'}
[7.327046] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/share/drill_msgs/msg/DriveAction.json\n'}
[7.327215] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/share/drill_msgs/msg/TowerState.json\n'}
[7.327401] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/share/drill_msgs/msg/DriveStatus.json\n'}
[7.327601] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/share/drill_msgs/srv/GetCurrentDriveAction.json\n'}
[7.330518] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/include/drill_msgs/drill_msgs/msg/air_ctrl.h\n'}
[7.330721] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/include/drill_msgs/drill_msgs/msg/arm_state_raw.h\n'}
[7.330826] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/include/drill_msgs/drill_msgs/msg/bool_stamped.h\n'}
[7.330916] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/include/drill_msgs/drill_msgs/msg/carousel_ctrl.h\n'}
[7.331044] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/include/drill_msgs/drill_msgs/msg/carousel_state_raw.h\n'}
[7.331178] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/include/drill_msgs/drill_msgs/msg/depth_info.h\n'}
[7.331311] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/include/drill_msgs/drill_msgs/msg/detail/air_ctrl__functions.h\n'}
[7.331450] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/include/drill_msgs/drill_msgs/msg/detail/air_ctrl__struct.h\n'}
[7.331583] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/include/drill_msgs/drill_msgs/msg/detail/air_ctrl__type_support.h\n'}
[7.331695] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/include/drill_msgs/drill_msgs/msg/detail/arm_state_raw__functions.h\n'}
[7.331810] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/include/drill_msgs/drill_msgs/msg/detail/arm_state_raw__struct.h\n'}
[7.331945] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/include/drill_msgs/drill_msgs/msg/detail/arm_state_raw__type_support.h\n'}
[7.332057] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/include/drill_msgs/drill_msgs/msg/detail/bool_stamped__functions.h\n'}
[7.332278] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/include/drill_msgs/drill_msgs/msg/detail/bool_stamped__struct.h\n'}
[7.332353] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/include/drill_msgs/drill_msgs/msg/detail/bool_stamped__type_support.h\n'}
[7.332473] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/include/drill_msgs/drill_msgs/msg/detail/carousel_ctrl__functions.h\n'}
[7.332594] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/include/drill_msgs/drill_msgs/msg/detail/carousel_ctrl__struct.h\n'}
[7.332723] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/include/drill_msgs/drill_msgs/msg/detail/carousel_ctrl__type_support.h\n'}
[7.332846] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/include/drill_msgs/drill_msgs/msg/detail/carousel_state_raw__functions.h\n'}
[7.333011] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/include/drill_msgs/drill_msgs/msg/detail/carousel_state_raw__struct.h\n'}
[7.333163] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/include/drill_msgs/drill_msgs/msg/detail/carousel_state_raw__type_support.h\n'}
[7.333278] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/include/drill_msgs/drill_msgs/msg/detail/depth_info__functions.h\n'}
[7.333402] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/include/drill_msgs/drill_msgs/msg/detail/depth_info__struct.h\n'}
[7.333529] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/include/drill_msgs/drill_msgs/msg/detail/depth_info__type_support.h\n'}
[7.333666] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/include/drill_msgs/drill_msgs/msg/detail/drill_actuator_ctrl__functions.h\n'}
[7.333794] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/include/drill_msgs/drill_msgs/msg/detail/drill_actuator_ctrl__struct.h\n'}
[7.333972] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/include/drill_msgs/drill_msgs/msg/detail/drill_actuator_ctrl__type_support.h\n'}
[7.334083] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/include/drill_msgs/drill_msgs/msg/detail/drill_ctrl__functions.h\n'}
[7.334267] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/include/drill_msgs/drill_msgs/msg/detail/drill_ctrl__struct.h\n'}
[7.334384] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/include/drill_msgs/drill_msgs/msg/detail/drill_ctrl__type_support.h\n'}
[7.334507] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/include/drill_msgs/drill_msgs/msg/detail/drill_state__functions.h\n'}
[7.334622] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/include/drill_msgs/drill_msgs/msg/detail/drill_state__struct.h\n'}
[7.334741] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/include/drill_msgs/drill_msgs/msg/detail/drill_state__type_support.h\n'}
[7.334861] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/include/drill_msgs/drill_msgs/msg/detail/drill_state_raw__functions.h\n'}
[7.334982] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/include/drill_msgs/drill_msgs/msg/detail/drill_state_raw__struct.h\n'}
[7.335099] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/include/drill_msgs/drill_msgs/msg/detail/drill_state_raw__type_support.h\n'}
[7.335217] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/include/drill_msgs/drill_msgs/msg/detail/drive_action__functions.h\n'}
[7.335340] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/include/drill_msgs/drill_msgs/msg/detail/drive_action__struct.h\n'}
[7.335460] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/include/drill_msgs/drill_msgs/msg/detail/drive_action__type_support.h\n'}
[7.335577] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/include/drill_msgs/drill_msgs/msg/detail/drive_status__functions.h\n'}
[7.335696] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/include/drill_msgs/drill_msgs/msg/detail/drive_status__struct.h\n'}
[7.335815] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/include/drill_msgs/drill_msgs/msg/detail/drive_status__type_support.h\n'}
[7.335933] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/include/drill_msgs/drill_msgs/msg/detail/dust_flaps_state__functions.h\n'}
[7.336052] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/include/drill_msgs/drill_msgs/msg/detail/dust_flaps_state__struct.h\n'}
[7.336175] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/include/drill_msgs/drill_msgs/msg/detail/dust_flaps_state__type_support.h\n'}
[7.336293] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/include/drill_msgs/drill_msgs/msg/detail/engine_state__functions.h\n'}
[7.336407] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/include/drill_msgs/drill_msgs/msg/detail/engine_state__struct.h\n'}
[7.336528] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/include/drill_msgs/drill_msgs/msg/detail/engine_state__type_support.h\n'}
[7.336646] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/include/drill_msgs/drill_msgs/msg/detail/event__functions.h\n'}
[7.336765] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/include/drill_msgs/drill_msgs/msg/detail/event__struct.h\n'}
[7.336884] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/include/drill_msgs/drill_msgs/msg/detail/event__type_support.h\n'}
[7.337000] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/include/drill_msgs/drill_msgs/msg/detail/float_ctrl__functions.h\n'}
[7.337122] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/include/drill_msgs/drill_msgs/msg/detail/float_ctrl__struct.h\n'}
[7.337238] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/include/drill_msgs/drill_msgs/msg/detail/float_ctrl__type_support.h\n'}
[7.337355] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/include/drill_msgs/drill_msgs/msg/detail/float_stamped__functions.h\n'}
[7.337474] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/include/drill_msgs/drill_msgs/msg/detail/float_stamped__struct.h\n'}
[7.337594] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/include/drill_msgs/drill_msgs/msg/detail/float_stamped__type_support.h\n'}
[7.337709] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/include/drill_msgs/drill_msgs/msg/detail/fork_state__functions.h\n'}
[7.337827] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/include/drill_msgs/drill_msgs/msg/detail/fork_state__struct.h\n'}
[7.337946] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/include/drill_msgs/drill_msgs/msg/detail/fork_state__type_support.h\n'}
[7.338067] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/include/drill_msgs/drill_msgs/msg/detail/fork_state_raw__functions.h\n'}
[7.338351] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/include/drill_msgs/drill_msgs/msg/detail/fork_state_raw__struct.h\n'}
[7.338485] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/include/drill_msgs/drill_msgs/msg/detail/fork_state_raw__type_support.h\n'}
[7.338624] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/include/drill_msgs/drill_msgs/msg/detail/gnss__functions.h\n'}
[7.338746] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/include/drill_msgs/drill_msgs/msg/detail/gnss__struct.h\n'}
[7.338862] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/include/drill_msgs/drill_msgs/msg/detail/gnss__type_support.h\n'}
[7.338992] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/include/drill_msgs/drill_msgs/msg/detail/imu__functions.h\n'}
[7.339119] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/include/drill_msgs/drill_msgs/msg/detail/imu__struct.h\n'}
[7.339248] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/include/drill_msgs/drill_msgs/msg/detail/imu__type_support.h\n'}
[7.339365] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/include/drill_msgs/drill_msgs/msg/detail/jacks_ctrl__functions.h\n'}
[7.339490] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/include/drill_msgs/drill_msgs/msg/detail/jacks_ctrl__struct.h\n'}
[7.339613] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/include/drill_msgs/drill_msgs/msg/detail/jacks_ctrl__type_support.h\n'}
[7.339738] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/include/drill_msgs/drill_msgs/msg/detail/jacks_state_raw__functions.h\n'}
[7.339859] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/include/drill_msgs/drill_msgs/msg/detail/jacks_state_raw__struct.h\n'}
[7.339981] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/include/drill_msgs/drill_msgs/msg/detail/jacks_state_raw__type_support.h\n'}
[7.340105] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/include/drill_msgs/drill_msgs/msg/detail/jacks_switch_state__functions.h\n'}
[7.340223] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/include/drill_msgs/drill_msgs/msg/detail/jacks_switch_state__struct.h\n'}
[7.340352] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/include/drill_msgs/drill_msgs/msg/detail/jacks_switch_state__type_support.h\n'}
[7.340478] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/include/drill_msgs/drill_msgs/msg/detail/jacks_switch_state_raw__functions.h\n'}
[7.340609] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/include/drill_msgs/drill_msgs/msg/detail/jacks_switch_state_raw__struct.h\n'}
[7.340735] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/include/drill_msgs/drill_msgs/msg/detail/jacks_switch_state_raw__type_support.h\n'}
[7.340859] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/include/drill_msgs/drill_msgs/msg/detail/lamp_ctrl__functions.h\n'}
[7.340978] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/include/drill_msgs/drill_msgs/msg/detail/lamp_ctrl__struct.h\n'}
[7.341112] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/include/drill_msgs/drill_msgs/msg/detail/lamp_ctrl__type_support.h\n'}
[7.341176] (-) TimerEvent: {}
[7.341316] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/include/drill_msgs/drill_msgs/msg/detail/level__functions.h\n'}
[7.341524] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/include/drill_msgs/drill_msgs/msg/detail/level__struct.h\n'}
[7.341575] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/include/drill_msgs/drill_msgs/msg/detail/level__type_support.h\n'}
[7.341643] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/include/drill_msgs/drill_msgs/msg/detail/main_action__functions.h\n'}
[7.341749] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/include/drill_msgs/drill_msgs/msg/detail/main_action__struct.h\n'}
[7.341867] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/include/drill_msgs/drill_msgs/msg/detail/main_action__type_support.h\n'}
[7.341991] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/include/drill_msgs/drill_msgs/msg/detail/mode_ctrl__functions.h\n'}
[7.342119] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/include/drill_msgs/drill_msgs/msg/detail/mode_ctrl__struct.h\n'}
[7.342245] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/include/drill_msgs/drill_msgs/msg/detail/mode_ctrl__type_support.h\n'}
[7.342373] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/include/drill_msgs/drill_msgs/msg/detail/param_notification__functions.h\n'}
[7.342500] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/include/drill_msgs/drill_msgs/msg/detail/param_notification__struct.h\n'}
[7.342688] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/include/drill_msgs/drill_msgs/msg/detail/param_notification__type_support.h\n'}
[7.342802] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/include/drill_msgs/drill_msgs/msg/detail/path__functions.h\n'}
[7.342918] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/include/drill_msgs/drill_msgs/msg/detail/path__struct.h\n'}
[7.343041] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/include/drill_msgs/drill_msgs/msg/detail/path__type_support.h\n'}
[7.343167] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/include/drill_msgs/drill_msgs/msg/detail/path_point__functions.h\n'}
[7.343284] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/include/drill_msgs/drill_msgs/msg/detail/path_point__struct.h\n'}
[7.343400] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/include/drill_msgs/drill_msgs/msg/detail/path_point__type_support.h\n'}
[7.343576] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/include/drill_msgs/drill_msgs/msg/detail/permission__functions.h\n'}
[7.343730] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/include/drill_msgs/drill_msgs/msg/detail/permission__struct.h\n'}
[7.343845] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/include/drill_msgs/drill_msgs/msg/detail/permission__type_support.h\n'}
[7.343954] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/include/drill_msgs/drill_msgs/msg/detail/pins_state_raw__functions.h\n'}
[7.344072] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/include/drill_msgs/drill_msgs/msg/detail/pins_state_raw__struct.h\n'}
[7.344193] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/include/drill_msgs/drill_msgs/msg/detail/pins_state_raw__type_support.h\n'}
[7.344304] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/include/drill_msgs/drill_msgs/msg/detail/point2d__functions.h\n'}
[7.344427] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/include/drill_msgs/drill_msgs/msg/detail/point2d__struct.h\n'}
[7.344537] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/include/drill_msgs/drill_msgs/msg/detail/point2d__type_support.h\n'}
[7.344661] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/include/drill_msgs/drill_msgs/msg/detail/position__functions.h\n'}
[7.344797] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/include/drill_msgs/drill_msgs/msg/detail/position__struct.h\n'}
[7.344923] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/include/drill_msgs/drill_msgs/msg/detail/position__type_support.h\n'}
[7.345033] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/include/drill_msgs/drill_msgs/msg/detail/report__functions.h\n'}
[7.345153] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/include/drill_msgs/drill_msgs/msg/detail/report__struct.h\n'}
[7.345266] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/include/drill_msgs/drill_msgs/msg/detail/report__type_support.h\n'}
[7.345380] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/include/drill_msgs/drill_msgs/msg/detail/rmo_health__functions.h\n'}
[7.345492] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/include/drill_msgs/drill_msgs/msg/detail/rmo_health__struct.h\n'}
[7.345605] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/include/drill_msgs/drill_msgs/msg/detail/rmo_health__type_support.h\n'}
[7.345721] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/include/drill_msgs/drill_msgs/msg/detail/speed_state__functions.h\n'}
[7.345838] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/include/drill_msgs/drill_msgs/msg/detail/speed_state__struct.h\n'}
[7.345954] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/include/drill_msgs/drill_msgs/msg/detail/speed_state__type_support.h\n'}
[7.346073] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/include/drill_msgs/drill_msgs/msg/detail/state_command__functions.h\n'}
[7.346184] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/include/drill_msgs/drill_msgs/msg/detail/state_command__struct.h\n'}
[7.346299] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/include/drill_msgs/drill_msgs/msg/detail/state_command__type_support.h\n'}
[7.346418] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/include/drill_msgs/drill_msgs/msg/detail/state_machine_status__functions.h\n'}
[7.346531] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/include/drill_msgs/drill_msgs/msg/detail/state_machine_status__struct.h\n'}
[7.346688] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/include/drill_msgs/drill_msgs/msg/detail/state_machine_status__type_support.h\n'}
[7.346806] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/include/drill_msgs/drill_msgs/msg/detail/tower_ctrl__functions.h\n'}
[7.346923] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/include/drill_msgs/drill_msgs/msg/detail/tower_ctrl__struct.h\n'}
[7.347039] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/include/drill_msgs/drill_msgs/msg/detail/tower_ctrl__type_support.h\n'}
[7.347161] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/include/drill_msgs/drill_msgs/msg/detail/tower_state__functions.h\n'}
[7.347277] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/include/drill_msgs/drill_msgs/msg/detail/tower_state__struct.h\n'}
[7.347395] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/include/drill_msgs/drill_msgs/msg/detail/tower_state__type_support.h\n'}
[7.347514] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/include/drill_msgs/drill_msgs/msg/detail/tracks_ctrl__functions.h\n'}
[7.347632] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/include/drill_msgs/drill_msgs/msg/detail/tracks_ctrl__struct.h\n'}
[7.347747] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/include/drill_msgs/drill_msgs/msg/detail/tracks_ctrl__type_support.h\n'}
[7.347864] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/include/drill_msgs/drill_msgs/msg/detail/tracks_state__functions.h\n'}
[7.347981] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/include/drill_msgs/drill_msgs/msg/detail/tracks_state__struct.h\n'}
[7.348098] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/include/drill_msgs/drill_msgs/msg/detail/tracks_state__type_support.h\n'}
[7.348213] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/include/drill_msgs/drill_msgs/msg/detail/ups_status__functions.h\n'}
[7.348334] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/include/drill_msgs/drill_msgs/msg/detail/ups_status__struct.h\n'}
[7.348450] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/include/drill_msgs/drill_msgs/msg/detail/ups_status__type_support.h\n'}
[7.348565] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/include/drill_msgs/drill_msgs/msg/detail/vector2d__functions.h\n'}
[7.348681] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/include/drill_msgs/drill_msgs/msg/detail/vector2d__struct.h\n'}
[7.348799] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/include/drill_msgs/drill_msgs/msg/detail/vector2d__type_support.h\n'}
[7.348917] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/include/drill_msgs/drill_msgs/msg/detail/wrench_ctrl__functions.h\n'}
[7.349034] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/include/drill_msgs/drill_msgs/msg/detail/wrench_ctrl__struct.h\n'}
[7.349152] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/include/drill_msgs/drill_msgs/msg/detail/wrench_ctrl__type_support.h\n'}
[7.349269] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/include/drill_msgs/drill_msgs/msg/detail/wrench_state_raw__functions.h\n'}
[7.349388] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/include/drill_msgs/drill_msgs/msg/detail/wrench_state_raw__struct.h\n'}
[7.349510] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/include/drill_msgs/drill_msgs/msg/detail/wrench_state_raw__type_support.h\n'}
[7.349623] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/include/drill_msgs/drill_msgs/msg/drill_actuator_ctrl.h\n'}
[7.349735] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/include/drill_msgs/drill_msgs/msg/drill_ctrl.h\n'}
[7.349869] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/include/drill_msgs/drill_msgs/msg/drill_state.h\n'}
[7.350106] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/include/drill_msgs/drill_msgs/msg/drill_state_raw.h\n'}
[7.350652] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/include/drill_msgs/drill_msgs/msg/drive_action.h\n'}
[7.350754] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/include/drill_msgs/drill_msgs/msg/drive_status.h\n'}
[7.350814] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/include/drill_msgs/drill_msgs/msg/dust_flaps_state.h\n'}
[7.350840] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/include/drill_msgs/drill_msgs/msg/engine_state.h\n'}
[7.350874] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/include/drill_msgs/drill_msgs/msg/event.h\n'}
[7.350913] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/include/drill_msgs/drill_msgs/msg/float_ctrl.h\n'}
[7.351055] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/include/drill_msgs/drill_msgs/msg/float_stamped.h\n'}
[7.351170] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/include/drill_msgs/drill_msgs/msg/fork_state.h\n'}
[7.351285] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/include/drill_msgs/drill_msgs/msg/fork_state_raw.h\n'}
[7.351405] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/include/drill_msgs/drill_msgs/msg/gnss.h\n'}
[7.351526] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/include/drill_msgs/drill_msgs/msg/imu.h\n'}
[7.351643] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/include/drill_msgs/drill_msgs/msg/jacks_ctrl.h\n'}
[7.351760] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/include/drill_msgs/drill_msgs/msg/jacks_state_raw.h\n'}
[7.351888] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/include/drill_msgs/drill_msgs/msg/jacks_switch_state.h\n'}
[7.352001] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/include/drill_msgs/drill_msgs/msg/jacks_switch_state_raw.h\n'}
[7.352119] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/include/drill_msgs/drill_msgs/msg/lamp_ctrl.h\n'}
[7.352232] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/include/drill_msgs/drill_msgs/msg/level.h\n'}
[7.352353] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/include/drill_msgs/drill_msgs/msg/main_action.h\n'}
[7.352468] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/include/drill_msgs/drill_msgs/msg/mode_ctrl.h\n'}
[7.352582] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/include/drill_msgs/drill_msgs/msg/param_notification.h\n'}
[7.352707] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/include/drill_msgs/drill_msgs/msg/path.h\n'}
[7.352828] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/include/drill_msgs/drill_msgs/msg/path_point.h\n'}
[7.352950] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/include/drill_msgs/drill_msgs/msg/permission.h\n'}
[7.353061] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/include/drill_msgs/drill_msgs/msg/pins_state_raw.h\n'}
[7.353177] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/include/drill_msgs/drill_msgs/msg/point2d.h\n'}
[7.353298] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/include/drill_msgs/drill_msgs/msg/position.h\n'}
[7.353412] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/include/drill_msgs/drill_msgs/msg/report.h\n'}
[7.353527] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/include/drill_msgs/drill_msgs/msg/rmo_health.h\n'}
[7.353650] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/include/drill_msgs/drill_msgs/msg/rosidl_generator_c__visibility_control.h\n'}
[7.353766] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/include/drill_msgs/drill_msgs/msg/speed_state.h\n'}
[7.353883] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/include/drill_msgs/drill_msgs/msg/state_command.h\n'}
[7.354002] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/include/drill_msgs/drill_msgs/msg/state_machine_status.h\n'}
[7.354111] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/include/drill_msgs/drill_msgs/msg/tower_ctrl.h\n'}
[7.354233] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/include/drill_msgs/drill_msgs/msg/tower_state.h\n'}
[7.354348] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/include/drill_msgs/drill_msgs/msg/tracks_ctrl.h\n'}
[7.354467] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/include/drill_msgs/drill_msgs/msg/tracks_state.h\n'}
[7.354581] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/include/drill_msgs/drill_msgs/msg/ups_status.h\n'}
[7.354695] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/include/drill_msgs/drill_msgs/msg/vector2d.h\n'}
[7.354810] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/include/drill_msgs/drill_msgs/msg/wrench_ctrl.h\n'}
[7.354924] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/include/drill_msgs/drill_msgs/msg/wrench_state_raw.h\n'}
[7.355062] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/include/drill_msgs/drill_msgs/srv/detail/get_current_drive_action__functions.h\n'}
[7.355183] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/include/drill_msgs/drill_msgs/srv/detail/get_current_drive_action__struct.h\n'}
[7.355321] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/include/drill_msgs/drill_msgs/srv/detail/get_current_drive_action__type_support.h\n'}
[7.355437] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/include/drill_msgs/drill_msgs/srv/get_current_drive_action.h\n'}
[7.355657] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/share/drill_msgs/environment/library_path.sh\n'}
[7.355833] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/share/drill_msgs/environment/library_path.dsv\n'}
[7.357716] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/include/drill_msgs/drill_msgs/msg/detail/air_ctrl__rosidl_typesupport_fastrtps_c.h\n'}
[7.357825] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/include/drill_msgs/drill_msgs/msg/detail/arm_state_raw__rosidl_typesupport_fastrtps_c.h\n'}
[7.357943] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/include/drill_msgs/drill_msgs/msg/detail/bool_stamped__rosidl_typesupport_fastrtps_c.h\n'}
[7.358068] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/include/drill_msgs/drill_msgs/msg/detail/carousel_ctrl__rosidl_typesupport_fastrtps_c.h\n'}
[7.358250] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/include/drill_msgs/drill_msgs/msg/detail/carousel_state_raw__rosidl_typesupport_fastrtps_c.h\n'}
[7.358408] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/include/drill_msgs/drill_msgs/msg/detail/depth_info__rosidl_typesupport_fastrtps_c.h\n'}
[7.358586] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/include/drill_msgs/drill_msgs/msg/detail/drill_actuator_ctrl__rosidl_typesupport_fastrtps_c.h\n'}
[7.358739] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/include/drill_msgs/drill_msgs/msg/detail/drill_ctrl__rosidl_typesupport_fastrtps_c.h\n'}
[7.358885] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/include/drill_msgs/drill_msgs/msg/detail/drill_state__rosidl_typesupport_fastrtps_c.h\n'}
[7.359027] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/include/drill_msgs/drill_msgs/msg/detail/drill_state_raw__rosidl_typesupport_fastrtps_c.h\n'}
[7.359166] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/include/drill_msgs/drill_msgs/msg/detail/drive_action__rosidl_typesupport_fastrtps_c.h\n'}
[7.359292] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/include/drill_msgs/drill_msgs/msg/detail/drive_status__rosidl_typesupport_fastrtps_c.h\n'}
[7.359463] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/include/drill_msgs/drill_msgs/msg/detail/dust_flaps_state__rosidl_typesupport_fastrtps_c.h\n'}
[7.359555] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/include/drill_msgs/drill_msgs/msg/detail/engine_state__rosidl_typesupport_fastrtps_c.h\n'}
[7.359683] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/include/drill_msgs/drill_msgs/msg/detail/event__rosidl_typesupport_fastrtps_c.h\n'}
[7.359820] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/include/drill_msgs/drill_msgs/msg/detail/float_ctrl__rosidl_typesupport_fastrtps_c.h\n'}
[7.359992] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/include/drill_msgs/drill_msgs/msg/detail/float_stamped__rosidl_typesupport_fastrtps_c.h\n'}
[7.360136] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/include/drill_msgs/drill_msgs/msg/detail/fork_state__rosidl_typesupport_fastrtps_c.h\n'}
[7.360285] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/include/drill_msgs/drill_msgs/msg/detail/fork_state_raw__rosidl_typesupport_fastrtps_c.h\n'}
[7.360415] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/include/drill_msgs/drill_msgs/msg/detail/gnss__rosidl_typesupport_fastrtps_c.h\n'}
[7.360544] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/include/drill_msgs/drill_msgs/msg/detail/imu__rosidl_typesupport_fastrtps_c.h\n'}
[7.360680] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/include/drill_msgs/drill_msgs/msg/detail/jacks_ctrl__rosidl_typesupport_fastrtps_c.h\n'}
[7.360821] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/include/drill_msgs/drill_msgs/msg/detail/jacks_state_raw__rosidl_typesupport_fastrtps_c.h\n'}
[7.360943] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/include/drill_msgs/drill_msgs/msg/detail/jacks_switch_state__rosidl_typesupport_fastrtps_c.h\n'}
[7.361078] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/include/drill_msgs/drill_msgs/msg/detail/jacks_switch_state_raw__rosidl_typesupport_fastrtps_c.h\n'}
[7.361235] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/include/drill_msgs/drill_msgs/msg/detail/lamp_ctrl__rosidl_typesupport_fastrtps_c.h\n'}
[7.361374] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/include/drill_msgs/drill_msgs/msg/detail/level__rosidl_typesupport_fastrtps_c.h\n'}
[7.361499] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/include/drill_msgs/drill_msgs/msg/detail/main_action__rosidl_typesupport_fastrtps_c.h\n'}
[7.361669] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/include/drill_msgs/drill_msgs/msg/detail/mode_ctrl__rosidl_typesupport_fastrtps_c.h\n'}
[7.361788] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/include/drill_msgs/drill_msgs/msg/detail/param_notification__rosidl_typesupport_fastrtps_c.h\n'}
[7.361923] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/include/drill_msgs/drill_msgs/msg/detail/path__rosidl_typesupport_fastrtps_c.h\n'}
[7.362053] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/include/drill_msgs/drill_msgs/msg/detail/path_point__rosidl_typesupport_fastrtps_c.h\n'}
[7.362178] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/include/drill_msgs/drill_msgs/msg/detail/permission__rosidl_typesupport_fastrtps_c.h\n'}
[7.362300] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/include/drill_msgs/drill_msgs/msg/detail/pins_state_raw__rosidl_typesupport_fastrtps_c.h\n'}
[7.362522] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/include/drill_msgs/drill_msgs/msg/detail/point2d__rosidl_typesupport_fastrtps_c.h\n'}
[7.362736] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/include/drill_msgs/drill_msgs/msg/detail/position__rosidl_typesupport_fastrtps_c.h\n'}
[7.362979] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/include/drill_msgs/drill_msgs/msg/detail/report__rosidl_typesupport_fastrtps_c.h\n'}
[7.363101] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/include/drill_msgs/drill_msgs/msg/detail/rmo_health__rosidl_typesupport_fastrtps_c.h\n'}
[7.363253] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/include/drill_msgs/drill_msgs/msg/detail/speed_state__rosidl_typesupport_fastrtps_c.h\n'}
[7.363408] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/include/drill_msgs/drill_msgs/msg/detail/state_command__rosidl_typesupport_fastrtps_c.h\n'}
[7.363521] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/include/drill_msgs/drill_msgs/msg/detail/state_machine_status__rosidl_typesupport_fastrtps_c.h\n'}
[7.363642] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/include/drill_msgs/drill_msgs/msg/detail/tower_ctrl__rosidl_typesupport_fastrtps_c.h\n'}
[7.363767] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/include/drill_msgs/drill_msgs/msg/detail/tower_state__rosidl_typesupport_fastrtps_c.h\n'}
[7.363888] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/include/drill_msgs/drill_msgs/msg/detail/tracks_ctrl__rosidl_typesupport_fastrtps_c.h\n'}
[7.364010] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/include/drill_msgs/drill_msgs/msg/detail/tracks_state__rosidl_typesupport_fastrtps_c.h\n'}
[7.364125] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/include/drill_msgs/drill_msgs/msg/detail/ups_status__rosidl_typesupport_fastrtps_c.h\n'}
[7.364248] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/include/drill_msgs/drill_msgs/msg/detail/vector2d__rosidl_typesupport_fastrtps_c.h\n'}
[7.364362] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/include/drill_msgs/drill_msgs/msg/detail/wrench_ctrl__rosidl_typesupport_fastrtps_c.h\n'}
[7.364490] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/include/drill_msgs/drill_msgs/msg/detail/wrench_state_raw__rosidl_typesupport_fastrtps_c.h\n'}
[7.364596] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/include/drill_msgs/drill_msgs/msg/rosidl_typesupport_fastrtps_c__visibility_control.h\n'}
[7.364734] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/include/drill_msgs/drill_msgs/srv/detail/get_current_drive_action__rosidl_typesupport_fastrtps_c.h\n'}
[7.367124] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/include/drill_msgs/drill_msgs/msg/air_ctrl.hpp\n'}
[7.367240] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/include/drill_msgs/drill_msgs/msg/arm_state_raw.hpp\n'}
[7.367324] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/include/drill_msgs/drill_msgs/msg/bool_stamped.hpp\n'}
[7.367482] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/include/drill_msgs/drill_msgs/msg/carousel_ctrl.hpp\n'}
[7.367611] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/include/drill_msgs/drill_msgs/msg/carousel_state_raw.hpp\n'}
[7.367733] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/include/drill_msgs/drill_msgs/msg/depth_info.hpp\n'}
[7.367850] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/include/drill_msgs/drill_msgs/msg/detail/air_ctrl__builder.hpp\n'}
[7.368011] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/include/drill_msgs/drill_msgs/msg/detail/air_ctrl__struct.hpp\n'}
[7.368134] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/include/drill_msgs/drill_msgs/msg/detail/air_ctrl__traits.hpp\n'}
[7.368259] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/include/drill_msgs/drill_msgs/msg/detail/air_ctrl__type_support.hpp\n'}
[7.368376] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/include/drill_msgs/drill_msgs/msg/detail/arm_state_raw__builder.hpp\n'}
[7.368495] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/include/drill_msgs/drill_msgs/msg/detail/arm_state_raw__struct.hpp\n'}
[7.368612] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/include/drill_msgs/drill_msgs/msg/detail/arm_state_raw__traits.hpp\n'}
[7.368735] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/include/drill_msgs/drill_msgs/msg/detail/arm_state_raw__type_support.hpp\n'}
[7.368858] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/include/drill_msgs/drill_msgs/msg/detail/bool_stamped__builder.hpp\n'}
[7.368977] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/include/drill_msgs/drill_msgs/msg/detail/bool_stamped__struct.hpp\n'}
[7.369091] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/include/drill_msgs/drill_msgs/msg/detail/bool_stamped__traits.hpp\n'}
[7.369220] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/include/drill_msgs/drill_msgs/msg/detail/bool_stamped__type_support.hpp\n'}
[7.369329] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/include/drill_msgs/drill_msgs/msg/detail/carousel_ctrl__builder.hpp\n'}
[7.369452] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/include/drill_msgs/drill_msgs/msg/detail/carousel_ctrl__struct.hpp\n'}
[7.369567] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/include/drill_msgs/drill_msgs/msg/detail/carousel_ctrl__traits.hpp\n'}
[7.369688] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/include/drill_msgs/drill_msgs/msg/detail/carousel_ctrl__type_support.hpp\n'}
[7.369802] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/include/drill_msgs/drill_msgs/msg/detail/carousel_state_raw__builder.hpp\n'}
[7.369926] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/include/drill_msgs/drill_msgs/msg/detail/carousel_state_raw__struct.hpp\n'}
[7.370044] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/include/drill_msgs/drill_msgs/msg/detail/carousel_state_raw__traits.hpp\n'}
[7.370164] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/include/drill_msgs/drill_msgs/msg/detail/carousel_state_raw__type_support.hpp\n'}
[7.370283] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/include/drill_msgs/drill_msgs/msg/detail/depth_info__builder.hpp\n'}
[7.370404] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/include/drill_msgs/drill_msgs/msg/detail/depth_info__struct.hpp\n'}
[7.370517] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/include/drill_msgs/drill_msgs/msg/detail/depth_info__traits.hpp\n'}
[7.370635] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/include/drill_msgs/drill_msgs/msg/detail/depth_info__type_support.hpp\n'}
[7.370753] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/include/drill_msgs/drill_msgs/msg/detail/drill_actuator_ctrl__builder.hpp\n'}
[7.370873] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/include/drill_msgs/drill_msgs/msg/detail/drill_actuator_ctrl__struct.hpp\n'}
[7.370989] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/include/drill_msgs/drill_msgs/msg/detail/drill_actuator_ctrl__traits.hpp\n'}
[7.371109] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/include/drill_msgs/drill_msgs/msg/detail/drill_actuator_ctrl__type_support.hpp\n'}
[7.371228] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/include/drill_msgs/drill_msgs/msg/detail/drill_ctrl__builder.hpp\n'}
[7.371348] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/include/drill_msgs/drill_msgs/msg/detail/drill_ctrl__struct.hpp\n'}
[7.371463] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/include/drill_msgs/drill_msgs/msg/detail/drill_ctrl__traits.hpp\n'}
[7.371580] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/include/drill_msgs/drill_msgs/msg/detail/drill_ctrl__type_support.hpp\n'}
[7.371697] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/include/drill_msgs/drill_msgs/msg/detail/drill_state__builder.hpp\n'}
[7.371816] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/include/drill_msgs/drill_msgs/msg/detail/drill_state__struct.hpp\n'}
[7.371964] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/include/drill_msgs/drill_msgs/msg/detail/drill_state__traits.hpp\n'}
[7.372090] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/include/drill_msgs/drill_msgs/msg/detail/drill_state__type_support.hpp\n'}
[7.372215] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/include/drill_msgs/drill_msgs/msg/detail/drill_state_raw__builder.hpp\n'}
[7.372412] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/include/drill_msgs/drill_msgs/msg/detail/drill_state_raw__struct.hpp\n'}
[7.372509] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/include/drill_msgs/drill_msgs/msg/detail/drill_state_raw__traits.hpp\n'}
[7.372608] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/include/drill_msgs/drill_msgs/msg/detail/drill_state_raw__type_support.hpp\n'}
[7.372728] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/include/drill_msgs/drill_msgs/msg/detail/drive_action__builder.hpp\n'}
[7.372853] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/include/drill_msgs/drill_msgs/msg/detail/drive_action__struct.hpp\n'}
[7.372985] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/include/drill_msgs/drill_msgs/msg/detail/drive_action__traits.hpp\n'}
[7.373102] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/include/drill_msgs/drill_msgs/msg/detail/drive_action__type_support.hpp\n'}
[7.373248] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/include/drill_msgs/drill_msgs/msg/detail/drive_status__builder.hpp\n'}
[7.373353] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/include/drill_msgs/drill_msgs/msg/detail/drive_status__struct.hpp\n'}
[7.373471] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/include/drill_msgs/drill_msgs/msg/detail/drive_status__traits.hpp\n'}
[7.373601] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/include/drill_msgs/drill_msgs/msg/detail/drive_status__type_support.hpp\n'}
[7.373718] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/include/drill_msgs/drill_msgs/msg/detail/dust_flaps_state__builder.hpp\n'}
[7.373834] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/include/drill_msgs/drill_msgs/msg/detail/dust_flaps_state__struct.hpp\n'}
[7.373969] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/include/drill_msgs/drill_msgs/msg/detail/dust_flaps_state__traits.hpp\n'}
[7.374112] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/include/drill_msgs/drill_msgs/msg/detail/dust_flaps_state__type_support.hpp\n'}
[7.374290] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/include/drill_msgs/drill_msgs/msg/detail/engine_state__builder.hpp\n'}
[7.374488] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/include/drill_msgs/drill_msgs/msg/detail/engine_state__struct.hpp\n'}
[7.375190] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/include/drill_msgs/drill_msgs/msg/detail/engine_state__traits.hpp\n'}
[7.375278] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/include/drill_msgs/drill_msgs/msg/detail/engine_state__type_support.hpp\n'}
[7.375389] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/include/drill_msgs/drill_msgs/msg/detail/event__builder.hpp\n'}
[7.375443] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/include/drill_msgs/drill_msgs/msg/detail/event__struct.hpp\n'}
[7.375496] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/include/drill_msgs/drill_msgs/msg/detail/event__traits.hpp\n'}
[7.375581] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/include/drill_msgs/drill_msgs/msg/detail/event__type_support.hpp\n'}
[7.375693] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/include/drill_msgs/drill_msgs/msg/detail/float_ctrl__builder.hpp\n'}
[7.375819] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/include/drill_msgs/drill_msgs/msg/detail/float_ctrl__struct.hpp\n'}
[7.375942] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/include/drill_msgs/drill_msgs/msg/detail/float_ctrl__traits.hpp\n'}
[7.376072] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/include/drill_msgs/drill_msgs/msg/detail/float_ctrl__type_support.hpp\n'}
[7.376185] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/include/drill_msgs/drill_msgs/msg/detail/float_stamped__builder.hpp\n'}
[7.376310] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/include/drill_msgs/drill_msgs/msg/detail/float_stamped__struct.hpp\n'}
[7.376439] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/include/drill_msgs/drill_msgs/msg/detail/float_stamped__traits.hpp\n'}
[7.376616] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/include/drill_msgs/drill_msgs/msg/detail/float_stamped__type_support.hpp\n'}
[7.376740] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/include/drill_msgs/drill_msgs/msg/detail/fork_state__builder.hpp\n'}
[7.376864] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/include/drill_msgs/drill_msgs/msg/detail/fork_state__struct.hpp\n'}
[7.376986] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/include/drill_msgs/drill_msgs/msg/detail/fork_state__traits.hpp\n'}
[7.377105] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/include/drill_msgs/drill_msgs/msg/detail/fork_state__type_support.hpp\n'}
[7.377229] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/include/drill_msgs/drill_msgs/msg/detail/fork_state_raw__builder.hpp\n'}
[7.377353] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/include/drill_msgs/drill_msgs/msg/detail/fork_state_raw__struct.hpp\n'}
[7.377470] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/include/drill_msgs/drill_msgs/msg/detail/fork_state_raw__traits.hpp\n'}
[7.377597] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/include/drill_msgs/drill_msgs/msg/detail/fork_state_raw__type_support.hpp\n'}
[7.377720] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/include/drill_msgs/drill_msgs/msg/detail/gnss__builder.hpp\n'}
[7.377842] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/include/drill_msgs/drill_msgs/msg/detail/gnss__struct.hpp\n'}
[7.377960] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/include/drill_msgs/drill_msgs/msg/detail/gnss__traits.hpp\n'}
[7.378083] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/include/drill_msgs/drill_msgs/msg/detail/gnss__type_support.hpp\n'}
[7.378204] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/include/drill_msgs/drill_msgs/msg/detail/imu__builder.hpp\n'}
[7.378322] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/include/drill_msgs/drill_msgs/msg/detail/imu__struct.hpp\n'}
[7.378446] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/include/drill_msgs/drill_msgs/msg/detail/imu__traits.hpp\n'}
[7.378565] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/include/drill_msgs/drill_msgs/msg/detail/imu__type_support.hpp\n'}
[7.378684] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/include/drill_msgs/drill_msgs/msg/detail/jacks_ctrl__builder.hpp\n'}
[7.378812] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/include/drill_msgs/drill_msgs/msg/detail/jacks_ctrl__struct.hpp\n'}
[7.378923] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/include/drill_msgs/drill_msgs/msg/detail/jacks_ctrl__traits.hpp\n'}
[7.379049] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/include/drill_msgs/drill_msgs/msg/detail/jacks_ctrl__type_support.hpp\n'}
[7.379175] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/include/drill_msgs/drill_msgs/msg/detail/jacks_state_raw__builder.hpp\n'}
[7.379295] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/include/drill_msgs/drill_msgs/msg/detail/jacks_state_raw__struct.hpp\n'}
[7.379415] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/include/drill_msgs/drill_msgs/msg/detail/jacks_state_raw__traits.hpp\n'}
[7.379541] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/include/drill_msgs/drill_msgs/msg/detail/jacks_state_raw__type_support.hpp\n'}
[7.379667] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/include/drill_msgs/drill_msgs/msg/detail/jacks_switch_state__builder.hpp\n'}
[7.379791] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/include/drill_msgs/drill_msgs/msg/detail/jacks_switch_state__struct.hpp\n'}
[7.379904] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/include/drill_msgs/drill_msgs/msg/detail/jacks_switch_state__traits.hpp\n'}
[7.380024] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/include/drill_msgs/drill_msgs/msg/detail/jacks_switch_state__type_support.hpp\n'}
[7.380155] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/include/drill_msgs/drill_msgs/msg/detail/jacks_switch_state_raw__builder.hpp\n'}
[7.380276] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/include/drill_msgs/drill_msgs/msg/detail/jacks_switch_state_raw__struct.hpp\n'}
[7.380393] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/include/drill_msgs/drill_msgs/msg/detail/jacks_switch_state_raw__traits.hpp\n'}
[7.380515] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/include/drill_msgs/drill_msgs/msg/detail/jacks_switch_state_raw__type_support.hpp\n'}
[7.380660] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/include/drill_msgs/drill_msgs/msg/detail/lamp_ctrl__builder.hpp\n'}
[7.380777] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/include/drill_msgs/drill_msgs/msg/detail/lamp_ctrl__struct.hpp\n'}
[7.380892] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/include/drill_msgs/drill_msgs/msg/detail/lamp_ctrl__traits.hpp\n'}
[7.380998] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/include/drill_msgs/drill_msgs/msg/detail/lamp_ctrl__type_support.hpp\n'}
[7.381118] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/include/drill_msgs/drill_msgs/msg/detail/level__builder.hpp\n'}
[7.381231] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/include/drill_msgs/drill_msgs/msg/detail/level__struct.hpp\n'}
[7.381344] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/include/drill_msgs/drill_msgs/msg/detail/level__traits.hpp\n'}
[7.381460] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/include/drill_msgs/drill_msgs/msg/detail/level__type_support.hpp\n'}
[7.381574] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/include/drill_msgs/drill_msgs/msg/detail/main_action__builder.hpp\n'}
[7.381687] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/include/drill_msgs/drill_msgs/msg/detail/main_action__struct.hpp\n'}
[7.381800] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/include/drill_msgs/drill_msgs/msg/detail/main_action__traits.hpp\n'}
[7.381922] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/include/drill_msgs/drill_msgs/msg/detail/main_action__type_support.hpp\n'}
[7.382031] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/include/drill_msgs/drill_msgs/msg/detail/mode_ctrl__builder.hpp\n'}
[7.382140] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/include/drill_msgs/drill_msgs/msg/detail/mode_ctrl__struct.hpp\n'}
[7.382255] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/include/drill_msgs/drill_msgs/msg/detail/mode_ctrl__traits.hpp\n'}
[7.382368] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/include/drill_msgs/drill_msgs/msg/detail/mode_ctrl__type_support.hpp\n'}
[7.382488] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/include/drill_msgs/drill_msgs/msg/detail/param_notification__builder.hpp\n'}
[7.382603] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/include/drill_msgs/drill_msgs/msg/detail/param_notification__struct.hpp\n'}
[7.382721] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/include/drill_msgs/drill_msgs/msg/detail/param_notification__traits.hpp\n'}
[7.382837] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/include/drill_msgs/drill_msgs/msg/detail/param_notification__type_support.hpp\n'}
[7.382949] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/include/drill_msgs/drill_msgs/msg/detail/path__builder.hpp\n'}
[7.383067] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/include/drill_msgs/drill_msgs/msg/detail/path__struct.hpp\n'}
[7.383178] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/include/drill_msgs/drill_msgs/msg/detail/path__traits.hpp\n'}
[7.383290] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/include/drill_msgs/drill_msgs/msg/detail/path__type_support.hpp\n'}
[7.383407] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/include/drill_msgs/drill_msgs/msg/detail/path_point__builder.hpp\n'}
[7.383520] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/include/drill_msgs/drill_msgs/msg/detail/path_point__struct.hpp\n'}
[7.383644] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/include/drill_msgs/drill_msgs/msg/detail/path_point__traits.hpp\n'}
[7.383753] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/include/drill_msgs/drill_msgs/msg/detail/path_point__type_support.hpp\n'}
[7.383863] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/include/drill_msgs/drill_msgs/msg/detail/permission__builder.hpp\n'}
[7.383988] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/include/drill_msgs/drill_msgs/msg/detail/permission__struct.hpp\n'}
[7.384101] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/include/drill_msgs/drill_msgs/msg/detail/permission__traits.hpp\n'}
[7.384214] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/include/drill_msgs/drill_msgs/msg/detail/permission__type_support.hpp\n'}
[7.384328] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/include/drill_msgs/drill_msgs/msg/detail/pins_state_raw__builder.hpp\n'}
[7.384445] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/include/drill_msgs/drill_msgs/msg/detail/pins_state_raw__struct.hpp\n'}
[7.384588] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/include/drill_msgs/drill_msgs/msg/detail/pins_state_raw__traits.hpp\n'}
[7.384762] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/include/drill_msgs/drill_msgs/msg/detail/pins_state_raw__type_support.hpp\n'}
[7.384857] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/include/drill_msgs/drill_msgs/msg/detail/point2d__builder.hpp\n'}
[7.384990] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/include/drill_msgs/drill_msgs/msg/detail/point2d__struct.hpp\n'}
[7.385106] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/include/drill_msgs/drill_msgs/msg/detail/point2d__traits.hpp\n'}
[7.385228] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/include/drill_msgs/drill_msgs/msg/detail/point2d__type_support.hpp\n'}
[7.385344] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/include/drill_msgs/drill_msgs/msg/detail/position__builder.hpp\n'}
[7.385456] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/include/drill_msgs/drill_msgs/msg/detail/position__struct.hpp\n'}
[7.385570] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/include/drill_msgs/drill_msgs/msg/detail/position__traits.hpp\n'}
[7.385687] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/include/drill_msgs/drill_msgs/msg/detail/position__type_support.hpp\n'}
[7.385831] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/include/drill_msgs/drill_msgs/msg/detail/report__builder.hpp\n'}
[7.385994] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/include/drill_msgs/drill_msgs/msg/detail/report__struct.hpp\n'}
[7.386074] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/include/drill_msgs/drill_msgs/msg/detail/report__traits.hpp\n'}
[7.386223] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/include/drill_msgs/drill_msgs/msg/detail/report__type_support.hpp\n'}
[7.386357] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/include/drill_msgs/drill_msgs/msg/detail/rmo_health__builder.hpp\n'}
[7.386557] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/include/drill_msgs/drill_msgs/msg/detail/rmo_health__struct.hpp\n'}
[7.386797] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/include/drill_msgs/drill_msgs/msg/detail/rmo_health__traits.hpp\n'}
[7.387035] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/include/drill_msgs/drill_msgs/msg/detail/rmo_health__type_support.hpp\n'}
[7.387156] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/include/drill_msgs/drill_msgs/msg/detail/speed_state__builder.hpp\n'}
[7.387273] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/include/drill_msgs/drill_msgs/msg/detail/speed_state__struct.hpp\n'}
[7.387393] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/include/drill_msgs/drill_msgs/msg/detail/speed_state__traits.hpp\n'}
[7.387545] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/include/drill_msgs/drill_msgs/msg/detail/speed_state__type_support.hpp\n'}
[7.387773] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/include/drill_msgs/drill_msgs/msg/detail/state_command__builder.hpp\n'}
[7.387860] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/include/drill_msgs/drill_msgs/msg/detail/state_command__struct.hpp\n'}
[7.387996] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/include/drill_msgs/drill_msgs/msg/detail/state_command__traits.hpp\n'}
[7.388196] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/include/drill_msgs/drill_msgs/msg/detail/state_command__type_support.hpp\n'}
[7.388304] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/include/drill_msgs/drill_msgs/msg/detail/state_machine_status__builder.hpp\n'}
[7.388417] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/include/drill_msgs/drill_msgs/msg/detail/state_machine_status__struct.hpp\n'}
[7.388544] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/include/drill_msgs/drill_msgs/msg/detail/state_machine_status__traits.hpp\n'}
[7.388666] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/include/drill_msgs/drill_msgs/msg/detail/state_machine_status__type_support.hpp\n'}
[7.388784] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/include/drill_msgs/drill_msgs/msg/detail/tower_ctrl__builder.hpp\n'}
[7.388902] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/include/drill_msgs/drill_msgs/msg/detail/tower_ctrl__struct.hpp\n'}
[7.389119] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/include/drill_msgs/drill_msgs/msg/detail/tower_ctrl__traits.hpp\n'}
[7.389305] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/include/drill_msgs/drill_msgs/msg/detail/tower_ctrl__type_support.hpp\n'}
[7.389481] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/include/drill_msgs/drill_msgs/msg/detail/tower_state__builder.hpp\n'}
[7.389587] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/include/drill_msgs/drill_msgs/msg/detail/tower_state__struct.hpp\n'}
[7.389712] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/include/drill_msgs/drill_msgs/msg/detail/tower_state__traits.hpp\n'}
[7.389833] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/include/drill_msgs/drill_msgs/msg/detail/tower_state__type_support.hpp\n'}
[7.389961] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/include/drill_msgs/drill_msgs/msg/detail/tracks_ctrl__builder.hpp\n'}
[7.390111] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/include/drill_msgs/drill_msgs/msg/detail/tracks_ctrl__struct.hpp\n'}
[7.390257] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/include/drill_msgs/drill_msgs/msg/detail/tracks_ctrl__traits.hpp\n'}
[7.390419] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/include/drill_msgs/drill_msgs/msg/detail/tracks_ctrl__type_support.hpp\n'}
[7.390566] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/include/drill_msgs/drill_msgs/msg/detail/tracks_state__builder.hpp\n'}
[7.390833] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/include/drill_msgs/drill_msgs/msg/detail/tracks_state__struct.hpp\n'}
[7.390931] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/include/drill_msgs/drill_msgs/msg/detail/tracks_state__traits.hpp\n'}
[7.391060] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/include/drill_msgs/drill_msgs/msg/detail/tracks_state__type_support.hpp\n'}
[7.391220] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/include/drill_msgs/drill_msgs/msg/detail/ups_status__builder.hpp\n'}
[7.391310] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/include/drill_msgs/drill_msgs/msg/detail/ups_status__struct.hpp\n'}
[7.391461] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/include/drill_msgs/drill_msgs/msg/detail/ups_status__traits.hpp\n'}
[7.391663] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/include/drill_msgs/drill_msgs/msg/detail/ups_status__type_support.hpp\n'}
[7.391818] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/include/drill_msgs/drill_msgs/msg/detail/vector2d__builder.hpp\n'}
[7.392063] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/include/drill_msgs/drill_msgs/msg/detail/vector2d__struct.hpp\n'}
[7.392183] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/include/drill_msgs/drill_msgs/msg/detail/vector2d__traits.hpp\n'}
[7.392293] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/include/drill_msgs/drill_msgs/msg/detail/vector2d__type_support.hpp\n'}
[7.392439] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/include/drill_msgs/drill_msgs/msg/detail/wrench_ctrl__builder.hpp\n'}
[7.392578] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/include/drill_msgs/drill_msgs/msg/detail/wrench_ctrl__struct.hpp\n'}
[7.392671] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/include/drill_msgs/drill_msgs/msg/detail/wrench_ctrl__traits.hpp\n'}
[7.392844] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/include/drill_msgs/drill_msgs/msg/detail/wrench_ctrl__type_support.hpp\n'}
[7.392993] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/include/drill_msgs/drill_msgs/msg/detail/wrench_state_raw__builder.hpp\n'}
[7.393130] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/include/drill_msgs/drill_msgs/msg/detail/wrench_state_raw__struct.hpp\n'}
[7.393360] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/include/drill_msgs/drill_msgs/msg/detail/wrench_state_raw__traits.hpp\n'}
[7.393466] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/include/drill_msgs/drill_msgs/msg/detail/wrench_state_raw__type_support.hpp\n'}
[7.393578] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/include/drill_msgs/drill_msgs/msg/drill_actuator_ctrl.hpp\n'}
[7.393723] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/include/drill_msgs/drill_msgs/msg/drill_ctrl.hpp\n'}
[7.393807] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/include/drill_msgs/drill_msgs/msg/drill_state.hpp\n'}
[7.393926] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/include/drill_msgs/drill_msgs/msg/drill_state_raw.hpp\n'}
[7.394056] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/include/drill_msgs/drill_msgs/msg/drive_action.hpp\n'}
[7.394237] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/include/drill_msgs/drill_msgs/msg/drive_status.hpp\n'}
[7.394373] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/include/drill_msgs/drill_msgs/msg/dust_flaps_state.hpp\n'}
[7.394553] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/include/drill_msgs/drill_msgs/msg/engine_state.hpp\n'}
[7.394714] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/include/drill_msgs/drill_msgs/msg/event.hpp\n'}
[7.394834] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/include/drill_msgs/drill_msgs/msg/float_ctrl.hpp\n'}
[7.394961] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/include/drill_msgs/drill_msgs/msg/float_stamped.hpp\n'}
[7.395078] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/include/drill_msgs/drill_msgs/msg/fork_state.hpp\n'}
[7.395198] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/include/drill_msgs/drill_msgs/msg/fork_state_raw.hpp\n'}
[7.395347] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/include/drill_msgs/drill_msgs/msg/gnss.hpp\n'}
[7.395499] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/include/drill_msgs/drill_msgs/msg/imu.hpp\n'}
[7.395661] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/include/drill_msgs/drill_msgs/msg/jacks_ctrl.hpp\n'}
[7.395787] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/include/drill_msgs/drill_msgs/msg/jacks_state_raw.hpp\n'}
[7.395998] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/include/drill_msgs/drill_msgs/msg/jacks_switch_state.hpp\n'}
[7.396086] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/include/drill_msgs/drill_msgs/msg/jacks_switch_state_raw.hpp\n'}
[7.396206] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/include/drill_msgs/drill_msgs/msg/lamp_ctrl.hpp\n'}
[7.396330] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/include/drill_msgs/drill_msgs/msg/level.hpp\n'}
[7.396462] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/include/drill_msgs/drill_msgs/msg/main_action.hpp\n'}
[7.396595] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/include/drill_msgs/drill_msgs/msg/mode_ctrl.hpp\n'}
[7.396775] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/include/drill_msgs/drill_msgs/msg/param_notification.hpp\n'}
[7.396867] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/include/drill_msgs/drill_msgs/msg/path.hpp\n'}
[7.396991] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/include/drill_msgs/drill_msgs/msg/path_point.hpp\n'}
[7.397172] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/include/drill_msgs/drill_msgs/msg/permission.hpp\n'}
[7.397303] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/include/drill_msgs/drill_msgs/msg/pins_state_raw.hpp\n'}
[7.397424] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/include/drill_msgs/drill_msgs/msg/point2d.hpp\n'}
[7.397572] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/include/drill_msgs/drill_msgs/msg/position.hpp\n'}
[7.397656] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/include/drill_msgs/drill_msgs/msg/report.hpp\n'}
[7.397771] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/include/drill_msgs/drill_msgs/msg/rmo_health.hpp\n'}
[7.397912] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/include/drill_msgs/drill_msgs/msg/rosidl_generator_cpp__visibility_control.hpp\n'}
[7.398086] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/include/drill_msgs/drill_msgs/msg/speed_state.hpp\n'}
[7.398205] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/include/drill_msgs/drill_msgs/msg/state_command.hpp\n'}
[7.398462] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/include/drill_msgs/drill_msgs/msg/state_machine_status.hpp\n'}
[7.398551] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/include/drill_msgs/drill_msgs/msg/tower_ctrl.hpp\n'}
[7.398640] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/include/drill_msgs/drill_msgs/msg/tower_state.hpp\n'}
[7.398810] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/include/drill_msgs/drill_msgs/msg/tracks_ctrl.hpp\n'}
[7.398899] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/include/drill_msgs/drill_msgs/msg/tracks_state.hpp\n'}
[7.399023] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/include/drill_msgs/drill_msgs/msg/ups_status.hpp\n'}
[7.399153] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/include/drill_msgs/drill_msgs/msg/vector2d.hpp\n'}
[7.399260] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/include/drill_msgs/drill_msgs/msg/wrench_ctrl.hpp\n'}
[7.399431] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/include/drill_msgs/drill_msgs/msg/wrench_state_raw.hpp\n'}
[7.399663] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/include/drill_msgs/drill_msgs/srv/detail/get_current_drive_action__builder.hpp\n'}
[7.399786] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/include/drill_msgs/drill_msgs/srv/detail/get_current_drive_action__struct.hpp\n'}
[7.399973] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/include/drill_msgs/drill_msgs/srv/detail/get_current_drive_action__traits.hpp\n'}
[7.400131] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/include/drill_msgs/drill_msgs/srv/detail/get_current_drive_action__type_support.hpp\n'}
[7.400235] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/include/drill_msgs/drill_msgs/srv/get_current_drive_action.hpp\n'}
[7.402445] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/include/drill_msgs/drill_msgs/msg/detail/air_ctrl__rosidl_typesupport_fastrtps_cpp.hpp\n'}
[7.402572] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/include/drill_msgs/drill_msgs/msg/detail/arm_state_raw__rosidl_typesupport_fastrtps_cpp.hpp\n'}
[7.402686] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/include/drill_msgs/drill_msgs/msg/detail/bool_stamped__rosidl_typesupport_fastrtps_cpp.hpp\n'}
[7.402840] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/include/drill_msgs/drill_msgs/msg/detail/carousel_ctrl__rosidl_typesupport_fastrtps_cpp.hpp\n'}
[7.402968] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/include/drill_msgs/drill_msgs/msg/detail/carousel_state_raw__rosidl_typesupport_fastrtps_cpp.hpp\n'}
[7.403110] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/include/drill_msgs/drill_msgs/msg/detail/depth_info__rosidl_typesupport_fastrtps_cpp.hpp\n'}
[7.403274] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/include/drill_msgs/drill_msgs/msg/detail/drill_actuator_ctrl__rosidl_typesupport_fastrtps_cpp.hpp\n'}
[7.403648] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/include/drill_msgs/drill_msgs/msg/detail/drill_ctrl__rosidl_typesupport_fastrtps_cpp.hpp\n'}
[7.403733] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/include/drill_msgs/drill_msgs/msg/detail/drill_state__rosidl_typesupport_fastrtps_cpp.hpp\n'}
[7.403851] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/include/drill_msgs/drill_msgs/msg/detail/drill_state_raw__rosidl_typesupport_fastrtps_cpp.hpp\n'}
[7.403953] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/include/drill_msgs/drill_msgs/msg/detail/drive_action__rosidl_typesupport_fastrtps_cpp.hpp\n'}
[7.404112] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/include/drill_msgs/drill_msgs/msg/detail/drive_status__rosidl_typesupport_fastrtps_cpp.hpp\n'}
[7.404209] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/include/drill_msgs/drill_msgs/msg/detail/dust_flaps_state__rosidl_typesupport_fastrtps_cpp.hpp\n'}
[7.404315] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/include/drill_msgs/drill_msgs/msg/detail/engine_state__rosidl_typesupport_fastrtps_cpp.hpp\n'}
[7.404463] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/include/drill_msgs/drill_msgs/msg/detail/event__rosidl_typesupport_fastrtps_cpp.hpp\n'}
[7.404603] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/include/drill_msgs/drill_msgs/msg/detail/float_ctrl__rosidl_typesupport_fastrtps_cpp.hpp\n'}
[7.404752] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/include/drill_msgs/drill_msgs/msg/detail/float_stamped__rosidl_typesupport_fastrtps_cpp.hpp\n'}
[7.404873] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/include/drill_msgs/drill_msgs/msg/detail/fork_state__rosidl_typesupport_fastrtps_cpp.hpp\n'}
[7.405058] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/include/drill_msgs/drill_msgs/msg/detail/fork_state_raw__rosidl_typesupport_fastrtps_cpp.hpp\n'}
[7.405182] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/include/drill_msgs/drill_msgs/msg/detail/gnss__rosidl_typesupport_fastrtps_cpp.hpp\n'}
[7.405318] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/include/drill_msgs/drill_msgs/msg/detail/imu__rosidl_typesupport_fastrtps_cpp.hpp\n'}
[7.405442] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/include/drill_msgs/drill_msgs/msg/detail/jacks_ctrl__rosidl_typesupport_fastrtps_cpp.hpp\n'}
[7.405558] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/include/drill_msgs/drill_msgs/msg/detail/jacks_state_raw__rosidl_typesupport_fastrtps_cpp.hpp\n'}
[7.405923] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/include/drill_msgs/drill_msgs/msg/detail/jacks_switch_state__rosidl_typesupport_fastrtps_cpp.hpp\n'}
[7.406047] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/include/drill_msgs/drill_msgs/msg/detail/jacks_switch_state_raw__rosidl_typesupport_fastrtps_cpp.hpp\n'}
[7.406253] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/include/drill_msgs/drill_msgs/msg/detail/lamp_ctrl__rosidl_typesupport_fastrtps_cpp.hpp\n'}
[7.406386] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/include/drill_msgs/drill_msgs/msg/detail/level__rosidl_typesupport_fastrtps_cpp.hpp\n'}
[7.406513] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/include/drill_msgs/drill_msgs/msg/detail/main_action__rosidl_typesupport_fastrtps_cpp.hpp\n'}
[7.406639] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/include/drill_msgs/drill_msgs/msg/detail/mode_ctrl__rosidl_typesupport_fastrtps_cpp.hpp\n'}
[7.406884] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/include/drill_msgs/drill_msgs/msg/detail/param_notification__rosidl_typesupport_fastrtps_cpp.hpp\n'}
[7.407220] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/include/drill_msgs/drill_msgs/msg/detail/path__rosidl_typesupport_fastrtps_cpp.hpp\n'}
[7.407458] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/include/drill_msgs/drill_msgs/msg/detail/path_point__rosidl_typesupport_fastrtps_cpp.hpp\n'}
[7.407550] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/include/drill_msgs/drill_msgs/msg/detail/permission__rosidl_typesupport_fastrtps_cpp.hpp\n'}
[7.407602] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/include/drill_msgs/drill_msgs/msg/detail/pins_state_raw__rosidl_typesupport_fastrtps_cpp.hpp\n'}
[7.407691] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/include/drill_msgs/drill_msgs/msg/detail/point2d__rosidl_typesupport_fastrtps_cpp.hpp\n'}
[7.407776] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/include/drill_msgs/drill_msgs/msg/detail/position__rosidl_typesupport_fastrtps_cpp.hpp\n'}
[7.407897] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/include/drill_msgs/drill_msgs/msg/detail/report__rosidl_typesupport_fastrtps_cpp.hpp\n'}
[7.408016] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/include/drill_msgs/drill_msgs/msg/detail/rmo_health__rosidl_typesupport_fastrtps_cpp.hpp\n'}
[7.408149] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/include/drill_msgs/drill_msgs/msg/detail/speed_state__rosidl_typesupport_fastrtps_cpp.hpp\n'}
[7.408336] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/include/drill_msgs/drill_msgs/msg/detail/state_command__rosidl_typesupport_fastrtps_cpp.hpp\n'}
[7.408466] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/include/drill_msgs/drill_msgs/msg/detail/state_machine_status__rosidl_typesupport_fastrtps_cpp.hpp\n'}
[7.408692] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/include/drill_msgs/drill_msgs/msg/detail/tower_ctrl__rosidl_typesupport_fastrtps_cpp.hpp\n'}
[7.408882] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/include/drill_msgs/drill_msgs/msg/detail/tower_state__rosidl_typesupport_fastrtps_cpp.hpp\n'}
[7.409001] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/include/drill_msgs/drill_msgs/msg/detail/tracks_ctrl__rosidl_typesupport_fastrtps_cpp.hpp\n'}
[7.409141] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/include/drill_msgs/drill_msgs/msg/detail/tracks_state__rosidl_typesupport_fastrtps_cpp.hpp\n'}
[7.409253] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/include/drill_msgs/drill_msgs/msg/detail/ups_status__rosidl_typesupport_fastrtps_cpp.hpp\n'}
[7.409381] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/include/drill_msgs/drill_msgs/msg/detail/vector2d__rosidl_typesupport_fastrtps_cpp.hpp\n'}
[7.409502] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/include/drill_msgs/drill_msgs/msg/detail/wrench_ctrl__rosidl_typesupport_fastrtps_cpp.hpp\n'}
[7.409638] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/include/drill_msgs/drill_msgs/msg/detail/wrench_state_raw__rosidl_typesupport_fastrtps_cpp.hpp\n'}
[7.409797] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/include/drill_msgs/drill_msgs/msg/rosidl_typesupport_fastrtps_cpp__visibility_control.h\n'}
[7.409955] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/include/drill_msgs/drill_msgs/srv/detail/get_current_drive_action__rosidl_typesupport_fastrtps_cpp.hpp\n'}
[7.411077] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/include/drill_msgs/drill_msgs/msg/detail/air_ctrl__rosidl_typesupport_introspection_c.h\n'}
[7.411267] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/include/drill_msgs/drill_msgs/msg/detail/arm_state_raw__rosidl_typesupport_introspection_c.h\n'}
[7.411395] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/include/drill_msgs/drill_msgs/msg/detail/bool_stamped__rosidl_typesupport_introspection_c.h\n'}
[7.411518] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/include/drill_msgs/drill_msgs/msg/detail/carousel_ctrl__rosidl_typesupport_introspection_c.h\n'}
[7.411640] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/include/drill_msgs/drill_msgs/msg/detail/carousel_state_raw__rosidl_typesupport_introspection_c.h\n'}
[7.411763] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/include/drill_msgs/drill_msgs/msg/detail/depth_info__rosidl_typesupport_introspection_c.h\n'}
[7.411958] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/include/drill_msgs/drill_msgs/msg/detail/drill_actuator_ctrl__rosidl_typesupport_introspection_c.h\n'}
[7.412038] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/include/drill_msgs/drill_msgs/msg/detail/drill_ctrl__rosidl_typesupport_introspection_c.h\n'}
[7.412258] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/include/drill_msgs/drill_msgs/msg/detail/drill_state__rosidl_typesupport_introspection_c.h\n'}
[7.412336] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/include/drill_msgs/drill_msgs/msg/detail/drill_state_raw__rosidl_typesupport_introspection_c.h\n'}
[7.412462] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/include/drill_msgs/drill_msgs/msg/detail/drive_action__rosidl_typesupport_introspection_c.h\n'}
[7.412909] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/include/drill_msgs/drill_msgs/msg/detail/drive_status__rosidl_typesupport_introspection_c.h\n'}
[7.413391] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/include/drill_msgs/drill_msgs/msg/detail/dust_flaps_state__rosidl_typesupport_introspection_c.h\n'}
[7.413586] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/include/drill_msgs/drill_msgs/msg/detail/engine_state__rosidl_typesupport_introspection_c.h\n'}
[7.413686] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/include/drill_msgs/drill_msgs/msg/detail/event__rosidl_typesupport_introspection_c.h\n'}
[7.413849] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/include/drill_msgs/drill_msgs/msg/detail/float_ctrl__rosidl_typesupport_introspection_c.h\n'}
[7.413943] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/include/drill_msgs/drill_msgs/msg/detail/float_stamped__rosidl_typesupport_introspection_c.h\n'}
[7.414097] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/include/drill_msgs/drill_msgs/msg/detail/fork_state__rosidl_typesupport_introspection_c.h\n'}
[7.414194] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/include/drill_msgs/drill_msgs/msg/detail/fork_state_raw__rosidl_typesupport_introspection_c.h\n'}
[7.414297] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/include/drill_msgs/drill_msgs/msg/detail/gnss__rosidl_typesupport_introspection_c.h\n'}
[7.414393] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/include/drill_msgs/drill_msgs/msg/detail/imu__rosidl_typesupport_introspection_c.h\n'}
[7.414539] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/include/drill_msgs/drill_msgs/msg/detail/jacks_ctrl__rosidl_typesupport_introspection_c.h\n'}
[7.414740] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/include/drill_msgs/drill_msgs/msg/detail/jacks_state_raw__rosidl_typesupport_introspection_c.h\n'}
[7.414815] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/include/drill_msgs/drill_msgs/msg/detail/jacks_switch_state__rosidl_typesupport_introspection_c.h\n'}
[7.414863] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/include/drill_msgs/drill_msgs/msg/detail/jacks_switch_state_raw__rosidl_typesupport_introspection_c.h\n'}
[7.414947] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/include/drill_msgs/drill_msgs/msg/detail/lamp_ctrl__rosidl_typesupport_introspection_c.h\n'}
[7.415053] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/include/drill_msgs/drill_msgs/msg/detail/level__rosidl_typesupport_introspection_c.h\n'}
[7.415184] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/include/drill_msgs/drill_msgs/msg/detail/main_action__rosidl_typesupport_introspection_c.h\n'}
[7.415361] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/include/drill_msgs/drill_msgs/msg/detail/mode_ctrl__rosidl_typesupport_introspection_c.h\n'}
[7.415524] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/include/drill_msgs/drill_msgs/msg/detail/param_notification__rosidl_typesupport_introspection_c.h\n'}
[7.415734] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/include/drill_msgs/drill_msgs/msg/detail/path__rosidl_typesupport_introspection_c.h\n'}
[7.415878] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/include/drill_msgs/drill_msgs/msg/detail/path_point__rosidl_typesupport_introspection_c.h\n'}
[7.415999] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/include/drill_msgs/drill_msgs/msg/detail/permission__rosidl_typesupport_introspection_c.h\n'}
[7.416169] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/include/drill_msgs/drill_msgs/msg/detail/pins_state_raw__rosidl_typesupport_introspection_c.h\n'}
[7.416345] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/include/drill_msgs/drill_msgs/msg/detail/point2d__rosidl_typesupport_introspection_c.h\n'}
[7.416474] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/include/drill_msgs/drill_msgs/msg/detail/position__rosidl_typesupport_introspection_c.h\n'}
[7.416860] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/include/drill_msgs/drill_msgs/msg/detail/report__rosidl_typesupport_introspection_c.h\n'}
[7.417210] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/include/drill_msgs/drill_msgs/msg/detail/rmo_health__rosidl_typesupport_introspection_c.h\n'}
[7.417480] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/include/drill_msgs/drill_msgs/msg/detail/speed_state__rosidl_typesupport_introspection_c.h\n'}
[7.417668] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/include/drill_msgs/drill_msgs/msg/detail/state_command__rosidl_typesupport_introspection_c.h\n'}
[7.417790] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/include/drill_msgs/drill_msgs/msg/detail/state_machine_status__rosidl_typesupport_introspection_c.h\n'}
[7.417984] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/include/drill_msgs/drill_msgs/msg/detail/tower_ctrl__rosidl_typesupport_introspection_c.h\n'}
[7.418060] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/include/drill_msgs/drill_msgs/msg/detail/tower_state__rosidl_typesupport_introspection_c.h\n'}
[7.418247] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/include/drill_msgs/drill_msgs/msg/detail/tracks_ctrl__rosidl_typesupport_introspection_c.h\n'}
[7.418325] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/include/drill_msgs/drill_msgs/msg/detail/tracks_state__rosidl_typesupport_introspection_c.h\n'}
[7.418495] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/include/drill_msgs/drill_msgs/msg/detail/ups_status__rosidl_typesupport_introspection_c.h\n'}
[7.418683] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/include/drill_msgs/drill_msgs/msg/detail/vector2d__rosidl_typesupport_introspection_c.h\n'}
[7.418913] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/include/drill_msgs/drill_msgs/msg/detail/wrench_ctrl__rosidl_typesupport_introspection_c.h\n'}
[7.419029] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/include/drill_msgs/drill_msgs/msg/detail/wrench_state_raw__rosidl_typesupport_introspection_c.h\n'}
[7.419136] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/include/drill_msgs/drill_msgs/msg/rosidl_typesupport_introspection_c__visibility_control.h\n'}
[7.419210] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/include/drill_msgs/drill_msgs/srv/detail/get_current_drive_action__rosidl_typesupport_introspection_c.h\n'}
[7.420227] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/include/drill_msgs/drill_msgs/msg/detail/air_ctrl__rosidl_typesupport_introspection_cpp.hpp\n'}
[7.420373] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/include/drill_msgs/drill_msgs/msg/detail/arm_state_raw__rosidl_typesupport_introspection_cpp.hpp\n'}
[7.420545] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/include/drill_msgs/drill_msgs/msg/detail/bool_stamped__rosidl_typesupport_introspection_cpp.hpp\n'}
[7.420757] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/include/drill_msgs/drill_msgs/msg/detail/carousel_ctrl__rosidl_typesupport_introspection_cpp.hpp\n'}
[7.420968] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/include/drill_msgs/drill_msgs/msg/detail/carousel_state_raw__rosidl_typesupport_introspection_cpp.hpp\n'}
[7.421043] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/include/drill_msgs/drill_msgs/msg/detail/depth_info__rosidl_typesupport_introspection_cpp.hpp\n'}
[7.421182] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/include/drill_msgs/drill_msgs/msg/detail/drill_actuator_ctrl__rosidl_typesupport_introspection_cpp.hpp\n'}
[7.421336] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/include/drill_msgs/drill_msgs/msg/detail/drill_ctrl__rosidl_typesupport_introspection_cpp.hpp\n'}
[7.421470] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/include/drill_msgs/drill_msgs/msg/detail/drill_state__rosidl_typesupport_introspection_cpp.hpp\n'}
[7.421767] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/include/drill_msgs/drill_msgs/msg/detail/drill_state_raw__rosidl_typesupport_introspection_cpp.hpp\n'}
[7.421942] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/include/drill_msgs/drill_msgs/msg/detail/drive_action__rosidl_typesupport_introspection_cpp.hpp\n'}
[7.422135] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/include/drill_msgs/drill_msgs/msg/detail/drive_status__rosidl_typesupport_introspection_cpp.hpp\n'}
[7.422286] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/include/drill_msgs/drill_msgs/msg/detail/dust_flaps_state__rosidl_typesupport_introspection_cpp.hpp\n'}
[7.422676] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/include/drill_msgs/drill_msgs/msg/detail/engine_state__rosidl_typesupport_introspection_cpp.hpp\n'}
[7.422974] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/include/drill_msgs/drill_msgs/msg/detail/event__rosidl_typesupport_introspection_cpp.hpp\n'}
[7.423112] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/include/drill_msgs/drill_msgs/msg/detail/float_ctrl__rosidl_typesupport_introspection_cpp.hpp\n'}
[7.423585] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/include/drill_msgs/drill_msgs/msg/detail/float_stamped__rosidl_typesupport_introspection_cpp.hpp\n'}
[7.424196] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/include/drill_msgs/drill_msgs/msg/detail/fork_state__rosidl_typesupport_introspection_cpp.hpp\n'}
[7.424310] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/include/drill_msgs/drill_msgs/msg/detail/fork_state_raw__rosidl_typesupport_introspection_cpp.hpp\n'}
[7.424439] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/include/drill_msgs/drill_msgs/msg/detail/gnss__rosidl_typesupport_introspection_cpp.hpp\n'}
[7.424752] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/include/drill_msgs/drill_msgs/msg/detail/imu__rosidl_typesupport_introspection_cpp.hpp\n'}
[7.424836] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/include/drill_msgs/drill_msgs/msg/detail/jacks_ctrl__rosidl_typesupport_introspection_cpp.hpp\n'}
[7.424984] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/include/drill_msgs/drill_msgs/msg/detail/jacks_state_raw__rosidl_typesupport_introspection_cpp.hpp\n'}
[7.425126] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/include/drill_msgs/drill_msgs/msg/detail/jacks_switch_state__rosidl_typesupport_introspection_cpp.hpp\n'}
[7.425238] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/include/drill_msgs/drill_msgs/msg/detail/jacks_switch_state_raw__rosidl_typesupport_introspection_cpp.hpp\n'}
[7.425359] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/include/drill_msgs/drill_msgs/msg/detail/lamp_ctrl__rosidl_typesupport_introspection_cpp.hpp\n'}
[7.425483] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/include/drill_msgs/drill_msgs/msg/detail/level__rosidl_typesupport_introspection_cpp.hpp\n'}
[7.426082] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/include/drill_msgs/drill_msgs/msg/detail/main_action__rosidl_typesupport_introspection_cpp.hpp\n'}
[7.426276] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/include/drill_msgs/drill_msgs/msg/detail/mode_ctrl__rosidl_typesupport_introspection_cpp.hpp\n'}
[7.426563] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/include/drill_msgs/drill_msgs/msg/detail/param_notification__rosidl_typesupport_introspection_cpp.hpp\n'}
[7.426632] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/include/drill_msgs/drill_msgs/msg/detail/path__rosidl_typesupport_introspection_cpp.hpp\n'}
[7.426807] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/include/drill_msgs/drill_msgs/msg/detail/path_point__rosidl_typesupport_introspection_cpp.hpp\n'}
[7.426934] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/include/drill_msgs/drill_msgs/msg/detail/permission__rosidl_typesupport_introspection_cpp.hpp\n'}
[7.427252] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/include/drill_msgs/drill_msgs/msg/detail/pins_state_raw__rosidl_typesupport_introspection_cpp.hpp\n'}
[7.427346] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/include/drill_msgs/drill_msgs/msg/detail/point2d__rosidl_typesupport_introspection_cpp.hpp\n'}
[7.427418] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/include/drill_msgs/drill_msgs/msg/detail/position__rosidl_typesupport_introspection_cpp.hpp\n'}
[7.427543] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/include/drill_msgs/drill_msgs/msg/detail/report__rosidl_typesupport_introspection_cpp.hpp\n'}
[7.428275] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/include/drill_msgs/drill_msgs/msg/detail/rmo_health__rosidl_typesupport_introspection_cpp.hpp\n'}
[7.428608] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/include/drill_msgs/drill_msgs/msg/detail/speed_state__rosidl_typesupport_introspection_cpp.hpp\n'}
[7.428738] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/include/drill_msgs/drill_msgs/msg/detail/state_command__rosidl_typesupport_introspection_cpp.hpp\n'}
[7.428943] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/include/drill_msgs/drill_msgs/msg/detail/state_machine_status__rosidl_typesupport_introspection_cpp.hpp\n'}
[7.429153] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/include/drill_msgs/drill_msgs/msg/detail/tower_ctrl__rosidl_typesupport_introspection_cpp.hpp\n'}
[7.429449] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/include/drill_msgs/drill_msgs/msg/detail/tower_state__rosidl_typesupport_introspection_cpp.hpp\n'}
[7.429524] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/include/drill_msgs/drill_msgs/msg/detail/tracks_ctrl__rosidl_typesupport_introspection_cpp.hpp\n'}
[7.429564] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/include/drill_msgs/drill_msgs/msg/detail/tracks_state__rosidl_typesupport_introspection_cpp.hpp\n'}
[7.429633] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/include/drill_msgs/drill_msgs/msg/detail/ups_status__rosidl_typesupport_introspection_cpp.hpp\n'}
[7.429698] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/include/drill_msgs/drill_msgs/msg/detail/vector2d__rosidl_typesupport_introspection_cpp.hpp\n'}
[7.429732] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/include/drill_msgs/drill_msgs/msg/detail/wrench_ctrl__rosidl_typesupport_introspection_cpp.hpp\n'}
[7.429793] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/include/drill_msgs/drill_msgs/msg/detail/wrench_state_raw__rosidl_typesupport_introspection_cpp.hpp\n'}
[7.429839] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/include/drill_msgs/drill_msgs/srv/detail/get_current_drive_action__rosidl_typesupport_introspection_cpp.hpp\n'}
[7.429920] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/share/drill_msgs/environment/pythonpath.sh\n'}
[7.430036] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/share/drill_msgs/environment/pythonpath.dsv\n'}
[7.430345] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/lib/python3.11/site-packages/drill_msgs-0.0.0-py3.11.egg-info/PKG-INFO\n'}
[7.430458] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/lib/python3.11/site-packages/drill_msgs-0.0.0-py3.11.egg-info/SOURCES.txt\n'}
[7.430581] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/lib/python3.11/site-packages/drill_msgs-0.0.0-py3.11.egg-info/dependency_links.txt\n'}
[7.430702] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/lib/python3.11/site-packages/drill_msgs-0.0.0-py3.11.egg-info/top_level.txt\n'}
[7.432731] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/lib/python3.11/site-packages/drill_msgs/__init__.py\n'}
[7.432867] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/lib/python3.11/site-packages/drill_msgs/_drill_msgs_s.ep.rosidl_typesupport_c.c\n'}
[7.432973] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/lib/python3.11/site-packages/drill_msgs/_drill_msgs_s.ep.rosidl_typesupport_fastrtps_c.c\n'}
[7.433088] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/lib/python3.11/site-packages/drill_msgs/_drill_msgs_s.ep.rosidl_typesupport_introspection_c.c\n'}
[7.433188] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/lib/python3.11/site-packages/drill_msgs/drill_msgs_s__rosidl_typesupport_c.so\n'}
[7.433304] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/lib/python3.11/site-packages/drill_msgs/drill_msgs_s__rosidl_typesupport_fastrtps_c.so\n'}
[7.433421] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/lib/python3.11/site-packages/drill_msgs/drill_msgs_s__rosidl_typesupport_introspection_c.so\n'}
[7.433548] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/lib/python3.11/site-packages/drill_msgs/msg/__init__.py\n'}
[7.433670] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/lib/python3.11/site-packages/drill_msgs/msg/_air_ctrl.py\n'}
[7.433784] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/lib/python3.11/site-packages/drill_msgs/msg/_air_ctrl_s.c\n'}
[7.433899] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/lib/python3.11/site-packages/drill_msgs/msg/_arm_state_raw.py\n'}
[7.434029] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/lib/python3.11/site-packages/drill_msgs/msg/_arm_state_raw_s.c\n'}
[7.434140] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/lib/python3.11/site-packages/drill_msgs/msg/_bool_stamped.py\n'}
[7.434245] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/lib/python3.11/site-packages/drill_msgs/msg/_bool_stamped_s.c\n'}
[7.434365] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/lib/python3.11/site-packages/drill_msgs/msg/_carousel_ctrl.py\n'}
[7.434486] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/lib/python3.11/site-packages/drill_msgs/msg/_carousel_ctrl_s.c\n'}
[7.434597] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/lib/python3.11/site-packages/drill_msgs/msg/_carousel_state_raw.py\n'}
[7.434704] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/lib/python3.11/site-packages/drill_msgs/msg/_carousel_state_raw_s.c\n'}
[7.434822] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/lib/python3.11/site-packages/drill_msgs/msg/_depth_info.py\n'}
[7.434934] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/lib/python3.11/site-packages/drill_msgs/msg/_depth_info_s.c\n'}
[7.435049] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/lib/python3.11/site-packages/drill_msgs/msg/_drill_actuator_ctrl.py\n'}
[7.435168] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/lib/python3.11/site-packages/drill_msgs/msg/_drill_actuator_ctrl_s.c\n'}
[7.435278] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/lib/python3.11/site-packages/drill_msgs/msg/_drill_ctrl.py\n'}
[7.435393] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/lib/python3.11/site-packages/drill_msgs/msg/_drill_ctrl_s.c\n'}
[7.435503] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/lib/python3.11/site-packages/drill_msgs/msg/_drill_state.py\n'}
[7.435619] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/lib/python3.11/site-packages/drill_msgs/msg/_drill_state_raw.py\n'}
[7.435728] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/lib/python3.11/site-packages/drill_msgs/msg/_drill_state_raw_s.c\n'}
[7.435841] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/lib/python3.11/site-packages/drill_msgs/msg/_drill_state_s.c\n'}
[7.435960] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/lib/python3.11/site-packages/drill_msgs/msg/_drive_action.py\n'}
[7.436073] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/lib/python3.11/site-packages/drill_msgs/msg/_drive_action_s.c\n'}
[7.436185] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/lib/python3.11/site-packages/drill_msgs/msg/_drive_status.py\n'}
[7.436301] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/lib/python3.11/site-packages/drill_msgs/msg/_drive_status_s.c\n'}
[7.436413] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/lib/python3.11/site-packages/drill_msgs/msg/_dust_flaps_state.py\n'}
[7.436529] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/lib/python3.11/site-packages/drill_msgs/msg/_dust_flaps_state_s.c\n'}
[7.436638] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/lib/python3.11/site-packages/drill_msgs/msg/_engine_state.py\n'}
[7.436755] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/lib/python3.11/site-packages/drill_msgs/msg/_engine_state_s.c\n'}
[7.436863] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/lib/python3.11/site-packages/drill_msgs/msg/_event.py\n'}
[7.436972] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/lib/python3.11/site-packages/drill_msgs/msg/_event_s.c\n'}
[7.437090] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/lib/python3.11/site-packages/drill_msgs/msg/_float_ctrl.py\n'}
[7.437203] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/lib/python3.11/site-packages/drill_msgs/msg/_float_ctrl_s.c\n'}
[7.437313] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/lib/python3.11/site-packages/drill_msgs/msg/_float_stamped.py\n'}
[7.437426] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/lib/python3.11/site-packages/drill_msgs/msg/_float_stamped_s.c\n'}
[7.437545] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/lib/python3.11/site-packages/drill_msgs/msg/_fork_state.py\n'}
[7.437659] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/lib/python3.11/site-packages/drill_msgs/msg/_fork_state_raw.py\n'}
[7.437767] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/lib/python3.11/site-packages/drill_msgs/msg/_fork_state_raw_s.c\n'}
[7.437877] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/lib/python3.11/site-packages/drill_msgs/msg/_fork_state_s.c\n'}
[7.437995] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/lib/python3.11/site-packages/drill_msgs/msg/_gnss.py\n'}
[7.438108] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/lib/python3.11/site-packages/drill_msgs/msg/_gnss_s.c\n'}
[7.438218] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/lib/python3.11/site-packages/drill_msgs/msg/_imu.py\n'}
[7.438331] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/lib/python3.11/site-packages/drill_msgs/msg/_imu_s.c\n'}
[7.438449] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/lib/python3.11/site-packages/drill_msgs/msg/_jacks_ctrl.py\n'}
[7.438554] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/lib/python3.11/site-packages/drill_msgs/msg/_jacks_ctrl_s.c\n'}
[7.438668] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/lib/python3.11/site-packages/drill_msgs/msg/_jacks_state_raw.py\n'}
[7.438782] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/lib/python3.11/site-packages/drill_msgs/msg/_jacks_state_raw_s.c\n'}
[7.438905] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/lib/python3.11/site-packages/drill_msgs/msg/_jacks_switch_state.py\n'}
[7.439021] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/lib/python3.11/site-packages/drill_msgs/msg/_jacks_switch_state_raw.py\n'}
[7.439136] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/lib/python3.11/site-packages/drill_msgs/msg/_jacks_switch_state_raw_s.c\n'}
[7.439247] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/lib/python3.11/site-packages/drill_msgs/msg/_jacks_switch_state_s.c\n'}
[7.439358] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/lib/python3.11/site-packages/drill_msgs/msg/_lamp_ctrl.py\n'}
[7.439475] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/lib/python3.11/site-packages/drill_msgs/msg/_lamp_ctrl_s.c\n'}
[7.439597] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/lib/python3.11/site-packages/drill_msgs/msg/_level.py\n'}
[7.439708] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/lib/python3.11/site-packages/drill_msgs/msg/_level_s.c\n'}
[7.439830] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/lib/python3.11/site-packages/drill_msgs/msg/_main_action.py\n'}
[7.439941] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/lib/python3.11/site-packages/drill_msgs/msg/_main_action_s.c\n'}
[7.440055] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/lib/python3.11/site-packages/drill_msgs/msg/_mode_ctrl.py\n'}
[7.440178] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/lib/python3.11/site-packages/drill_msgs/msg/_mode_ctrl_s.c\n'}
[7.440286] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/lib/python3.11/site-packages/drill_msgs/msg/_param_notification.py\n'}
[7.440396] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/lib/python3.11/site-packages/drill_msgs/msg/_param_notification_s.c\n'}
[7.440511] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/lib/python3.11/site-packages/drill_msgs/msg/_path.py\n'}
[7.440634] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/lib/python3.11/site-packages/drill_msgs/msg/_path_point.py\n'}
[7.440745] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/lib/python3.11/site-packages/drill_msgs/msg/_path_point_s.c\n'}
[7.440860] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/lib/python3.11/site-packages/drill_msgs/msg/_path_s.c\n'}
[7.440974] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/lib/python3.11/site-packages/drill_msgs/msg/_permission.py\n'}
[7.441088] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/lib/python3.11/site-packages/drill_msgs/msg/_permission_s.c\n'}
[7.441192] (-) TimerEvent: {}
[7.441279] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/lib/python3.11/site-packages/drill_msgs/msg/_pins_state_raw.py\n'}
[7.441447] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/lib/python3.11/site-packages/drill_msgs/msg/_pins_state_raw_s.c\n'}
[7.441521] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/lib/python3.11/site-packages/drill_msgs/msg/_point2d.py\n'}
[7.441577] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/lib/python3.11/site-packages/drill_msgs/msg/_point2d_s.c\n'}
[7.441667] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/lib/python3.11/site-packages/drill_msgs/msg/_position.py\n'}
[7.441781] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/lib/python3.11/site-packages/drill_msgs/msg/_position_s.c\n'}
[7.441891] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/lib/python3.11/site-packages/drill_msgs/msg/_report.py\n'}
[7.442003] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/lib/python3.11/site-packages/drill_msgs/msg/_report_s.c\n'}
[7.442120] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/lib/python3.11/site-packages/drill_msgs/msg/_rmo_health.py\n'}
[7.442231] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/lib/python3.11/site-packages/drill_msgs/msg/_rmo_health_s.c\n'}
[7.442353] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/lib/python3.11/site-packages/drill_msgs/msg/_speed_state.py\n'}
[7.442469] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/lib/python3.11/site-packages/drill_msgs/msg/_speed_state_s.c\n'}
[7.442577] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/lib/python3.11/site-packages/drill_msgs/msg/_state_command.py\n'}
[7.442687] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/lib/python3.11/site-packages/drill_msgs/msg/_state_command_s.c\n'}
[7.442804] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/lib/python3.11/site-packages/drill_msgs/msg/_state_machine_status.py\n'}
[7.442915] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/lib/python3.11/site-packages/drill_msgs/msg/_state_machine_status_s.c\n'}
[7.443027] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/lib/python3.11/site-packages/drill_msgs/msg/_tower_ctrl.py\n'}
[7.443136] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/lib/python3.11/site-packages/drill_msgs/msg/_tower_ctrl_s.c\n'}
[7.443256] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/lib/python3.11/site-packages/drill_msgs/msg/_tower_state.py\n'}
[7.443369] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/lib/python3.11/site-packages/drill_msgs/msg/_tower_state_s.c\n'}
[7.443481] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/lib/python3.11/site-packages/drill_msgs/msg/_tracks_ctrl.py\n'}
[7.443598] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/lib/python3.11/site-packages/drill_msgs/msg/_tracks_ctrl_s.c\n'}
[7.443721] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/lib/python3.11/site-packages/drill_msgs/msg/_tracks_state.py\n'}
[7.443835] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/lib/python3.11/site-packages/drill_msgs/msg/_tracks_state_s.c\n'}
[7.444277] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/lib/python3.11/site-packages/drill_msgs/msg/_ups_status.py\n'}
[7.444411] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/lib/python3.11/site-packages/drill_msgs/msg/_ups_status_s.c\n'}
[7.444509] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/lib/python3.11/site-packages/drill_msgs/msg/_vector2d.py\n'}
[7.444551] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/lib/python3.11/site-packages/drill_msgs/msg/_vector2d_s.c\n'}
[7.444649] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/lib/python3.11/site-packages/drill_msgs/msg/_wrench_ctrl.py\n'}
[7.444774] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/lib/python3.11/site-packages/drill_msgs/msg/_wrench_ctrl_s.c\n'}
[7.444893] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/lib/python3.11/site-packages/drill_msgs/msg/_wrench_state_raw.py\n'}
[7.445009] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/lib/python3.11/site-packages/drill_msgs/msg/_wrench_state_raw_s.c\n'}
[7.445136] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/lib/python3.11/site-packages/drill_msgs/srv/__init__.py\n'}
[7.445259] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/lib/python3.11/site-packages/drill_msgs/srv/_get_current_drive_action.py\n'}
[7.445381] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/lib/python3.11/site-packages/drill_msgs/srv/_get_current_drive_action_s.c\n'}
[7.445877] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/lib/python3.11/site-packages/drill_msgs/drill_msgs_s__rosidl_typesupport_introspection_c.so\n'}
[7.446279] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/lib/python3.11/site-packages/drill_msgs/drill_msgs_s__rosidl_typesupport_fastrtps_c.so\n'}
[7.446624] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/lib/python3.11/site-packages/drill_msgs/drill_msgs_s__rosidl_typesupport_c.so\n'}
[7.446815] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/share/drill_msgs/msg/Event.idl\n'}
[7.446948] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/share/drill_msgs/msg/Report.idl\n'}
[7.447110] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/share/drill_msgs/msg/Permission.idl\n'}
[7.447264] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/share/drill_msgs/msg/ParamNotification.idl\n'}
[7.447409] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/share/drill_msgs/msg/BoolStamped.idl\n'}
[7.447567] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/share/drill_msgs/msg/Vector2d.idl\n'}
[7.447715] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/share/drill_msgs/msg/IMU.idl\n'}
[7.447852] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/share/drill_msgs/msg/EngineState.idl\n'}
[7.447998] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/share/drill_msgs/msg/DrillStateRaw.idl\n'}
[7.448153] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/share/drill_msgs/msg/DrillState.idl\n'}
[7.448294] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/share/drill_msgs/msg/DepthInfo.idl\n'}
[7.448443] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/share/drill_msgs/msg/FloatStamped.idl\n'}
[7.448585] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/share/drill_msgs/msg/JacksStateRaw.idl\n'}
[7.448729] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/share/drill_msgs/msg/JacksSwitchState.idl\n'}
[7.448869] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/share/drill_msgs/msg/JacksSwitchStateRaw.idl\n'}
[7.449008] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/share/drill_msgs/msg/PinsStateRaw.idl\n'}
[7.449141] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/share/drill_msgs/msg/ArmStateRaw.idl\n'}
[7.449281] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/share/drill_msgs/msg/ForkStateRaw.idl\n'}
[7.449419] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/share/drill_msgs/msg/ForkState.idl\n'}
[7.449562] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/share/drill_msgs/msg/CarouselStateRaw.idl\n'}
[7.449709] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/share/drill_msgs/msg/WrenchStateRaw.idl\n'}
[7.449854] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/share/drill_msgs/msg/DustFlapsState.idl\n'}
[7.449997] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/share/drill_msgs/msg/StateMachineStatus.idl\n'}
[7.450138] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/share/drill_msgs/msg/StateCommand.idl\n'}
[7.450279] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/share/drill_msgs/msg/RmoHealth.idl\n'}
[7.450417] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/share/drill_msgs/msg/CarouselCtrl.idl\n'}
[7.450567] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/share/drill_msgs/msg/TowerCtrl.idl\n'}
[7.450704] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/share/drill_msgs/msg/WrenchCtrl.idl\n'}
[7.450847] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/share/drill_msgs/msg/TracksCtrl.idl\n'}
[7.450983] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/share/drill_msgs/msg/FloatCtrl.idl\n'}
[7.451126] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/share/drill_msgs/msg/DrillCtrl.idl\n'}
[7.451260] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/share/drill_msgs/msg/DrillActuatorCtrl.idl\n'}
[7.451415] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/share/drill_msgs/msg/AirCtrl.idl\n'}
[7.451546] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/share/drill_msgs/msg/JacksCtrl.idl\n'}
[7.451681] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/share/drill_msgs/msg/UpsStatus.idl\n'}
[7.451814] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/share/drill_msgs/msg/LampCtrl.idl\n'}
[7.451966] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/share/drill_msgs/msg/Level.idl\n'}
[7.452091] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/share/drill_msgs/msg/GNSS.idl\n'}
[7.452233] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/share/drill_msgs/msg/Position.idl\n'}
[7.452367] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/share/drill_msgs/msg/SpeedState.idl\n'}
[7.452509] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/share/drill_msgs/msg/TracksState.idl\n'}
[7.452646] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/share/drill_msgs/msg/ModeCtrl.idl\n'}
[7.452783] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/share/drill_msgs/msg/Point2d.idl\n'}
[7.452915] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/share/drill_msgs/msg/Path.idl\n'}
[7.453050] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/share/drill_msgs/msg/PathPoint.idl\n'}
[7.453186] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/share/drill_msgs/msg/MainAction.idl\n'}
[7.453329] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/share/drill_msgs/msg/DriveAction.idl\n'}
[7.453461] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/share/drill_msgs/msg/TowerState.idl\n'}
[7.453595] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/share/drill_msgs/msg/DriveStatus.idl\n'}
[7.453737] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/share/drill_msgs/srv/GetCurrentDriveAction.idl\n'}
[7.453879] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/share/drill_msgs/msg/Event.msg\n'}
[7.454013] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/share/drill_msgs/msg/Report.msg\n'}
[7.454144] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/share/drill_msgs/msg/Permission.msg\n'}
[7.454285] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/share/drill_msgs/msg/ParamNotification.msg\n'}
[7.454417] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/share/drill_msgs/msg/BoolStamped.msg\n'}
[7.454548] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/share/drill_msgs/msg/Vector2d.msg\n'}
[7.454686] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/share/drill_msgs/msg/IMU.msg\n'}
[7.454831] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/share/drill_msgs/msg/EngineState.msg\n'}
[7.454969] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/share/drill_msgs/msg/DrillStateRaw.msg\n'}
[7.455089] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/share/drill_msgs/msg/DrillState.msg\n'}
[7.455224] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/share/drill_msgs/msg/DepthInfo.msg\n'}
[7.455356] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/share/drill_msgs/msg/FloatStamped.msg\n'}
[7.455485] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/share/drill_msgs/msg/JacksStateRaw.msg\n'}
[7.455620] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/share/drill_msgs/msg/JacksSwitchState.msg\n'}
[7.455761] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/share/drill_msgs/msg/JacksSwitchStateRaw.msg\n'}
[7.455901] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/share/drill_msgs/msg/PinsStateRaw.msg\n'}
[7.456024] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/share/drill_msgs/msg/ArmStateRaw.msg\n'}
[7.456158] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/share/drill_msgs/msg/ForkStateRaw.msg\n'}
[7.456285] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/share/drill_msgs/msg/ForkState.msg\n'}
[7.456419] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/share/drill_msgs/msg/CarouselStateRaw.msg\n'}
[7.456550] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/share/drill_msgs/msg/WrenchStateRaw.msg\n'}
[7.456758] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/share/drill_msgs/msg/DustFlapsState.msg\n'}
[7.456925] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/share/drill_msgs/msg/StateMachineStatus.msg\n'}
[7.457073] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/share/drill_msgs/msg/StateCommand.msg\n'}
[7.457219] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/share/drill_msgs/msg/RmoHealth.msg\n'}
[7.457354] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/share/drill_msgs/msg/CarouselCtrl.msg\n'}
[7.457490] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/share/drill_msgs/msg/TowerCtrl.msg\n'}
[7.457630] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/share/drill_msgs/msg/WrenchCtrl.msg\n'}
[7.457762] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/share/drill_msgs/msg/TracksCtrl.msg\n'}
[7.457899] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/share/drill_msgs/msg/FloatCtrl.msg\n'}
[7.458031] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/share/drill_msgs/msg/DrillCtrl.msg\n'}
[7.458165] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/share/drill_msgs/msg/DrillActuatorCtrl.msg\n'}
[7.458303] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/share/drill_msgs/msg/AirCtrl.msg\n'}
[7.458431] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/share/drill_msgs/msg/JacksCtrl.msg\n'}
[7.458565] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/share/drill_msgs/msg/UpsStatus.msg\n'}
[7.458703] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/share/drill_msgs/msg/LampCtrl.msg\n'}
[7.458832] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/share/drill_msgs/msg/Level.msg\n'}
[7.458967] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/share/drill_msgs/msg/GNSS.msg\n'}
[7.459099] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/share/drill_msgs/msg/Position.msg\n'}
[7.459236] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/share/drill_msgs/msg/SpeedState.msg\n'}
[7.459366] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/share/drill_msgs/msg/TracksState.msg\n'}
[7.459504] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/share/drill_msgs/msg/ModeCtrl.msg\n'}
[7.459643] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/share/drill_msgs/msg/Point2d.msg\n'}
[7.459777] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/share/drill_msgs/msg/Path.msg\n'}
[7.459901] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/share/drill_msgs/msg/PathPoint.msg\n'}
[7.460041] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/share/drill_msgs/msg/MainAction.msg\n'}
[7.460172] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/share/drill_msgs/msg/DriveAction.msg\n'}
[7.460307] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/share/drill_msgs/msg/TowerState.msg\n'}
[7.460452] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/share/drill_msgs/msg/DriveStatus.msg\n'}
[7.460601] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/share/drill_msgs/srv/GetCurrentDriveAction.srv\n'}
[7.460779] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/share/ament_index/resource_index/package_run_dependencies/drill_msgs\n'}
[7.460946] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/share/ament_index/resource_index/parent_prefix_path/drill_msgs\n'}
[7.461146] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/share/drill_msgs/environment/ament_prefix_path.sh\n'}
[7.461279] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/share/drill_msgs/environment/ament_prefix_path.dsv\n'}
[7.461448] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/share/drill_msgs/environment/path.sh\n'}
[7.461583] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/share/drill_msgs/environment/path.dsv\n'}
[7.461717] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/share/drill_msgs/local_setup.bash\n'}
[7.461852] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/share/drill_msgs/local_setup.sh\n'}
[7.461977] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/share/drill_msgs/local_setup.zsh\n'}
[7.462117] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/share/drill_msgs/local_setup.dsv\n'}
[7.462211] (drill_msgs) StdoutLine: {'line': b'-- Symlinking: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/share/drill_msgs/package.dsv\n'}
[7.499652] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/share/ament_index/resource_index/packages/drill_msgs\n'}
[7.499929] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/share/drill_msgs/cmake/rosidl_cmake-extras.cmake\n'}
[7.500014] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/share/drill_msgs/cmake/ament_cmake_export_dependencies-extras.cmake\n'}
[7.500222] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/share/drill_msgs/cmake/ament_cmake_export_include_directories-extras.cmake\n'}
[7.500508] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/share/drill_msgs/cmake/ament_cmake_export_libraries-extras.cmake\n'}
[7.500564] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/share/drill_msgs/cmake/ament_cmake_export_targets-extras.cmake\n'}
[7.500652] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/share/drill_msgs/cmake/rosidl_cmake_export_typesupport_targets-extras.cmake\n'}
[7.500847] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/share/drill_msgs/cmake/rosidl_cmake_export_typesupport_libraries-extras.cmake\n'}
[7.501280] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/share/drill_msgs/cmake/drill_msgsConfig.cmake\n'}
[7.501625] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/share/drill_msgs/cmake/drill_msgsConfig-version.cmake\n'}
[7.501822] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/share/drill_msgs/package.xml\n'}
[7.502012] (drill_msgs) StdoutLine: {'line': b'-- Installing: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/lib/libdrill_msgs__rosidl_generator_c.dylib\n'}
[7.542265] (-) TimerEvent: {}
[7.629640] (drill_msgs) StdoutLine: {'line': b'-- Installing: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/lib/libdrill_msgs__rosidl_typesupport_fastrtps_c.dylib\n'}
[7.642680] (-) TimerEvent: {}
[7.699413] (drill_msgs) StdoutLine: {'line': b'-- Installing: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/lib/libdrill_msgs__rosidl_typesupport_fastrtps_cpp.dylib\n'}
[7.745334] (-) TimerEvent: {}
[7.767751] (drill_msgs) StdoutLine: {'line': b'-- Installing: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/lib/libdrill_msgs__rosidl_typesupport_introspection_c.dylib\n'}
[7.843614] (drill_msgs) StdoutLine: {'line': b'-- Installing: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/lib/libdrill_msgs__rosidl_typesupport_c.dylib\n'}
[7.845397] (-) TimerEvent: {}
[7.918353] (drill_msgs) StdoutLine: {'line': b'-- Installing: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/lib/libdrill_msgs__rosidl_typesupport_introspection_cpp.dylib\n'}
[7.946800] (-) TimerEvent: {}
[7.998574] (drill_msgs) StdoutLine: {'line': b'-- Installing: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/lib/libdrill_msgs__rosidl_typesupport_cpp.dylib\n'}
[8.051680] (-) TimerEvent: {}
[8.156286] (-) TimerEvent: {}
[8.251591] (drill_msgs) StdoutLine: {'line': b"Listing '/Users/<USER>/Work/drill2/onboard/install/drill_msgs/lib/python3.11/site-packages/drill_msgs'...\n"}
[8.251728] (drill_msgs) StdoutLine: {'line': b"Listing '/Users/<USER>/Work/drill2/onboard/install/drill_msgs/lib/python3.11/site-packages/drill_msgs/msg'...\n"}
[8.251762] (drill_msgs) StdoutLine: {'line': b"Listing '/Users/<USER>/Work/drill2/onboard/install/drill_msgs/lib/python3.11/site-packages/drill_msgs/srv'...\n"}
[8.256622] (drill_msgs) StdoutLine: {'line': b'-- Installing: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/lib/libdrill_msgs__rosidl_generator_py.dylib\n'}
[8.256716] (-) TimerEvent: {}
[8.331958] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/share/drill_msgs/cmake/export_drill_msgs__rosidl_generator_cExport.cmake\n'}
[8.332103] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/share/drill_msgs/cmake/export_drill_msgs__rosidl_generator_cExport-noconfig.cmake\n'}
[8.332370] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/share/drill_msgs/cmake/export_drill_msgs__rosidl_typesupport_fastrtps_cExport.cmake\n'}
[8.332490] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/share/drill_msgs/cmake/export_drill_msgs__rosidl_typesupport_fastrtps_cExport-noconfig.cmake\n'}
[8.332835] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/share/drill_msgs/cmake/export_drill_msgs__rosidl_generator_cppExport.cmake\n'}
[8.333180] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/share/drill_msgs/cmake/export_drill_msgs__rosidl_typesupport_fastrtps_cppExport.cmake\n'}
[8.333359] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/share/drill_msgs/cmake/export_drill_msgs__rosidl_typesupport_fastrtps_cppExport-noconfig.cmake\n'}
[8.333562] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/share/drill_msgs/cmake/drill_msgs__rosidl_typesupport_introspection_cExport.cmake\n'}
[8.333705] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/share/drill_msgs/cmake/drill_msgs__rosidl_typesupport_introspection_cExport-noconfig.cmake\n'}
[8.334167] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/share/drill_msgs/cmake/drill_msgs__rosidl_typesupport_cExport.cmake\n'}
[8.334225] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/share/drill_msgs/cmake/drill_msgs__rosidl_typesupport_cExport-noconfig.cmake\n'}
[8.334574] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/share/drill_msgs/cmake/drill_msgs__rosidl_typesupport_introspection_cppExport.cmake\n'}
[8.334630] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/share/drill_msgs/cmake/drill_msgs__rosidl_typesupport_introspection_cppExport-noconfig.cmake\n'}
[8.334966] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/share/drill_msgs/cmake/drill_msgs__rosidl_typesupport_cppExport.cmake\n'}
[8.335040] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/share/drill_msgs/cmake/drill_msgs__rosidl_typesupport_cppExport-noconfig.cmake\n'}
[8.335317] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/share/drill_msgs/cmake/export_drill_msgs__rosidl_generator_pyExport.cmake\n'}
[8.335402] (drill_msgs) StdoutLine: {'line': b'-- Up-to-date: /Users/<USER>/Work/drill2/onboard/install/drill_msgs/share/drill_msgs/cmake/export_drill_msgs__rosidl_generator_pyExport-noconfig.cmake\n'}
[8.337151] (drill_msgs) CommandEnded: {'returncode': 0}
[8.357814] (-) TimerEvent: {}
[8.382297] (drill_msgs) JobEnded: {'identifier': 'drill_msgs', 'rc': 0}
[8.392824] (leveler) JobStarted: {'identifier': 'leveler'}
[8.463026] (-) TimerEvent: {}
[8.564705] (-) TimerEvent: {}
[8.666794] (-) TimerEvent: {}
[8.768589] (-) TimerEvent: {}
[8.869866] (-) TimerEvent: {}
[8.972396] (-) TimerEvent: {}
[9.075610] (-) TimerEvent: {}
[9.176613] (-) TimerEvent: {}
[9.281563] (-) TimerEvent: {}
[9.382794] (-) TimerEvent: {}
[9.484932] (-) TimerEvent: {}
[9.590227] (-) TimerEvent: {}
[9.695401] (-) TimerEvent: {}
[9.796345] (-) TimerEvent: {}
[9.897687] (-) TimerEvent: {}
[10.000239] (-) TimerEvent: {}
[10.101782] (-) TimerEvent: {}
[10.204123] (-) TimerEvent: {}
[10.306626] (-) TimerEvent: {}
[10.411766] (-) TimerEvent: {}
[10.512218] (-) TimerEvent: {}
[10.582040] (leveler) Command: {'cmd': ['/Users/<USER>/.ros2_venv/bin/python3', '-W', 'ignore:setup.py install is deprecated', '-W', 'ignore:easy_install command is deprecated', 'setup.py', 'develop', '--uninstall', '--editable', '--build-directory', '/Users/<USER>/Work/drill2/onboard/build/leveler/build'], 'cwd': '/Users/<USER>/Work/drill2/onboard/build/leveler', 'env': {'NVM_INC': '/Users/<USER>/.nvm/versions/node/v22.14.0/include/node', 'TERM_PROGRAM': 'iTerm.app', 'NVM_CD_FLAGS': '-q', 'TERM': 'xterm-256color', 'SHELL': '/bin/zsh', 'HOMEBREW_REPOSITORY': '/opt/homebrew', 'TMPDIR': '/var/folders/6b/9kvsjxmn2wg9n27c78lfw18r0000gn/T/', 'TERM_PROGRAM_VERSION': '3.5.15beta1', 'ROS_PYTHON_VERSION': '3', 'RCUTILS_LOGGING_SEVERITY': 'DEBUG', 'ROS_VERSION': '2', 'COLCON_PREFIX_PATH': '/Users/<USER>/ros2_jazzy/install:/Users/<USER>/ros2_jazzy/install:/Users/<USER>/Work/drill2/onboard/install', 'TERM_SESSION_ID': 'w0t3p0:22583761-CC84-481F-B886-691DB3AE381C', 'AMENT_PREFIX_PATH': '/Users/<USER>/Work/drill2/onboard/install/base_node:/Users/<USER>/Work/drill2/onboard/install/depth_tracker:/Users/<USER>/ros2_jazzy/install/rosbag2:/Users/<USER>/ros2_jazzy/install/rosbag2_storage_default_plugins:/Users/<USER>/ros2_jazzy/install/rosbag2_storage_mcap:/Users/<USER>/ros2_jazzy/install/rosbag2_compression_zstd:/Users/<USER>/ros2_jazzy/install/mcap_vendor:/Users/<USER>/ros2_jazzy/install/zstd_vendor:/Users/<USER>/ros2_jazzy/install/rviz_visual_testing_framework:/Users/<USER>/ros2_jazzy/install/rviz2:/Users/<USER>/ros2_jazzy/install/rviz_default_plugins:/Users/<USER>/ros2_jazzy/install/rviz_common:/Users/<USER>/ros2_jazzy/install/rosbag2_examples_py:/Users/<USER>/ros2_jazzy/install/launch_testing_examples:/Users/<USER>/ros2_jazzy/install/ros2bag:/Users/<USER>/ros2_jazzy/install/rosbag2_py:/Users/<USER>/ros2_jazzy/install/rosbag2_transport:/Users/<USER>/ros2_jazzy/install/rosbag2_storage_sqlite3:/Users/<USER>/ros2_jazzy/install/rosbag2_examples_cpp:/Users/<USER>/ros2_jazzy/install/rosbag2_compression:/Users/<USER>/ros2_jazzy/install/rosbag2_cpp:/Users/<USER>/ros2_jazzy/install/rosbag2_storage:/Users/<USER>/ros2_jazzy/install/image_common:/Users/<USER>/ros2_jazzy/install/camera_info_manager:/Users/<USER>/ros2_jazzy/install/camera_calibration_parsers:/Users/<USER>/ros2_jazzy/install/yaml_cpp_vendor:/Users/<USER>/ros2_jazzy/install/interactive_markers:/Users/<USER>/ros2_jazzy/install/common_interfaces:/Users/<USER>/ros2_jazzy/install/visualization_msgs:/Users/<USER>/ros2_jazzy/install/dummy_robot_bringup:/Users/<USER>/ros2_jazzy/install/robot_state_publisher:/Users/<USER>/ros2_jazzy/install/kdl_parser:/Users/<USER>/ros2_jazzy/install/urdf:/Users/<USER>/ros2_jazzy/install/urdf_parser_plugin:/Users/<USER>/ros2_jazzy/install/turtlesim:/Users/<USER>/ros2_jazzy/install/trajectory_msgs:/Users/<USER>/ros2_jazzy/install/tracetools_test:/Users/<USER>/ros2_jazzy/install/tracetools_launch:/Users/<USER>/ros2_jazzy/install/topic_statistics_demo:/Users/<USER>/ros2_jazzy/install/topic_monitor:/Users/<USER>/ros2_jazzy/install/geometry2:/Users/<USER>/ros2_jazzy/install/tf2_tools:/Users/<USER>/ros2_jazzy/install/tf2_sensor_msgs:/Users/<USER>/ros2_jazzy/install/test_tf2:/Users/<USER>/ros2_jazzy/install/tf2_kdl:/Users/<USER>/ros2_jazzy/install/tf2_geometry_msgs:/Users/<USER>/ros2_jazzy/install/examples_tf2_py:/Users/<USER>/ros2_jazzy/install/tf2_ros_py:/Users/<USER>/ros2_jazzy/install/tf2_eigen:/Users/<USER>/ros2_jazzy/install/tf2_bullet:/Users/<USER>/ros2_jazzy/install/tf2_ros:/Users/<USER>/ros2_jazzy/install/tf2_py:/Users/<USER>/ros2_jazzy/install/tf2_msgs:/Users/<USER>/ros2_jazzy/install/tf2_eigen_kdl:/Users/<USER>/ros2_jazzy/install/laser_geometry:/Users/<USER>/ros2_jazzy/install/tf2:/Users/<USER>/ros2_jazzy/install/test_tracetools:/Users/<USER>/ros2_jazzy/install/rosbag2_test_common:/Users/<USER>/ros2_jazzy/install/test_msgs:/Users/<USER>/ros2_jazzy/install/test_communication:/Users/<USER>/ros2_jazzy/install/stereo_msgs:/Users/<USER>/ros2_jazzy/install/std_srvs:/Users/<USER>/ros2_jazzy/install/shape_msgs:/Users/<USER>/ros2_jazzy/install/examples_rclpy_pointcloud_publisher:/Users/<USER>/ros2_jazzy/install/sensor_msgs_py:/Users/<USER>/ros2_jazzy/install/quality_of_service_demo_py:/Users/<USER>/ros2_jazzy/install/quality_of_service_demo_cpp:/Users/<USER>/ros2_jazzy/install/point_cloud_transport_py:/Users/<USER>/ros2_jazzy/install/point_cloud_transport:/Users/<USER>/ros2_jazzy/install/map_msgs:/Users/<USER>/ros2_jazzy/install/intra_process_demo:/Users/<USER>/ros2_jazzy/install/image_transport:/Users/<USER>/ros2_jazzy/install/image_tools:/Users/<USER>/ros2_jazzy/install/dummy_sensors:/Users/<USER>/ros2_jazzy/install/camera_info_manager_py:/Users/<USER>/ros2_jazzy/install/sensor_msgs:/Users/<USER>/ros2_jazzy/install/ros2cli_common_extensions:/Users/<USER>/ros2_jazzy/install/ros2doctor:/Users/<USER>/ros2_jazzy/install/dummy_map_server:/Users/<USER>/ros2_jazzy/install/nav_msgs:/Users/<USER>/ros2_jazzy/install/message_filters:/Users/<USER>/ros2_jazzy/install/logging_demo:/Users/<USER>/ros2_jazzy/install/lifecycle_py:/Users/<USER>/ros2_jazzy/install/lifecycle:/Users/<USER>/ros2_jazzy/install/diagnostic_msgs:/Users/<USER>/ros2_jazzy/install/geometry_msgs:/Users/<USER>/ros2_jazzy/install/examples_rclpy_minimal_subscriber:/Users/<USER>/ros2_jazzy/install/examples_rclpy_minimal_service:/Users/<USER>/ros2_jazzy/install/examples_rclpy_minimal_publisher:/Users/<USER>/ros2_jazzy/install/examples_rclpy_minimal_client:/Users/<USER>/ros2_jazzy/install/examples_rclpy_executors:/Users/<USER>/ros2_jazzy/install/examples_rclcpp_wait_set:/Users/<USER>/ros2_jazzy/install/examples_rclcpp_multithreaded_executor:/Users/<USER>/ros2_jazzy/install/examples_rclcpp_minimal_subscriber:/Users/<USER>/ros2_jazzy/install/examples_rclcpp_minimal_publisher:/Users/<USER>/ros2_jazzy/install/examples_rclcpp_minimal_composition:/Users/<USER>/ros2_jazzy/install/examples_rclcpp_cbg_executor:/Users/<USER>/ros2_jazzy/install/demo_nodes_py:/Users/<USER>/ros2_jazzy/install/demo_nodes_cpp_native:/Users/<USER>/ros2_jazzy/install/demo_nodes_cpp:/Users/<USER>/ros2_jazzy/install/composition:/Users/<USER>/ros2_jazzy/install/actionlib_msgs:/Users/<USER>/ros2_jazzy/install/std_msgs:/Users/<USER>/ros2_jazzy/install/ros2lifecycle_test_fixtures:/Users/<USER>/ros2_jazzy/install/ros2component:/Users/<USER>/ros2_jazzy/install/rclcpp_lifecycle:/Users/<USER>/ros2_jazzy/install/action_tutorials_cpp:/Users/<USER>/ros2_jazzy/install/rclcpp_components:/Users/<USER>/ros2_jazzy/install/examples_rclcpp_minimal_action_server:/Users/<USER>/ros2_jazzy/install/examples_rclcpp_minimal_action_client:/Users/<USER>/ros2_jazzy/install/rclcpp_action:/Users/<USER>/ros2_jazzy/install/examples_rclcpp_minimal_timer:/Users/<USER>/ros2_jazzy/install/examples_rclcpp_minimal_service:/Users/<USER>/ros2_jazzy/install/examples_rclcpp_minimal_client:/Users/<USER>/ros2_jazzy/install/examples_rclcpp_async_client:/Users/<USER>/ros2_jazzy/install/rclcpp:/Users/<USER>/ros2_jazzy/install/libstatistics_collector:/Users/<USER>/ros2_jazzy/install/statistics_msgs:/Users/<USER>/ros2_jazzy/install/sros2_cmake:/Users/<USER>/ros2_jazzy/install/sros2:/Users/<USER>/ros2_jazzy/install/ros_testing:/Users/<USER>/ros2_jazzy/install/ros2trace:/Users/<USER>/ros2_jazzy/install/ros2topic:/Users/<USER>/ros2_jazzy/install/ros2test:/Users/<USER>/ros2_jazzy/install/ros2param:/Users/<USER>/ros2_jazzy/install/ros2lifecycle:/Users/<USER>/ros2_jazzy/install/ros2service:/Users/<USER>/ros2_jazzy/install/ros2run:/Users/<USER>/ros2_jazzy/install/ros2launch:/Users/<USER>/ros2_jazzy/install/ros2pkg:/Users/<USER>/ros2_jazzy/install/ros2node:/Users/<USER>/ros2_jazzy/install/ros2multicast:/Users/<USER>/ros2_jazzy/install/ros2interface:/Users/<USER>/ros2_jazzy/install/ros2action:/Users/<USER>/ros2_jazzy/install/ros2cli:/Users/<USER>/ros2_jazzy/install/launch_testing_ros:/Users/<USER>/ros2_jazzy/install/launch_ros:/Users/<USER>/ros2_jazzy/install/examples_rclpy_minimal_action_server:/Users/<USER>/ros2_jazzy/install/examples_rclpy_minimal_action_client:/Users/<USER>/ros2_jazzy/install/examples_rclpy_guard_conditions:/Users/<USER>/ros2_jazzy/install/action_tutorials_py:/Users/<USER>/ros2_jazzy/install/rclpy:/Users/<USER>/ros2_jazzy/install/rosgraph_msgs:/Users/<USER>/ros2_jazzy/install/rosbag2_interfaces:/Users/<USER>/ros2_jazzy/install/ros2cli_test_interfaces:/Users/<USER>/ros2_jazzy/install/rcl_lifecycle:/Users/<USER>/ros2_jazzy/install/rcl_action:/Users/<USER>/ros2_jazzy/install/rcl:/Users/<USER>/ros2_jazzy/install/rmw_implementation:/Users/<USER>/ros2_jazzy/install/rmw_fastrtps_dynamic_cpp:/Users/<USER>/ros2_jazzy/install/rmw_fastrtps_cpp:/Users/<USER>/ros2_jazzy/install/rmw_fastrtps_shared_cpp:/Users/<USER>/ros2_jazzy/install/rmw_cyclonedds_cpp:/Users/<USER>/ros2_jazzy/install/rmw_connextddsmicro:/Users/<USER>/ros2_jazzy/install/rmw_connextdds:/Users/<USER>/ros2_jazzy/install/rmw_connextdds_common:/Users/<USER>/ros2_jazzy/install/rmw_dds_common:/Users/<USER>/ros2_jazzy/install/composition_interfaces:/Users/<USER>/ros2_jazzy/install/rcl_interfaces:/Users/<USER>/ros2_jazzy/install/pendulum_msgs:/Users/<USER>/ros2_jazzy/install/lifecycle_msgs:/Users/<USER>/ros2_jazzy/install/example_interfaces:/Users/<USER>/ros2_jazzy/install/action_tutorials_interfaces:/Users/<USER>/ros2_jazzy/install/rosidl_default_runtime:/Users/<USER>/ros2_jazzy/install/rosidl_default_generators:/Users/<USER>/ros2_jazzy/install/action_msgs:/Users/<USER>/ros2_jazzy/install/unique_identifier_msgs:/Users/<USER>/ros2_jazzy/install/type_description_interfaces:/Users/<USER>/ros2_jazzy/install/service_msgs:/Users/<USER>/ros2_jazzy/install/builtin_interfaces:/Users/<USER>/ros2_jazzy/install/rosidl_core_runtime:/Users/<USER>/ros2_jazzy/install/rosidl_core_generators:/Users/<USER>/ros2_jazzy/install/rosidl_generator_py:/Users/<USER>/ros2_jazzy/install/ament_lint_common:/Users/<USER>/ros2_jazzy/install/ament_cmake_uncrustify:/Users/<USER>/ros2_jazzy/install/ament_uncrustify:/Users/<USER>/ros2_jazzy/install/uncrustify_vendor:/Users/<USER>/ros2_jazzy/install/tracetools_trace:/Users/<USER>/ros2_jazzy/install/tracetools_read:/Users/<USER>/ros2_jazzy/install/tracetools:/Users/<USER>/ros2_jazzy/install/pluginlib:/Users/<USER>/ros2_jazzy/install/tinyxml2_vendor:/Users/<USER>/ros2_jazzy/install/test_tracetools_launch:/Users/<USER>/ros2_jazzy/install/test_security:/Users/<USER>/ros2_jazzy/install/test_ros2trace:/Users/<USER>/ros2_jazzy/install/test_rmw_implementation:/Users/<USER>/ros2_jazzy/install/test_rclcpp:/Users/<USER>/ros2_jazzy/install/test_quality_of_service:/Users/<USER>/ros2_jazzy/install/test_launch_testing:/Users/<USER>/ros2_jazzy/install/test_launch_ros:/Users/<USER>/ros2_jazzy/install/test_interface_files:/Users/<USER>/ros2_jazzy/install/test_cli_remapping:/Users/<USER>/ros2_jazzy/install/test_cli:/Users/<USER>/ros2_jazzy/install/tango_icons_vendor:/Users/<USER>/ros2_jazzy/install/sqlite3_vendor:/Users/<USER>/ros2_jazzy/install/rcl_logging_spdlog:/Users/<USER>/ros2_jazzy/install/spdlog_vendor:/Users/<USER>/ros2_jazzy/install/shared_queues_vendor:/Users/<USER>/ros2_jazzy/install/rviz_rendering_tests:/Users/<USER>/ros2_jazzy/install/rviz_rendering:/Users/<USER>/ros2_jazzy/install/rviz_ogre_vendor:/Users/<USER>/ros2_jazzy/install/rviz_assimp_vendor:/Users/<USER>/ros2_jazzy/install/rti_connext_dds_cmake_module:/Users/<USER>/ros2_jazzy/install/lttngpy:/Users/<USER>/ros2_jazzy/install/rpyutils:/Users/<USER>/ros2_jazzy/install/rosidl_typesupport_tests:/Users/<USER>/ros2_jazzy/install/rosidl_typesupport_introspection_tests:/Users/<USER>/ros2_jazzy/install/rosidl_typesupport_cpp:/Users/<USER>/ros2_jazzy/install/rosidl_typesupport_introspection_cpp:/Users/<USER>/ros2_jazzy/install/rosidl_typesupport_c:/Users/<USER>/ros2_jazzy/install/rosidl_typesupport_introspection_c:/Users/<USER>/ros2_jazzy/install/rosidl_typesupport_fastrtps_c:/Users/<USER>/ros2_jazzy/install/rosidl_typesupport_fastrtps_cpp:/Users/<USER>/ros2_jazzy/install/rosidl_generator_cpp:/Users/<USER>/ros2_jazzy/install/rosidl_runtime_cpp:/Users/<USER>/ros2_jazzy/install/rosidl_dynamic_typesupport_fastrtps:/Users/<USER>/ros2_jazzy/install/rcl_yaml_param_parser:/Users/<USER>/ros2_jazzy/install/rmw:/Users/<USER>/ros2_jazzy/install/rosidl_dynamic_typesupport:/Users/<USER>/ros2_jazzy/install/rosidl_runtime_c:/Users/<USER>/ros2_jazzy/install/rosidl_generator_c:/Users/<USER>/ros2_jazzy/install/rosidl_typesupport_interface:/Users/<USER>/ros2_jazzy/install/rosidl_runtime_py:/Users/<USER>/ros2_jazzy/install/rosidl_generator_dds_idl:/Users/<USER>/ros2_jazzy/install/rosidl_cmake:/Users/<USER>/ros2_jazzy/install/rosidl_pycommon:/Users/<USER>/ros2_jazzy/install/rosidl_generator_type_description:/Users/<USER>/ros2_jazzy/install/rosidl_parser:/Users/<USER>/ros2_jazzy/install/rosidl_generator_tests:/Users/<USER>/ros2_jazzy/install/rosidl_adapter:/Users/<USER>/ros2_jazzy/install/rosidl_cli:/Users/<USER>/ros2_jazzy/install/rosbag2_tests:/Users/<USER>/ros2_jazzy/install/rosbag2_test_msgdefs:/Users/<USER>/ros2_jazzy/install/ros_environment:/Users/<USER>/ros2_jazzy/install/rmw_implementation_cmake:/Users/<USER>/ros2_jazzy/install/resource_retriever:/Users/<USER>/ros2_jazzy/install/class_loader:/Users/<USER>/ros2_jazzy/install/rcpputils:/Users/<USER>/ros2_jazzy/install/rcl_logging_noop:/Users/<USER>/ros2_jazzy/install/rcl_logging_interface:/Users/<USER>/ros2_jazzy/install/rcutils:/Users/<USER>/ros2_jazzy/install/python_qt_binding:/Users/<USER>/ros2_jazzy/install/python_orocos_kdl_vendor:/Users/<USER>/ros2_jazzy/install/launch_testing_ament_cmake:/Users/<USER>/ros2_jazzy/install/python_cmake_module:/Users/<USER>/ros2_jazzy/install/pybind11_vendor:/Users/<USER>/ros2_jazzy/install/performance_test_fixture:/Users/<USER>/ros2_jazzy/install/launch_pytest:/Users/<USER>/ros2_jazzy/install/launch_testing:/Users/<USER>/ros2_jazzy/install/launch_yaml:/Users/<USER>/ros2_jazzy/install/launch_xml:/Users/<USER>/ros2_jazzy/install/launch:/Users/<USER>/ros2_jazzy/install/osrf_pycommon:/Users/<USER>/ros2_jazzy/install/orocos_kdl_vendor:/Users/<USER>/ros2_jazzy/install/mimick_vendor:/Users/<USER>/ros2_jazzy/install/libyaml_vendor:/Users/<USER>/ros2_jazzy/install/liblz4_vendor:/Users/<USER>/ros2_jazzy/install/libcurl_vendor:/Users/<USER>/ros2_jazzy/install/keyboard_handler:/Users/<USER>/ros2_jazzy/install/gz_math_vendor:/Users/<USER>/ros2_jazzy/install/gz_utils_vendor:/Users/<USER>/ros2_jazzy/install/gz_cmake_vendor:/Users/<USER>/ros2_jazzy/install/ament_cmake_ros:/Users/<USER>/ros2_jazzy/install/ament_cmake_auto:/Users/<USER>/ros2_jazzy/install/ament_cmake_gmock:/Users/<USER>/ros2_jazzy/install/ament_cmake_gtest:/Users/<USER>/ros2_jazzy/install/ament_cmake_google_benchmark:/Users/<USER>/ros2_jazzy/install/fastrtps_cmake_module:/Users/<USER>/ros2_jazzy/install/eigen3_cmake_module:/Users/<USER>/ros2_jazzy/install/domain_coordinator:/Users/<USER>/ros2_jazzy/install/console_bridge_vendor:/Users/<USER>/ros2_jazzy/install/ament_cmake_xmllint:/Users/<USER>/ros2_jazzy/install/ament_xmllint:/Users/<USER>/ros2_jazzy/install/ament_cmake_pyflakes:/Users/<USER>/ros2_jazzy/install/ament_pyflakes:/Users/<USER>/ros2_jazzy/install/ament_cmake_pycodestyle:/Users/<USER>/ros2_jazzy/install/ament_pycodestyle:/Users/<USER>/ros2_jazzy/install/ament_cmake_pep257:/Users/<USER>/ros2_jazzy/install/ament_pep257:/Users/<USER>/ros2_jazzy/install/ament_cmake_pclint:/Users/<USER>/ros2_jazzy/install/ament_pclint:/Users/<USER>/ros2_jazzy/install/ament_lint_auto:/Users/<USER>/ros2_jazzy/install/ament_cmake:/Users/<USER>/ros2_jazzy/install/ament_cmake_version:/Users/<USER>/ros2_jazzy/install/ament_cmake_vendor_package:/Users/<USER>/ros2_jazzy/install/ament_cmake_pytest:/Users/<USER>/ros2_jazzy/install/ament_cmake_mypy:/Users/<USER>/ros2_jazzy/install/ament_cmake_lint_cmake:/Users/<USER>/ros2_jazzy/install/ament_cmake_flake8:/Users/<USER>/ros2_jazzy/install/ament_cmake_cpplint:/Users/<USER>/ros2_jazzy/install/ament_cmake_cppcheck:/Users/<USER>/ros2_jazzy/install/ament_cmake_copyright:/Users/<USER>/ros2_jazzy/install/ament_cmake_clang_tidy:/Users/<USER>/ros2_jazzy/install/ament_cmake_clang_format:/Users/<USER>/ros2_jazzy/install/ament_cmake_test:/Users/<USER>/ros2_jazzy/install/ament_cmake_target_dependencies:/Users/<USER>/ros2_jazzy/install/ament_cmake_python:/Users/<USER>/ros2_jazzy/install/ament_cmake_export_dependencies:/Users/<USER>/ros2_jazzy/install/ament_cmake_libraries:/Users/<USER>/ros2_jazzy/install/ament_cmake_include_directories:/Users/<USER>/ros2_jazzy/install/ament_cmake_gen_version_h:/Users/<USER>/ros2_jazzy/install/ament_cmake_export_targets:/Users/<USER>/ros2_jazzy/install/ament_cmake_export_link_flags:/Users/<USER>/ros2_jazzy/install/ament_cmake_export_interfaces:/Users/<USER>/ros2_jazzy/install/ament_cmake_export_libraries:/Users/<USER>/ros2_jazzy/install/ament_cmake_export_include_directories:/Users/<USER>/ros2_jazzy/install/ament_cmake_export_definitions:/Users/<USER>/ros2_jazzy/install/ament_cmake_core:/Users/<USER>/ros2_jazzy/install/ament_package:/Users/<USER>/ros2_jazzy/install/ament_mypy:/Users/<USER>/ros2_jazzy/install/ament_lint_cmake:/Users/<USER>/ros2_jazzy/install/ament_flake8:/Users/<USER>/ros2_jazzy/install/ament_copyright:/Users/<USER>/ros2_jazzy/install/ament_lint:/Users/<USER>/ros2_jazzy/install/ament_index_python:/Users/<USER>/ros2_jazzy/install/ament_index_cpp:/Users/<USER>/ros2_jazzy/install/ament_cpplint:/Users/<USER>/ros2_jazzy/install/ament_cppcheck:/Users/<USER>/ros2_jazzy/install/ament_clang_tidy:/Users/<USER>/ros2_jazzy/install/ament_clang_format:/Users/<USER>/Work/drill2/onboard/install/tracks_regulator:/Users/<USER>/Work/drill2/onboard/install/state_tracker:/Users/<USER>/Work/drill2/onboard/install/rtk_connector:/Users/<USER>/Work/drill2/onboard/install/remote_connector:/Users/<USER>/Work/drill2/onboard/install/pydubins:/Users/<USER>/Work/drill2/onboard/install/path_follower:/Users/<USER>/Work/drill2/onboard/install/params_server:/Users/<USER>/Work/drill2/onboard/install/modbus_node:/Users/<USER>/Work/drill2/onboard/install/main_state_machine:/Users/<USER>/Work/drill2/onboard/install/maneuver_builder_cpp:/Users/<USER>/Work/drill2/onboard/install/leveler:/Users/<USER>/Work/drill2/onboard/install/launchpack:/Users/<USER>/Work/drill2/onboard/install/can_encoder:/Users/<USER>/Work/drill2/onboard/install/can_decoder:/Users/<USER>/Work/drill2/onboard/install/drill_msgs:/Users/<USER>/Work/drill2/onboard/install/can_msgs', 'NVM_DIR': '/Users/<USER>/.nvm', 'USER': 'frontwise', 'COMMAND_MODE': 'unix2003', 'SSH_AUTH_SOCK': '/private/tmp/com.apple.launchd.QIsZBTGupl/Listeners', '__CF_USER_TEXT_ENCODING': '0x1F5:0x0:0x0', 'TERM_FEATURES': 'T3LrMSc7UUw9Ts3BFGsSyHNoSxF', 'VIRTUAL_ENV': '/Users/<USER>/.ros2_venv', 'TERMINFO_DIRS': '/Applications/iTerm.app/Contents/Resources/terminfo:/usr/share/terminfo', 'PATH': '/Users/<USER>/ros2_jazzy/install/rviz2/bin:/Users/<USER>/ros2_jazzy/install/urdfdom/bin:/Users/<USER>/ros2_jazzy/install/ros2cli/bin:/Users/<USER>/ros2_jazzy/install/ament_uncrustify/bin:/Users/<USER>/ros2_jazzy/install/uncrustify_vendor/opt/uncrustify_vendor/bin:/Users/<USER>/ros2_jazzy/install/rviz_ogre_vendor/opt/rviz_ogre_vendor/bin:/Users/<USER>/ros2_jazzy/install/rosidl_cli/bin:/Users/<USER>/ros2_jazzy/install/launch_testing/bin:/Users/<USER>/ros2_jazzy/install/iceoryx_introspection/bin:/Users/<USER>/ros2_jazzy/install/cyclonedds/bin:/Users/<USER>/ros2_jazzy/install/iceoryx_posh/bin:/Users/<USER>/ros2_jazzy/install/fastrtps/bin:/Users/<USER>/ros2_jazzy/install/foonathan_memory_vendor/bin:/Users/<USER>/ros2_jazzy/install/ament_xmllint/bin:/Users/<USER>/ros2_jazzy/install/ament_pyflakes/bin:/Users/<USER>/ros2_jazzy/install/ament_pycodestyle/bin:/Users/<USER>/ros2_jazzy/install/ament_pep257/bin:/Users/<USER>/ros2_jazzy/install/ament_pclint/bin:/Users/<USER>/ros2_jazzy/install/ament_mypy/bin:/Users/<USER>/ros2_jazzy/install/ament_lint_cmake/bin:/Users/<USER>/ros2_jazzy/install/ament_flake8/bin:/Users/<USER>/ros2_jazzy/install/ament_copyright/bin:/Users/<USER>/ros2_jazzy/install/ament_index_python/bin:/Users/<USER>/ros2_jazzy/install/ament_cpplint/bin:/Users/<USER>/ros2_jazzy/install/ament_cppcheck/bin:/Users/<USER>/ros2_jazzy/install/ament_clang_tidy/bin:/Users/<USER>/ros2_jazzy/install/ament_clang_format/bin:/Users/<USER>/.ros2_venv/bin:/Users/<USER>/.codeium/windsurf/bin:/Users/<USER>/yandex-cloud/bin:/Users/<USER>/.nvm/versions/node/v22.14.0/bin:/opt/homebrew/bin:/opt/homebrew/sbin:/usr/local/bin:/System/Cryptexes/App/usr/bin:/usr/bin:/bin:/usr/sbin:/sbin:/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/local/bin:/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/bin:/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/appleinternal/bin:/opt/X11/bin:/Library/Apple/usr/bin:/usr/local/share/dotnet:~/.dotnet/tools:/Library/Frameworks/Mono.framework/Versions/Current/Commands:/Applications/iTerm.app/Contents/Resources/utilities:/Users/<USER>/.spoofdpi/bin', 'LaunchInstanceID': '672A663D-5DC3-4014-BF99-18B0DF3B92FC', '__CFBundleIdentifier': 'com.googlecode.iterm2', 'PWD': '/Users/<USER>/Work/drill2/onboard/build/leveler', 'COLCON': '1', 'LANG': 'ru_RU.UTF-8', 'ITERM_PROFILE': 'Default', 'XPC_FLAGS': '0x0', 'XPC_SERVICE_NAME': '0', 'OPENSSL_ROOT_DIR': '/opt/homebrew/opt/openssl@3', 'SHLVL': '2', 'HOME': '/Users/<USER>', 'COLORFGBG': '15;0', 'ROS_DISTRO': 'jazzy', 'LC_TERMINAL_VERSION': '3.5.15beta1', 'HOMEBREW_PREFIX': '/opt/homebrew', 'DYLD_LIBRARY_PATH': '/Users/<USER>/Work/drill2/onboard/install/drill_msgs/lib:/Users/<USER>/ros2_jazzy/install/rosbag2_storage_mcap/lib:/Users/<USER>/ros2_jazzy/install/rosbag2_compression_zstd/lib:/Users/<USER>/ros2_jazzy/install/mcap_vendor/lib:/Users/<USER>/ros2_jazzy/install/rviz_default_plugins/lib:/Users/<USER>/ros2_jazzy/install/rviz_common/lib:/Users/<USER>/ros2_jazzy/install/rosbag2_transport/lib:/Users/<USER>/ros2_jazzy/install/rosbag2_storage_sqlite3/lib:/Users/<USER>/ros2_jazzy/install/rosbag2_compression/lib:/Users/<USER>/ros2_jazzy/install/rosbag2_cpp/lib:/Users/<USER>/ros2_jazzy/install/rosbag2_storage/lib:/Users/<USER>/ros2_jazzy/install/camera_info_manager/lib:/Users/<USER>/ros2_jazzy/install/camera_calibration_parsers/lib:/Users/<USER>/ros2_jazzy/install/interactive_markers/lib:/Users/<USER>/ros2_jazzy/install/visualization_msgs/lib:/Users/<USER>/ros2_jazzy/install/robot_state_publisher/lib:/Users/<USER>/ros2_jazzy/install/kdl_parser/lib:/Users/<USER>/ros2_jazzy/install/urdf/lib:/Users/<USER>/ros2_jazzy/install/urdfdom/lib:/Users/<USER>/ros2_jazzy/install/turtlesim/lib:/Users/<USER>/ros2_jazzy/install/trajectory_msgs/lib:/Users/<USER>/ros2_jazzy/install/tf2_ros/lib:/Users/<USER>/ros2_jazzy/install/tf2_msgs/lib:/Users/<USER>/ros2_jazzy/install/tf2_eigen_kdl/lib:/Users/<USER>/ros2_jazzy/install/laser_geometry/lib:/Users/<USER>/ros2_jazzy/install/tf2/lib:/Users/<USER>/ros2_jazzy/install/test_msgs/lib:/Users/<USER>/ros2_jazzy/install/stereo_msgs/lib:/Users/<USER>/ros2_jazzy/install/std_srvs/lib:/Users/<USER>/ros2_jazzy/install/shape_msgs/lib:/Users/<USER>/ros2_jazzy/install/quality_of_service_demo_cpp/lib:/Users/<USER>/ros2_jazzy/install/point_cloud_transport/lib:/Users/<USER>/ros2_jazzy/install/map_msgs/lib:/Users/<USER>/ros2_jazzy/install/image_transport/lib:/Users/<USER>/ros2_jazzy/install/image_tools/lib:/Users/<USER>/ros2_jazzy/install/sensor_msgs/lib:/Users/<USER>/ros2_jazzy/install/nav_msgs/lib:/Users/<USER>/ros2_jazzy/install/message_filters/lib:/Users/<USER>/ros2_jazzy/install/logging_demo/lib:/Users/<USER>/ros2_jazzy/install/diagnostic_msgs/lib:/Users/<USER>/ros2_jazzy/install/geometry_msgs/lib:/Users/<USER>/ros2_jazzy/install/examples_rclcpp_wait_set/lib:/Users/<USER>/ros2_jazzy/install/examples_rclcpp_minimal_subscriber/lib:/Users/<USER>/ros2_jazzy/install/examples_rclcpp_minimal_composition/lib:/Users/<USER>/ros2_jazzy/install/demo_nodes_cpp_native/lib:/Users/<USER>/ros2_jazzy/install/demo_nodes_cpp/lib:/Users/<USER>/ros2_jazzy/install/composition/lib:/Users/<USER>/ros2_jazzy/install/actionlib_msgs/lib:/Users/<USER>/ros2_jazzy/install/std_msgs/lib:/Users/<USER>/ros2_jazzy/install/rclcpp_lifecycle/lib:/Users/<USER>/ros2_jazzy/install/action_tutorials_cpp/lib:/Users/<USER>/ros2_jazzy/install/rclcpp_components/lib:/Users/<USER>/ros2_jazzy/install/rclcpp_action/lib:/Users/<USER>/ros2_jazzy/install/rclcpp/lib:/Users/<USER>/ros2_jazzy/install/libstatistics_collector/lib:/Users/<USER>/ros2_jazzy/install/statistics_msgs/lib:/Users/<USER>/ros2_jazzy/install/rclpy/lib:/Users/<USER>/ros2_jazzy/install/rosgraph_msgs/lib:/Users/<USER>/ros2_jazzy/install/rosbag2_interfaces/lib:/Users/<USER>/ros2_jazzy/install/ros2cli_test_interfaces/lib:/Users/<USER>/ros2_jazzy/install/rcl_lifecycle/lib:/Users/<USER>/ros2_jazzy/install/rcl_action/lib:/Users/<USER>/ros2_jazzy/install/rcl/lib:/Users/<USER>/ros2_jazzy/install/rmw_implementation/lib:/Users/<USER>/ros2_jazzy/install/rmw_fastrtps_dynamic_cpp/lib:/Users/<USER>/ros2_jazzy/install/rmw_fastrtps_cpp/lib:/Users/<USER>/ros2_jazzy/install/rmw_fastrtps_shared_cpp/lib:/Users/<USER>/ros2_jazzy/install/rmw_cyclonedds_cpp/lib:/Users/<USER>/ros2_jazzy/install/rmw_dds_common/lib:/Users/<USER>/ros2_jazzy/install/composition_interfaces/lib:/Users/<USER>/ros2_jazzy/install/rcl_interfaces/lib:/Users/<USER>/ros2_jazzy/install/pendulum_msgs/lib:/Users/<USER>/ros2_jazzy/install/lifecycle_msgs/lib:/Users/<USER>/ros2_jazzy/install/example_interfaces/lib:/Users/<USER>/ros2_jazzy/install/action_tutorials_interfaces/lib:/Users/<USER>/ros2_jazzy/install/action_msgs/lib:/Users/<USER>/ros2_jazzy/install/unique_identifier_msgs/lib:/Users/<USER>/ros2_jazzy/install/type_description_interfaces/lib:/Users/<USER>/ros2_jazzy/install/service_msgs/lib:/Users/<USER>/ros2_jazzy/install/builtin_interfaces/lib:/Users/<USER>/ros2_jazzy/install/rcl_logging_spdlog/lib:/Users/<USER>/ros2_jazzy/install/rviz_rendering/lib:/Users/<USER>/ros2_jazzy/install/rviz_ogre_vendor/opt/rviz_ogre_vendor/lib:/Users/<USER>/ros2_jazzy/install/rosidl_typesupport_cpp/lib:/Users/<USER>/ros2_jazzy/install/rosidl_typesupport_introspection_cpp/lib:/Users/<USER>/ros2_jazzy/install/rosidl_typesupport_c/lib:/Users/<USER>/ros2_jazzy/install/rosidl_typesupport_introspection_c/lib:/Users/<USER>/ros2_jazzy/install/rosidl_typesupport_fastrtps_c/lib:/Users/<USER>/ros2_jazzy/install/rosidl_typesupport_fastrtps_cpp/lib:/Users/<USER>/ros2_jazzy/install/rosidl_dynamic_typesupport_fastrtps/lib:/Users/<USER>/ros2_jazzy/install/rcl_yaml_param_parser/lib:/Users/<USER>/ros2_jazzy/install/rmw/lib:/Users/<USER>/ros2_jazzy/install/rosidl_dynamic_typesupport/lib:/Users/<USER>/ros2_jazzy/install/rosidl_runtime_c/lib:/Users/<USER>/ros2_jazzy/install/rosbag2_test_msgdefs/lib:/Users/<USER>/ros2_jazzy/install/resource_retriever/lib:/Users/<USER>/ros2_jazzy/install/class_loader/lib:/Users/<USER>/ros2_jazzy/install/rcpputils/lib:/Users/<USER>/ros2_jazzy/install/rcl_logging_noop/lib:/Users/<USER>/ros2_jazzy/install/rcl_logging_interface/lib:/Users/<USER>/ros2_jazzy/install/rcutils/lib:/Users/<USER>/ros2_jazzy/install/performance_test_fixture/lib:/Users/<USER>/ros2_jazzy/install/osrf_testing_tools_cpp/lib:/Users/<USER>/ros2_jazzy/install/mimick_vendor/opt/mimick_vendor/lib:/Users/<USER>/ros2_jazzy/install/keyboard_handler/lib:/Users/<USER>/ros2_jazzy/install/iceoryx_introspection/lib:/Users/<USER>/ros2_jazzy/install/cyclonedds/lib:/Users/<USER>/ros2_jazzy/install/iceoryx_posh/lib:/Users/<USER>/ros2_jazzy/install/iceoryx_hoofs/lib:/Users/<USER>/ros2_jazzy/install/iceoryx_binding_c/lib:/Users/<USER>/ros2_jazzy/install/gz_math_vendor/opt/gz_math_vendor/lib:/Users/<USER>/ros2_jazzy/install/gz_utils_vendor/opt/gz_utils_vendor/lib:/Users/<USER>/ros2_jazzy/install/gz_cmake_vendor/opt/gz_cmake_vendor/lib:/Users/<USER>/ros2_jazzy/install/fastrtps/lib:/Users/<USER>/ros2_jazzy/install/fastcdr/lib:/Users/<USER>/ros2_jazzy/install/google_benchmark_vendor/lib:/Users/<USER>/ros2_jazzy/install/ament_index_cpp/lib:/Users/<USER>/Work/drill2/onboard/install/can_msgs/lib', 'ITERM_SESSION_ID': 'w0t3p0:22583761-CC84-481F-B886-691DB3AE381C', 'PYTHONPATH': '/Users/<USER>/Work/drill2/onboard/build/leveler/prefix_override:/Users/<USER>/.ros2_venv/lib/python3.11/site-packages/colcon_core/task/python/colcon_distutils_commands:/Users/<USER>/Work/drill2/onboard/install/leveler/lib/python3.11/site-packages:/Users/<USER>/Work/drill2/onboard/install/base_node/lib/python3.11/site-packages:/Users/<USER>/Work/drill2/onboard/install/drill_msgs/lib/python3.11/site-packages:/Users/<USER>/Work/drill2/onboard/install/depth_tracker/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/install/rosbag2_storage_mcap/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/build/rosbag2_examples_py:/Users/<USER>/ros2_jazzy/install/rosbag2_examples_py/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/build/launch_testing_examples:/Users/<USER>/ros2_jazzy/install/launch_testing_examples/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/build/ros2bag:/Users/<USER>/ros2_jazzy/install/ros2bag/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/install/rosbag2_py/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/install/rosbag2_storage_sqlite3/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/install/interactive_markers/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/install/visualization_msgs/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/install/turtlesim/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/install/trajectory_msgs/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/build/tracetools_test:/Users/<USER>/ros2_jazzy/install/tracetools_test/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/build/tracetools_launch:/Users/<USER>/ros2_jazzy/install/tracetools_launch/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/build/topic_monitor:/Users/<USER>/ros2_jazzy/install/topic_monitor/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/build/tf2_tools:/Users/<USER>/ros2_jazzy/install/tf2_tools/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/install/tf2_sensor_msgs/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/install/tf2_kdl/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/install/tf2_geometry_msgs/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/build/examples_tf2_py:/Users/<USER>/ros2_jazzy/install/examples_tf2_py/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/build/tf2_ros_py:/Users/<USER>/ros2_jazzy/install/tf2_ros_py/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/install/tf2_py/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/install/tf2_msgs/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/install/laser_geometry/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/install/rosbag2_test_common/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/install/test_msgs/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/install/stereo_msgs/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/install/std_srvs/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/install/shape_msgs/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/build/examples_rclpy_pointcloud_publisher:/Users/<USER>/ros2_jazzy/install/examples_rclpy_pointcloud_publisher/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/build/sensor_msgs_py:/Users/<USER>/ros2_jazzy/install/sensor_msgs_py/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/build/quality_of_service_demo_py:/Users/<USER>/ros2_jazzy/install/quality_of_service_demo_py/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/install/point_cloud_transport_py/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/install/map_msgs/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/build/camera_info_manager_py:/Users/<USER>/ros2_jazzy/install/camera_info_manager_py/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/install/sensor_msgs/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/build/ros2doctor:/Users/<USER>/ros2_jazzy/install/ros2doctor/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/install/nav_msgs/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/install/message_filters/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/install/logging_demo/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/build/lifecycle_py:/Users/<USER>/ros2_jazzy/install/lifecycle_py/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/install/diagnostic_msgs/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/install/geometry_msgs/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/build/examples_rclpy_minimal_subscriber:/Users/<USER>/ros2_jazzy/install/examples_rclpy_minimal_subscriber/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/build/examples_rclpy_minimal_service:/Users/<USER>/ros2_jazzy/install/examples_rclpy_minimal_service/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/build/examples_rclpy_minimal_publisher:/Users/<USER>/ros2_jazzy/install/examples_rclpy_minimal_publisher/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/build/examples_rclpy_minimal_client:/Users/<USER>/ros2_jazzy/install/examples_rclpy_minimal_client/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/build/examples_rclpy_executors:/Users/<USER>/ros2_jazzy/install/examples_rclpy_executors/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/build/demo_nodes_py:/Users/<USER>/ros2_jazzy/install/demo_nodes_py/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/install/actionlib_msgs/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/install/std_msgs/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/build/ros2component:/Users/<USER>/ros2_jazzy/install/ros2component/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/install/statistics_msgs/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/build/sros2:/Users/<USER>/ros2_jazzy/install/sros2/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/build/ros2trace:/Users/<USER>/ros2_jazzy/install/ros2trace/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/build/ros2topic:/Users/<USER>/ros2_jazzy/install/ros2topic/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/build/ros2test:/Users/<USER>/ros2_jazzy/install/ros2test/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/build/ros2param:/Users/<USER>/ros2_jazzy/install/ros2param/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/build/ros2lifecycle:/Users/<USER>/ros2_jazzy/install/ros2lifecycle/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/build/ros2service:/Users/<USER>/ros2_jazzy/install/ros2service/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/build/ros2run:/Users/<USER>/ros2_jazzy/install/ros2run/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/build/ros2launch:/Users/<USER>/ros2_jazzy/install/ros2launch/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/build/ros2pkg:/Users/<USER>/ros2_jazzy/install/ros2pkg/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/build/ros2node:/Users/<USER>/ros2_jazzy/install/ros2node/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/build/ros2multicast:/Users/<USER>/ros2_jazzy/install/ros2multicast/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/build/ros2interface:/Users/<USER>/ros2_jazzy/install/ros2interface/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/build/ros2action:/Users/<USER>/ros2_jazzy/install/ros2action/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/build/ros2cli:/Users/<USER>/ros2_jazzy/install/ros2cli/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/build/launch_testing_ros:/Users/<USER>/ros2_jazzy/install/launch_testing_ros/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/build/launch_ros:/Users/<USER>/ros2_jazzy/install/launch_ros/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/build/examples_rclpy_minimal_action_server:/Users/<USER>/ros2_jazzy/install/examples_rclpy_minimal_action_server/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/build/examples_rclpy_minimal_action_client:/Users/<USER>/ros2_jazzy/install/examples_rclpy_minimal_action_client/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/build/examples_rclpy_guard_conditions:/Users/<USER>/ros2_jazzy/install/examples_rclpy_guard_conditions/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/build/action_tutorials_py:/Users/<USER>/ros2_jazzy/install/action_tutorials_py/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/install/rclpy/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/install/rosgraph_msgs/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/install/rosbag2_interfaces/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/install/ros2cli_test_interfaces/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/install/rmw_dds_common/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/install/composition_interfaces/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/install/rcl_interfaces/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/install/pendulum_msgs/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/install/lifecycle_msgs/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/install/example_interfaces/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/install/action_tutorials_interfaces/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/install/action_msgs/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/install/unique_identifier_msgs/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/install/type_description_interfaces/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/install/service_msgs/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/install/builtin_interfaces/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/install/rosidl_generator_py/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/build/ament_uncrustify:/Users/<USER>/ros2_jazzy/install/ament_uncrustify/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/build/tracetools_trace:/Users/<USER>/ros2_jazzy/install/tracetools_trace/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/build/tracetools_read:/Users/<USER>/ros2_jazzy/install/tracetools_read/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/build/test_tracetools_launch:/Users/<USER>/ros2_jazzy/install/test_tracetools_launch/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/build/test_ros2trace:/Users/<USER>/ros2_jazzy/install/test_ros2trace/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/build/test_launch_ros:/Users/<USER>/ros2_jazzy/install/test_launch_ros/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/install/lttngpy/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/build/rpyutils:/Users/<USER>/ros2_jazzy/install/rpyutils/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/install/rosidl_typesupport_cpp/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/install/rosidl_typesupport_introspection_cpp/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/install/rosidl_typesupport_c/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/install/rosidl_typesupport_introspection_c/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/install/rosidl_typesupport_fastrtps_c/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/install/rosidl_typesupport_fastrtps_cpp/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/install/rosidl_generator_cpp/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/install/rosidl_generator_c/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/build/rosidl_runtime_py:/Users/<USER>/ros2_jazzy/install/rosidl_runtime_py/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/install/rosidl_generator_dds_idl/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/install/rosidl_cmake/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/build/rosidl_pycommon:/Users/<USER>/ros2_jazzy/install/rosidl_pycommon/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/install/rosidl_generator_type_description/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/install/rosidl_parser/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/install/rosidl_adapter/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/build/rosidl_cli:/Users/<USER>/ros2_jazzy/install/rosidl_cli/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/install/rosbag2_test_msgdefs/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/install/resource_retriever/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/install/rcutils/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/install/python_qt_binding/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/install/python_orocos_kdl_vendor/lib/python3.11/dist-packages:/Users/<USER>/ros2_jazzy/build/launch_pytest:/Users/<USER>/ros2_jazzy/install/launch_pytest/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/build/launch_testing:/Users/<USER>/ros2_jazzy/install/launch_testing/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/build/launch_yaml:/Users/<USER>/ros2_jazzy/install/launch_yaml/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/build/launch_xml:/Users/<USER>/ros2_jazzy/install/launch_xml/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/build/launch:/Users/<USER>/ros2_jazzy/install/launch/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/build/osrf_pycommon:/Users/<USER>/ros2_jazzy/install/osrf_pycommon/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/install/ament_cmake_google_benchmark/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/build/domain_coordinator:/Users/<USER>/ros2_jazzy/install/domain_coordinator/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/build/ament_xmllint:/Users/<USER>/ros2_jazzy/install/ament_xmllint/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/build/ament_pyflakes:/Users/<USER>/ros2_jazzy/install/ament_pyflakes/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/build/ament_pycodestyle:/Users/<USER>/ros2_jazzy/install/ament_pycodestyle/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/build/ament_pep257:/Users/<USER>/ros2_jazzy/install/ament_pep257/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/build/ament_pclint:/Users/<USER>/ros2_jazzy/install/ament_pclint/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/install/ament_cmake_test/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/build/ament_package:/Users/<USER>/ros2_jazzy/install/ament_package/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/build/ament_mypy:/Users/<USER>/ros2_jazzy/install/ament_mypy/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/build/ament_lint_cmake:/Users/<USER>/ros2_jazzy/install/ament_lint_cmake/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/build/ament_flake8:/Users/<USER>/ros2_jazzy/install/ament_flake8/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/build/ament_copyright:/Users/<USER>/ros2_jazzy/install/ament_copyright/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/build/ament_lint:/Users/<USER>/ros2_jazzy/install/ament_lint/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/build/ament_index_python:/Users/<USER>/ros2_jazzy/install/ament_index_python/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/build/ament_cpplint:/Users/<USER>/ros2_jazzy/install/ament_cpplint/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/build/ament_cppcheck:/Users/<USER>/ros2_jazzy/install/ament_cppcheck/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/build/ament_clang_tidy:/Users/<USER>/ros2_jazzy/install/ament_clang_tidy/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/build/ament_clang_format:/Users/<USER>/ros2_jazzy/install/ament_clang_format/lib/python3.11/site-packages:/Users/<USER>/Work/drill2/onboard/install/tracks_regulator/lib/python3.11/site-packages:/Users/<USER>/Work/drill2/onboard/install/state_tracker/lib/python3.11/site-packages:/Users/<USER>/Work/drill2/onboard/install/rtk_connector/lib/python3.11/site-packages:/Users/<USER>/Work/drill2/onboard/install/remote_connector/lib/python3.11/site-packages:/Users/<USER>/Work/drill2/onboard/install/pydubins/lib/python3.11/site-packages:/Users/<USER>/Work/drill2/onboard/install/path_follower/lib/python3.11/site-packages:/Users/<USER>/Work/drill2/onboard/install/params_server/lib/python3.11/site-packages:/Users/<USER>/Work/drill2/onboard/install/modbus_node/lib/python3.11/site-packages:/Users/<USER>/Work/drill2/onboard/install/main_state_machine/lib/python3.11/site-packages:/Users/<USER>/Work/drill2/onboard/install/maneuver_builder_cpp/lib/python3.11/site-packages:/Users/<USER>/Work/drill2/onboard/install/leveler/lib/python3.11/site-packages:/Users/<USER>/Work/drill2/onboard/install/launchpack/lib/python3.11/site-packages:/Users/<USER>/Work/drill2/onboard/install/can_encoder/lib/python3.11/site-packages:/Users/<USER>/Work/drill2/onboard/install/can_decoder/lib/python3.11/site-packages:/Users/<USER>/Work/drill2/onboard/install/can_msgs/lib/python3.11/site-packages:/Users/<USER>/Work/drill2/onboard/install/external/maneuver_builder_cpp/lib/python3.11/site-packages:/Users/<USER>/Work/drill2/onboard/install/external/maneuver_builder_cpp/lib/python3.11/site-packages', 'LOGNAME': 'frontwise', 'ROS_AUTOMATIC_DISCOVERY_RANGE': 'SUBNET', 'PKG_CONFIG_PATH': '/Users/<USER>/ros2_jazzy/install/urdfdom/lib/pkgconfig:/Users/<USER>/ros2_jazzy/install/urdfdom_headers/lib/pkgconfig:/Users/<USER>/ros2_jazzy/install/osrf_testing_tools_cpp/lib/pkgconfig:/Users/<USER>/ros2_jazzy/install/iceoryx_introspection/lib/pkgconfig:/Users/<USER>/ros2_jazzy/install/cyclonedds/lib/pkgconfig:/Users/<USER>/ros2_jazzy/install/iceoryx_posh/lib/pkgconfig:/Users/<USER>/ros2_jazzy/install/iceoryx_hoofs/lib/pkgconfig:/Users/<USER>/ros2_jazzy/install/iceoryx_binding_c/lib/pkgconfig:/Users/<USER>/ros2_jazzy/install/gmock_vendor/lib/pkgconfig:/Users/<USER>/ros2_jazzy/install/gtest_vendor/lib/pkgconfig:/Users/<USER>/ros2_jazzy/install/google_benchmark_vendor/lib/pkgconfig:/Users/<USER>/ros2_jazzy/install/foonathan_memory_vendor/lib/pkgconfig', 'NVM_BIN': '/Users/<USER>/.nvm/versions/node/v22.14.0/bin', 'INFOPATH': '/opt/homebrew/share/info:', 'HOMEBREW_CELLAR': '/opt/homebrew/Cellar', 'CMAKE_PREFIX_PATH': '/Users/<USER>/Work/drill2/onboard/install/drill_msgs:/opt/homebrew/opt/qt@5/lib/cmake:/opt/homebrew/opt:/opt/homebrew/opt/qt@5/lib/cmake:/opt/homebrew/opt:/opt/homebrew/opt/qt@5/lib/cmake:/opt/homebrew/opt:/Users/<USER>/ros2_jazzy/install/rosbag2:/Users/<USER>/ros2_jazzy/install/rosbag2_storage_default_plugins:/Users/<USER>/ros2_jazzy/install/rosbag2_storage_mcap:/Users/<USER>/ros2_jazzy/install/rosbag2_compression_zstd:/Users/<USER>/ros2_jazzy/install/mcap_vendor:/Users/<USER>/ros2_jazzy/install/zstd_vendor:/Users/<USER>/ros2_jazzy/install/rviz_visual_testing_framework:/Users/<USER>/ros2_jazzy/install/rviz2:/Users/<USER>/ros2_jazzy/install/rviz_default_plugins:/Users/<USER>/ros2_jazzy/install/rviz_common:/Users/<USER>/ros2_jazzy/install/rosbag2_py:/Users/<USER>/ros2_jazzy/install/rosbag2_transport:/Users/<USER>/ros2_jazzy/install/rosbag2_storage_sqlite3:/Users/<USER>/ros2_jazzy/install/rosbag2_examples_cpp:/Users/<USER>/ros2_jazzy/install/rosbag2_compression:/Users/<USER>/ros2_jazzy/install/rosbag2_cpp:/Users/<USER>/ros2_jazzy/install/rosbag2_storage:/Users/<USER>/ros2_jazzy/install/image_common:/Users/<USER>/ros2_jazzy/install/camera_info_manager:/Users/<USER>/ros2_jazzy/install/camera_calibration_parsers:/Users/<USER>/ros2_jazzy/install/yaml_cpp_vendor:/Users/<USER>/ros2_jazzy/install/interactive_markers:/Users/<USER>/ros2_jazzy/install/common_interfaces:/Users/<USER>/ros2_jazzy/install/visualization_msgs:/Users/<USER>/ros2_jazzy/install/dummy_robot_bringup:/Users/<USER>/ros2_jazzy/install/robot_state_publisher:/Users/<USER>/ros2_jazzy/install/kdl_parser:/Users/<USER>/ros2_jazzy/install/urdf:/Users/<USER>/ros2_jazzy/install/urdfdom:/Users/<USER>/ros2_jazzy/install/urdf_parser_plugin:/Users/<USER>/ros2_jazzy/install/urdfdom_headers:/Users/<USER>/ros2_jazzy/install/turtlesim:/Users/<USER>/ros2_jazzy/install/trajectory_msgs:/Users/<USER>/ros2_jazzy/install/topic_statistics_demo:/Users/<USER>/ros2_jazzy/install/geometry2:/Users/<USER>/ros2_jazzy/install/tf2_sensor_msgs:/Users/<USER>/ros2_jazzy/install/test_tf2:/Users/<USER>/ros2_jazzy/install/tf2_kdl:/Users/<USER>/ros2_jazzy/install/tf2_geometry_msgs:/Users/<USER>/ros2_jazzy/install/tf2_eigen:/Users/<USER>/ros2_jazzy/install/tf2_bullet:/Users/<USER>/ros2_jazzy/install/tf2_ros:/Users/<USER>/ros2_jazzy/install/tf2_py:/Users/<USER>/ros2_jazzy/install/tf2_msgs:/Users/<USER>/ros2_jazzy/install/tf2_eigen_kdl:/Users/<USER>/ros2_jazzy/install/laser_geometry:/Users/<USER>/ros2_jazzy/install/tf2:/Users/<USER>/ros2_jazzy/install/test_tracetools:/Users/<USER>/ros2_jazzy/install/rosbag2_test_common:/Users/<USER>/ros2_jazzy/install/test_msgs:/Users/<USER>/ros2_jazzy/install/test_communication:/Users/<USER>/ros2_jazzy/install/stereo_msgs:/Users/<USER>/ros2_jazzy/install/std_srvs:/Users/<USER>/ros2_jazzy/install/shape_msgs:/Users/<USER>/ros2_jazzy/install/quality_of_service_demo_cpp:/Users/<USER>/ros2_jazzy/install/point_cloud_transport_py:/Users/<USER>/ros2_jazzy/install/point_cloud_transport:/Users/<USER>/ros2_jazzy/install/map_msgs:/Users/<USER>/ros2_jazzy/install/intra_process_demo:/Users/<USER>/ros2_jazzy/install/image_transport:/Users/<USER>/ros2_jazzy/install/image_tools:/Users/<USER>/ros2_jazzy/install/dummy_sensors:/Users/<USER>/ros2_jazzy/install/sensor_msgs:/Users/<USER>/ros2_jazzy/install/ros2cli_common_extensions:/Users/<USER>/ros2_jazzy/install/dummy_map_server:/Users/<USER>/ros2_jazzy/install/nav_msgs:/Users/<USER>/ros2_jazzy/install/message_filters:/Users/<USER>/ros2_jazzy/install/logging_demo:/Users/<USER>/ros2_jazzy/install/lifecycle:/Users/<USER>/ros2_jazzy/install/diagnostic_msgs:/Users/<USER>/ros2_jazzy/install/geometry_msgs:/Users/<USER>/ros2_jazzy/install/examples_rclcpp_wait_set:/Users/<USER>/ros2_jazzy/install/examples_rclcpp_multithreaded_executor:/Users/<USER>/ros2_jazzy/install/examples_rclcpp_minimal_subscriber:/Users/<USER>/ros2_jazzy/install/examples_rclcpp_minimal_publisher:/Users/<USER>/ros2_jazzy/install/examples_rclcpp_minimal_composition:/Users/<USER>/ros2_jazzy/install/examples_rclcpp_cbg_executor:/Users/<USER>/ros2_jazzy/install/demo_nodes_cpp_native:/Users/<USER>/ros2_jazzy/install/demo_nodes_cpp:/Users/<USER>/ros2_jazzy/install/composition:/Users/<USER>/ros2_jazzy/install/actionlib_msgs:/Users/<USER>/ros2_jazzy/install/std_msgs:/Users/<USER>/ros2_jazzy/install/ros2lifecycle_test_fixtures:/Users/<USER>/ros2_jazzy/install/rclcpp_lifecycle:/Users/<USER>/ros2_jazzy/install/action_tutorials_cpp:/Users/<USER>/ros2_jazzy/install/rclcpp_components:/Users/<USER>/ros2_jazzy/install/examples_rclcpp_minimal_action_server:/Users/<USER>/ros2_jazzy/install/examples_rclcpp_minimal_action_client:/Users/<USER>/ros2_jazzy/install/rclcpp_action:/Users/<USER>/ros2_jazzy/install/examples_rclcpp_minimal_timer:/Users/<USER>/ros2_jazzy/install/examples_rclcpp_minimal_service:/Users/<USER>/ros2_jazzy/install/examples_rclcpp_minimal_client:/Users/<USER>/ros2_jazzy/install/examples_rclcpp_async_client:/Users/<USER>/ros2_jazzy/install/rclcpp:/Users/<USER>/ros2_jazzy/install/libstatistics_collector:/Users/<USER>/ros2_jazzy/install/statistics_msgs:/Users/<USER>/ros2_jazzy/install/sros2_cmake:/Users/<USER>/ros2_jazzy/install/ros_testing:/Users/<USER>/ros2_jazzy/install/rclpy:/Users/<USER>/ros2_jazzy/install/rosgraph_msgs:/Users/<USER>/ros2_jazzy/install/rosbag2_interfaces:/Users/<USER>/ros2_jazzy/install/ros2cli_test_interfaces:/Users/<USER>/ros2_jazzy/install/rcl_lifecycle:/Users/<USER>/ros2_jazzy/install/rcl_action:/Users/<USER>/ros2_jazzy/install/rcl:/Users/<USER>/ros2_jazzy/install/rmw_implementation:/Users/<USER>/ros2_jazzy/install/rmw_fastrtps_dynamic_cpp:/Users/<USER>/ros2_jazzy/install/rmw_fastrtps_cpp:/Users/<USER>/ros2_jazzy/install/rmw_fastrtps_shared_cpp:/Users/<USER>/ros2_jazzy/install/rmw_cyclonedds_cpp:/Users/<USER>/ros2_jazzy/install/rmw_connextddsmicro:/Users/<USER>/ros2_jazzy/install/rmw_connextdds:/Users/<USER>/ros2_jazzy/install/rmw_connextdds_common:/Users/<USER>/ros2_jazzy/install/rmw_dds_common:/Users/<USER>/ros2_jazzy/install/composition_interfaces:/Users/<USER>/ros2_jazzy/install/rcl_interfaces:/Users/<USER>/ros2_jazzy/install/pendulum_msgs:/Users/<USER>/ros2_jazzy/install/lifecycle_msgs:/Users/<USER>/ros2_jazzy/install/example_interfaces:/Users/<USER>/ros2_jazzy/install/action_tutorials_interfaces:/Users/<USER>/ros2_jazzy/install/rosidl_default_runtime:/Users/<USER>/ros2_jazzy/install/rosidl_default_generators:/Users/<USER>/ros2_jazzy/install/action_msgs:/Users/<USER>/ros2_jazzy/install/unique_identifier_msgs:/Users/<USER>/ros2_jazzy/install/type_description_interfaces:/Users/<USER>/ros2_jazzy/install/service_msgs:/Users/<USER>/ros2_jazzy/install/builtin_interfaces:/Users/<USER>/ros2_jazzy/install/rosidl_core_runtime:/Users/<USER>/ros2_jazzy/install/rosidl_core_generators:/Users/<USER>/ros2_jazzy/install/rosidl_generator_py:/Users/<USER>/ros2_jazzy/install/ament_lint_common:/Users/<USER>/ros2_jazzy/install/ament_cmake_uncrustify:/Users/<USER>/ros2_jazzy/install/uncrustify_vendor:/Users/<USER>/ros2_jazzy/install/tracetools:/Users/<USER>/ros2_jazzy/install/pluginlib:/Users/<USER>/ros2_jazzy/install/tinyxml2_vendor:/Users/<USER>/ros2_jazzy/install/test_security:/Users/<USER>/ros2_jazzy/install/test_rmw_implementation:/Users/<USER>/ros2_jazzy/install/test_rclcpp:/Users/<USER>/ros2_jazzy/install/test_quality_of_service:/Users/<USER>/ros2_jazzy/install/test_launch_testing:/Users/<USER>/ros2_jazzy/install/test_interface_files:/Users/<USER>/ros2_jazzy/install/test_cli_remapping:/Users/<USER>/ros2_jazzy/install/test_cli:/Users/<USER>/ros2_jazzy/install/tango_icons_vendor:/Users/<USER>/ros2_jazzy/install/sqlite3_vendor:/Users/<USER>/ros2_jazzy/install/rcl_logging_spdlog:/Users/<USER>/ros2_jazzy/install/spdlog_vendor:/Users/<USER>/ros2_jazzy/install/shared_queues_vendor:/Users/<USER>/ros2_jazzy/install/rviz_rendering_tests:/Users/<USER>/ros2_jazzy/install/rviz_rendering:/Users/<USER>/ros2_jazzy/install/rviz_ogre_vendor:/Users/<USER>/ros2_jazzy/install/rviz_assimp_vendor:/Users/<USER>/ros2_jazzy/install/rti_connext_dds_cmake_module:/Users/<USER>/ros2_jazzy/install/lttngpy:/Users/<USER>/ros2_jazzy/install/rosidl_typesupport_tests:/Users/<USER>/ros2_jazzy/install/rosidl_typesupport_introspection_tests:/Users/<USER>/ros2_jazzy/install/rosidl_typesupport_cpp:/Users/<USER>/ros2_jazzy/install/rosidl_typesupport_introspection_cpp:/Users/<USER>/ros2_jazzy/install/rosidl_typesupport_c:/Users/<USER>/ros2_jazzy/install/rosidl_typesupport_introspection_c:/Users/<USER>/ros2_jazzy/install/rosidl_typesupport_fastrtps_c:/Users/<USER>/ros2_jazzy/install/rosidl_typesupport_fastrtps_cpp:/Users/<USER>/ros2_jazzy/install/rosidl_generator_cpp:/Users/<USER>/ros2_jazzy/install/rosidl_runtime_cpp:/Users/<USER>/ros2_jazzy/install/rosidl_dynamic_typesupport_fastrtps:/Users/<USER>/ros2_jazzy/install/rcl_yaml_param_parser:/Users/<USER>/ros2_jazzy/install/rmw:/Users/<USER>/ros2_jazzy/install/rosidl_dynamic_typesupport:/Users/<USER>/ros2_jazzy/install/rosidl_runtime_c:/Users/<USER>/ros2_jazzy/install/rosidl_generator_c:/Users/<USER>/ros2_jazzy/install/rosidl_typesupport_interface:/Users/<USER>/ros2_jazzy/install/rosidl_generator_dds_idl:/Users/<USER>/ros2_jazzy/install/rosidl_cmake:/Users/<USER>/ros2_jazzy/install/rosidl_generator_type_description:/Users/<USER>/ros2_jazzy/install/rosidl_parser:/Users/<USER>/ros2_jazzy/install/rosidl_generator_tests:/Users/<USER>/ros2_jazzy/install/rosidl_adapter:/Users/<USER>/ros2_jazzy/install/rosbag2_tests:/Users/<USER>/ros2_jazzy/install/rosbag2_test_msgdefs:/Users/<USER>/ros2_jazzy/install/ros_environment:/Users/<USER>/ros2_jazzy/install/rmw_implementation_cmake:/Users/<USER>/ros2_jazzy/install/resource_retriever:/Users/<USER>/ros2_jazzy/install/class_loader:/Users/<USER>/ros2_jazzy/install/rcpputils:/Users/<USER>/ros2_jazzy/install/rcl_logging_noop:/Users/<USER>/ros2_jazzy/install/rcl_logging_interface:/Users/<USER>/ros2_jazzy/install/rcutils:/Users/<USER>/ros2_jazzy/install/python_qt_binding:/Users/<USER>/ros2_jazzy/install/python_orocos_kdl_vendor:/Users/<USER>/ros2_jazzy/install/launch_testing_ament_cmake:/Users/<USER>/ros2_jazzy/install/python_cmake_module:/Users/<USER>/ros2_jazzy/install/pybind11_vendor:/Users/<USER>/ros2_jazzy/install/performance_test_fixture:/Users/<USER>/ros2_jazzy/install/osrf_testing_tools_cpp:/Users/<USER>/ros2_jazzy/install/orocos_kdl_vendor:/Users/<USER>/ros2_jazzy/install/mimick_vendor:/Users/<USER>/ros2_jazzy/install/libyaml_vendor:/Users/<USER>/ros2_jazzy/install/liblz4_vendor:/Users/<USER>/ros2_jazzy/install/libcurl_vendor:/Users/<USER>/ros2_jazzy/install/keyboard_handler:/Users/<USER>/ros2_jazzy/install/iceoryx_introspection:/Users/<USER>/ros2_jazzy/install/cyclonedds:/Users/<USER>/ros2_jazzy/install/iceoryx_posh:/Users/<USER>/ros2_jazzy/install/iceoryx_hoofs:/Users/<USER>/ros2_jazzy/install/iceoryx_binding_c:/Users/<USER>/ros2_jazzy/install/gz_math_vendor/opt/gz_math_vendor:/Users/<USER>/ros2_jazzy/install/gz_math_vendor:/Users/<USER>/ros2_jazzy/install/gz_utils_vendor/opt/gz_utils_vendor:/Users/<USER>/ros2_jazzy/install/gz_utils_vendor:/Users/<USER>/ros2_jazzy/install/gz_cmake_vendor/opt/gz_cmake_vendor:/Users/<USER>/ros2_jazzy/install/gz_cmake_vendor:/Users/<USER>/ros2_jazzy/install/ament_cmake_ros:/Users/<USER>/ros2_jazzy/install/ament_cmake_auto:/Users/<USER>/ros2_jazzy/install/ament_cmake_gmock:/Users/<USER>/ros2_jazzy/install/gmock_vendor:/Users/<USER>/ros2_jazzy/install/fastrtps:/Users/<USER>/ros2_jazzy/install/fastcdr:/Users/<USER>/ros2_jazzy/install/ament_cmake_gtest:/Users/<USER>/ros2_jazzy/install/gtest_vendor:/Users/<USER>/ros2_jazzy/install/ament_cmake_google_benchmark:/Users/<USER>/ros2_jazzy/install/google_benchmark_vendor:/Users/<USER>/ros2_jazzy/install/foonathan_memory_vendor:/Users/<USER>/ros2_jazzy/install/fastrtps_cmake_module:/Users/<USER>/ros2_jazzy/install/eigen3_cmake_module:/Users/<USER>/ros2_jazzy/install/console_bridge_vendor:/Users/<USER>/ros2_jazzy/install/ament_cmake_xmllint:/Users/<USER>/ros2_jazzy/install/ament_cmake_pyflakes:/Users/<USER>/ros2_jazzy/install/ament_cmake_pycodestyle:/Users/<USER>/ros2_jazzy/install/ament_cmake_pep257:/Users/<USER>/ros2_jazzy/install/ament_cmake_pclint:/Users/<USER>/ros2_jazzy/install/ament_lint_auto:/Users/<USER>/ros2_jazzy/install/ament_cmake:/Users/<USER>/ros2_jazzy/install/ament_cmake_version:/Users/<USER>/ros2_jazzy/install/ament_cmake_vendor_package:/Users/<USER>/ros2_jazzy/install/ament_cmake_pytest:/Users/<USER>/ros2_jazzy/install/ament_cmake_mypy:/Users/<USER>/ros2_jazzy/install/ament_cmake_lint_cmake:/Users/<USER>/ros2_jazzy/install/ament_cmake_flake8:/Users/<USER>/ros2_jazzy/install/ament_cmake_cpplint:/Users/<USER>/ros2_jazzy/install/ament_cmake_cppcheck:/Users/<USER>/ros2_jazzy/install/ament_cmake_copyright:/Users/<USER>/ros2_jazzy/install/ament_cmake_clang_tidy:/Users/<USER>/ros2_jazzy/install/ament_cmake_clang_format:/Users/<USER>/ros2_jazzy/install/ament_cmake_test:/Users/<USER>/ros2_jazzy/install/ament_cmake_target_dependencies:/Users/<USER>/ros2_jazzy/install/ament_cmake_python:/Users/<USER>/ros2_jazzy/install/ament_cmake_export_dependencies:/Users/<USER>/ros2_jazzy/install/ament_cmake_libraries:/Users/<USER>/ros2_jazzy/install/ament_cmake_include_directories:/Users/<USER>/ros2_jazzy/install/ament_cmake_gen_version_h:/Users/<USER>/ros2_jazzy/install/ament_cmake_export_targets:/Users/<USER>/ros2_jazzy/install/ament_cmake_export_link_flags:/Users/<USER>/ros2_jazzy/install/ament_cmake_export_interfaces:/Users/<USER>/ros2_jazzy/install/ament_cmake_export_libraries:/Users/<USER>/ros2_jazzy/install/ament_cmake_export_include_directories:/Users/<USER>/ros2_jazzy/install/ament_cmake_export_definitions:/Users/<USER>/ros2_jazzy/install/ament_cmake_core:/Users/<USER>/ros2_jazzy/install/ament_index_cpp:/Users/<USER>/Work/drill2/onboard/install/maneuver_builder_cpp:/Users/<USER>/Work/drill2/onboard/install/can_msgs:/opt/homebrew/opt/qt@5/lib/cmake:/opt/homebrew/opt', 'LC_TERMINAL': 'iTerm2', 'DISPLAY': '/private/tmp/com.apple.launchd.UjTF9XIlm8/org.xquartz:0', 'SECURITYSESSIONID': '186ab', 'VIRTUAL_ENV_PROMPT': '(.ros2_venv)', 'COLORTERM': 'truecolor', '_': '/usr/bin/env'}, 'shell': False}
[10.617144] (-) TimerEvent: {}
[10.719823] (-) TimerEvent: {}
[10.825082] (-) TimerEvent: {}
[10.927525] (-) TimerEvent: {}
[11.032296] (-) TimerEvent: {}
[11.067140] (leveler) StdoutLine: {'line': b'running develop\n'}
[11.137038] (-) TimerEvent: {}
[11.196595] (leveler) StdoutLine: {'line': b'Removing /Users/<USER>/Work/drill2/onboard/install/leveler/lib/python3.11/site-packages/leveler.egg-link (link to .)\n'}
[11.217681] (leveler) CommandEnded: {'returncode': 0}
[11.220766] (leveler) Command: {'cmd': ['/Users/<USER>/.ros2_venv/bin/python3', '-W', 'ignore:setup.py install is deprecated', '-W', 'ignore:easy_install command is deprecated', 'setup.py', 'egg_info', '--egg-base', '../../build/leveler', 'build', '--build-base', '/Users/<USER>/Work/drill2/onboard/build/leveler/build', 'install', '--record', '/Users/<USER>/Work/drill2/onboard/build/leveler/install.log', '--single-version-externally-managed', 'install_data', '--force'], 'cwd': '/Users/<USER>/Work/drill2/onboard/src/leveler', 'env': {'NVM_INC': '/Users/<USER>/.nvm/versions/node/v22.14.0/include/node', 'TERM_PROGRAM': 'iTerm.app', 'NVM_CD_FLAGS': '-q', 'TERM': 'xterm-256color', 'SHELL': '/bin/zsh', 'HOMEBREW_REPOSITORY': '/opt/homebrew', 'TMPDIR': '/var/folders/6b/9kvsjxmn2wg9n27c78lfw18r0000gn/T/', 'TERM_PROGRAM_VERSION': '3.5.15beta1', 'ROS_PYTHON_VERSION': '3', 'RCUTILS_LOGGING_SEVERITY': 'DEBUG', 'ROS_VERSION': '2', 'COLCON_PREFIX_PATH': '/Users/<USER>/ros2_jazzy/install:/Users/<USER>/ros2_jazzy/install:/Users/<USER>/Work/drill2/onboard/install', 'TERM_SESSION_ID': 'w0t3p0:22583761-CC84-481F-B886-691DB3AE381C', 'AMENT_PREFIX_PATH': '/Users/<USER>/Work/drill2/onboard/install/base_node:/Users/<USER>/Work/drill2/onboard/install/depth_tracker:/Users/<USER>/ros2_jazzy/install/rosbag2:/Users/<USER>/ros2_jazzy/install/rosbag2_storage_default_plugins:/Users/<USER>/ros2_jazzy/install/rosbag2_storage_mcap:/Users/<USER>/ros2_jazzy/install/rosbag2_compression_zstd:/Users/<USER>/ros2_jazzy/install/mcap_vendor:/Users/<USER>/ros2_jazzy/install/zstd_vendor:/Users/<USER>/ros2_jazzy/install/rviz_visual_testing_framework:/Users/<USER>/ros2_jazzy/install/rviz2:/Users/<USER>/ros2_jazzy/install/rviz_default_plugins:/Users/<USER>/ros2_jazzy/install/rviz_common:/Users/<USER>/ros2_jazzy/install/rosbag2_examples_py:/Users/<USER>/ros2_jazzy/install/launch_testing_examples:/Users/<USER>/ros2_jazzy/install/ros2bag:/Users/<USER>/ros2_jazzy/install/rosbag2_py:/Users/<USER>/ros2_jazzy/install/rosbag2_transport:/Users/<USER>/ros2_jazzy/install/rosbag2_storage_sqlite3:/Users/<USER>/ros2_jazzy/install/rosbag2_examples_cpp:/Users/<USER>/ros2_jazzy/install/rosbag2_compression:/Users/<USER>/ros2_jazzy/install/rosbag2_cpp:/Users/<USER>/ros2_jazzy/install/rosbag2_storage:/Users/<USER>/ros2_jazzy/install/image_common:/Users/<USER>/ros2_jazzy/install/camera_info_manager:/Users/<USER>/ros2_jazzy/install/camera_calibration_parsers:/Users/<USER>/ros2_jazzy/install/yaml_cpp_vendor:/Users/<USER>/ros2_jazzy/install/interactive_markers:/Users/<USER>/ros2_jazzy/install/common_interfaces:/Users/<USER>/ros2_jazzy/install/visualization_msgs:/Users/<USER>/ros2_jazzy/install/dummy_robot_bringup:/Users/<USER>/ros2_jazzy/install/robot_state_publisher:/Users/<USER>/ros2_jazzy/install/kdl_parser:/Users/<USER>/ros2_jazzy/install/urdf:/Users/<USER>/ros2_jazzy/install/urdf_parser_plugin:/Users/<USER>/ros2_jazzy/install/turtlesim:/Users/<USER>/ros2_jazzy/install/trajectory_msgs:/Users/<USER>/ros2_jazzy/install/tracetools_test:/Users/<USER>/ros2_jazzy/install/tracetools_launch:/Users/<USER>/ros2_jazzy/install/topic_statistics_demo:/Users/<USER>/ros2_jazzy/install/topic_monitor:/Users/<USER>/ros2_jazzy/install/geometry2:/Users/<USER>/ros2_jazzy/install/tf2_tools:/Users/<USER>/ros2_jazzy/install/tf2_sensor_msgs:/Users/<USER>/ros2_jazzy/install/test_tf2:/Users/<USER>/ros2_jazzy/install/tf2_kdl:/Users/<USER>/ros2_jazzy/install/tf2_geometry_msgs:/Users/<USER>/ros2_jazzy/install/examples_tf2_py:/Users/<USER>/ros2_jazzy/install/tf2_ros_py:/Users/<USER>/ros2_jazzy/install/tf2_eigen:/Users/<USER>/ros2_jazzy/install/tf2_bullet:/Users/<USER>/ros2_jazzy/install/tf2_ros:/Users/<USER>/ros2_jazzy/install/tf2_py:/Users/<USER>/ros2_jazzy/install/tf2_msgs:/Users/<USER>/ros2_jazzy/install/tf2_eigen_kdl:/Users/<USER>/ros2_jazzy/install/laser_geometry:/Users/<USER>/ros2_jazzy/install/tf2:/Users/<USER>/ros2_jazzy/install/test_tracetools:/Users/<USER>/ros2_jazzy/install/rosbag2_test_common:/Users/<USER>/ros2_jazzy/install/test_msgs:/Users/<USER>/ros2_jazzy/install/test_communication:/Users/<USER>/ros2_jazzy/install/stereo_msgs:/Users/<USER>/ros2_jazzy/install/std_srvs:/Users/<USER>/ros2_jazzy/install/shape_msgs:/Users/<USER>/ros2_jazzy/install/examples_rclpy_pointcloud_publisher:/Users/<USER>/ros2_jazzy/install/sensor_msgs_py:/Users/<USER>/ros2_jazzy/install/quality_of_service_demo_py:/Users/<USER>/ros2_jazzy/install/quality_of_service_demo_cpp:/Users/<USER>/ros2_jazzy/install/point_cloud_transport_py:/Users/<USER>/ros2_jazzy/install/point_cloud_transport:/Users/<USER>/ros2_jazzy/install/map_msgs:/Users/<USER>/ros2_jazzy/install/intra_process_demo:/Users/<USER>/ros2_jazzy/install/image_transport:/Users/<USER>/ros2_jazzy/install/image_tools:/Users/<USER>/ros2_jazzy/install/dummy_sensors:/Users/<USER>/ros2_jazzy/install/camera_info_manager_py:/Users/<USER>/ros2_jazzy/install/sensor_msgs:/Users/<USER>/ros2_jazzy/install/ros2cli_common_extensions:/Users/<USER>/ros2_jazzy/install/ros2doctor:/Users/<USER>/ros2_jazzy/install/dummy_map_server:/Users/<USER>/ros2_jazzy/install/nav_msgs:/Users/<USER>/ros2_jazzy/install/message_filters:/Users/<USER>/ros2_jazzy/install/logging_demo:/Users/<USER>/ros2_jazzy/install/lifecycle_py:/Users/<USER>/ros2_jazzy/install/lifecycle:/Users/<USER>/ros2_jazzy/install/diagnostic_msgs:/Users/<USER>/ros2_jazzy/install/geometry_msgs:/Users/<USER>/ros2_jazzy/install/examples_rclpy_minimal_subscriber:/Users/<USER>/ros2_jazzy/install/examples_rclpy_minimal_service:/Users/<USER>/ros2_jazzy/install/examples_rclpy_minimal_publisher:/Users/<USER>/ros2_jazzy/install/examples_rclpy_minimal_client:/Users/<USER>/ros2_jazzy/install/examples_rclpy_executors:/Users/<USER>/ros2_jazzy/install/examples_rclcpp_wait_set:/Users/<USER>/ros2_jazzy/install/examples_rclcpp_multithreaded_executor:/Users/<USER>/ros2_jazzy/install/examples_rclcpp_minimal_subscriber:/Users/<USER>/ros2_jazzy/install/examples_rclcpp_minimal_publisher:/Users/<USER>/ros2_jazzy/install/examples_rclcpp_minimal_composition:/Users/<USER>/ros2_jazzy/install/examples_rclcpp_cbg_executor:/Users/<USER>/ros2_jazzy/install/demo_nodes_py:/Users/<USER>/ros2_jazzy/install/demo_nodes_cpp_native:/Users/<USER>/ros2_jazzy/install/demo_nodes_cpp:/Users/<USER>/ros2_jazzy/install/composition:/Users/<USER>/ros2_jazzy/install/actionlib_msgs:/Users/<USER>/ros2_jazzy/install/std_msgs:/Users/<USER>/ros2_jazzy/install/ros2lifecycle_test_fixtures:/Users/<USER>/ros2_jazzy/install/ros2component:/Users/<USER>/ros2_jazzy/install/rclcpp_lifecycle:/Users/<USER>/ros2_jazzy/install/action_tutorials_cpp:/Users/<USER>/ros2_jazzy/install/rclcpp_components:/Users/<USER>/ros2_jazzy/install/examples_rclcpp_minimal_action_server:/Users/<USER>/ros2_jazzy/install/examples_rclcpp_minimal_action_client:/Users/<USER>/ros2_jazzy/install/rclcpp_action:/Users/<USER>/ros2_jazzy/install/examples_rclcpp_minimal_timer:/Users/<USER>/ros2_jazzy/install/examples_rclcpp_minimal_service:/Users/<USER>/ros2_jazzy/install/examples_rclcpp_minimal_client:/Users/<USER>/ros2_jazzy/install/examples_rclcpp_async_client:/Users/<USER>/ros2_jazzy/install/rclcpp:/Users/<USER>/ros2_jazzy/install/libstatistics_collector:/Users/<USER>/ros2_jazzy/install/statistics_msgs:/Users/<USER>/ros2_jazzy/install/sros2_cmake:/Users/<USER>/ros2_jazzy/install/sros2:/Users/<USER>/ros2_jazzy/install/ros_testing:/Users/<USER>/ros2_jazzy/install/ros2trace:/Users/<USER>/ros2_jazzy/install/ros2topic:/Users/<USER>/ros2_jazzy/install/ros2test:/Users/<USER>/ros2_jazzy/install/ros2param:/Users/<USER>/ros2_jazzy/install/ros2lifecycle:/Users/<USER>/ros2_jazzy/install/ros2service:/Users/<USER>/ros2_jazzy/install/ros2run:/Users/<USER>/ros2_jazzy/install/ros2launch:/Users/<USER>/ros2_jazzy/install/ros2pkg:/Users/<USER>/ros2_jazzy/install/ros2node:/Users/<USER>/ros2_jazzy/install/ros2multicast:/Users/<USER>/ros2_jazzy/install/ros2interface:/Users/<USER>/ros2_jazzy/install/ros2action:/Users/<USER>/ros2_jazzy/install/ros2cli:/Users/<USER>/ros2_jazzy/install/launch_testing_ros:/Users/<USER>/ros2_jazzy/install/launch_ros:/Users/<USER>/ros2_jazzy/install/examples_rclpy_minimal_action_server:/Users/<USER>/ros2_jazzy/install/examples_rclpy_minimal_action_client:/Users/<USER>/ros2_jazzy/install/examples_rclpy_guard_conditions:/Users/<USER>/ros2_jazzy/install/action_tutorials_py:/Users/<USER>/ros2_jazzy/install/rclpy:/Users/<USER>/ros2_jazzy/install/rosgraph_msgs:/Users/<USER>/ros2_jazzy/install/rosbag2_interfaces:/Users/<USER>/ros2_jazzy/install/ros2cli_test_interfaces:/Users/<USER>/ros2_jazzy/install/rcl_lifecycle:/Users/<USER>/ros2_jazzy/install/rcl_action:/Users/<USER>/ros2_jazzy/install/rcl:/Users/<USER>/ros2_jazzy/install/rmw_implementation:/Users/<USER>/ros2_jazzy/install/rmw_fastrtps_dynamic_cpp:/Users/<USER>/ros2_jazzy/install/rmw_fastrtps_cpp:/Users/<USER>/ros2_jazzy/install/rmw_fastrtps_shared_cpp:/Users/<USER>/ros2_jazzy/install/rmw_cyclonedds_cpp:/Users/<USER>/ros2_jazzy/install/rmw_connextddsmicro:/Users/<USER>/ros2_jazzy/install/rmw_connextdds:/Users/<USER>/ros2_jazzy/install/rmw_connextdds_common:/Users/<USER>/ros2_jazzy/install/rmw_dds_common:/Users/<USER>/ros2_jazzy/install/composition_interfaces:/Users/<USER>/ros2_jazzy/install/rcl_interfaces:/Users/<USER>/ros2_jazzy/install/pendulum_msgs:/Users/<USER>/ros2_jazzy/install/lifecycle_msgs:/Users/<USER>/ros2_jazzy/install/example_interfaces:/Users/<USER>/ros2_jazzy/install/action_tutorials_interfaces:/Users/<USER>/ros2_jazzy/install/rosidl_default_runtime:/Users/<USER>/ros2_jazzy/install/rosidl_default_generators:/Users/<USER>/ros2_jazzy/install/action_msgs:/Users/<USER>/ros2_jazzy/install/unique_identifier_msgs:/Users/<USER>/ros2_jazzy/install/type_description_interfaces:/Users/<USER>/ros2_jazzy/install/service_msgs:/Users/<USER>/ros2_jazzy/install/builtin_interfaces:/Users/<USER>/ros2_jazzy/install/rosidl_core_runtime:/Users/<USER>/ros2_jazzy/install/rosidl_core_generators:/Users/<USER>/ros2_jazzy/install/rosidl_generator_py:/Users/<USER>/ros2_jazzy/install/ament_lint_common:/Users/<USER>/ros2_jazzy/install/ament_cmake_uncrustify:/Users/<USER>/ros2_jazzy/install/ament_uncrustify:/Users/<USER>/ros2_jazzy/install/uncrustify_vendor:/Users/<USER>/ros2_jazzy/install/tracetools_trace:/Users/<USER>/ros2_jazzy/install/tracetools_read:/Users/<USER>/ros2_jazzy/install/tracetools:/Users/<USER>/ros2_jazzy/install/pluginlib:/Users/<USER>/ros2_jazzy/install/tinyxml2_vendor:/Users/<USER>/ros2_jazzy/install/test_tracetools_launch:/Users/<USER>/ros2_jazzy/install/test_security:/Users/<USER>/ros2_jazzy/install/test_ros2trace:/Users/<USER>/ros2_jazzy/install/test_rmw_implementation:/Users/<USER>/ros2_jazzy/install/test_rclcpp:/Users/<USER>/ros2_jazzy/install/test_quality_of_service:/Users/<USER>/ros2_jazzy/install/test_launch_testing:/Users/<USER>/ros2_jazzy/install/test_launch_ros:/Users/<USER>/ros2_jazzy/install/test_interface_files:/Users/<USER>/ros2_jazzy/install/test_cli_remapping:/Users/<USER>/ros2_jazzy/install/test_cli:/Users/<USER>/ros2_jazzy/install/tango_icons_vendor:/Users/<USER>/ros2_jazzy/install/sqlite3_vendor:/Users/<USER>/ros2_jazzy/install/rcl_logging_spdlog:/Users/<USER>/ros2_jazzy/install/spdlog_vendor:/Users/<USER>/ros2_jazzy/install/shared_queues_vendor:/Users/<USER>/ros2_jazzy/install/rviz_rendering_tests:/Users/<USER>/ros2_jazzy/install/rviz_rendering:/Users/<USER>/ros2_jazzy/install/rviz_ogre_vendor:/Users/<USER>/ros2_jazzy/install/rviz_assimp_vendor:/Users/<USER>/ros2_jazzy/install/rti_connext_dds_cmake_module:/Users/<USER>/ros2_jazzy/install/lttngpy:/Users/<USER>/ros2_jazzy/install/rpyutils:/Users/<USER>/ros2_jazzy/install/rosidl_typesupport_tests:/Users/<USER>/ros2_jazzy/install/rosidl_typesupport_introspection_tests:/Users/<USER>/ros2_jazzy/install/rosidl_typesupport_cpp:/Users/<USER>/ros2_jazzy/install/rosidl_typesupport_introspection_cpp:/Users/<USER>/ros2_jazzy/install/rosidl_typesupport_c:/Users/<USER>/ros2_jazzy/install/rosidl_typesupport_introspection_c:/Users/<USER>/ros2_jazzy/install/rosidl_typesupport_fastrtps_c:/Users/<USER>/ros2_jazzy/install/rosidl_typesupport_fastrtps_cpp:/Users/<USER>/ros2_jazzy/install/rosidl_generator_cpp:/Users/<USER>/ros2_jazzy/install/rosidl_runtime_cpp:/Users/<USER>/ros2_jazzy/install/rosidl_dynamic_typesupport_fastrtps:/Users/<USER>/ros2_jazzy/install/rcl_yaml_param_parser:/Users/<USER>/ros2_jazzy/install/rmw:/Users/<USER>/ros2_jazzy/install/rosidl_dynamic_typesupport:/Users/<USER>/ros2_jazzy/install/rosidl_runtime_c:/Users/<USER>/ros2_jazzy/install/rosidl_generator_c:/Users/<USER>/ros2_jazzy/install/rosidl_typesupport_interface:/Users/<USER>/ros2_jazzy/install/rosidl_runtime_py:/Users/<USER>/ros2_jazzy/install/rosidl_generator_dds_idl:/Users/<USER>/ros2_jazzy/install/rosidl_cmake:/Users/<USER>/ros2_jazzy/install/rosidl_pycommon:/Users/<USER>/ros2_jazzy/install/rosidl_generator_type_description:/Users/<USER>/ros2_jazzy/install/rosidl_parser:/Users/<USER>/ros2_jazzy/install/rosidl_generator_tests:/Users/<USER>/ros2_jazzy/install/rosidl_adapter:/Users/<USER>/ros2_jazzy/install/rosidl_cli:/Users/<USER>/ros2_jazzy/install/rosbag2_tests:/Users/<USER>/ros2_jazzy/install/rosbag2_test_msgdefs:/Users/<USER>/ros2_jazzy/install/ros_environment:/Users/<USER>/ros2_jazzy/install/rmw_implementation_cmake:/Users/<USER>/ros2_jazzy/install/resource_retriever:/Users/<USER>/ros2_jazzy/install/class_loader:/Users/<USER>/ros2_jazzy/install/rcpputils:/Users/<USER>/ros2_jazzy/install/rcl_logging_noop:/Users/<USER>/ros2_jazzy/install/rcl_logging_interface:/Users/<USER>/ros2_jazzy/install/rcutils:/Users/<USER>/ros2_jazzy/install/python_qt_binding:/Users/<USER>/ros2_jazzy/install/python_orocos_kdl_vendor:/Users/<USER>/ros2_jazzy/install/launch_testing_ament_cmake:/Users/<USER>/ros2_jazzy/install/python_cmake_module:/Users/<USER>/ros2_jazzy/install/pybind11_vendor:/Users/<USER>/ros2_jazzy/install/performance_test_fixture:/Users/<USER>/ros2_jazzy/install/launch_pytest:/Users/<USER>/ros2_jazzy/install/launch_testing:/Users/<USER>/ros2_jazzy/install/launch_yaml:/Users/<USER>/ros2_jazzy/install/launch_xml:/Users/<USER>/ros2_jazzy/install/launch:/Users/<USER>/ros2_jazzy/install/osrf_pycommon:/Users/<USER>/ros2_jazzy/install/orocos_kdl_vendor:/Users/<USER>/ros2_jazzy/install/mimick_vendor:/Users/<USER>/ros2_jazzy/install/libyaml_vendor:/Users/<USER>/ros2_jazzy/install/liblz4_vendor:/Users/<USER>/ros2_jazzy/install/libcurl_vendor:/Users/<USER>/ros2_jazzy/install/keyboard_handler:/Users/<USER>/ros2_jazzy/install/gz_math_vendor:/Users/<USER>/ros2_jazzy/install/gz_utils_vendor:/Users/<USER>/ros2_jazzy/install/gz_cmake_vendor:/Users/<USER>/ros2_jazzy/install/ament_cmake_ros:/Users/<USER>/ros2_jazzy/install/ament_cmake_auto:/Users/<USER>/ros2_jazzy/install/ament_cmake_gmock:/Users/<USER>/ros2_jazzy/install/ament_cmake_gtest:/Users/<USER>/ros2_jazzy/install/ament_cmake_google_benchmark:/Users/<USER>/ros2_jazzy/install/fastrtps_cmake_module:/Users/<USER>/ros2_jazzy/install/eigen3_cmake_module:/Users/<USER>/ros2_jazzy/install/domain_coordinator:/Users/<USER>/ros2_jazzy/install/console_bridge_vendor:/Users/<USER>/ros2_jazzy/install/ament_cmake_xmllint:/Users/<USER>/ros2_jazzy/install/ament_xmllint:/Users/<USER>/ros2_jazzy/install/ament_cmake_pyflakes:/Users/<USER>/ros2_jazzy/install/ament_pyflakes:/Users/<USER>/ros2_jazzy/install/ament_cmake_pycodestyle:/Users/<USER>/ros2_jazzy/install/ament_pycodestyle:/Users/<USER>/ros2_jazzy/install/ament_cmake_pep257:/Users/<USER>/ros2_jazzy/install/ament_pep257:/Users/<USER>/ros2_jazzy/install/ament_cmake_pclint:/Users/<USER>/ros2_jazzy/install/ament_pclint:/Users/<USER>/ros2_jazzy/install/ament_lint_auto:/Users/<USER>/ros2_jazzy/install/ament_cmake:/Users/<USER>/ros2_jazzy/install/ament_cmake_version:/Users/<USER>/ros2_jazzy/install/ament_cmake_vendor_package:/Users/<USER>/ros2_jazzy/install/ament_cmake_pytest:/Users/<USER>/ros2_jazzy/install/ament_cmake_mypy:/Users/<USER>/ros2_jazzy/install/ament_cmake_lint_cmake:/Users/<USER>/ros2_jazzy/install/ament_cmake_flake8:/Users/<USER>/ros2_jazzy/install/ament_cmake_cpplint:/Users/<USER>/ros2_jazzy/install/ament_cmake_cppcheck:/Users/<USER>/ros2_jazzy/install/ament_cmake_copyright:/Users/<USER>/ros2_jazzy/install/ament_cmake_clang_tidy:/Users/<USER>/ros2_jazzy/install/ament_cmake_clang_format:/Users/<USER>/ros2_jazzy/install/ament_cmake_test:/Users/<USER>/ros2_jazzy/install/ament_cmake_target_dependencies:/Users/<USER>/ros2_jazzy/install/ament_cmake_python:/Users/<USER>/ros2_jazzy/install/ament_cmake_export_dependencies:/Users/<USER>/ros2_jazzy/install/ament_cmake_libraries:/Users/<USER>/ros2_jazzy/install/ament_cmake_include_directories:/Users/<USER>/ros2_jazzy/install/ament_cmake_gen_version_h:/Users/<USER>/ros2_jazzy/install/ament_cmake_export_targets:/Users/<USER>/ros2_jazzy/install/ament_cmake_export_link_flags:/Users/<USER>/ros2_jazzy/install/ament_cmake_export_interfaces:/Users/<USER>/ros2_jazzy/install/ament_cmake_export_libraries:/Users/<USER>/ros2_jazzy/install/ament_cmake_export_include_directories:/Users/<USER>/ros2_jazzy/install/ament_cmake_export_definitions:/Users/<USER>/ros2_jazzy/install/ament_cmake_core:/Users/<USER>/ros2_jazzy/install/ament_package:/Users/<USER>/ros2_jazzy/install/ament_mypy:/Users/<USER>/ros2_jazzy/install/ament_lint_cmake:/Users/<USER>/ros2_jazzy/install/ament_flake8:/Users/<USER>/ros2_jazzy/install/ament_copyright:/Users/<USER>/ros2_jazzy/install/ament_lint:/Users/<USER>/ros2_jazzy/install/ament_index_python:/Users/<USER>/ros2_jazzy/install/ament_index_cpp:/Users/<USER>/ros2_jazzy/install/ament_cpplint:/Users/<USER>/ros2_jazzy/install/ament_cppcheck:/Users/<USER>/ros2_jazzy/install/ament_clang_tidy:/Users/<USER>/ros2_jazzy/install/ament_clang_format:/Users/<USER>/Work/drill2/onboard/install/tracks_regulator:/Users/<USER>/Work/drill2/onboard/install/state_tracker:/Users/<USER>/Work/drill2/onboard/install/rtk_connector:/Users/<USER>/Work/drill2/onboard/install/remote_connector:/Users/<USER>/Work/drill2/onboard/install/pydubins:/Users/<USER>/Work/drill2/onboard/install/path_follower:/Users/<USER>/Work/drill2/onboard/install/params_server:/Users/<USER>/Work/drill2/onboard/install/modbus_node:/Users/<USER>/Work/drill2/onboard/install/main_state_machine:/Users/<USER>/Work/drill2/onboard/install/maneuver_builder_cpp:/Users/<USER>/Work/drill2/onboard/install/leveler:/Users/<USER>/Work/drill2/onboard/install/launchpack:/Users/<USER>/Work/drill2/onboard/install/can_encoder:/Users/<USER>/Work/drill2/onboard/install/can_decoder:/Users/<USER>/Work/drill2/onboard/install/drill_msgs:/Users/<USER>/Work/drill2/onboard/install/can_msgs', 'NVM_DIR': '/Users/<USER>/.nvm', 'USER': 'frontwise', 'COMMAND_MODE': 'unix2003', 'SSH_AUTH_SOCK': '/private/tmp/com.apple.launchd.QIsZBTGupl/Listeners', '__CF_USER_TEXT_ENCODING': '0x1F5:0x0:0x0', 'TERM_FEATURES': 'T3LrMSc7UUw9Ts3BFGsSyHNoSxF', 'VIRTUAL_ENV': '/Users/<USER>/.ros2_venv', 'TERMINFO_DIRS': '/Applications/iTerm.app/Contents/Resources/terminfo:/usr/share/terminfo', 'PATH': '/Users/<USER>/ros2_jazzy/install/rviz2/bin:/Users/<USER>/ros2_jazzy/install/urdfdom/bin:/Users/<USER>/ros2_jazzy/install/ros2cli/bin:/Users/<USER>/ros2_jazzy/install/ament_uncrustify/bin:/Users/<USER>/ros2_jazzy/install/uncrustify_vendor/opt/uncrustify_vendor/bin:/Users/<USER>/ros2_jazzy/install/rviz_ogre_vendor/opt/rviz_ogre_vendor/bin:/Users/<USER>/ros2_jazzy/install/rosidl_cli/bin:/Users/<USER>/ros2_jazzy/install/launch_testing/bin:/Users/<USER>/ros2_jazzy/install/iceoryx_introspection/bin:/Users/<USER>/ros2_jazzy/install/cyclonedds/bin:/Users/<USER>/ros2_jazzy/install/iceoryx_posh/bin:/Users/<USER>/ros2_jazzy/install/fastrtps/bin:/Users/<USER>/ros2_jazzy/install/foonathan_memory_vendor/bin:/Users/<USER>/ros2_jazzy/install/ament_xmllint/bin:/Users/<USER>/ros2_jazzy/install/ament_pyflakes/bin:/Users/<USER>/ros2_jazzy/install/ament_pycodestyle/bin:/Users/<USER>/ros2_jazzy/install/ament_pep257/bin:/Users/<USER>/ros2_jazzy/install/ament_pclint/bin:/Users/<USER>/ros2_jazzy/install/ament_mypy/bin:/Users/<USER>/ros2_jazzy/install/ament_lint_cmake/bin:/Users/<USER>/ros2_jazzy/install/ament_flake8/bin:/Users/<USER>/ros2_jazzy/install/ament_copyright/bin:/Users/<USER>/ros2_jazzy/install/ament_index_python/bin:/Users/<USER>/ros2_jazzy/install/ament_cpplint/bin:/Users/<USER>/ros2_jazzy/install/ament_cppcheck/bin:/Users/<USER>/ros2_jazzy/install/ament_clang_tidy/bin:/Users/<USER>/ros2_jazzy/install/ament_clang_format/bin:/Users/<USER>/.ros2_venv/bin:/Users/<USER>/.codeium/windsurf/bin:/Users/<USER>/yandex-cloud/bin:/Users/<USER>/.nvm/versions/node/v22.14.0/bin:/opt/homebrew/bin:/opt/homebrew/sbin:/usr/local/bin:/System/Cryptexes/App/usr/bin:/usr/bin:/bin:/usr/sbin:/sbin:/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/local/bin:/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/bin:/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/appleinternal/bin:/opt/X11/bin:/Library/Apple/usr/bin:/usr/local/share/dotnet:~/.dotnet/tools:/Library/Frameworks/Mono.framework/Versions/Current/Commands:/Applications/iTerm.app/Contents/Resources/utilities:/Users/<USER>/.spoofdpi/bin', 'LaunchInstanceID': '672A663D-5DC3-4014-BF99-18B0DF3B92FC', '__CFBundleIdentifier': 'com.googlecode.iterm2', 'PWD': '/Users/<USER>/Work/drill2/onboard/build/leveler', 'COLCON': '1', 'LANG': 'ru_RU.UTF-8', 'ITERM_PROFILE': 'Default', 'XPC_FLAGS': '0x0', 'XPC_SERVICE_NAME': '0', 'OPENSSL_ROOT_DIR': '/opt/homebrew/opt/openssl@3', 'SHLVL': '2', 'HOME': '/Users/<USER>', 'COLORFGBG': '15;0', 'ROS_DISTRO': 'jazzy', 'LC_TERMINAL_VERSION': '3.5.15beta1', 'HOMEBREW_PREFIX': '/opt/homebrew', 'DYLD_LIBRARY_PATH': '/Users/<USER>/Work/drill2/onboard/install/drill_msgs/lib:/Users/<USER>/ros2_jazzy/install/rosbag2_storage_mcap/lib:/Users/<USER>/ros2_jazzy/install/rosbag2_compression_zstd/lib:/Users/<USER>/ros2_jazzy/install/mcap_vendor/lib:/Users/<USER>/ros2_jazzy/install/rviz_default_plugins/lib:/Users/<USER>/ros2_jazzy/install/rviz_common/lib:/Users/<USER>/ros2_jazzy/install/rosbag2_transport/lib:/Users/<USER>/ros2_jazzy/install/rosbag2_storage_sqlite3/lib:/Users/<USER>/ros2_jazzy/install/rosbag2_compression/lib:/Users/<USER>/ros2_jazzy/install/rosbag2_cpp/lib:/Users/<USER>/ros2_jazzy/install/rosbag2_storage/lib:/Users/<USER>/ros2_jazzy/install/camera_info_manager/lib:/Users/<USER>/ros2_jazzy/install/camera_calibration_parsers/lib:/Users/<USER>/ros2_jazzy/install/interactive_markers/lib:/Users/<USER>/ros2_jazzy/install/visualization_msgs/lib:/Users/<USER>/ros2_jazzy/install/robot_state_publisher/lib:/Users/<USER>/ros2_jazzy/install/kdl_parser/lib:/Users/<USER>/ros2_jazzy/install/urdf/lib:/Users/<USER>/ros2_jazzy/install/urdfdom/lib:/Users/<USER>/ros2_jazzy/install/turtlesim/lib:/Users/<USER>/ros2_jazzy/install/trajectory_msgs/lib:/Users/<USER>/ros2_jazzy/install/tf2_ros/lib:/Users/<USER>/ros2_jazzy/install/tf2_msgs/lib:/Users/<USER>/ros2_jazzy/install/tf2_eigen_kdl/lib:/Users/<USER>/ros2_jazzy/install/laser_geometry/lib:/Users/<USER>/ros2_jazzy/install/tf2/lib:/Users/<USER>/ros2_jazzy/install/test_msgs/lib:/Users/<USER>/ros2_jazzy/install/stereo_msgs/lib:/Users/<USER>/ros2_jazzy/install/std_srvs/lib:/Users/<USER>/ros2_jazzy/install/shape_msgs/lib:/Users/<USER>/ros2_jazzy/install/quality_of_service_demo_cpp/lib:/Users/<USER>/ros2_jazzy/install/point_cloud_transport/lib:/Users/<USER>/ros2_jazzy/install/map_msgs/lib:/Users/<USER>/ros2_jazzy/install/image_transport/lib:/Users/<USER>/ros2_jazzy/install/image_tools/lib:/Users/<USER>/ros2_jazzy/install/sensor_msgs/lib:/Users/<USER>/ros2_jazzy/install/nav_msgs/lib:/Users/<USER>/ros2_jazzy/install/message_filters/lib:/Users/<USER>/ros2_jazzy/install/logging_demo/lib:/Users/<USER>/ros2_jazzy/install/diagnostic_msgs/lib:/Users/<USER>/ros2_jazzy/install/geometry_msgs/lib:/Users/<USER>/ros2_jazzy/install/examples_rclcpp_wait_set/lib:/Users/<USER>/ros2_jazzy/install/examples_rclcpp_minimal_subscriber/lib:/Users/<USER>/ros2_jazzy/install/examples_rclcpp_minimal_composition/lib:/Users/<USER>/ros2_jazzy/install/demo_nodes_cpp_native/lib:/Users/<USER>/ros2_jazzy/install/demo_nodes_cpp/lib:/Users/<USER>/ros2_jazzy/install/composition/lib:/Users/<USER>/ros2_jazzy/install/actionlib_msgs/lib:/Users/<USER>/ros2_jazzy/install/std_msgs/lib:/Users/<USER>/ros2_jazzy/install/rclcpp_lifecycle/lib:/Users/<USER>/ros2_jazzy/install/action_tutorials_cpp/lib:/Users/<USER>/ros2_jazzy/install/rclcpp_components/lib:/Users/<USER>/ros2_jazzy/install/rclcpp_action/lib:/Users/<USER>/ros2_jazzy/install/rclcpp/lib:/Users/<USER>/ros2_jazzy/install/libstatistics_collector/lib:/Users/<USER>/ros2_jazzy/install/statistics_msgs/lib:/Users/<USER>/ros2_jazzy/install/rclpy/lib:/Users/<USER>/ros2_jazzy/install/rosgraph_msgs/lib:/Users/<USER>/ros2_jazzy/install/rosbag2_interfaces/lib:/Users/<USER>/ros2_jazzy/install/ros2cli_test_interfaces/lib:/Users/<USER>/ros2_jazzy/install/rcl_lifecycle/lib:/Users/<USER>/ros2_jazzy/install/rcl_action/lib:/Users/<USER>/ros2_jazzy/install/rcl/lib:/Users/<USER>/ros2_jazzy/install/rmw_implementation/lib:/Users/<USER>/ros2_jazzy/install/rmw_fastrtps_dynamic_cpp/lib:/Users/<USER>/ros2_jazzy/install/rmw_fastrtps_cpp/lib:/Users/<USER>/ros2_jazzy/install/rmw_fastrtps_shared_cpp/lib:/Users/<USER>/ros2_jazzy/install/rmw_cyclonedds_cpp/lib:/Users/<USER>/ros2_jazzy/install/rmw_dds_common/lib:/Users/<USER>/ros2_jazzy/install/composition_interfaces/lib:/Users/<USER>/ros2_jazzy/install/rcl_interfaces/lib:/Users/<USER>/ros2_jazzy/install/pendulum_msgs/lib:/Users/<USER>/ros2_jazzy/install/lifecycle_msgs/lib:/Users/<USER>/ros2_jazzy/install/example_interfaces/lib:/Users/<USER>/ros2_jazzy/install/action_tutorials_interfaces/lib:/Users/<USER>/ros2_jazzy/install/action_msgs/lib:/Users/<USER>/ros2_jazzy/install/unique_identifier_msgs/lib:/Users/<USER>/ros2_jazzy/install/type_description_interfaces/lib:/Users/<USER>/ros2_jazzy/install/service_msgs/lib:/Users/<USER>/ros2_jazzy/install/builtin_interfaces/lib:/Users/<USER>/ros2_jazzy/install/rcl_logging_spdlog/lib:/Users/<USER>/ros2_jazzy/install/rviz_rendering/lib:/Users/<USER>/ros2_jazzy/install/rviz_ogre_vendor/opt/rviz_ogre_vendor/lib:/Users/<USER>/ros2_jazzy/install/rosidl_typesupport_cpp/lib:/Users/<USER>/ros2_jazzy/install/rosidl_typesupport_introspection_cpp/lib:/Users/<USER>/ros2_jazzy/install/rosidl_typesupport_c/lib:/Users/<USER>/ros2_jazzy/install/rosidl_typesupport_introspection_c/lib:/Users/<USER>/ros2_jazzy/install/rosidl_typesupport_fastrtps_c/lib:/Users/<USER>/ros2_jazzy/install/rosidl_typesupport_fastrtps_cpp/lib:/Users/<USER>/ros2_jazzy/install/rosidl_dynamic_typesupport_fastrtps/lib:/Users/<USER>/ros2_jazzy/install/rcl_yaml_param_parser/lib:/Users/<USER>/ros2_jazzy/install/rmw/lib:/Users/<USER>/ros2_jazzy/install/rosidl_dynamic_typesupport/lib:/Users/<USER>/ros2_jazzy/install/rosidl_runtime_c/lib:/Users/<USER>/ros2_jazzy/install/rosbag2_test_msgdefs/lib:/Users/<USER>/ros2_jazzy/install/resource_retriever/lib:/Users/<USER>/ros2_jazzy/install/class_loader/lib:/Users/<USER>/ros2_jazzy/install/rcpputils/lib:/Users/<USER>/ros2_jazzy/install/rcl_logging_noop/lib:/Users/<USER>/ros2_jazzy/install/rcl_logging_interface/lib:/Users/<USER>/ros2_jazzy/install/rcutils/lib:/Users/<USER>/ros2_jazzy/install/performance_test_fixture/lib:/Users/<USER>/ros2_jazzy/install/osrf_testing_tools_cpp/lib:/Users/<USER>/ros2_jazzy/install/mimick_vendor/opt/mimick_vendor/lib:/Users/<USER>/ros2_jazzy/install/keyboard_handler/lib:/Users/<USER>/ros2_jazzy/install/iceoryx_introspection/lib:/Users/<USER>/ros2_jazzy/install/cyclonedds/lib:/Users/<USER>/ros2_jazzy/install/iceoryx_posh/lib:/Users/<USER>/ros2_jazzy/install/iceoryx_hoofs/lib:/Users/<USER>/ros2_jazzy/install/iceoryx_binding_c/lib:/Users/<USER>/ros2_jazzy/install/gz_math_vendor/opt/gz_math_vendor/lib:/Users/<USER>/ros2_jazzy/install/gz_utils_vendor/opt/gz_utils_vendor/lib:/Users/<USER>/ros2_jazzy/install/gz_cmake_vendor/opt/gz_cmake_vendor/lib:/Users/<USER>/ros2_jazzy/install/fastrtps/lib:/Users/<USER>/ros2_jazzy/install/fastcdr/lib:/Users/<USER>/ros2_jazzy/install/google_benchmark_vendor/lib:/Users/<USER>/ros2_jazzy/install/ament_index_cpp/lib:/Users/<USER>/Work/drill2/onboard/install/can_msgs/lib', 'ITERM_SESSION_ID': 'w0t3p0:22583761-CC84-481F-B886-691DB3AE381C', 'PYTHONPATH': '/Users/<USER>/Work/drill2/onboard/build/leveler/prefix_override:/Users/<USER>/.ros2_venv/lib/python3.11/site-packages/colcon_core/task/python/colcon_distutils_commands:/Users/<USER>/Work/drill2/onboard/install/leveler/lib/python3.11/site-packages:/Users/<USER>/Work/drill2/onboard/install/base_node/lib/python3.11/site-packages:/Users/<USER>/Work/drill2/onboard/install/drill_msgs/lib/python3.11/site-packages:/Users/<USER>/Work/drill2/onboard/install/depth_tracker/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/install/rosbag2_storage_mcap/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/build/rosbag2_examples_py:/Users/<USER>/ros2_jazzy/install/rosbag2_examples_py/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/build/launch_testing_examples:/Users/<USER>/ros2_jazzy/install/launch_testing_examples/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/build/ros2bag:/Users/<USER>/ros2_jazzy/install/ros2bag/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/install/rosbag2_py/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/install/rosbag2_storage_sqlite3/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/install/interactive_markers/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/install/visualization_msgs/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/install/turtlesim/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/install/trajectory_msgs/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/build/tracetools_test:/Users/<USER>/ros2_jazzy/install/tracetools_test/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/build/tracetools_launch:/Users/<USER>/ros2_jazzy/install/tracetools_launch/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/build/topic_monitor:/Users/<USER>/ros2_jazzy/install/topic_monitor/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/build/tf2_tools:/Users/<USER>/ros2_jazzy/install/tf2_tools/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/install/tf2_sensor_msgs/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/install/tf2_kdl/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/install/tf2_geometry_msgs/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/build/examples_tf2_py:/Users/<USER>/ros2_jazzy/install/examples_tf2_py/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/build/tf2_ros_py:/Users/<USER>/ros2_jazzy/install/tf2_ros_py/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/install/tf2_py/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/install/tf2_msgs/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/install/laser_geometry/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/install/rosbag2_test_common/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/install/test_msgs/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/install/stereo_msgs/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/install/std_srvs/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/install/shape_msgs/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/build/examples_rclpy_pointcloud_publisher:/Users/<USER>/ros2_jazzy/install/examples_rclpy_pointcloud_publisher/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/build/sensor_msgs_py:/Users/<USER>/ros2_jazzy/install/sensor_msgs_py/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/build/quality_of_service_demo_py:/Users/<USER>/ros2_jazzy/install/quality_of_service_demo_py/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/install/point_cloud_transport_py/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/install/map_msgs/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/build/camera_info_manager_py:/Users/<USER>/ros2_jazzy/install/camera_info_manager_py/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/install/sensor_msgs/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/build/ros2doctor:/Users/<USER>/ros2_jazzy/install/ros2doctor/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/install/nav_msgs/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/install/message_filters/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/install/logging_demo/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/build/lifecycle_py:/Users/<USER>/ros2_jazzy/install/lifecycle_py/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/install/diagnostic_msgs/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/install/geometry_msgs/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/build/examples_rclpy_minimal_subscriber:/Users/<USER>/ros2_jazzy/install/examples_rclpy_minimal_subscriber/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/build/examples_rclpy_minimal_service:/Users/<USER>/ros2_jazzy/install/examples_rclpy_minimal_service/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/build/examples_rclpy_minimal_publisher:/Users/<USER>/ros2_jazzy/install/examples_rclpy_minimal_publisher/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/build/examples_rclpy_minimal_client:/Users/<USER>/ros2_jazzy/install/examples_rclpy_minimal_client/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/build/examples_rclpy_executors:/Users/<USER>/ros2_jazzy/install/examples_rclpy_executors/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/build/demo_nodes_py:/Users/<USER>/ros2_jazzy/install/demo_nodes_py/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/install/actionlib_msgs/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/install/std_msgs/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/build/ros2component:/Users/<USER>/ros2_jazzy/install/ros2component/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/install/statistics_msgs/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/build/sros2:/Users/<USER>/ros2_jazzy/install/sros2/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/build/ros2trace:/Users/<USER>/ros2_jazzy/install/ros2trace/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/build/ros2topic:/Users/<USER>/ros2_jazzy/install/ros2topic/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/build/ros2test:/Users/<USER>/ros2_jazzy/install/ros2test/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/build/ros2param:/Users/<USER>/ros2_jazzy/install/ros2param/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/build/ros2lifecycle:/Users/<USER>/ros2_jazzy/install/ros2lifecycle/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/build/ros2service:/Users/<USER>/ros2_jazzy/install/ros2service/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/build/ros2run:/Users/<USER>/ros2_jazzy/install/ros2run/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/build/ros2launch:/Users/<USER>/ros2_jazzy/install/ros2launch/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/build/ros2pkg:/Users/<USER>/ros2_jazzy/install/ros2pkg/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/build/ros2node:/Users/<USER>/ros2_jazzy/install/ros2node/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/build/ros2multicast:/Users/<USER>/ros2_jazzy/install/ros2multicast/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/build/ros2interface:/Users/<USER>/ros2_jazzy/install/ros2interface/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/build/ros2action:/Users/<USER>/ros2_jazzy/install/ros2action/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/build/ros2cli:/Users/<USER>/ros2_jazzy/install/ros2cli/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/build/launch_testing_ros:/Users/<USER>/ros2_jazzy/install/launch_testing_ros/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/build/launch_ros:/Users/<USER>/ros2_jazzy/install/launch_ros/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/build/examples_rclpy_minimal_action_server:/Users/<USER>/ros2_jazzy/install/examples_rclpy_minimal_action_server/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/build/examples_rclpy_minimal_action_client:/Users/<USER>/ros2_jazzy/install/examples_rclpy_minimal_action_client/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/build/examples_rclpy_guard_conditions:/Users/<USER>/ros2_jazzy/install/examples_rclpy_guard_conditions/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/build/action_tutorials_py:/Users/<USER>/ros2_jazzy/install/action_tutorials_py/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/install/rclpy/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/install/rosgraph_msgs/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/install/rosbag2_interfaces/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/install/ros2cli_test_interfaces/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/install/rmw_dds_common/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/install/composition_interfaces/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/install/rcl_interfaces/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/install/pendulum_msgs/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/install/lifecycle_msgs/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/install/example_interfaces/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/install/action_tutorials_interfaces/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/install/action_msgs/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/install/unique_identifier_msgs/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/install/type_description_interfaces/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/install/service_msgs/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/install/builtin_interfaces/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/install/rosidl_generator_py/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/build/ament_uncrustify:/Users/<USER>/ros2_jazzy/install/ament_uncrustify/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/build/tracetools_trace:/Users/<USER>/ros2_jazzy/install/tracetools_trace/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/build/tracetools_read:/Users/<USER>/ros2_jazzy/install/tracetools_read/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/build/test_tracetools_launch:/Users/<USER>/ros2_jazzy/install/test_tracetools_launch/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/build/test_ros2trace:/Users/<USER>/ros2_jazzy/install/test_ros2trace/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/build/test_launch_ros:/Users/<USER>/ros2_jazzy/install/test_launch_ros/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/install/lttngpy/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/build/rpyutils:/Users/<USER>/ros2_jazzy/install/rpyutils/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/install/rosidl_typesupport_cpp/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/install/rosidl_typesupport_introspection_cpp/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/install/rosidl_typesupport_c/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/install/rosidl_typesupport_introspection_c/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/install/rosidl_typesupport_fastrtps_c/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/install/rosidl_typesupport_fastrtps_cpp/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/install/rosidl_generator_cpp/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/install/rosidl_generator_c/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/build/rosidl_runtime_py:/Users/<USER>/ros2_jazzy/install/rosidl_runtime_py/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/install/rosidl_generator_dds_idl/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/install/rosidl_cmake/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/build/rosidl_pycommon:/Users/<USER>/ros2_jazzy/install/rosidl_pycommon/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/install/rosidl_generator_type_description/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/install/rosidl_parser/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/install/rosidl_adapter/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/build/rosidl_cli:/Users/<USER>/ros2_jazzy/install/rosidl_cli/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/install/rosbag2_test_msgdefs/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/install/resource_retriever/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/install/rcutils/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/install/python_qt_binding/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/install/python_orocos_kdl_vendor/lib/python3.11/dist-packages:/Users/<USER>/ros2_jazzy/build/launch_pytest:/Users/<USER>/ros2_jazzy/install/launch_pytest/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/build/launch_testing:/Users/<USER>/ros2_jazzy/install/launch_testing/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/build/launch_yaml:/Users/<USER>/ros2_jazzy/install/launch_yaml/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/build/launch_xml:/Users/<USER>/ros2_jazzy/install/launch_xml/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/build/launch:/Users/<USER>/ros2_jazzy/install/launch/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/build/osrf_pycommon:/Users/<USER>/ros2_jazzy/install/osrf_pycommon/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/install/ament_cmake_google_benchmark/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/build/domain_coordinator:/Users/<USER>/ros2_jazzy/install/domain_coordinator/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/build/ament_xmllint:/Users/<USER>/ros2_jazzy/install/ament_xmllint/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/build/ament_pyflakes:/Users/<USER>/ros2_jazzy/install/ament_pyflakes/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/build/ament_pycodestyle:/Users/<USER>/ros2_jazzy/install/ament_pycodestyle/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/build/ament_pep257:/Users/<USER>/ros2_jazzy/install/ament_pep257/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/build/ament_pclint:/Users/<USER>/ros2_jazzy/install/ament_pclint/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/install/ament_cmake_test/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/build/ament_package:/Users/<USER>/ros2_jazzy/install/ament_package/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/build/ament_mypy:/Users/<USER>/ros2_jazzy/install/ament_mypy/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/build/ament_lint_cmake:/Users/<USER>/ros2_jazzy/install/ament_lint_cmake/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/build/ament_flake8:/Users/<USER>/ros2_jazzy/install/ament_flake8/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/build/ament_copyright:/Users/<USER>/ros2_jazzy/install/ament_copyright/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/build/ament_lint:/Users/<USER>/ros2_jazzy/install/ament_lint/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/build/ament_index_python:/Users/<USER>/ros2_jazzy/install/ament_index_python/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/build/ament_cpplint:/Users/<USER>/ros2_jazzy/install/ament_cpplint/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/build/ament_cppcheck:/Users/<USER>/ros2_jazzy/install/ament_cppcheck/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/build/ament_clang_tidy:/Users/<USER>/ros2_jazzy/install/ament_clang_tidy/lib/python3.11/site-packages:/Users/<USER>/ros2_jazzy/build/ament_clang_format:/Users/<USER>/ros2_jazzy/install/ament_clang_format/lib/python3.11/site-packages:/Users/<USER>/Work/drill2/onboard/install/tracks_regulator/lib/python3.11/site-packages:/Users/<USER>/Work/drill2/onboard/install/state_tracker/lib/python3.11/site-packages:/Users/<USER>/Work/drill2/onboard/install/rtk_connector/lib/python3.11/site-packages:/Users/<USER>/Work/drill2/onboard/install/remote_connector/lib/python3.11/site-packages:/Users/<USER>/Work/drill2/onboard/install/pydubins/lib/python3.11/site-packages:/Users/<USER>/Work/drill2/onboard/install/path_follower/lib/python3.11/site-packages:/Users/<USER>/Work/drill2/onboard/install/params_server/lib/python3.11/site-packages:/Users/<USER>/Work/drill2/onboard/install/modbus_node/lib/python3.11/site-packages:/Users/<USER>/Work/drill2/onboard/install/main_state_machine/lib/python3.11/site-packages:/Users/<USER>/Work/drill2/onboard/install/maneuver_builder_cpp/lib/python3.11/site-packages:/Users/<USER>/Work/drill2/onboard/install/leveler/lib/python3.11/site-packages:/Users/<USER>/Work/drill2/onboard/install/launchpack/lib/python3.11/site-packages:/Users/<USER>/Work/drill2/onboard/install/can_encoder/lib/python3.11/site-packages:/Users/<USER>/Work/drill2/onboard/install/can_decoder/lib/python3.11/site-packages:/Users/<USER>/Work/drill2/onboard/install/can_msgs/lib/python3.11/site-packages:/Users/<USER>/Work/drill2/onboard/install/external/maneuver_builder_cpp/lib/python3.11/site-packages:/Users/<USER>/Work/drill2/onboard/install/external/maneuver_builder_cpp/lib/python3.11/site-packages', 'LOGNAME': 'frontwise', 'ROS_AUTOMATIC_DISCOVERY_RANGE': 'SUBNET', 'PKG_CONFIG_PATH': '/Users/<USER>/ros2_jazzy/install/urdfdom/lib/pkgconfig:/Users/<USER>/ros2_jazzy/install/urdfdom_headers/lib/pkgconfig:/Users/<USER>/ros2_jazzy/install/osrf_testing_tools_cpp/lib/pkgconfig:/Users/<USER>/ros2_jazzy/install/iceoryx_introspection/lib/pkgconfig:/Users/<USER>/ros2_jazzy/install/cyclonedds/lib/pkgconfig:/Users/<USER>/ros2_jazzy/install/iceoryx_posh/lib/pkgconfig:/Users/<USER>/ros2_jazzy/install/iceoryx_hoofs/lib/pkgconfig:/Users/<USER>/ros2_jazzy/install/iceoryx_binding_c/lib/pkgconfig:/Users/<USER>/ros2_jazzy/install/gmock_vendor/lib/pkgconfig:/Users/<USER>/ros2_jazzy/install/gtest_vendor/lib/pkgconfig:/Users/<USER>/ros2_jazzy/install/google_benchmark_vendor/lib/pkgconfig:/Users/<USER>/ros2_jazzy/install/foonathan_memory_vendor/lib/pkgconfig', 'NVM_BIN': '/Users/<USER>/.nvm/versions/node/v22.14.0/bin', 'INFOPATH': '/opt/homebrew/share/info:', 'HOMEBREW_CELLAR': '/opt/homebrew/Cellar', 'CMAKE_PREFIX_PATH': '/Users/<USER>/Work/drill2/onboard/install/drill_msgs:/opt/homebrew/opt/qt@5/lib/cmake:/opt/homebrew/opt:/opt/homebrew/opt/qt@5/lib/cmake:/opt/homebrew/opt:/opt/homebrew/opt/qt@5/lib/cmake:/opt/homebrew/opt:/Users/<USER>/ros2_jazzy/install/rosbag2:/Users/<USER>/ros2_jazzy/install/rosbag2_storage_default_plugins:/Users/<USER>/ros2_jazzy/install/rosbag2_storage_mcap:/Users/<USER>/ros2_jazzy/install/rosbag2_compression_zstd:/Users/<USER>/ros2_jazzy/install/mcap_vendor:/Users/<USER>/ros2_jazzy/install/zstd_vendor:/Users/<USER>/ros2_jazzy/install/rviz_visual_testing_framework:/Users/<USER>/ros2_jazzy/install/rviz2:/Users/<USER>/ros2_jazzy/install/rviz_default_plugins:/Users/<USER>/ros2_jazzy/install/rviz_common:/Users/<USER>/ros2_jazzy/install/rosbag2_py:/Users/<USER>/ros2_jazzy/install/rosbag2_transport:/Users/<USER>/ros2_jazzy/install/rosbag2_storage_sqlite3:/Users/<USER>/ros2_jazzy/install/rosbag2_examples_cpp:/Users/<USER>/ros2_jazzy/install/rosbag2_compression:/Users/<USER>/ros2_jazzy/install/rosbag2_cpp:/Users/<USER>/ros2_jazzy/install/rosbag2_storage:/Users/<USER>/ros2_jazzy/install/image_common:/Users/<USER>/ros2_jazzy/install/camera_info_manager:/Users/<USER>/ros2_jazzy/install/camera_calibration_parsers:/Users/<USER>/ros2_jazzy/install/yaml_cpp_vendor:/Users/<USER>/ros2_jazzy/install/interactive_markers:/Users/<USER>/ros2_jazzy/install/common_interfaces:/Users/<USER>/ros2_jazzy/install/visualization_msgs:/Users/<USER>/ros2_jazzy/install/dummy_robot_bringup:/Users/<USER>/ros2_jazzy/install/robot_state_publisher:/Users/<USER>/ros2_jazzy/install/kdl_parser:/Users/<USER>/ros2_jazzy/install/urdf:/Users/<USER>/ros2_jazzy/install/urdfdom:/Users/<USER>/ros2_jazzy/install/urdf_parser_plugin:/Users/<USER>/ros2_jazzy/install/urdfdom_headers:/Users/<USER>/ros2_jazzy/install/turtlesim:/Users/<USER>/ros2_jazzy/install/trajectory_msgs:/Users/<USER>/ros2_jazzy/install/topic_statistics_demo:/Users/<USER>/ros2_jazzy/install/geometry2:/Users/<USER>/ros2_jazzy/install/tf2_sensor_msgs:/Users/<USER>/ros2_jazzy/install/test_tf2:/Users/<USER>/ros2_jazzy/install/tf2_kdl:/Users/<USER>/ros2_jazzy/install/tf2_geometry_msgs:/Users/<USER>/ros2_jazzy/install/tf2_eigen:/Users/<USER>/ros2_jazzy/install/tf2_bullet:/Users/<USER>/ros2_jazzy/install/tf2_ros:/Users/<USER>/ros2_jazzy/install/tf2_py:/Users/<USER>/ros2_jazzy/install/tf2_msgs:/Users/<USER>/ros2_jazzy/install/tf2_eigen_kdl:/Users/<USER>/ros2_jazzy/install/laser_geometry:/Users/<USER>/ros2_jazzy/install/tf2:/Users/<USER>/ros2_jazzy/install/test_tracetools:/Users/<USER>/ros2_jazzy/install/rosbag2_test_common:/Users/<USER>/ros2_jazzy/install/test_msgs:/Users/<USER>/ros2_jazzy/install/test_communication:/Users/<USER>/ros2_jazzy/install/stereo_msgs:/Users/<USER>/ros2_jazzy/install/std_srvs:/Users/<USER>/ros2_jazzy/install/shape_msgs:/Users/<USER>/ros2_jazzy/install/quality_of_service_demo_cpp:/Users/<USER>/ros2_jazzy/install/point_cloud_transport_py:/Users/<USER>/ros2_jazzy/install/point_cloud_transport:/Users/<USER>/ros2_jazzy/install/map_msgs:/Users/<USER>/ros2_jazzy/install/intra_process_demo:/Users/<USER>/ros2_jazzy/install/image_transport:/Users/<USER>/ros2_jazzy/install/image_tools:/Users/<USER>/ros2_jazzy/install/dummy_sensors:/Users/<USER>/ros2_jazzy/install/sensor_msgs:/Users/<USER>/ros2_jazzy/install/ros2cli_common_extensions:/Users/<USER>/ros2_jazzy/install/dummy_map_server:/Users/<USER>/ros2_jazzy/install/nav_msgs:/Users/<USER>/ros2_jazzy/install/message_filters:/Users/<USER>/ros2_jazzy/install/logging_demo:/Users/<USER>/ros2_jazzy/install/lifecycle:/Users/<USER>/ros2_jazzy/install/diagnostic_msgs:/Users/<USER>/ros2_jazzy/install/geometry_msgs:/Users/<USER>/ros2_jazzy/install/examples_rclcpp_wait_set:/Users/<USER>/ros2_jazzy/install/examples_rclcpp_multithreaded_executor:/Users/<USER>/ros2_jazzy/install/examples_rclcpp_minimal_subscriber:/Users/<USER>/ros2_jazzy/install/examples_rclcpp_minimal_publisher:/Users/<USER>/ros2_jazzy/install/examples_rclcpp_minimal_composition:/Users/<USER>/ros2_jazzy/install/examples_rclcpp_cbg_executor:/Users/<USER>/ros2_jazzy/install/demo_nodes_cpp_native:/Users/<USER>/ros2_jazzy/install/demo_nodes_cpp:/Users/<USER>/ros2_jazzy/install/composition:/Users/<USER>/ros2_jazzy/install/actionlib_msgs:/Users/<USER>/ros2_jazzy/install/std_msgs:/Users/<USER>/ros2_jazzy/install/ros2lifecycle_test_fixtures:/Users/<USER>/ros2_jazzy/install/rclcpp_lifecycle:/Users/<USER>/ros2_jazzy/install/action_tutorials_cpp:/Users/<USER>/ros2_jazzy/install/rclcpp_components:/Users/<USER>/ros2_jazzy/install/examples_rclcpp_minimal_action_server:/Users/<USER>/ros2_jazzy/install/examples_rclcpp_minimal_action_client:/Users/<USER>/ros2_jazzy/install/rclcpp_action:/Users/<USER>/ros2_jazzy/install/examples_rclcpp_minimal_timer:/Users/<USER>/ros2_jazzy/install/examples_rclcpp_minimal_service:/Users/<USER>/ros2_jazzy/install/examples_rclcpp_minimal_client:/Users/<USER>/ros2_jazzy/install/examples_rclcpp_async_client:/Users/<USER>/ros2_jazzy/install/rclcpp:/Users/<USER>/ros2_jazzy/install/libstatistics_collector:/Users/<USER>/ros2_jazzy/install/statistics_msgs:/Users/<USER>/ros2_jazzy/install/sros2_cmake:/Users/<USER>/ros2_jazzy/install/ros_testing:/Users/<USER>/ros2_jazzy/install/rclpy:/Users/<USER>/ros2_jazzy/install/rosgraph_msgs:/Users/<USER>/ros2_jazzy/install/rosbag2_interfaces:/Users/<USER>/ros2_jazzy/install/ros2cli_test_interfaces:/Users/<USER>/ros2_jazzy/install/rcl_lifecycle:/Users/<USER>/ros2_jazzy/install/rcl_action:/Users/<USER>/ros2_jazzy/install/rcl:/Users/<USER>/ros2_jazzy/install/rmw_implementation:/Users/<USER>/ros2_jazzy/install/rmw_fastrtps_dynamic_cpp:/Users/<USER>/ros2_jazzy/install/rmw_fastrtps_cpp:/Users/<USER>/ros2_jazzy/install/rmw_fastrtps_shared_cpp:/Users/<USER>/ros2_jazzy/install/rmw_cyclonedds_cpp:/Users/<USER>/ros2_jazzy/install/rmw_connextddsmicro:/Users/<USER>/ros2_jazzy/install/rmw_connextdds:/Users/<USER>/ros2_jazzy/install/rmw_connextdds_common:/Users/<USER>/ros2_jazzy/install/rmw_dds_common:/Users/<USER>/ros2_jazzy/install/composition_interfaces:/Users/<USER>/ros2_jazzy/install/rcl_interfaces:/Users/<USER>/ros2_jazzy/install/pendulum_msgs:/Users/<USER>/ros2_jazzy/install/lifecycle_msgs:/Users/<USER>/ros2_jazzy/install/example_interfaces:/Users/<USER>/ros2_jazzy/install/action_tutorials_interfaces:/Users/<USER>/ros2_jazzy/install/rosidl_default_runtime:/Users/<USER>/ros2_jazzy/install/rosidl_default_generators:/Users/<USER>/ros2_jazzy/install/action_msgs:/Users/<USER>/ros2_jazzy/install/unique_identifier_msgs:/Users/<USER>/ros2_jazzy/install/type_description_interfaces:/Users/<USER>/ros2_jazzy/install/service_msgs:/Users/<USER>/ros2_jazzy/install/builtin_interfaces:/Users/<USER>/ros2_jazzy/install/rosidl_core_runtime:/Users/<USER>/ros2_jazzy/install/rosidl_core_generators:/Users/<USER>/ros2_jazzy/install/rosidl_generator_py:/Users/<USER>/ros2_jazzy/install/ament_lint_common:/Users/<USER>/ros2_jazzy/install/ament_cmake_uncrustify:/Users/<USER>/ros2_jazzy/install/uncrustify_vendor:/Users/<USER>/ros2_jazzy/install/tracetools:/Users/<USER>/ros2_jazzy/install/pluginlib:/Users/<USER>/ros2_jazzy/install/tinyxml2_vendor:/Users/<USER>/ros2_jazzy/install/test_security:/Users/<USER>/ros2_jazzy/install/test_rmw_implementation:/Users/<USER>/ros2_jazzy/install/test_rclcpp:/Users/<USER>/ros2_jazzy/install/test_quality_of_service:/Users/<USER>/ros2_jazzy/install/test_launch_testing:/Users/<USER>/ros2_jazzy/install/test_interface_files:/Users/<USER>/ros2_jazzy/install/test_cli_remapping:/Users/<USER>/ros2_jazzy/install/test_cli:/Users/<USER>/ros2_jazzy/install/tango_icons_vendor:/Users/<USER>/ros2_jazzy/install/sqlite3_vendor:/Users/<USER>/ros2_jazzy/install/rcl_logging_spdlog:/Users/<USER>/ros2_jazzy/install/spdlog_vendor:/Users/<USER>/ros2_jazzy/install/shared_queues_vendor:/Users/<USER>/ros2_jazzy/install/rviz_rendering_tests:/Users/<USER>/ros2_jazzy/install/rviz_rendering:/Users/<USER>/ros2_jazzy/install/rviz_ogre_vendor:/Users/<USER>/ros2_jazzy/install/rviz_assimp_vendor:/Users/<USER>/ros2_jazzy/install/rti_connext_dds_cmake_module:/Users/<USER>/ros2_jazzy/install/lttngpy:/Users/<USER>/ros2_jazzy/install/rosidl_typesupport_tests:/Users/<USER>/ros2_jazzy/install/rosidl_typesupport_introspection_tests:/Users/<USER>/ros2_jazzy/install/rosidl_typesupport_cpp:/Users/<USER>/ros2_jazzy/install/rosidl_typesupport_introspection_cpp:/Users/<USER>/ros2_jazzy/install/rosidl_typesupport_c:/Users/<USER>/ros2_jazzy/install/rosidl_typesupport_introspection_c:/Users/<USER>/ros2_jazzy/install/rosidl_typesupport_fastrtps_c:/Users/<USER>/ros2_jazzy/install/rosidl_typesupport_fastrtps_cpp:/Users/<USER>/ros2_jazzy/install/rosidl_generator_cpp:/Users/<USER>/ros2_jazzy/install/rosidl_runtime_cpp:/Users/<USER>/ros2_jazzy/install/rosidl_dynamic_typesupport_fastrtps:/Users/<USER>/ros2_jazzy/install/rcl_yaml_param_parser:/Users/<USER>/ros2_jazzy/install/rmw:/Users/<USER>/ros2_jazzy/install/rosidl_dynamic_typesupport:/Users/<USER>/ros2_jazzy/install/rosidl_runtime_c:/Users/<USER>/ros2_jazzy/install/rosidl_generator_c:/Users/<USER>/ros2_jazzy/install/rosidl_typesupport_interface:/Users/<USER>/ros2_jazzy/install/rosidl_generator_dds_idl:/Users/<USER>/ros2_jazzy/install/rosidl_cmake:/Users/<USER>/ros2_jazzy/install/rosidl_generator_type_description:/Users/<USER>/ros2_jazzy/install/rosidl_parser:/Users/<USER>/ros2_jazzy/install/rosidl_generator_tests:/Users/<USER>/ros2_jazzy/install/rosidl_adapter:/Users/<USER>/ros2_jazzy/install/rosbag2_tests:/Users/<USER>/ros2_jazzy/install/rosbag2_test_msgdefs:/Users/<USER>/ros2_jazzy/install/ros_environment:/Users/<USER>/ros2_jazzy/install/rmw_implementation_cmake:/Users/<USER>/ros2_jazzy/install/resource_retriever:/Users/<USER>/ros2_jazzy/install/class_loader:/Users/<USER>/ros2_jazzy/install/rcpputils:/Users/<USER>/ros2_jazzy/install/rcl_logging_noop:/Users/<USER>/ros2_jazzy/install/rcl_logging_interface:/Users/<USER>/ros2_jazzy/install/rcutils:/Users/<USER>/ros2_jazzy/install/python_qt_binding:/Users/<USER>/ros2_jazzy/install/python_orocos_kdl_vendor:/Users/<USER>/ros2_jazzy/install/launch_testing_ament_cmake:/Users/<USER>/ros2_jazzy/install/python_cmake_module:/Users/<USER>/ros2_jazzy/install/pybind11_vendor:/Users/<USER>/ros2_jazzy/install/performance_test_fixture:/Users/<USER>/ros2_jazzy/install/osrf_testing_tools_cpp:/Users/<USER>/ros2_jazzy/install/orocos_kdl_vendor:/Users/<USER>/ros2_jazzy/install/mimick_vendor:/Users/<USER>/ros2_jazzy/install/libyaml_vendor:/Users/<USER>/ros2_jazzy/install/liblz4_vendor:/Users/<USER>/ros2_jazzy/install/libcurl_vendor:/Users/<USER>/ros2_jazzy/install/keyboard_handler:/Users/<USER>/ros2_jazzy/install/iceoryx_introspection:/Users/<USER>/ros2_jazzy/install/cyclonedds:/Users/<USER>/ros2_jazzy/install/iceoryx_posh:/Users/<USER>/ros2_jazzy/install/iceoryx_hoofs:/Users/<USER>/ros2_jazzy/install/iceoryx_binding_c:/Users/<USER>/ros2_jazzy/install/gz_math_vendor/opt/gz_math_vendor:/Users/<USER>/ros2_jazzy/install/gz_math_vendor:/Users/<USER>/ros2_jazzy/install/gz_utils_vendor/opt/gz_utils_vendor:/Users/<USER>/ros2_jazzy/install/gz_utils_vendor:/Users/<USER>/ros2_jazzy/install/gz_cmake_vendor/opt/gz_cmake_vendor:/Users/<USER>/ros2_jazzy/install/gz_cmake_vendor:/Users/<USER>/ros2_jazzy/install/ament_cmake_ros:/Users/<USER>/ros2_jazzy/install/ament_cmake_auto:/Users/<USER>/ros2_jazzy/install/ament_cmake_gmock:/Users/<USER>/ros2_jazzy/install/gmock_vendor:/Users/<USER>/ros2_jazzy/install/fastrtps:/Users/<USER>/ros2_jazzy/install/fastcdr:/Users/<USER>/ros2_jazzy/install/ament_cmake_gtest:/Users/<USER>/ros2_jazzy/install/gtest_vendor:/Users/<USER>/ros2_jazzy/install/ament_cmake_google_benchmark:/Users/<USER>/ros2_jazzy/install/google_benchmark_vendor:/Users/<USER>/ros2_jazzy/install/foonathan_memory_vendor:/Users/<USER>/ros2_jazzy/install/fastrtps_cmake_module:/Users/<USER>/ros2_jazzy/install/eigen3_cmake_module:/Users/<USER>/ros2_jazzy/install/console_bridge_vendor:/Users/<USER>/ros2_jazzy/install/ament_cmake_xmllint:/Users/<USER>/ros2_jazzy/install/ament_cmake_pyflakes:/Users/<USER>/ros2_jazzy/install/ament_cmake_pycodestyle:/Users/<USER>/ros2_jazzy/install/ament_cmake_pep257:/Users/<USER>/ros2_jazzy/install/ament_cmake_pclint:/Users/<USER>/ros2_jazzy/install/ament_lint_auto:/Users/<USER>/ros2_jazzy/install/ament_cmake:/Users/<USER>/ros2_jazzy/install/ament_cmake_version:/Users/<USER>/ros2_jazzy/install/ament_cmake_vendor_package:/Users/<USER>/ros2_jazzy/install/ament_cmake_pytest:/Users/<USER>/ros2_jazzy/install/ament_cmake_mypy:/Users/<USER>/ros2_jazzy/install/ament_cmake_lint_cmake:/Users/<USER>/ros2_jazzy/install/ament_cmake_flake8:/Users/<USER>/ros2_jazzy/install/ament_cmake_cpplint:/Users/<USER>/ros2_jazzy/install/ament_cmake_cppcheck:/Users/<USER>/ros2_jazzy/install/ament_cmake_copyright:/Users/<USER>/ros2_jazzy/install/ament_cmake_clang_tidy:/Users/<USER>/ros2_jazzy/install/ament_cmake_clang_format:/Users/<USER>/ros2_jazzy/install/ament_cmake_test:/Users/<USER>/ros2_jazzy/install/ament_cmake_target_dependencies:/Users/<USER>/ros2_jazzy/install/ament_cmake_python:/Users/<USER>/ros2_jazzy/install/ament_cmake_export_dependencies:/Users/<USER>/ros2_jazzy/install/ament_cmake_libraries:/Users/<USER>/ros2_jazzy/install/ament_cmake_include_directories:/Users/<USER>/ros2_jazzy/install/ament_cmake_gen_version_h:/Users/<USER>/ros2_jazzy/install/ament_cmake_export_targets:/Users/<USER>/ros2_jazzy/install/ament_cmake_export_link_flags:/Users/<USER>/ros2_jazzy/install/ament_cmake_export_interfaces:/Users/<USER>/ros2_jazzy/install/ament_cmake_export_libraries:/Users/<USER>/ros2_jazzy/install/ament_cmake_export_include_directories:/Users/<USER>/ros2_jazzy/install/ament_cmake_export_definitions:/Users/<USER>/ros2_jazzy/install/ament_cmake_core:/Users/<USER>/ros2_jazzy/install/ament_index_cpp:/Users/<USER>/Work/drill2/onboard/install/maneuver_builder_cpp:/Users/<USER>/Work/drill2/onboard/install/can_msgs:/opt/homebrew/opt/qt@5/lib/cmake:/opt/homebrew/opt', 'LC_TERMINAL': 'iTerm2', 'DISPLAY': '/private/tmp/com.apple.launchd.UjTF9XIlm8/org.xquartz:0', 'SECURITYSESSIONID': '186ab', 'VIRTUAL_ENV_PROMPT': '(.ros2_venv)', 'COLORTERM': 'truecolor', '_': '/usr/bin/env'}, 'shell': False}
[11.238761] (-) TimerEvent: {}
[11.342873] (-) TimerEvent: {}
[11.444650] (-) TimerEvent: {}
[11.549085] (-) TimerEvent: {}
[11.646029] (leveler) StdoutLine: {'line': b'running egg_info\n'}
[11.646235] (leveler) StdoutLine: {'line': b'writing ../../build/leveler/leveler.egg-info/PKG-INFO\n'}
[11.647256] (leveler) StdoutLine: {'line': b'writing dependency_links to ../../build/leveler/leveler.egg-info/dependency_links.txt\n'}
[11.647358] (leveler) StdoutLine: {'line': b'writing entry points to ../../build/leveler/leveler.egg-info/entry_points.txt\n'}
[11.647460] (leveler) StdoutLine: {'line': b'writing requirements to ../../build/leveler/leveler.egg-info/requires.txt\n'}
[11.647795] (leveler) StdoutLine: {'line': b'writing top-level names to ../../build/leveler/leveler.egg-info/top_level.txt\n'}
[11.649223] (leveler) StdoutLine: {'line': b"reading manifest file '../../build/leveler/leveler.egg-info/SOURCES.txt'\n"}
[11.649457] (-) TimerEvent: {}
[11.650064] (leveler) StdoutLine: {'line': b"writing manifest file '../../build/leveler/leveler.egg-info/SOURCES.txt'\n"}
[11.650175] (leveler) StdoutLine: {'line': b'running build\n'}
[11.650209] (leveler) StdoutLine: {'line': b'running build_py\n'}
[11.650423] (leveler) StdoutLine: {'line': b'running install\n'}
[11.650624] (leveler) StdoutLine: {'line': b'running install_lib\n'}
[11.651068] (leveler) StdoutLine: {'line': b'creating /Users/<USER>/Work/drill2/onboard/install/leveler/lib/python3.11/site-packages/leveler\n'}
[11.651197] (leveler) StdoutLine: {'line': b'copying /Users/<USER>/Work/drill2/onboard/build/leveler/build/lib/leveler/leveler_fsm.py -> /Users/<USER>/Work/drill2/onboard/install/leveler/lib/python3.11/site-packages/leveler\n'}
[11.651330] (leveler) StdoutLine: {'line': b'copying /Users/<USER>/Work/drill2/onboard/build/leveler/build/lib/leveler/constants.py -> /Users/<USER>/Work/drill2/onboard/install/leveler/lib/python3.11/site-packages/leveler\n'}
[11.651450] (leveler) StdoutLine: {'line': b'copying /Users/<USER>/Work/drill2/onboard/build/leveler/build/lib/leveler/jacks_model.py -> /Users/<USER>/Work/drill2/onboard/install/leveler/lib/python3.11/site-packages/leveler\n'}
[11.651572] (leveler) StdoutLine: {'line': b'copying /Users/<USER>/Work/drill2/onboard/build/leveler/build/lib/leveler/__init__.py -> /Users/<USER>/Work/drill2/onboard/install/leveler/lib/python3.11/site-packages/leveler\n'}
[11.651698] (leveler) StdoutLine: {'line': b'copying /Users/<USER>/Work/drill2/onboard/build/leveler/build/lib/leveler/leveler_states.py -> /Users/<USER>/Work/drill2/onboard/install/leveler/lib/python3.11/site-packages/leveler\n'}
[11.651916] (leveler) StdoutLine: {'line': b'copying /Users/<USER>/Work/drill2/onboard/build/leveler/build/lib/leveler/leveler_node.py -> /Users/<USER>/Work/drill2/onboard/install/leveler/lib/python3.11/site-packages/leveler\n'}
[11.653868] (leveler) StdoutLine: {'line': b'byte-compiling /Users/<USER>/Work/drill2/onboard/install/leveler/lib/python3.11/site-packages/leveler/leveler_fsm.py to leveler_fsm.cpython-311.pyc\n'}
[11.656126] (leveler) StdoutLine: {'line': b'byte-compiling /Users/<USER>/Work/drill2/onboard/install/leveler/lib/python3.11/site-packages/leveler/constants.py to constants.cpython-311.pyc\n'}
[11.656575] (leveler) StdoutLine: {'line': b'byte-compiling /Users/<USER>/Work/drill2/onboard/install/leveler/lib/python3.11/site-packages/leveler/jacks_model.py to jacks_model.cpython-311.pyc\n'}
[11.657696] (leveler) StdoutLine: {'line': b'byte-compiling /Users/<USER>/Work/drill2/onboard/install/leveler/lib/python3.11/site-packages/leveler/__init__.py to __init__.cpython-311.pyc\n'}
[11.657829] (leveler) StdoutLine: {'line': b'byte-compiling /Users/<USER>/Work/drill2/onboard/install/leveler/lib/python3.11/site-packages/leveler/leveler_states.py to leveler_states.cpython-311.pyc\n'}
[11.659301] (leveler) StdoutLine: {'line': b'byte-compiling /Users/<USER>/Work/drill2/onboard/install/leveler/lib/python3.11/site-packages/leveler/leveler_node.py to leveler_node.cpython-311.pyc\n'}
[11.659796] (leveler) StdoutLine: {'line': b'running install_data\n'}
[11.659894] (leveler) StdoutLine: {'line': b'copying resource/leveler -> /Users/<USER>/Work/drill2/onboard/install/leveler/share/ament_index/resource_index/packages\n'}
[11.660187] (leveler) StdoutLine: {'line': b'copying package.xml -> /Users/<USER>/Work/drill2/onboard/install/leveler/share/leveler\n'}
[11.660585] (leveler) StdoutLine: {'line': b'copying launch/leveler.launch.xml -> /Users/<USER>/Work/drill2/onboard/install/leveler/share/leveler/launch\n'}
[11.660784] (leveler) StdoutLine: {'line': b'running install_egg_info\n'}
[11.663509] (leveler) StdoutLine: {'line': b'Copying ../../build/leveler/leveler.egg-info to /Users/<USER>/Work/drill2/onboard/install/leveler/lib/python3.11/site-packages/leveler-0.0.0-py3.11.egg-info\n'}
[11.665525] (leveler) StdoutLine: {'line': b'running install_scripts\n'}
[11.711475] (leveler) StdoutLine: {'line': b'Installing leveler_node script to /Users/<USER>/Work/drill2/onboard/install/leveler/lib/leveler\n'}
[11.712186] (leveler) StdoutLine: {'line': b"writing list of installed files to '/Users/<USER>/Work/drill2/onboard/build/leveler/install.log'\n"}
[11.755007] (-) TimerEvent: {}
[11.763641] (leveler) CommandEnded: {'returncode': 0}
[11.768287] (leveler) JobEnded: {'identifier': 'leveler', 'rc': 0}
[11.768974] (-) EventReactorShutdown: {}
