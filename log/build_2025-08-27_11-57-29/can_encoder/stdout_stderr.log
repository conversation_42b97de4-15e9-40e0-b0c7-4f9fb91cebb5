running develop
Removing /Users/<USER>/Work/drill2/onboard/install/can_encoder/lib/python3.11/site-packages/can-encoder.egg-link (link to .)
running egg_info
writing ../../build/can_encoder/can_encoder.egg-info/PKG-INFO
writing dependency_links to ../../build/can_encoder/can_encoder.egg-info/dependency_links.txt
writing entry points to ../../build/can_encoder/can_encoder.egg-info/entry_points.txt
writing requirements to ../../build/can_encoder/can_encoder.egg-info/requires.txt
writing top-level names to ../../build/can_encoder/can_encoder.egg-info/top_level.txt
reading manifest file '../../build/can_encoder/can_encoder.egg-info/SOURCES.txt'
writing manifest file '../../build/can_encoder/can_encoder.egg-info/SOURCES.txt'
running build
running build_py
copying can_encoder/can_encoder.py -> /Users/<USER>/Work/drill2/onboard/build/can_encoder/build/lib/can_encoder
running install
running install_lib
creating /Users/<USER>/Work/drill2/onboard/install/can_encoder/lib/python3.11/site-packages/can_encoder
copying /Users/<USER>/Work/drill2/onboard/build/can_encoder/build/lib/can_encoder/can_encoder.py -> /Users/<USER>/Work/drill2/onboard/install/can_encoder/lib/python3.11/site-packages/can_encoder
copying /Users/<USER>/Work/drill2/onboard/build/can_encoder/build/lib/can_encoder/__init__.py -> /Users/<USER>/Work/drill2/onboard/install/can_encoder/lib/python3.11/site-packages/can_encoder
byte-compiling /Users/<USER>/Work/drill2/onboard/install/can_encoder/lib/python3.11/site-packages/can_encoder/can_encoder.py to can_encoder.cpython-311.pyc
byte-compiling /Users/<USER>/Work/drill2/onboard/install/can_encoder/lib/python3.11/site-packages/can_encoder/__init__.py to __init__.cpython-311.pyc
running install_data
copying resource/can_encoder -> /Users/<USER>/Work/drill2/onboard/install/can_encoder/share/ament_index/resource_index/packages
copying package.xml -> /Users/<USER>/Work/drill2/onboard/install/can_encoder/share/can_encoder
copying can_encoder/protocols/x90DustFlapsCtrl.yaml -> /Users/<USER>/Work/drill2/onboard/install/can_encoder/share/can_encoder/protocols
copying can_encoder/protocols/x90FanSupCtrl.yaml -> /Users/<USER>/Work/drill2/onboard/install/can_encoder/share/can_encoder/protocols
copying can_encoder/protocols/x90JacksBackCtrl.yaml -> /Users/<USER>/Work/drill2/onboard/install/can_encoder/share/can_encoder/protocols
copying can_encoder/protocols/x90CatCtrl.yaml -> /Users/<USER>/Work/drill2/onboard/install/can_encoder/share/can_encoder/protocols
copying can_encoder/protocols/x90BracketCtrl.yaml -> /Users/<USER>/Work/drill2/onboard/install/can_encoder/share/can_encoder/protocols
copying can_encoder/protocols/x90ArmCtrl.yaml -> /Users/<USER>/Work/drill2/onboard/install/can_encoder/share/can_encoder/protocols
copying can_encoder/protocols/x90RotSupCtrl.yaml -> /Users/<USER>/Work/drill2/onboard/install/can_encoder/share/can_encoder/protocols
copying can_encoder/protocols/x90MastCtrl.yaml -> /Users/<USER>/Work/drill2/onboard/install/can_encoder/share/can_encoder/protocols
copying can_encoder/protocols/x90PinsCtrl.yaml -> /Users/<USER>/Work/drill2/onboard/install/can_encoder/share/can_encoder/protocols
copying can_encoder/protocols/x90JacksFrontCtrl.yaml -> /Users/<USER>/Work/drill2/onboard/install/can_encoder/share/can_encoder/protocols
copying can_encoder/protocols/compressor.yaml -> /Users/<USER>/Work/drill2/onboard/install/can_encoder/share/can_encoder/protocols
copying can_encoder/protocols/x90WrenchCtrl.yaml -> /Users/<USER>/Work/drill2/onboard/install/can_encoder/share/can_encoder/protocols
copying can_encoder/protocols/x90CarouselCtrl.yaml -> /Users/<USER>/Work/drill2/onboard/install/can_encoder/share/can_encoder/protocols
copying can_encoder/protocols/x90HooverRelayCtrl.yaml -> /Users/<USER>/Work/drill2/onboard/install/can_encoder/share/can_encoder/protocols
running install_egg_info
Copying ../../build/can_encoder/can_encoder.egg-info to /Users/<USER>/Work/drill2/onboard/install/can_encoder/lib/python3.11/site-packages/can_encoder-0.0.0-py3.11.egg-info
running install_scripts
Installing can_encoder script to /Users/<USER>/Work/drill2/onboard/install/can_encoder/lib/can_encoder
writing list of installed files to '/Users/<USER>/Work/drill2/onboard/build/can_encoder/install.log'
