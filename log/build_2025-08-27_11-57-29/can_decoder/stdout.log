running egg_info
writing ../../build/can_decoder/can_decoder.egg-info/PKG-INFO
writing dependency_links to ../../build/can_decoder/can_decoder.egg-info/dependency_links.txt
writing entry points to ../../build/can_decoder/can_decoder.egg-info/entry_points.txt
writing requirements to ../../build/can_decoder/can_decoder.egg-info/requires.txt
writing top-level names to ../../build/can_decoder/can_decoder.egg-info/top_level.txt
reading manifest file '../../build/can_decoder/can_decoder.egg-info/SOURCES.txt'
writing manifest file '../../build/can_decoder/can_decoder.egg-info/SOURCES.txt'
running build
running build_py
running install
running install_lib
running install_data
running install_egg_info
removing '/Users/<USER>/Work/drill2/onboard/install/can_decoder/lib/python3.11/site-packages/can_decoder-0.0.0-py3.11.egg-info' (and everything under it)
Copying ../../build/can_decoder/can_decoder.egg-info to /Users/<USER>/Work/drill2/onboard/install/can_decoder/lib/python3.11/site-packages/can_decoder-0.0.0-py3.11.egg-info
running install_scripts
Installing can_decoder script to /Users/<USER>/Work/drill2/onboard/install/can_decoder/lib/can_decoder
writing list of installed files to '/Users/<USER>/Work/drill2/onboard/build/can_decoder/install.log'
