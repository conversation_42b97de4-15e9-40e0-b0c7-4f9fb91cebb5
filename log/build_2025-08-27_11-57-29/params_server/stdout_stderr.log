running egg_info
writing ../../build/params_server/params_server.egg-info/PKG-INFO
writing dependency_links to ../../build/params_server/params_server.egg-info/dependency_links.txt
writing entry points to ../../build/params_server/params_server.egg-info/entry_points.txt
writing requirements to ../../build/params_server/params_server.egg-info/requires.txt
writing top-level names to ../../build/params_server/params_server.egg-info/top_level.txt
reading manifest file '../../build/params_server/params_server.egg-info/SOURCES.txt'
writing manifest file '../../build/params_server/params_server.egg-info/SOURCES.txt'
running build
running build_py
running install
running install_lib
running install_data
running install_egg_info
removing '/Users/<USER>/Work/drill2/onboard/install/params_server/lib/python3.11/site-packages/params_server-0.0.0-py3.11.egg-info' (and everything under it)
Copying ../../build/params_server/params_server.egg-info to /Users/<USER>/Work/drill2/onboard/install/params_server/lib/python3.11/site-packages/params_server-0.0.0-py3.11.egg-info
running install_scripts
Installing params_server script to /Users/<USER>/Work/drill2/onboard/install/params_server/lib/params_server
writing list of installed files to '/Users/<USER>/Work/drill2/onboard/build/params_server/install.log'
