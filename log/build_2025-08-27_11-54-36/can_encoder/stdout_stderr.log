running develop
running egg_info
writing can_encoder.egg-info/PKG-INFO
writing dependency_links to can_encoder.egg-info/dependency_links.txt
writing entry points to can_encoder.egg-info/entry_points.txt
writing requirements to can_encoder.egg-info/requires.txt
writing top-level names to can_encoder.egg-info/top_level.txt
reading manifest file 'can_encoder.egg-info/SOURCES.txt'
writing manifest file 'can_encoder.egg-info/SOURCES.txt'
running build_ext
Creating /Users/<USER>/Work/drill2/onboard/install/can_encoder/lib/python3.11/site-packages/can-encoder.egg-link (link to .)
Installing can_encoder script to /Users/<USER>/Work/drill2/onboard/install/can_encoder/lib/can_encoder

Installed /Users/<USER>/Work/drill2/onboard/build/can_encoder
running symlink_data
symbolically linking /Users/<USER>/Work/drill2/onboard/build/can_encoder/resource/can_encoder -> /Users/<USER>/Work/drill2/onboard/install/can_encoder/share/ament_index/resource_index/packages
symbolically linking /Users/<USER>/Work/drill2/onboard/build/can_encoder/package.xml -> /Users/<USER>/Work/drill2/onboard/install/can_encoder/share/can_encoder
symbolically linking /Users/<USER>/Work/drill2/onboard/build/can_encoder/can_encoder/protocols/x90DustFlapsCtrl.yaml -> /Users/<USER>/Work/drill2/onboard/install/can_encoder/share/can_encoder/protocols
symbolically linking /Users/<USER>/Work/drill2/onboard/build/can_encoder/can_encoder/protocols/x90FanSupCtrl.yaml -> /Users/<USER>/Work/drill2/onboard/install/can_encoder/share/can_encoder/protocols
symbolically linking /Users/<USER>/Work/drill2/onboard/build/can_encoder/can_encoder/protocols/x90JacksBackCtrl.yaml -> /Users/<USER>/Work/drill2/onboard/install/can_encoder/share/can_encoder/protocols
symbolically linking /Users/<USER>/Work/drill2/onboard/build/can_encoder/can_encoder/protocols/x90CatCtrl.yaml -> /Users/<USER>/Work/drill2/onboard/install/can_encoder/share/can_encoder/protocols
symbolically linking /Users/<USER>/Work/drill2/onboard/build/can_encoder/can_encoder/protocols/x90BracketCtrl.yaml -> /Users/<USER>/Work/drill2/onboard/install/can_encoder/share/can_encoder/protocols
symbolically linking /Users/<USER>/Work/drill2/onboard/build/can_encoder/can_encoder/protocols/x90ArmCtrl.yaml -> /Users/<USER>/Work/drill2/onboard/install/can_encoder/share/can_encoder/protocols
symbolically linking /Users/<USER>/Work/drill2/onboard/build/can_encoder/can_encoder/protocols/x90RotSupCtrl.yaml -> /Users/<USER>/Work/drill2/onboard/install/can_encoder/share/can_encoder/protocols
symbolically linking /Users/<USER>/Work/drill2/onboard/build/can_encoder/can_encoder/protocols/x90MastCtrl.yaml -> /Users/<USER>/Work/drill2/onboard/install/can_encoder/share/can_encoder/protocols
symbolically linking /Users/<USER>/Work/drill2/onboard/build/can_encoder/can_encoder/protocols/x90PinsCtrl.yaml -> /Users/<USER>/Work/drill2/onboard/install/can_encoder/share/can_encoder/protocols
symbolically linking /Users/<USER>/Work/drill2/onboard/build/can_encoder/can_encoder/protocols/x90JacksFrontCtrl.yaml -> /Users/<USER>/Work/drill2/onboard/install/can_encoder/share/can_encoder/protocols
symbolically linking /Users/<USER>/Work/drill2/onboard/build/can_encoder/can_encoder/protocols/compressor.yaml -> /Users/<USER>/Work/drill2/onboard/install/can_encoder/share/can_encoder/protocols
symbolically linking /Users/<USER>/Work/drill2/onboard/build/can_encoder/can_encoder/protocols/x90WrenchCtrl.yaml -> /Users/<USER>/Work/drill2/onboard/install/can_encoder/share/can_encoder/protocols
symbolically linking /Users/<USER>/Work/drill2/onboard/build/can_encoder/can_encoder/protocols/x90CarouselCtrl.yaml -> /Users/<USER>/Work/drill2/onboard/install/can_encoder/share/can_encoder/protocols
symbolically linking /Users/<USER>/Work/drill2/onboard/build/can_encoder/can_encoder/protocols/x90HooverRelayCtrl.yaml -> /Users/<USER>/Work/drill2/onboard/install/can_encoder/share/can_encoder/protocols
