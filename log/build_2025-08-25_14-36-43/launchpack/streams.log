[3.891s] Invoking command in '/Users/<USER>/Work/drill2/onboard/src/launchpack': PYTHONPATH=/Users/<USER>/Work/drill2/onboard/build/launchpack/prefix_override:/Users/<USER>/.ros2_venv/lib/python3.11/site-packages/colcon_core/task/python/colcon_distutils_commands:/Users/<USER>/Work/drill2/onboard/install/launchpack/lib/python3.11/site-packages:${PYTHONPATH} SHLVL=2 VIRTUAL_ENV_PROMPT=(.ros2_venv) _=/usr/bin/env /Users/<USER>/.ros2_venv/bin/python3 -W ignore:setup.py install is deprecated -W ignore:easy_install command is deprecated setup.py egg_info --egg-base ../../build/launchpack build --build-base /Users/<USER>/Work/drill2/onboard/build/launchpack/build install --record /Users/<USER>/Work/drill2/onboard/build/launchpack/install.log --single-version-externally-managed install_data
[4.550s] running egg_info
[4.552s] writing ../../build/launchpack/launchpack.egg-info/PKG-INFO
[4.552s] writing dependency_links to ../../build/launchpack/launchpack.egg-info/dependency_links.txt
[4.552s] writing entry points to ../../build/launchpack/launchpack.egg-info/entry_points.txt
[4.552s] writing requirements to ../../build/launchpack/launchpack.egg-info/requires.txt
[4.552s] writing top-level names to ../../build/launchpack/launchpack.egg-info/top_level.txt
[4.553s] reading manifest file '../../build/launchpack/launchpack.egg-info/SOURCES.txt'
[4.554s] writing manifest file '../../build/launchpack/launchpack.egg-info/SOURCES.txt'
[4.554s] running build
[4.556s] running install
[4.558s] running install_data
[4.559s] copying launch/general.xml -> /Users/<USER>/Work/drill2/onboard/install/launchpack/share/launchpack/launch
[4.561s] running install_egg_info
[4.562s] removing '/Users/<USER>/Work/drill2/onboard/install/launchpack/lib/python3.11/site-packages/launchpack-0.0.0-py3.11.egg-info' (and everything under it)
[4.562s] Copying ../../build/launchpack/launchpack.egg-info to /Users/<USER>/Work/drill2/onboard/install/launchpack/lib/python3.11/site-packages/launchpack-0.0.0-py3.11.egg-info
[4.568s] running install_scripts
[4.644s] writing list of installed files to '/Users/<USER>/Work/drill2/onboard/build/launchpack/install.log'
[4.669s] Invoked command in '/Users/<USER>/Work/drill2/onboard/src/launchpack' returned '0': PYTHONPATH=/Users/<USER>/Work/drill2/onboard/build/launchpack/prefix_override:/Users/<USER>/.ros2_venv/lib/python3.11/site-packages/colcon_core/task/python/colcon_distutils_commands:/Users/<USER>/Work/drill2/onboard/install/launchpack/lib/python3.11/site-packages:${PYTHONPATH} SHLVL=2 VIRTUAL_ENV_PROMPT=(.ros2_venv) _=/usr/bin/env /Users/<USER>/.ros2_venv/bin/python3 -W ignore:setup.py install is deprecated -W ignore:easy_install command is deprecated setup.py egg_info --egg-base ../../build/launchpack build --build-base /Users/<USER>/Work/drill2/onboard/build/launchpack/build install --record /Users/<USER>/Work/drill2/onboard/build/launchpack/install.log --single-version-externally-managed install_data
