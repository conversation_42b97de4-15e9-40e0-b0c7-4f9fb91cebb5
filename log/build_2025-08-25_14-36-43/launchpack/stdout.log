running egg_info
writing ../../build/launchpack/launchpack.egg-info/PKG-INFO
writing dependency_links to ../../build/launchpack/launchpack.egg-info/dependency_links.txt
writing entry points to ../../build/launchpack/launchpack.egg-info/entry_points.txt
writing requirements to ../../build/launchpack/launchpack.egg-info/requires.txt
writing top-level names to ../../build/launchpack/launchpack.egg-info/top_level.txt
reading manifest file '../../build/launchpack/launchpack.egg-info/SOURCES.txt'
writing manifest file '../../build/launchpack/launchpack.egg-info/SOURCES.txt'
running build
running install
running install_data
copying launch/general.xml -> /Users/<USER>/Work/drill2/onboard/install/launchpack/share/launchpack/launch
running install_egg_info
removing '/Users/<USER>/Work/drill2/onboard/install/launchpack/lib/python3.11/site-packages/launchpack-0.0.0-py3.11.egg-info' (and everything under it)
Copying ../../build/launchpack/launchpack.egg-info to /Users/<USER>/Work/drill2/onboard/install/launchpack/lib/python3.11/site-packages/launchpack-0.0.0-py3.11.egg-info
running install_scripts
writing list of installed files to '/Users/<USER>/Work/drill2/onboard/build/launchpack/install.log'
