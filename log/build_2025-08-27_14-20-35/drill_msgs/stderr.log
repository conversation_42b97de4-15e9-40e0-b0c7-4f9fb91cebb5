CMake Deprecation Warning at CMakeLists.txt:1 (cmake_minimum_required):
  Compatibility with CMake < 3.10 will be removed from a future version of
  CMake.

  Update the VERSION argument <min> value.  Or, use the <min>...<max> syntax
  to tell CMake that the project requires at least <min> but has been updated
  to work with policies introduced by <max> or earlier.


make[1]: *** [CMakeFiles/drill_msgs__rosidl_typesupport_cpp.dir/all] Interrupt: 2
make[1]: *** [CMakeFiles/drill_msgs__rosidl_typesupport_fastrtps_cpp.dir/all] Interrupt: 2
make[1]: *** [CMakeFiles/drill_msgs__rosidl_typesupport_fastrtps_c.dir/all] Interrupt: 2
make: *** [all] Interrupt: 2
