CMake Deprecation Warning at CMakeLists.txt:1 (cmake_minimum_required):
  Compatibility with CMake < 3.10 will be removed from a future version of
  CMake.

  Update the VERSION argument <min> value.  Or, use the <min>...<max> syntax
  to tell CMake that the project requires at least <min> but has been updated
  to work with policies introduced by <max> or earlier.


-- Found ament_cmake: 2.5.3 (/Users/<USER>/ros2_jazzy/install/ament_cmake/share/ament_cmake/cmake)
-- Found Python3: /Users/<USER>/.ros2_venv/bin/python3 (found version "3.11.12") found components: Interpreter
-- Override CMake install command with custom implementation using symlinks instead of copying resources
-- Found std_msgs: 5.3.5 (/Users/<USER>/ros2_jazzy/install/std_msgs/share/std_msgs/cmake)
-- Found rosidl_generator_c: 4.6.5 (/Users/<USER>/ros2_jazzy/install/rosidl_generator_c/share/rosidl_generator_c/cmake)
-- Found rosidl_generator_cpp: 4.6.5 (/Users/<USER>/ros2_jazzy/install/rosidl_generator_cpp/share/rosidl_generator_cpp/cmake)
-- Using all available rosidl_typesupport_c: rosidl_typesupport_introspection_c;rosidl_typesupport_fastrtps_c
-- Using all available rosidl_typesupport_cpp: rosidl_typesupport_introspection_cpp;rosidl_typesupport_fastrtps_cpp
-- Found geometry_msgs: 5.3.5 (/Users/<USER>/ros2_jazzy/install/geometry_msgs/share/geometry_msgs/cmake)
-- Found rosidl_default_generators: 1.6.0 (/Users/<USER>/ros2_jazzy/install/rosidl_default_generators/share/rosidl_default_generators/cmake)
-- Found rosidl_adapter: 4.6.5 (/Users/<USER>/ros2_jazzy/install/rosidl_adapter/share/rosidl_adapter/cmake)
-- Found ament_cmake_ros: 0.12.0 (/Users/<USER>/ros2_jazzy/install/ament_cmake_ros/share/ament_cmake_ros/cmake)
-- Using all available rosidl_typesupport_c: rosidl_typesupport_introspection_c;rosidl_typesupport_fastrtps_c
-- Using all available rosidl_typesupport_cpp: rosidl_typesupport_introspection_cpp;rosidl_typesupport_fastrtps_cpp
-- Found python_cmake_module: 0.11.1 (/Users/<USER>/ros2_jazzy/install/python_cmake_module/share/python_cmake_module/cmake)
-- Found Python3: /Users/<USER>/.ros2_venv/bin/python3 (found version "3.11.12") found components: Interpreter Development NumPy Development.Module Development.Embed
-- Found ament_lint_auto: 0.17.1 (/Users/<USER>/ros2_jazzy/install/ament_lint_auto/share/ament_lint_auto/cmake)
-- Added test 'lint_cmake' to check CMake code style
-- Added test 'xmllint' to check XML markup files
-- Configuring done (12.9s)
-- Generating done (0.2s)
-- Build files have been written to: /Users/<USER>/Work/drill2/onboard/build/drill_msgs
[  0%] Built target drill_msgs__rosidl_generator_type_description
[  0%] Built target ament_cmake_python_symlink_drill_msgs
[  0%] Built target drill_msgs__cpp
[ 29%] Built target drill_msgs__rosidl_generator_c
[ 38%] Built target drill_msgs__rosidl_typesupport_introspection_c
[ 48%] Built target drill_msgs__rosidl_typesupport_c
running egg_info
writing drill_msgs.egg-info/PKG-INFO
writing dependency_links to drill_msgs.egg-info/dependency_links.txt
writing top-level names to drill_msgs.egg-info/top_level.txt
reading manifest file 'drill_msgs.egg-info/SOURCES.txt'
writing manifest file 'drill_msgs.egg-info/SOURCES.txt'
[ 48%] Built target ament_cmake_python_build_drill_msgs_egg
[ 58%] Built target drill_msgs__rosidl_typesupport_introspection_cpp
make[1]: *** [CMakeFiles/drill_msgs__rosidl_typesupport_cpp.dir/all] Interrupt: 2
make[1]: *** [CMakeFiles/drill_msgs__rosidl_typesupport_fastrtps_cpp.dir/all] Interrupt: 2
make[1]: *** [CMakeFiles/drill_msgs__rosidl_typesupport_fastrtps_c.dir/all] Interrupt: 2
make: *** [all] Interrupt: 2
