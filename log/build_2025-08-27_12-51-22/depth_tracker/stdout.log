running egg_info
writing ../../build/depth_tracker/depth_tracker.egg-info/PKG-INFO
writing dependency_links to ../../build/depth_tracker/depth_tracker.egg-info/dependency_links.txt
writing entry points to ../../build/depth_tracker/depth_tracker.egg-info/entry_points.txt
writing requirements to ../../build/depth_tracker/depth_tracker.egg-info/requires.txt
writing top-level names to ../../build/depth_tracker/depth_tracker.egg-info/top_level.txt
reading manifest file '../../build/depth_tracker/depth_tracker.egg-info/SOURCES.txt'
writing manifest file '../../build/depth_tracker/depth_tracker.egg-info/SOURCES.txt'
running build
running build_py
running install
running install_lib
running install_data
running install_egg_info
removing '/Users/<USER>/Work/drill2/onboard/install/depth_tracker/lib/python3.11/site-packages/depth_tracker-0.0.1-py3.11.egg-info' (and everything under it)
Copying ../../build/depth_tracker/depth_tracker.egg-info to /Users/<USER>/Work/drill2/onboard/install/depth_tracker/lib/python3.11/site-packages/depth_tracker-0.0.1-py3.11.egg-info
running install_scripts
Installing depth_tracker script to /Users/<USER>/Work/drill2/onboard/install/depth_tracker/lib/depth_tracker
writing list of installed files to '/Users/<USER>/Work/drill2/onboard/build/depth_tracker/install.log'
