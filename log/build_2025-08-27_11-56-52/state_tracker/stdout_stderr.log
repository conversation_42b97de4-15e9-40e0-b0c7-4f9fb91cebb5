running egg_info
writing ../../build/state_tracker/state_tracker.egg-info/PKG-INFO
writing dependency_links to ../../build/state_tracker/state_tracker.egg-info/dependency_links.txt
writing entry points to ../../build/state_tracker/state_tracker.egg-info/entry_points.txt
writing requirements to ../../build/state_tracker/state_tracker.egg-info/requires.txt
writing top-level names to ../../build/state_tracker/state_tracker.egg-info/top_level.txt
reading manifest file '../../build/state_tracker/state_tracker.egg-info/SOURCES.txt'
writing manifest file '../../build/state_tracker/state_tracker.egg-info/SOURCES.txt'
running build
running build_py
running install
running install_lib
running install_data
running install_egg_info
removing '/Users/<USER>/Work/drill2/onboard/install/state_tracker/lib/python3.11/site-packages/state_tracker-0.0.0-py3.11.egg-info' (and everything under it)
Copying ../../build/state_tracker/state_tracker.egg-info to /Users/<USER>/Work/drill2/onboard/install/state_tracker/lib/python3.11/site-packages/state_tracker-0.0.0-py3.11.egg-info
running install_scripts
Installing state_tracker script to /Users/<USER>/Work/drill2/onboard/install/state_tracker/lib/state_tracker
writing list of installed files to '/Users/<USER>/Work/drill2/onboard/build/state_tracker/install.log'
