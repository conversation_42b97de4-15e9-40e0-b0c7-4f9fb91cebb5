<?xml version="1.0"?>
<launch>
  <!-- Use the same param server env as launchpack/general.xml -->
  <set_env name="PARAM_SERVER_ADDRESS" value="localhost" />
  <set_env name="PARAM_SERVER_PORT" value="5055" />
  <set_env name="PARAM_SERVER_LOGLEVEL" value="info" />
  
  <arg name="log_level" default="info" description="Logging level for the leveler node"/>
  
  <node pkg="leveler" exec="leveler_node" name="leveler" output="screen">
    <param name="use_sim_time" value="false"/>
    <param name="log_level" value="$(var log_level)"/>
  </node>
</launch> 