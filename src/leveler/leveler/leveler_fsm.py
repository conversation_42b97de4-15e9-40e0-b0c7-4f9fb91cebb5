#!/usr/bin/env python3
"""
Leveler FSM for drill platform automatic leveling system.
Controls three jacks (left, right, rear) to level the platform.
"""

import numpy as np
from typing import List, Optional
from rclpy.qos import QoSProfile

from base_node.base_fsm import BaseFSM, BaseState
from drill_msgs.msg import (
    JacksSwitchState,
    JacksSwitchStateRaw,
    JacksCtrl,
    Level,
    StateMachineStatus,
    BoolStamped,
    Permission,
    Position,
    GNSS,
)

from .jacks_model import Jacks
from .constants import LevelerEvents


class LevelerFSM(BaseFSM):
    """Finite State Machine for drill platform leveling system."""
    
    def __init__(self):
        super().__init__(node_name="leveler")
        
        # Initialize control message
        self.jacks_control = JacksCtrl()
        
        # Create publisher for jack control commands
        self.jacks_ctrl_pub = self.create_publisher(
            JacksCtrl,
            # "/jacks_target_speed", 
            "/jacks_ctrl",
            QoSProfile(depth=10)
        )
        
        # Register custom subscribers for leveler-specific topics
        self.add_subscriber(
            "/jacks_switch_state",
            Jacks<PERSON><PERSON>State,
            "jacks_state",
            timeout=1.0
        )
        
        self.add_subscriber(
            "/level",
            Level,
            "level",
            timeout=1.0
        )

        # RTK-based absolute position (Z) – used for lift height and RTK quality
        self.add_subscriber(
            "/position",
            Position,
            "position",
            timeout=1.0,
        )

        # GNSS raw data (quality field) – used for RTK Fixed check
        self.add_subscriber(
            "/gnss_position",
            GNSS,
            "gnss",
            timeout=1.0,
        )

        # Main state machine status – used for mode-dependent behavior and safety checks
        self.add_subscriber(
            "/main_state_machine_status",
            StateMachineStatus,
            "main_state_machine_status",
            timeout=2.0,
        )

        # ------------------------------------------------------------------
        # TEMPORARY WORKAROUND: Always map /jacks_switch_state_raw -> /jacks_switch_state
        # Remove when a proper publisher for /jacks_switch_state is available.
        # ------------------------------------------------------------------
        self.jacks_state_pub = self.create_publisher(
            JacksSwitchState,
            "/jacks_switch_state",
            QoSProfile(depth=10),
        )
        self.create_subscription(
            JacksSwitchStateRaw,
            "/jacks_switch_state_raw",
            self._jacks_state_raw_cb,
            qos_profile=self.qos_default,
        )
        
        # Safety and limit tracking variables
        self.bad_angles_start_ts: Optional[float] = None
        self.ground_pitch: Optional[float] = None
        self.ground_roll: Optional[float] = None
        self.min_pitch: Optional[float] = None
        self.min_roll: Optional[float] = None
        
        # Event codes for error reporting
        self.events = LevelerEvents()
        
        # Load parameters from parameter server
        self._load_parameters()

        # Internal helpers for lift height control
        self._initial_lift_z: Optional[float] = None  # recorded at LIFT entry
        
        # Add all FSM states
        self._register_states()
        
        # Set initial state
        self.set_state("init")
        
        self.log("LevelerFSM initialized and ready")
    
    def _load_parameters(self):
        """Load all configuration parameters from parameter server."""
        # Platform geometry parameters
        self.platform_length = self.vehicle_params.get("geometry", {}).get("platform_length", 3.0)
        self.platform_width = self.vehicle_params.get("geometry", {}).get("platform_width", 2.0)
        
        # Angle limits for jacks position
        restrictions = self.vehicle_params.get("restrictions", {})
        self.min_allowed_roll = restrictions.get("min_allowed_roll", -6.5)
        self.max_allowed_roll = restrictions.get("max_allowed_roll", 6.5)
        self.min_allowed_pitch = restrictions.get("min_allowed_pitch", -6.5)
        self.max_allowed_pitch = restrictions.get("max_allowed_pitch", 6.5)
        
        # Angle limits for movement
        self.min_allowed_roll_move = restrictions.get("min_allowed_roll_move", -10.5)
        self.max_allowed_roll_move = restrictions.get("max_allowed_roll_move", 10.5)
        self.min_allowed_pitch_move = restrictions.get("min_allowed_pitch_move", -10.5)
        self.max_allowed_pitch_move = restrictions.get("max_allowed_pitch_move", 10.5)
        
        # Critical angle threshold
        self.crt_angle_thr = restrictions.get("crt_angle_thr_1", 1.0)
        
        # Node-specific parameters  
        node_params = self.node_params
        self.max_jacks_speed = node_params.get("max_jacks_speed", 1.0)
        self.angles_time_thr = node_params.get("angles_time_thr", 0.6)
        
        # Control parameters
        self.pulling_speed = node_params.get("pulling_speed", 0.8)
        self.pulling_speed_front = node_params.get("pulling_speed_front", 0.8)
        self.pulling_speed_rear = node_params.get("pulling_speed_rear", 0.8)
        
        self.touchdown_jacks_control_front = node_params.get("touchdown_jacks_control_front", 0.3)
        self.touchdown_jacks_control_back = node_params.get("touchdown_jacks_control_back", 0.3)
        
        self.common_lift_control = node_params.get("common_lift_control", 0.2)
        self.min_lift_height = node_params.get("min_lift_height", 0.09)
        
        # PID parameters for pulling state
        self.pull_jacks_speed_p = node_params.get("pull_jacks_speed_p", 5.0)
        self.pull_jacks_speed_i = node_params.get("pull_jacks_speed_i", 0.04)
        self.pull_jacks_speed_d = node_params.get("pull_jacks_speed_d", 2.0)
        self.pull_jacks_i_limit = node_params.get("pull_jacks_i_limit", 20.0)
        
        # Leveling parameters
        self.jacks_sync_p = node_params.get("jacks_sync_p", 3.0)
        self.leveling_roll_p = node_params.get("leveling_roll_p", 9.0)
        self.leveling_pitch_p = node_params.get("leveling_pitch_p", 8.0)
        self.max_level_speed = node_params.get("max_level_speed", 0.7)
        
        # Dead zone parameters  
        self.holding_dead_zone = node_params.get("holding_dead_zone", 0.5)
        self.holding_dead_zone_hist_high = node_params.get("holding_dead_zone_hist_high", 0.21)
        self.holding_dead_zone_hist_low = node_params.get("holding_dead_zone_hist_low", 0.1)
        
        # Timing parameters
        self.required_stab_time = node_params.get("required_stab_time", 3.0)
        
        # Safety parameters
        self.max_roll_above_start = node_params.get("max_roll_above_start", 1.5)
        self.max_pitch_above_start = node_params.get("max_pitch_above_start", 2.0)
        
        # Differential control parameters
        self.leveling_ctrl_jacks_integral_min = node_params.get("leveling_ctrl_jacks_integral_min", 0.5)
        self.final_level_ctrl = node_params.get("final_level_ctrl", 0.25)
        
        # Pulling parameters
        self.pulling_down_speed_on_ground = node_params.get("pulling_down_speed_on_ground", 0.1)
        self.pulling_ang_err_to_restore = node_params.get("pulling_ang_err_to_restore", 1.0)

        # Direction fixes (sensor/frame alignment)
        # TODO: remove these parameters and fix in model instead
        self.roll_sign = -1.0 if node_params.get("invert_roll", False) else 1.0
        self.pitch_sign = -1.0 if node_params.get("invert_pitch", False) else 1.0

        # Compact diagnostic of control/mapping params
        # self.log(
        #     "Params: max_level_speed=%.2f, P(roll)=%.2f, P(pitch)=%.2f, syncP=%.2f, final=%.2f, map: invR=%s invP=%s"
        #     % (
        #         self.max_level_speed,
        #         self.leveling_roll_p,
        #         self.leveling_pitch_p,
        #         self.jacks_sync_p,
        #         self.final_level_ctrl,
        #         (self.roll_sign < 0),
        #         (self.pitch_sign < 0),
        #     ),
        #     level=self.INFO,
        #     period=2.0,
        # )
    
    def _register_states(self):
        """Register all FSM states."""
        from .leveler_states import (
            InitState, PullingState, PulledState, RestorePulledState,
            TouchdownState, LiftState, LevelingState, FinalLevelingState,
            HoldingState
        )
        
        self.add_states(
            InitState(self),
            PullingState(self),
            PulledState(self),
            RestorePulledState(self),
            TouchdownState(self),
            LiftState(self),
            LevelingState(self),
            FinalLevelingState(self),
            HoldingState(self)
        )

    def on_params_update(self, interesting_updated_keys):
        """Reload parameters when node/vehicle/global configs are updated."""
        # Re-read loaded parameters from the param server caches
        self._load_parameters()
        self.log("Leveler parameters updated from params server", level=self.INFO)
    
    def stop_control(self):
        """Safely stop all jack movements."""
        # Set zero targets; actual publish happens in do_work_finally()
        self.jacks_control = JacksCtrl()
        # self.log("All jacks stopped")
    
    def safety_check(self) -> bool:
        """Perform safety checks for leveling operation."""
        # Check if we have valid level data
        if self.subs.level is None:
            return False
            
        if not self.subs.level.is_reliable:
            self.log("Level sensor data not reliable", level=self.WARN)
            return False
        
        # Check angle limits
        if not self._check_angle_limits():
            return False
            
        # ------------------------------------------------------------------
        # RTK quality check – require quality == 4 (RTK Fixed) when performing
        # any leveling-related operation (lift, touchdown, leveling, etc.)
        # ------------------------------------------------------------------

        rtk_required_states = [
            "touchdown",
            "lift",
            "leveling",
            "final_leveling",
            "holding",
        ]

        if self.current_state and self.current_state.name in rtk_required_states:
            # Use GNSS message with quality field if available
            gnss_ok = (
                self.subs.gnss is not None and self.subs.gnss.quality == 4
            )

            # Fallback – some systems may deliver only Position with is_reliable flag
            pos_ok = (
                self.subs.position is not None and self.subs.position.is_reliable
            )

            if not (gnss_ok or pos_ok):
                self.handle_error(
                    "No RTK (quality != 4 or data unreliable)",
                    level=self.WARN,
                    event_code=self.events.NO_RTK,
                )
                return False
        
        return True
    
    def _check_angle_limits(self) -> bool:
        """Check if current angles are within safe limits."""
        if self.subs.level is None:
            return False
            
        roll = self.subs.level.roll
        pitch = self.subs.level.pitch
        
        # Determine which limits to use based on current state
        on_jacks = (self.current_state and 
                   self.current_state.name not in ["pulled", "restore_pulled"])
        
        if on_jacks:
            # Stricter limits when on jacks
            angle_limits_exceeded = (
                roll > self.max_allowed_roll or pitch > self.max_allowed_pitch or
                roll < self.min_allowed_roll or pitch < self.min_allowed_pitch
            )
            
            angle_limits_exceeded_critical = (
                roll > self.max_allowed_roll + self.crt_angle_thr or
                pitch > self.max_allowed_pitch + self.crt_angle_thr or
                roll < self.min_allowed_roll - self.crt_angle_thr or
                pitch < self.min_allowed_pitch - self.crt_angle_thr
            )
        else:
            # More relaxed limits when moving
            angle_limits_exceeded = (
                roll > self.max_allowed_roll_move or 
                pitch > self.max_allowed_pitch_move or
                roll < self.min_allowed_roll_move or 
                pitch < self.min_allowed_pitch_move
            )
            
            angle_limits_exceeded_critical = (
                roll > self.max_allowed_roll_move + self.crt_angle_thr or
                pitch > self.max_allowed_pitch_move + self.crt_angle_thr or
                roll < self.min_allowed_roll_move - self.crt_angle_thr or
                pitch < self.min_allowed_pitch_move - self.crt_angle_thr
            )
        
        # Immediate stop if critical limits exceeded
        if angle_limits_exceeded_critical:
            self.handle_error(
                f"Critical angle limits exceeded! Roll: {roll:.2f}°, Pitch: {pitch:.2f}°",
                level=self.ERROR,
                event_code=self.events.ANGLES_ABOVE_LIMITS
            )
            return False
        
        # Time-based check for normal limit violations
        if angle_limits_exceeded:
            current_time = self.get_time()
            if self.bad_angles_start_ts is None:
                self.bad_angles_start_ts = current_time
            elif current_time - self.bad_angles_start_ts > self.angles_time_thr:
                self.handle_error(
                    f"Angle limits exceeded for too long! Roll: {roll:.2f}°, Pitch: {pitch:.2f}°",
                    level=self.ERROR,
                    event_code=self.events.ANGLES_ABOVE_LIMITS
                )
                return False
        else:
            self.bad_angles_start_ts = None
        
        return True
    
    def angle_diff2len_diff(self, dpitch: float, droll: float) -> Jacks:
        """Convert angle differences to jack length differences.
        
        Args:
            dpitch: Pitch angle difference in radians
            droll: Roll angle difference in radians
            
        Returns:
            Jacks object with calculated length differences
        """
        # Apply optional sign corrections (sensor/frame alignment)
        dpitch = dpitch * self.pitch_sign
        droll = droll * self.roll_sign

        half_len = self.platform_length / 2
        half_width_front = self.platform_width / 2
        
        jl = Jacks()
        jl.left = half_len * (-np.sin(dpitch)) + half_width_front * np.cos(dpitch) * np.sin(droll)
        jl.right = half_len * (-np.sin(dpitch)) - half_width_front * np.cos(dpitch) * np.sin(droll)
        jl.rear = half_len * np.sin(dpitch)
        
        return jl

    # ------------------------------------------------------------------
    # Helpers for LiftState – initial Z handling
    # ------------------------------------------------------------------

    def record_initial_lift_height(self):
        """Store reference Z at the beginning of LIFT state if data valid."""
        if (
            self.subs.position is not None
            and self.subs.position.is_reliable
        ):
            self._initial_lift_z = self.subs.position.z

    def lift_height_reached(self) -> bool:
        """Return True when platform raised by >= min_lift_height."""
        if (
            self._initial_lift_z is None
            or self.subs.position is None
            or not self.subs.position.is_reliable
        ):
            return False
        return (
            self.subs.position.z - self._initial_lift_z
            >= self.min_lift_height
        )
    
    def all_jacks_pulled(self) -> bool:
        """Check if all jacks are in retracted position."""
        if self.subs.jacks_state is None:
            return False
        js = self.subs.jacks_state
        return js.left_retracted and js.right_retracted and js.rear_retracted
    
    def all_jacks_on_ground(self) -> bool:
        """Check if all jacks are touching the ground."""
        if self.subs.jacks_state is None:
            return False
        js = self.subs.jacks_state
        return js.left_on_ground and js.right_on_ground and js.rear_on_ground
    
    def any_jack_not_on_ground(self) -> bool:
        """Check if any jack is not touching the ground."""
        if self.subs.jacks_state is None:
            return True
        js = self.subs.jacks_state
        return not (js.left_on_ground and js.right_on_ground and js.rear_on_ground)
    
    def set_control_from_jacks(self, jacks: Jacks, only_not_pulled: bool = False, 
                              only_not_on_ground: bool = False):
        """Set jack control message from Jacks object.
        
        Args:
            jacks: Jacks object with target speeds
            only_not_pulled: Only control jacks that are not pulled
            only_not_on_ground: Only control jacks that are not on ground
        """
        if self.subs.jacks_state is None:
            return
            
        js = self.subs.jacks_state
        control = JacksCtrl()
        control.header.stamp = self.get_clock().now().to_msg()
        
        # Set rear jack control
        if (not only_not_pulled or not js.rear_retracted) and \
           (not only_not_on_ground or not js.rear_on_ground):
            control.rear = jacks.rear
        else:
            control.rear = 0.0
        
        # Set right jack control  
        if (not only_not_pulled or not js.right_retracted) and \
           (not only_not_on_ground or not js.right_on_ground):
            control.right = jacks.right
        else:
            control.right = 0.0
        
        # Set left jack control
        if (not only_not_pulled or not js.left_retracted) and \
           (not only_not_on_ground or not js.left_on_ground):
            control.left = jacks.left
        else:
            control.left = 0.0
        
        self.jacks_control = control

        # Diagnostic – commanded values and ground contact
        # self.log(
        #     "CMD: L=%.3f R=%.3f Rear=%.3f | onGround L=%s R=%s Rear=%s retracted L=%s R=%s Rear=%s"
        #     % (
        #         control.left,
        #         control.right,
        #         control.rear,
        #         getattr(js, "left_on_ground", False),
        #         getattr(js, "right_on_ground", False),
        #         getattr(js, "rear_on_ground", False),
        #         getattr(js, "left_retracted", False),
        #         getattr(js, "right_retracted", False),
        #         getattr(js, "rear_retracted", False),
        #     ),
        #     level=self.DEBUG,
        #     period=0.5,
        # )
        
    def publish_control(self):
        """Publish jack control commands."""
        self.jacks_control.header.stamp = self.get_clock().now().to_msg()
        self.jacks_ctrl_pub.publish(self.jacks_control)
    
    def do_work_finally(self):
        """Called at the end of each work cycle - publish control commands."""
        self.publish_control()
        
        # Update ground reference angles
        self._update_ground_reference()
        
        # Check for angle growth from minimum values during leveling operations
        self._check_angle_growth()
    
    def _update_ground_reference(self):
        """Update ground reference angles when appropriate."""
        if (self.subs.level is None or 
            self.subs.jacks_state is None or
            not self.subs.level.is_reliable):
            return
            
        js = self.subs.jacks_state
        all_jacks_retracted = (js.left_retracted and js.right_retracted and js.rear_retracted)
        
        # Update ground reference when all jacks are retracted OR all are on ground
        if (self.ground_pitch is None or self.ground_roll is None or 
            all_jacks_retracted or self.all_jacks_on_ground()):
            self.ground_roll = abs(self.subs.level.roll)
            self.ground_pitch = abs(self.subs.level.pitch)
    
    def _check_angle_growth(self):
        """Check for excessive angle growth during leveling operations."""
        if (self.subs.level is None or 
            not self.subs.level.is_reliable or
            self.current_state is None):
            return
            
        current_state_name = self.current_state.name
        
        # Track minimum angles during active leveling states
        if current_state_name in ["leveling", "lift", "touchdown"]:
            current_roll = abs(self.subs.level.roll)
            current_pitch = abs(self.subs.level.pitch)
            
            if self.min_pitch is None or current_pitch < self.min_pitch:
                self.min_pitch = current_pitch
            if self.min_roll is None or current_roll < self.min_roll:
                self.min_roll = current_roll
            
            # Check growth from minimum values
            if current_pitch > self.min_pitch + self.max_pitch_above_start:
                self.handle_error(
                    f"Pitch grew too much from minimum! Current: {current_pitch:.2f}°, Min: {self.min_pitch:.2f}°",
                    level=self.ERROR,
                    event_code=self.events.RC_LEVELING
                )
                return
            
            # Special handling for roll in touchdown state (handled in state itself)
            if (current_state_name != "touchdown" and 
                current_roll > self.min_roll + self.max_roll_above_start):
                self.handle_error(
                    f"Roll grew too much from minimum! Current: {current_roll:.2f}°, Min: {self.min_roll:.2f}°",
                    level=self.ERROR,
                    event_code=self.events.RC_LEVELING
                )
                return
        else:
            # Reset minimum tracking outside leveling states
            self.min_pitch = None
            self.min_roll = None
        
        # Check growth from ground level during leveling/grounding modes
        if (hasattr(self.subs, 'main_state_machine_status') and 
            self.subs.main_state_machine_status and
            self.subs.main_state_machine_status.current_state in ["leveling", "grounding"] and
            self.ground_pitch is not None and self.ground_roll is not None):
            
            current_pitch = abs(self.subs.level.pitch)
            current_roll = abs(self.subs.level.roll)
            
            if current_pitch > self.ground_pitch + self.max_pitch_above_start:
                self.handle_error(
                    f"Pitch grew too much above ground level! Current: {current_pitch:.2f}°, Ground: {self.ground_pitch:.2f}°",
                    level=self.ERROR,
                    event_code=self.events.RC_LEVELING
                )
                return
            
            # Special handling for roll in touchdown state  
            if (current_state_name != "touchdown" and 
                current_roll > self.ground_roll + self.max_roll_above_start):
                self.handle_error(
                    f"Roll grew too much above ground level! Current: {current_roll:.2f}°, Ground: {self.ground_roll:.2f}°",
                    level=self.ERROR,
                    event_code=self.events.RC_LEVELING
                )
                return
    
    def on_subscribers_timeout(self, expired: List[str]):
        """Handle subscriber timeouts with appropriate error reporting."""
        for topic in expired:
            if topic == "jacks_state":
                self.handle_error(
                    "Lost communication with jack sensors!",
                    level=self.ERROR,
                    event_code=self.events.JACK_FAILURE
                )
            elif topic == "level":
                self.handle_error(
                    "Lost communication with level sensor!",
                    level=self.ERROR,
                    event_code=self.events.LEVEL_SENSOR_FAILURE
                ) 

    # ------------------------------------------------------------------
    # TODO: remove this when /jacks_switch_state is available
    # ------------------------------------------------------------------
    def _jacks_state_raw_cb(self, msg: JacksSwitchStateRaw):
        """Convert raw jacks switch state to compact JacksSwitchState and publish (temporary)."""
        out = JacksSwitchState()
        out.header.stamp = self.get_clock().now().to_msg()
        # Map front sensors directly to left/right; rear is aggregated
        out.left_retracted = bool(msg.front_left_retracted)
        out.right_retracted = bool(msg.front_right_retracted)
        out.rear_retracted = bool(msg.rear_left_retracted and msg.rear_right_retracted)

        out.left_on_ground = bool(msg.front_left_on_ground)
        out.right_on_ground = bool(msg.front_right_on_ground)
        out.rear_on_ground = bool(msg.rear_left_on_ground or msg.rear_right_on_ground)

        self.jacks_state_pub.publish(out)