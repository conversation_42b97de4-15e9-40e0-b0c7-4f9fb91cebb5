#!/usr/bin/env python3
"""
Jacks model for mathematical operations with drill platform jacks.
Provides unified interface for working with three jacks (left, right, rear).
"""

from typing import List, Union
import numpy as np


class Jacks:
    """Mathematical model for three jacks with arithmetic operations support."""
    
    def __init__(self, left: float = 0.0, right: float = 0.0, rear: float = 0.0):
        """Initialize jacks with specified values.
        
        Args:
            left: Left front jack value
            right: Right front jack value  
            rear: Rear jack value (same for both rear jacks)
        """
        self.left = float(left)
        self.right = float(right)
        self.rear = float(rear)
    
    # Arithmetic operations with other Jacks or numbers
    
    def __add__(self, other: Union['Jacks', float, int]) -> 'Jacks':
        """Add another Jacks object or number to this one."""
        result = Jacks()
        if isinstance(other, (float, int)):
            result.left = self.left + other
            result.right = self.right + other
            result.rear = self.rear + other
        elif isinstance(other, Jacks):
            result.left = self.left + other.left
            result.right = self.right + other.right
            result.rear = self.rear + other.rear
        else:
            raise ValueError("Can add only numbers or Jacks")
        return result
    
    def __sub__(self, other: Union['Jacks', float, int]) -> 'Jacks':
        """Subtract another Jacks object or number from this one."""
        result = Jacks()
        if isinstance(other, (float, int)):
            result.left = self.left - other
            result.right = self.right - other
            result.rear = self.rear - other
        elif isinstance(other, Jacks):
            result.left = self.left - other.left
            result.right = self.right - other.right
            result.rear = self.rear - other.rear
        else:
            raise ValueError("Can subtract only numbers or Jacks")
        return result
    
    def __mul__(self, other: Union['Jacks', float, int]) -> 'Jacks':
        """Multiply this Jacks by another Jacks object or number."""
        result = Jacks()
        if isinstance(other, (float, int)):
            result.left = self.left * other
            result.right = self.right * other
            result.rear = self.rear * other
        elif isinstance(other, Jacks):
            result.left = self.left * other.left
            result.right = self.right * other.right
            result.rear = self.rear * other.rear
        else:
            raise ValueError("Can multiply only numbers or Jacks")
        return result
    
    def __truediv__(self, other: Union['Jacks', float, int]) -> 'Jacks':
        """Divide this Jacks by another Jacks object or number."""
        result = Jacks()
        if isinstance(other, (float, int)):
            if other == 0:
                raise ValueError("Division by zero")
            result.left = self.left / other
            result.right = self.right / other
            result.rear = self.rear / other
        elif isinstance(other, Jacks):
            if other.left == 0 or other.right == 0 or other.rear == 0:
                raise ValueError("Division by zero in Jacks")
            result.left = self.left / other.left
            result.right = self.right / other.right
            result.rear = self.rear / other.rear
        else:
            raise ValueError("Can divide only numbers or Jacks")
        return result
    
    # In-place arithmetic operations
    
    def __iadd__(self, other: Union['Jacks', float, int]) -> 'Jacks':
        """Add another Jacks object or number to this one in-place."""
        if isinstance(other, (float, int)):
            self.left += other
            self.right += other
            self.rear += other
        elif isinstance(other, Jacks):
            self.left += other.left
            self.right += other.right
            self.rear += other.rear
        else:
            raise ValueError("Can add only numbers or Jacks")
        return self
    
    def __isub__(self, other: Union['Jacks', float, int]) -> 'Jacks':
        """Subtract another Jacks object or number from this one in-place."""
        if isinstance(other, (float, int)):
            self.left -= other
            self.right -= other
            self.rear -= other
        elif isinstance(other, Jacks):
            self.left -= other.left
            self.right -= other.right
            self.rear -= other.rear
        else:
            raise ValueError("Can subtract only numbers or Jacks")
        return self
    
    def __imul__(self, other: Union['Jacks', float, int]) -> 'Jacks':
        """Multiply this Jacks by another Jacks object or number in-place."""
        if isinstance(other, (float, int)):
            self.left *= other
            self.right *= other
            self.rear *= other
        elif isinstance(other, Jacks):
            self.left *= other.left
            self.right *= other.right
            self.rear *= other.rear
        else:
            raise ValueError("Can multiply only numbers or Jacks")
        return self
    
    def __itruediv__(self, other: Union['Jacks', float, int]) -> 'Jacks':
        """Divide this Jacks by another Jacks object or number in-place."""
        if isinstance(other, (float, int)):
            if other == 0:
                raise ValueError("Division by zero")
            self.left /= other
            self.right /= other
            self.rear /= other
        elif isinstance(other, Jacks):
            if other.left == 0 or other.right == 0 or other.rear == 0:
                raise ValueError("Division by zero in Jacks")
            self.left /= other.left
            self.right /= other.right
            self.rear /= other.rear
        else:
            raise ValueError("Can divide only numbers or Jacks")
        return self
    
    # Utility methods
    
    def as_list(self) -> List[float]:
        """Convert jacks values to list [left, right, rear]."""
        return [self.left, self.right, self.rear]
    
    def norm_abs(self, max_value: float) -> None:
        """Normalize all values to not exceed max_value by absolute value.
        
        Args:
            max_value: Maximum allowed absolute value
        """
        max_value = abs(max_value)
        values = np.array(self.as_list())
        max_abs_value = np.max(np.abs(values))
        
        if max_abs_value > max_value:
            scale_factor = max_value / max_abs_value
            self.left *= scale_factor
            self.right *= scale_factor
            self.rear *= scale_factor
    
    def clip_positive(self) -> None:
        """Clip all values to be non-negative."""
        self.clip_bot(0.0)
    
    def clip_negative(self) -> None:
        """Clip all values to be non-positive."""
        self.clip_top(0.0)
    
    def clip_top(self, max_value: Union['Jacks', float]) -> None:
        """Clip all values to not exceed max_value.
        
        Args:
            max_value: Maximum allowed value (Jacks object or number)
        """
        if isinstance(max_value, Jacks):
            self.left = min(self.left, max_value.left)
            self.right = min(self.right, max_value.right)
            self.rear = min(self.rear, max_value.rear)
        else:
            self.left = min(self.left, max_value)
            self.right = min(self.right, max_value)
            self.rear = min(self.rear, max_value)
    
    def clip_bot(self, min_value: Union['Jacks', float]) -> None:
        """Clip all values to be at least min_value.
        
        Args:
            min_value: Minimum allowed value (Jacks object or number)
        """
        if isinstance(min_value, Jacks):
            self.left = max(self.left, min_value.left)
            self.right = max(self.right, min_value.right)
            self.rear = max(self.rear, min_value.rear)
        else:
            self.left = max(self.left, min_value)
            self.right = max(self.right, min_value)
            self.rear = max(self.rear, min_value)
    
    def clip_abs(self, max_abs_value: float) -> None:
        """Clip all values to be within [-max_abs_value, max_abs_value].
        
        Args:
            max_abs_value: Maximum allowed absolute value
        """
        max_abs_value = abs(max_abs_value)
        self.clip_top(max_abs_value)
        self.clip_bot(-max_abs_value)
    
    def only_max(self, target_value: float = 1.0) -> None:
        """Set only the jack with maximum absolute value, preserving sign.

        Args:
            target_value: Magnitude to set for the maximum jack
        """
        abs_values = [abs(self.left), abs(self.right), abs(self.rear)]
        # Index of the maximum by absolute value
        max_idx = int(np.argmax(abs_values))
        # Original signs
        signs = [np.sign(self.left), np.sign(self.right), np.sign(self.rear)]

        # Zero all first
        self.left = 0.0
        self.right = 0.0
        self.rear = 0.0

        # Restore only the maximum with preserved sign
        if max_idx == 0 and abs_values[0] > 0:
            self.left = target_value * (1.0 if signs[0] >= 0 else -1.0)
        elif max_idx == 1 and abs_values[1] > 0:
            self.right = target_value * (1.0 if signs[1] >= 0 else -1.0)
        elif max_idx == 2 and abs_values[2] > 0:
            self.rear = target_value * (1.0 if signs[2] >= 0 else -1.0)
    
    def __str__(self) -> str:
        """String representation of Jacks object."""
        return f"Jacks(left={self.left:.3f}, right={self.right:.3f}, rear={self.rear:.3f})"
    
    def __repr__(self) -> str:
        """Debug representation of Jacks object."""
        return self.__str__() 