#!/usr/bin/env python3
"""
Leveler FSM states implementation.
Each state manages a specific phase of the leveling process.
"""

import numpy as np
from typing import Optional

from base_node.base_fsm import BaseState
from base_node.pid import PID
from .jacks_model import Jacks


class InitState(BaseState):
    """Initial state - immediately transitions to pulling."""
    
    def __init__(self, node):
        super().__init__(
            name="init",
            node=node,
            remember_as_prev=False
        )
    
    def do_work(self):
        """Initialize and transition to pulling state."""
        self.log("Leveler system initializing")
        self.node.set_state("pulling")


class PullingState(BaseState):
    """Pull all jacks to retracted position with PID control when on ground."""
    
    def __init__(self, node):
        super().__init__(
            name="pulling", 
            node=node,
            remember_as_prev=True
        )
        self.roll_pid: Optional[PID] = None
        self.pitch_pid: Optional[PID] = None
    
    def on_transition_to(self):
        """Initialize PID controllers for roll and pitch stabilization."""
        # Create PID controllers for roll and pitch angles
        self.roll_pid = PID(
            get_time=self.node.get_time,
            node=self.node,
            p=self.node.pull_jacks_speed_p,
            i=self.node.pull_jacks_speed_i,
            d=self.node.pull_jacks_speed_d,
            i_saturation=self.node.pull_jacks_i_limit,
            out_min=-self.node.pulling_speed,
            out_max=self.node.pulling_speed,
            name="roll_pull_pid",
            enable_logging=False
        )
        
        self.pitch_pid = PID(
            get_time=self.node.get_time,
            node=self.node,
            p=self.node.pull_jacks_speed_p,
            i=self.node.pull_jacks_speed_i,
            d=self.node.pull_jacks_speed_d,
            i_saturation=self.node.pull_jacks_i_limit,
            out_min=-self.node.pulling_speed,
            out_max=self.node.pulling_speed,
            name="pitch_pull_pid",
            enable_logging=False
        )
        
        self.log("Starting jacks pulling sequence with PID controllers")
    
    def do_work(self):
        """Pull jacks with PID control when on ground, fixed speeds otherwise."""
        if self.node.all_jacks_pulled():
            self.log("All jacks pulled successfully")
            self.node.set_state("pulled")
            return
        
        if self.node.all_jacks_on_ground():
            # Use PID control to stabilize angles while pulling
            self._pull_with_pid_control()
        else:
            # Use fixed pulling speeds
            self._pull_with_fixed_speeds()
    
    def _pull_with_pid_control(self):
        """Pull jacks using PID control based on angle errors."""
        current_roll = self.node.subs.level.roll
        current_pitch = self.node.subs.level.pitch
        
        # Target angles are 0 degrees
        target_roll = 0.0
        target_pitch = 0.0
        
        # Use PID controllers to calculate angle corrections
        roll_correction = self.roll_pid.update(target_roll, current_roll)
        pitch_correction = self.pitch_pid.update(target_pitch, current_pitch)
        
        # Convert angle corrections to jack speed differences
        target_speeds = self.node.angle_diff2len_diff(
            np.deg2rad(pitch_correction), 
            np.deg2rad(roll_correction)
        )
        
        # Add pulling down speed if angles are small (close to target)
        roll_err = abs(current_roll - target_roll)
        pitch_err = abs(current_pitch - target_pitch)
        if (roll_err < self.node.pulling_ang_err_to_restore and 
            pitch_err < self.node.pulling_ang_err_to_restore):
            target_speeds -= self.node.pulling_down_speed_on_ground
        
        # Normalize to maximum pulling speed
        target_speeds.norm_abs(self.node.pulling_speed)
        
        # Apply only to jacks that are not pulled
        self.node.set_control_from_jacks(target_speeds, only_not_pulled=True)
    
    def _pull_with_fixed_speeds(self):
        """Pull jacks using fixed speeds."""
        target_speeds = Jacks(
            left=-self.node.pulling_speed_front,
            right=-self.node.pulling_speed_front,
            rear=-self.node.pulling_speed_rear
        )
        target_speeds.norm_abs(self.node.max_jacks_speed)
        self.node.set_control_from_jacks(target_speeds, only_not_pulled=True)


class PulledState(BaseState):
    """Safe waiting state with all jacks retracted."""
    
    def __init__(self, node):
        super().__init__(
            name="pulled",
            node=node,
            remember_as_prev=True
        )
    
    def do_work(self):
        """Wait for commands and monitor jack states."""
        if not self.node.all_jacks_pulled():
            self.log("Jack not pulled detected - restoring", level=self.WARN)
            self.node.set_state("restore_pulled")
            return
        
        # Stop all control
        self.node.stop_control()
        
        # Check for leveling command from main state machine
        if (hasattr(self.node.subs, 'main_state_machine_status') and 
            self.node.subs.main_state_machine_status and
            self.node.subs.main_state_machine_status.current_state == "leveling"):
            self.log("Leveling command received - starting touchdown")
            self.node.set_state("touchdown")


class RestorePulledState(BaseState):
    """Restore all jacks to pulled position."""
    
    def __init__(self, node):
        super().__init__(
            name="restore_pulled",
            node=node,
            remember_as_prev=False
        )
    
    def do_work(self):
        """Force all jacks to pull with fixed speed."""
        if self.node.all_jacks_pulled():
            self.log("Jacks restored to pulled position")
            self.node.set_state("pulled")
            return
        
        # Force pulling all jacks
        target_speeds = Jacks(
            left=-self.node.pulling_speed,
            right=-self.node.pulling_speed,
            rear=-self.node.pulling_speed
        )
        self.node.set_control_from_jacks(target_speeds)


class TouchdownState(BaseState):
    """Lower jacks until they touch the ground with roll protection."""
    
    def __init__(self, node):
        super().__init__(
            name="touchdown",
            node=node,
            remember_as_prev=True
        )
        self.starting_roll: Optional[float] = None
        self.starting_pitch: Optional[float] = None
    
    def on_transition_to(self):
        """Record starting angles for safety monitoring."""
        if self.node.subs.level:
            self.starting_roll = self.node.subs.level.roll
            self.starting_pitch = self.node.subs.level.pitch
            self.log(f"Starting touchdown from roll={self.starting_roll:.2f}°, pitch={self.starting_pitch:.2f}°")
    
    def do_work(self):
        """Lower jacks with roll protection."""
        if self.node.all_jacks_on_ground():
            self.log("All jacks on ground - proceeding to lift")
            self.node.set_state("lift")
            return
        
        # Base touchdown speeds
        target_speeds = Jacks(
            left=self.node.touchdown_jacks_control_front,
            right=self.node.touchdown_jacks_control_front,
            rear=self.node.touchdown_jacks_control_back
        )
        
        # Safety: stop jacks if roll grows too much
        if self.starting_roll is not None and self.node.subs.level:
            current_roll = self.node.subs.level.roll
            roll_change = current_roll - self.starting_roll
            
            if roll_change > self.node.max_roll_above_start:
                target_speeds.left = 0.0  # Stop left jack
                self.log("Stopping left jack due to excessive roll change", level=self.WARN)
            elif roll_change < -self.node.max_roll_above_start:
                target_speeds.right = 0.0  # Stop right jack  
                self.log("Stopping right jack due to excessive roll change", level=self.WARN)
        
        # Normalize and ensure positive speeds
        target_speeds.norm_abs(self.node.max_jacks_speed)
        target_speeds.clip_positive()
        
        # Apply only to jacks not on ground
        self.node.set_control_from_jacks(target_speeds, only_not_on_ground=True)


class LiftState(BaseState):
    """Lift platform while maintaining angle synchronization."""
    
    def __init__(self, node):
        super().__init__(
            name="lift",
            node=node,
            remember_as_prev=True
        )
        self.initial_z: Optional[float] = None
        self.initial_roll: Optional[float] = None
        self.initial_pitch: Optional[float] = None
    
    def on_transition_to(self):
        """Record initial conditions for lift control."""
        if self.node.subs.level:
            self.initial_roll = self.node.subs.level.roll
            self.initial_pitch = self.node.subs.level.pitch
            
        # Record initial absolute Z using RTK position (if reliable)
        self.node.record_initial_lift_height()

        # Preserve fallback in case RTK unavailable – will be None
        if self.node._initial_lift_z is not None:
            self.log(
                f"Starting lift from roll={self.initial_roll:.2f}°, pitch={self.initial_pitch:.2f}°, z={self.node._initial_lift_z:.3f}"
            )
        else:
            self.log(
                "Starting lift – RTK position unreliable, will fallback to time-based height check",
                level=self.WARN,
            )
    
    def do_work(self):
        """Lift platform with angle synchronization."""
        if self.node.any_jack_not_on_ground():
            self.log("Jack lost ground contact during lift", level=self.WARN)
            self.node.set_state("touchdown")
            return
        
        # Calculate angle errors from initial position
        roll_err = self.initial_roll - self.node.subs.level.roll
        pitch_err = self.initial_pitch - self.node.subs.level.pitch
        
        # P-control for angle synchronization
        ctrl_roll = self.node.jacks_sync_p * roll_err
        ctrl_pitch = self.node.jacks_sync_p * pitch_err
        
        # Convert to jack speed differences
        target_speeds = self.node.angle_diff2len_diff(np.deg2rad(ctrl_pitch), np.deg2rad(ctrl_roll))
        
        # Ensure all speeds are positive (lifting)
        min_target_speed = min(target_speeds.as_list())
        if min_target_speed <= 0:
            target_speeds += -min_target_speed
        
        # Add base lifting speed
        target_speeds += self.node.common_lift_control
        
        # --------------------------------------------------------------
        # Check if we've lifted enough using RTK Z when available
        # --------------------------------------------------------------

        height_ok = self.node.lift_height_reached()

        # If RTK unavailable, keep previous time-based fallback (5 s)
        time_fallback = self.node.get_current_state_duration() > 5.0

        if height_ok or time_fallback:
            self.log(
                "Minimum lift height reached – proceeding to leveling",
                level=self.INFO,
            )
            self.node.set_state("leveling")
            return
        
        self.node.set_control_from_jacks(target_speeds)


class LevelingState(BaseState):
    """Precise leveling with P-controllers and differential control."""
    
    def __init__(self, node):
        super().__init__(
            name="leveling",
            node=node, 
            remember_as_prev=True
        )
        self.t_old: Optional[float] = None
        self.stab_time_started: Optional[float] = None
        self.ctrl_jacks_integral = Jacks()
    
    def on_transition_to(self):
        """Initialize leveling control state."""
        self.t_old = self.node.get_time()
        self.stab_time_started = self.node.get_time()
        self.ctrl_jacks_integral = Jacks()
        self.log("Starting precise leveling")
    
    def do_work(self):
        """Perform precise leveling with P-control and differential logic."""
        if self.node.any_jack_not_on_ground():
            self.log("Jack lost ground contact during leveling", level=self.WARN)
            self.node.set_state("touchdown")
            return
        
        current_time = self.node.get_time()
        dt = current_time - self.t_old
        if dt < 0 or dt > 0.2:
            dt = 0.0
        
        # Calculate angle errors (target is 0 degrees)
        roll_err = 0.0 - self.node.subs.level.roll
        pitch_err = 0.0 - self.node.subs.level.pitch
        
        # P-control for leveling
        ctrl_roll = self.node.leveling_roll_p * roll_err
        ctrl_pitch = self.node.leveling_pitch_p * pitch_err
        
        # Convert to jack speeds
        target_speeds = self.node.angle_diff2len_diff(np.deg2rad(ctrl_pitch), np.deg2rad(ctrl_roll))
        
        # Differential control logic to prevent losing ground contact
        ci = min(self.ctrl_jacks_integral.as_list())
        min_ci = self.node.leveling_ctrl_jacks_integral_min
        if min_ci < 0:
            min_ci = 0
        
        full_diff_control = ci > min_ci * 2
        
        if not full_diff_control:
            # Prevent negative speeds (losing ground contact)
            min_target_speed = min(target_speeds.as_list())
            if min_target_speed <= 0:
                if min_ci > 0:
                    correction_k = max(0, min(1, (2 * min_ci - ci) / min_ci))
                else:
                    correction_k = 1
                target_speeds += -min_target_speed * correction_k
        
        # Limit maximum speed
        target_speeds.norm_abs(self.node.max_level_speed)
        
        # Dead zone control with hysteresis
        in_dead_zone = (abs(roll_err) < self.node.holding_dead_zone and 
                       abs(pitch_err) < self.node.holding_dead_zone)
        
        in_hist_zone = (abs(roll_err) < self.node.holding_dead_zone_hist_high and 
                       abs(pitch_err) < self.node.holding_dead_zone_hist_high)
        
        if not in_dead_zone:
            self.stab_time_started = current_time
        elif in_hist_zone:
            target_speeds = Jacks()  # Stop movement
        
        # Check for stabilization
        if current_time - self.stab_time_started > self.node.required_stab_time:
            self.log("Leveling stabilized - proceeding to final leveling")
            self.node.set_state("final_leveling")
            return
        
        self.node.set_control_from_jacks(target_speeds)
        
        # Update integral for differential control
        if dt > 0:
            self.ctrl_jacks_integral += target_speeds * dt
        
        self.t_old = current_time


class FinalLevelingState(BaseState):
    """Final precision leveling using only the most needed jack."""
    
    def __init__(self, node):
        super().__init__(
            name="final_leveling",
            node=node,
            remember_as_prev=True
        )
        self.stab_time_started: Optional[float] = None
    
    def on_transition_to(self):
        """Initialize final leveling state."""
        self.stab_time_started = self.node.get_time()
        self.log("Starting final precision leveling")
    
    def do_work(self):
        """Fine-tune leveling using only the jack that needs most adjustment."""
        if self.node.any_jack_not_on_ground():
            self.log("Jack lost ground contact during final leveling", level=self.WARN)
            self.node.set_state("touchdown")
            return
        
        # Calculate angle errors
        roll_err = 0.0 - self.node.subs.level.roll
        pitch_err = 0.0 - self.node.subs.level.pitch
        
        # Convert to jack adjustments
        target_speeds_raw = self.node.angle_diff2len_diff(np.deg2rad(pitch_err), np.deg2rad(roll_err))
        # Diagnostic: raw vector before selecting a single jack
        # self.log(
        #     "Final raw: err roll=%.2f°, pitch=%.2f° -> L=%.3f R=%.3f Rear=%.3f"
        #     % (roll_err, pitch_err, target_speeds_raw.left, target_speeds_raw.right, target_speeds_raw.rear),
        #     period=0.3,
        # )
        target_speeds = target_speeds_raw
        
        # Use only the jack that needs the most adjustment
        target_speeds.only_max(self.node.final_level_ctrl)
        # Diagnostic: which jack chosen (non-zero)
        # self.log(
        #     "Final choose: L=%.3f R=%.3f Rear=%.3f (v=%.2f)"
        #     % (target_speeds.left, target_speeds.right, target_speeds.rear, self.node.final_level_ctrl),
        #     period=0.3,
        # )
        
        # Dead zone control with hysteresis
        if (abs(roll_err) > self.node.holding_dead_zone_hist_high or 
            abs(pitch_err) > self.node.holding_dead_zone_hist_high):
            self.stab_time_started = self.node.get_time()
        elif (abs(roll_err) < self.node.holding_dead_zone_hist_low and 
              abs(pitch_err) < self.node.holding_dead_zone_hist_low):
            target_speeds = Jacks()  # Stop movement
        
        # Check for stabilization
        current_time = self.node.get_time()
        if current_time - self.stab_time_started > self.node.required_stab_time:
            self.log("Final leveling complete - entering holding state")
            self.node.set_state("holding")
            return
        
        self.node.set_control_from_jacks(target_speeds)


class HoldingState(BaseState):
    """Hold level position and monitor for ground contact loss."""
    
    def __init__(self, node):
        super().__init__(
            name="holding",
            node=node,
            remember_as_prev=True
        )
    
    def on_transition_to(self):
        """Enter holding state."""
        self.log("Platform leveled - entering holding state")
    
    def do_work(self):
        """Maintain position and monitor for issues."""
        # Stop active control
        self.node.stop_control()
        
        # Safety check: ensure jacks maintain ground contact
        if not self.node.all_jacks_on_ground():
            self.handle_error(
                "Jack ground contact lost in holding state!",
                level=self.ERROR,
                event_code=self.node.events.RC_LEVEL_TOO_LOW
            )
            return
        
        # Check for grounding command from main state machine
        if (hasattr(self.node.subs, 'main_state_machine_status') and 
            self.node.subs.main_state_machine_status and
            self.node.subs.main_state_machine_status.current_state == "grounding"):
            self.log("Grounding command received - starting pulling sequence")
            self.node.set_state("pulling") 