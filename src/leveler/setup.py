from setuptools import setup

package_name = 'leveler'

setup(
    name=package_name,
    version='0.0.0',
    packages=[package_name],
    data_files=[
        ('share/ament_index/resource_index/packages',
            ['resource/' + package_name]),
        ('share/' + package_name, ['package.xml']),
        ('share/' + package_name + '/launch', ['launch/leveler.launch.xml']),
    ],
    install_requires=['setuptools'],
    zip_safe=True,
    maintainer='Roman Fedorenko',
    maintainer_email='<EMAIL>',
    description='Leveling system FSM for drill platform on jacks',
    license='Proprietary',
    tests_require=['pytest'],
    entry_points={
        'console_scripts': [
            'leveler_node = leveler.leveler_node:main',
        ],
    },
) 