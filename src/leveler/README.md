# Leveler - Система автоматического горизонтирования бурового станка

Модуль автоматического горизонтирования буровой установки на базе конечного автомата (FSM). Управляет тремя домкратами (левый передний, правый передний, задний) для выравнивания платформы и контролирует безопасность операций.

## Архитектура

### Основные компоненты
- **LevelerFSM** (`leveler_fsm.py`) - главный узел конечного автомата на базе BaseFSM
- **Jacks** (`jacks_model.py`) - математическая модель домкратов с арифметическими операциями
- **States** (`leveler_states.py`) - состояния автомата горизонтирования
- **Constants** (`constants.py`) - константы и коды событий

### Модель домкратов (Jacks)
Класс для работы с тремя домкратами как единым объектом:
- `left`, `right`, `rear` - скорости/позиции домкратов
- Поддержка арифметических операций (+, -, *, /, +=, -=, *=, /=)
- Методы нормализации (`norm_abs`), ограничения (`clip_*`), выбора максимума (`only_max`)

## Входы и выходы

### Входные топики

| Топик | Тип сообщения | Описание |
|-------|---------------|----------|
| `/jacks_switch_state` | `drill_msgs/JacksSwitchState` | Состояния домкратов (втянут/на земле) |
| `/level` | `drill_msgs/Level` | Углы ориентации платформы (крен/тангаж) |
| `/position` | `drill_msgs/Position` | RTK-позиция, высота Z, флаг `is_reliable` |
| `/gnss_position` | `drill_msgs/GNSS` | Сырые данные GNSS, поле `quality` для RTK-Fixed |
| `/permission` | `drill_msgs/Permission` | Разрешения системы (автоматически через BaseFSM) |
| `/robomode` | `drill_msgs/BoolStamped` | Режим робота (автоматически через BaseFSM) |
| `/main_state_machine_status` | `drill_msgs/StateMachineStatus` | Статус главного автомата (подписка в LevelerFSM) |

> Временно: т.к. `/jacks_switch_state` пока недоступен, узел подписывается на `/jacks_switch_state_raw` и публикует агрегированный `/jacks_switch_state` сам (костыль до появления штатного издателя).

### Выходные топики

| Топик | Тип сообщения | Описание |
|-------|---------------|----------|
| `/jacks_target_speed` | `drill_msgs/JacksCtrl` | Целевые скорости домкратов |
| `/events` | `drill_msgs/Event` | События и ошибки (автоматически через BaseFSM) |
| `/internal_report` | `drill_msgs/Report` | Внутренние отчеты (автоматически через BaseFSM) |
| `/leveler_status` | `drill_msgs/StateMachineStatus` | Статус FSM левелера |

## Конечный автомат состояний

### 1. INIT → PULLING
**Цель**: Инициализация системы, автоматический переход к втягиванию домкратов.

### 2. PULLING
**Цель**: Втянуть все домкраты в исходное положение
- **Если все домкраты на земле**: PID-регулирование по углам наклона для стабилизации
  - P-коэффициент: `pull_jacks_speed_p = 5.0`
  - I-коэффициент: `pull_jacks_speed_i = 0.04`, лимит: `pull_jacks_i_limit = 20.0`
  - D-коэффициент: `pull_jacks_speed_d = 2.0`
- **Если домкраты не на земле**: фиксированные скорости втягивания
  - Передние: `pulling_speed_front = 0.8`
  - Задний: `pulling_speed_rear = 0.8`
- **Переход**: PULLED когда все домкраты втянуты

### 3. PULLED
**Цель**: Ожидание команд в безопасном состоянии
- Остановка управления домкратами
- **Переход**: RESTORE_PULLED если домкрат не втянут, TOUCHDOWN при команде горизонтирования

### 4. RESTORE_PULLED
**Цель**: Восстановление втянутого состояния
- Принудительное втягивание всех домкратов со скоростью `pulling_speed = 0.8`
- **Переход**: PULLED когда все домкраты втянуты

### 5. TOUCHDOWN
**Цель**: Опускание домкратов до касания с землей
- Выдвижение со скоростями:
  - Передние: `touchdown_jacks_control_front = 0.3`
  - Задний: `touchdown_jacks_control_back = 0.3`
- **Защита от крена**: остановка домкрата при превышении `max_roll_above_start = 1.5°`
- **Переход**: LIFT когда все домкраты на земле

### 6. LIFT
**Цель**: Подъем платформы с синхронизацией углов
- Сохранение начальных углов крена/тангажа **и абсолютной высоты Z по RTK**
- P-регулирование: `jacks_sync_p = 3.0 * angle_error`
- Базовая скорость подъема: `common_lift_control = 0.2`
- **Условие перехода**: достижение прироста высоты `min_lift_height = 0.09 м` по сообщению `/position` (RTK-Z). При недоступности надежного RTK срабатывает резервный тайм-аут 5 с.
- **Переход**: LEVELING после определенного времени подъема (упрощенная логика без GPS)

### 7. LEVELING
**Цель**: Точное горизонтирование платформы
- **P-регуляторы**:
  - Крен: `leveling_roll_p = 9.0`
  - Тангаж: `leveling_pitch_p = 8.0`
- **Дифференциальное управление**: включается при накоплении интеграла управлений
- **Ограничение скорости**: `max_level_speed = 0.7`
- **Зоны нечувствительности**:
  - Активная: `holding_dead_zone = 0.5°`
  - Гистерезис: `holding_dead_zone_hist_high = 0.21°`, `holding_dead_zone_hist_low = 0.1°`
- **Переход**: FINAL_LEVELING после стабилизации `required_stab_time = 3.0с`

### 8. FINAL_LEVELING
**Цель**: Финальная точная подстройка
- Управление только максимально отклоненным домкратом
- Скорость: `final_level_ctrl = 0.25`
- **Переход**: HOLDING после стабилизации

### 9. HOLDING
**Цель**: Удержание горизонтального положения
- Остановка активного управления
- **Аварийная проверка**: потеря контакта домкратов с землей
- **Переход**: PULLING при команде снятия с домкратов

## Математическая модель

### Кинематическое преобразование углов
Функция `angle_diff2len_diff(dpitch, droll)` преобразует углы в изменения длин домкратов:
```python
half_len = platform_length / 2
half_width_front = platform_width / 2
left = half_len * (-sin(dpitch)) + half_width_front * cos(dpitch) * sin(droll)
right = half_len * (-sin(dpitch)) - half_width_front * cos(dpitch) * sin(droll)
rear = half_len * sin(dpitch)
```

## GPS RTK интеграция

| Топик | Тип | Использование |
|-------|-----|---------------|
| `/gnss_position` | `drill_msgs/GNSS` | Проверка `quality == 4` (RTK Fixed) |
| `/position` | `drill_msgs/Position` | Абсолютная высота Z и флаг `is_reliable` |

- **Обязательное условие**: при выполнении `touchdown / lift / leveling / final_leveling / holding` требуется «RTK Fixed». Достаточно выполнения одного из условий:<br/>  • `gnss_position.quality == 4`<br/>  • `position.is_reliable == true`  
- При невыполнении условий публикация события `no_rtk` и переход FSM в `failure`.
- В состоянии **LIFT** дополнительно используется `position.z` для определения достижения `min_lift_height`.

### Система безопасности

### Ограничения углов наклона
**На домкратах** (restrictions):
- Крен: `±6.5°`, критический порог: `±7.5°`
- Тангаж: `±6.5°`, критический порог: `±7.5°`

**При движении**:
- Крен: `±10.5°`, критический порог: `±11.5°`
- Тангаж: `±10.5°`, критический порог: `±11.5°`

### Контроль роста углов
- **Максимальный рост от старта**: `max_roll_above_start = 1.5°`, `max_pitch_above_start = 2.0°`
- **Контроль от уровня земли**: дополнительная проверка роста углов от начальных значений
- **Временной порог**: `angles_time_thr = 0.6с` для превышения лимитов

### Аварийные ситуации
1. **Превышение углов**: остановка с событием `angles_above_limits`
2. **Потеря RTK**: событие `no_rtk`, остановка работы
3. **Неисправность датчика уровня**: событие `level_sensor_failure`
4. **Рост углов при горизонтировании**: событие `rc_leveling`
5. **Потеря контакта домкратов**: событие `rc_level_too_low`
6. **Отказ домкратов**: событие `jack_failure`

## Параметры конфигурации

### Скорости и управление (nodes.yaml)
- `max_jacks_speed: 1.0` - максимальная скорость домкратов
- `pulling_speed*` - скорости втягивания
- `touchdown_jacks_control_*` - скорости опускания
- `max_level_speed: 0.7` - максимальная скорость горизонтирования

### Регуляторы
- `jacks_sync_p: 3.0` - P-коэффициент синхронизации при подъеме
- `leveling_roll_p: 9.0`, `leveling_pitch_p: 8.0` - P-коэффициенты горизонтирования
- `pull_jacks_speed_p/i/d` - PID-коэффициенты втягивания

### Геометрия платформы (vehicle.yaml)
- `platform_length: 8.5` - длина платформы (м)
- `platform_width: 3.8` - ширина платформы (м)

### Ограничения углов (vehicle.yaml)
- `max_allowed_roll/pitch` - ограничения на домкратах
- `max_allowed_roll_move/pitch_move` - ограничения при движении

## Особенности реализации

### Дифференциальное управление в LEVELING
Система работает в двух режимах:
1. **Только положительные скорости** (подъем): предотвращает потерю контакта с землей
2. **Дифференциальное управление**: включается когда интеграл управлений превышает порог

### Защита от опрокидывания в TOUCHDOWN
При опускании домкратов контролируется крен относительно начального значения:
- Если крен растет влево > `max_roll_above_start`: останавливается левый домкрат
- Если крен растет вправо > `max_roll_above_start`: останавливается правый домкрат

### Комментарий по безопасности
"При левелинге передние должны приземляться первее задних, а при граундинге задние должны втягиваться первее или как минимум не позднее передних" - требование мануала по безопасности.

## Запуск и использование

### Запуск ноды
```bash
ros2 launch leveler leveler.launch.xml
```

### Проверка статуса
```bash
ros2 topic echo /leveler_status
```

### Управление состояниями
```bash
# Принудительный переход в состояние
ros2 topic pub /set_state drill_msgs/StateCommand "{node_name: 'leveler', state: 'pulled'}"

# Возврат к предыдущему состоянию  
ros2 topic pub /set_state drill_msgs/StateCommand "{node_name: 'leveler', state: 'prev'}"
```

### Мониторинг домкратов
```bash
ros2 topic echo /jacks_target_speed
ros2 topic echo /jacks_switch_state
ros2 topic echo /level
```

## Диагностика и отладка

### Логирование
Система использует многоуровневое логирование через BaseFSM:
- **DEBUG**: Детальная отладочная информация
- **INFO**: Общая информация о работе
- **WARN**: Предупреждения (не критичные)
- **ERROR**: Ошибки с переходом в failure состояние

### События для мониторинга
- `no_rtk` - потеря RTK сигнала
- `level_sensor_failure` - отказ датчика уровня
- `angles_above_limits` - превышение углов
- `rc_leveling` - ошибки горизонтирования
- `rc_level_too_low` - потеря контакта с землей
- `jack_failure` - отказ домкратов

### Типичные проблемы
1. **Домкраты не втягиваются**: проверить датчики втянутого положения
2. **Превышение углов**: проверить калибровку датчика уровня
3. **Потеря контакта с землей**: проверить датчики касания земли
4. **Медленное горизонтирование**: настроить P-коэффициенты

## Зависимости

- `rclpy` - ROS2 Python API
- `base_node` - базовая нода и FSM
- `drill_msgs` - сообщения бурового робота
- `std_msgs` - стандартные ROS сообщения
- `numpy` - математические операции

## Лицензия

Proprietary. © Zyfra robotics. Все права защищены.