<launch>
  <set_env name="PARAM_SERVER_ADDRESS" value="localhost" />
  <set_env name="PARAM_SERVER_PORT" value="5055" />
  <set_env name="PARAM_SERVER_LOGLEVEL" value="info" />
  <set_env name="PARAM_DUMP_FILE" value="~/.drill_params_dump.json" />
  <set_env name="REPORT_RATE" value="5.0" />

  <include file="$(find-pkg-share params_server)/launch/normal.xml"/>
  <node pkg="can_decoder"
        exec="can_decoder"
        name="can_decoder"
        output="screen"
        respawn="true"
        respawn_delay="5"
  />
  <node pkg="can_encoder"
        exec="can_encoder"
        name="can_encoder"
        output="screen"
        respawn="true"
        respawn_delay="5"
  />

  <node pkg="rtk_connector"
        exec="rtk_connector"
        name="rtk_connector"
        output="screen"
        respawn="true"
        respawn_delay="5"
  />

  <node pkg="state_tracker"
        exec="state_tracker"
        name="state_tracker"
        output="screen"
        respawn="true"
        respawn_delay="5"
  />

  <node pkg="depth_tracker"
        exec="depth_tracker"
        name="depth_tracker"
        output="screen"
        respawn="true"
        respawn_delay="5"
  />

  <!-- <node pkg="main_state_machine"
        exec="main_state_machine"
        name="main_state_machine"
        output="screen"
        respawn="true"
        respawn_delay="5"
  /> -->

  <node pkg="remote_connector"
      exec="remote_connector"
      name="remote_connector"
      output="screen"
      respawn="true"
      respawn_delay="5"
  />

  <node pkg="path_follower"
        exec="path_follower"
        name="path_follower"
        output="screen"
        respawn="true"
        respawn_delay="5"
  />

  <node pkg="tracks_regulator"
        exec="tracks_regulator_node"
        name="tracks_regulator"
        output="screen"
        respawn="true"
        respawn_delay="5"
  />
</launch>

