
#LevelerNode:
#  rate: 10
#  debug: false
#  log_period: 2
#
#  max_roll_above_start: 1.5
#  max_pitch_above_start: 2.0
#
#  ground_detect_roll_thr: 0.5
#  ground_detect_pitch_thr: 0.3
#
#  angles_time_thr: 0.6
#  #  crt_angle_thr: 1
#
#  # control input for touchdown
#  touchdown_jacks_control_front: 1.0
#  touchdown_jacks_control_back: 0.7
#
#  pulling_down_speed_on_ground: 0.7
#  pulling_ang_err_to_restore: 2
#
#  allowed_modes:
#    "pulling":
#      - "idle"
#      - "grounding"
#
#    "restore_pulled":
#      - "moving"
#      - "idle"
#      - "wait_before_level"
#      - "leveling"
#      - "restore_string"
#
#    "pulled":
#      - "grounding"
#      - "moving"
#      - "leveling"
#      - "idle"
#      - "restore_string"
#      - "wait_before_level"
#
#    "leveling":
#      - "leveling"
#      - "grounding"
#      - "restore_string"
#
#    "holding":
#      - "calib"
#      - "post_calib"
#      - "drilling"
#      - "tower_tilt"
#      - "grounding"
#      - "leveling"
#      - "shaft_buildup"
#      - "shaft_stow"
#      - "wait_after_level"
#      - "rod_lock/unlock"
#      - "idle"
#      - "restore_string"
#
#    "final_leveling":
#      - "leveling"
#      - "restore_string"
#
#  pulling_speed: 0.7
#  pulling_speed_front: 0.8
#  pulling_speed_rear: 1.0
#
#  jacks_sync_p: 5
#
#  max_jacks_speed: 1.0
#  #  touchdown_max_err: 0.01
#
#  #  level_extra_height: 0.03
#  #  pull_supress_k: 3
#
#  pull_jacks_speed_p: 5
#  pull_jacks_speed_i: 0.04
#  pull_jacks_speed_d: 2
#  pull_jacks_i_limit: 20
#
#  max_level_speed: 0.7
#
#  # Roll and pitch PID-regulator gains for leveling state
#
#  leveling_roll_p: 9
#  leveling_pitch_p: 8
#  leveling_roll_i: 0.0
#  leveling_pitch_i: 0.0
#  leveling_roll_i_lim: 2.0
#  leveling_pitch_i_lim: 2.0
#  leveling_roll_d: 0
#  leveling_pitch_d: 0
#
#  # after the smallest element of the integral of
#  # the jack controls exceeds this value, we stop
#  # applying only positive controls
#  leveling_ctrl_jacks_integral_min: 0.025
#  common_lift_control: 0.2
#  min_lift_height: 0.09
#
#  final_level_ctrl: 0.25
#
#  holding_dead_zone: 0.5 #deg
#  holding_dead_zone_hist_low: 0.1 #deg
#  holding_dead_zone_hist_high: 0.21 #deg
#  required_stab_time: 3.0
#
#TowerControllerNode:
#  debug: true
#  rate: 20
#
#  # Режимы допускающие работу
#  allowed_modes:
#    - "tower_tilt"
#
#  # Допустимые углы бурения
#  allowed_angles:
#    '0': 0 # 0°
#    '5': 4.5
#    '10': 9.5
#    '15': 14.2
#
#  # Допустимая ошибка по углу наклона
#  allowed_tilt_error: 0.3
#
#  # Минимальная величина на которую должен отличаться угол из задания от текущего угла
#  min_angle_delta: 1
#
#  # T1
#  pins_move_time: 6
#
#  # T2
#  pins_no_reaction_time: 2
#
#  # T3
#  tilt_fixed_time: 3
#
#  # Коэффициент для преобразования отклонения текущего угла наклона от целевого в скорость изменения угла
#  tilt_error2speed: 0.3
#
#  # Время стабилизации мачты секунды
#  tilt_regulation_time: 40
#
#  allow_inclined_with_no_feedback: False

#MainStateMachineNode:
#  debug: true
#  rate: 10
#  log_period: 2
#
#  engine_idle_timeout: 30 # time in seconds to wait before low down engine rpm in Idle state
#  engine_nopermission_timeout: 30 # time in seconds to wait before low down engine rpm when no permission
#  low_rpm: 1200
#  high_rpm: 1800
#
#  use_z_depth_correction: True
#  correction_limit: 3.0
#
#  skip_moving: False
#  skip_leveling: False
#  skip_tower_tilt: False
#  skip_drilling: False
#
#  top_to_initial: 1.15 #m Hudbay 02
#
#  allowed_string_drawdown: 0.3 #m
#  allowed_string_exceeding: 0.45 #m
#  allowed_string_drawdown_for_tower_tilt: 0.5
#
#  wal_delay: 1.0
#  wbl_delay: 4.0
#
#  string_restore_speed: 0.08
#
#  # Дополнительная глубина забуривания при бурении с наращиванием метры
#  extra_depth: 0.2
#
#  # Максимальное время ожидания подготовки машины (торможение и т.д) к переходу в ДУ секунду
#  max_remote_prepare_waite_time: 3
#
#  # Допустимая максимальная скорость движения для перехода в ДУ метры в секунду
#  remote_prepare_max_speed: 0.05
#
#  no_move_allowed_err: 0.2
#
#  ignore_buildup_diff: 1.0

#DrillerNode:
#  debug: true
#  rate: 20
#  log_period: 2
#
#  user_feed_pressure: 186    # initial psp_regulated, controlled by RMO
#  user_rotation_speed: 95

#DrillRegulatorNode:
#  rate: 20
#  pdf_free_moving: 0.327 # pulldown force to move drill when not actually drilling
#  default_rot_torque: 1.0 # rot torque limit to send when incoming torque ctrl is zero (most likely free moving)
#  feed_reg:
#    i_saturation: 1.0 # absolute max for I component
#    p: 0.49 # P gain
#    i: 0.60 # I gain
#    d: 0.52 # D gain
#    ff: 0.53 # FF gain. out = ctrl*FF + PID(ctrl, fb)
#    min: -0.75 # max ctrl
#    max: 0.56 # min ctrl
#    out_min: -1 # max output
#    out_max: 1 # min output
#    d_term_tc: 0.6 # time constant to smootd D component
#    out_tc: 0.1 # time constant to smooth output
#
#  press_reg:
#    i_saturation: 0.8
#    p: 0.0002
#    i: 0.00025
#    d: 0.00
#    ff: 0.0012
#    min: 0
#    max: 276
#    out_min: 0
#    out_max: 1.0
#    d_term_tc: 0.2
#    out_tc: 1.3
#    one_side_i: True
#
#  #  # c НЕ работабщим счетчиком
#  #  rot_reg:
#  #    i_saturation: 0.3
#  #    p: 0
#  #    i: 0
#  #    d: 0
#  #    ff: 0.0068
#  #    min: -100
#  #    max: 100
#  #    out_min: -1
#  #    out_max: 1
#  #    d_term_tc: 0.2
#  #    out_tc: 0.1
#
#  # с работающим счетчиком
#  rot_reg:
#    i_saturation: 0.8
#    p: 0.0079 # 0.00135
#    i: 0.0025 # 0.0022
#    d: 0.0000 # 0.0001
#    ff: 0.0027
#    min: -130
#    max: 130
#    out_min: -1
#    out_max: 1
#    d_term_tc: 0.2
#    out_tc: 0.55

#JackRegulatorNode:
#  rate: 20
#  left:
#    i_saturation: 1.0
#    p: 0.5
#    i: 0.75
#    d: 0.35
#    ff: 0.60
#    min: -0.75
#    max: 0.56
#    out_min: -1
#    out_max: 1
#    d_term_tc: 0.6
#    out_tc: 0.1
#    force_zero: false
#
#  right:
#    i_saturation: 1.0
#    p: 0.5
#    i: 0.75
#    d: 0.35
#    ff: 0.60
#    min: -0.75
#    max: 0.56
#    out_min: -1
#    out_max: 1
#    d_term_tc: 0.6
#    out_tc: 0.1
#    force_zero: false
#
#  rear:
#    i_saturation: 1.0
#    p: 0.5
#    i: 0.75
#    d: 0.35
#    ff: 0.60
#    min: -0.75
#    max: 0.56
#    out_min: -1
#    out_max: 1
#    d_term_tc: 0.6
#    out_tc: 0.1
#    force_zero: false
#
#  max_ls_timeout: 0.5


#CollisionPreventerNode:
#  rate: 20
#  log_period: 2
#  safety_dist: 10
#  safety_dist_tailing: 1.5
#  max_ignoring_period: 20
#  tailing_include_dist: 2
#  min_dangerous_points: 10
#  min_dangerous_events: 5
#  danger_timeout: 10
#  obstacle_topic_timeout: 2
#  cameras_stop_classes:
#    worker: 0.9
#    car: 0.9
#    truck: 0.9
#  #    dozer: 0.9
#  cameras_stop_areas:
#    left:
#      - [ 614, 0 ]
#      - [ 1278, 40 ]
#      - [ 1278, 156 ]
#      - [ 1015, 476 ]
#      - [ 566, 381 ]
#    right:
#      - [ 257, 0 ]
#      - [ 919, 0 ]
#      - [ 826, 565 ]
#      - [ 284, 623 ]
#  ignoring_period: 8
#
#ArmControllerNode:
#  rate: 10
#  # Допустимые режимы для каждого из состояний
#  allowed_modes:
#    - "drilling"
#    - "tower_tilt"
#    - "shaft_buildup"
#    - "shaft_stow"
#    - "idle"
#    - 'moving'
#
#  # Допустимое время выполнения каждого состояния
#  opening_time: 30
#  closing_time: 30
#  open_push_time: 0.5
#  close_push_time: 0.5
#  grip_push_time: 1.5
#  no_reaction_time: 5.5
#  max_depth_to_close: 13.0
#
#DrillSupervisorNode:
#  rate: 30
#  log_period: 2
#
#  # Отслеживаемые ноды. Обязательно на основе BaseNode
#  nodes: [ ]
#  # Отслеживаемые топики
#  topic_paramkeys_to_monitor: [ ]
#  topics_to_monitor: [ ]
#  topics_to_restart_nodes:
#    "segmented_obstacles": "tailing_detector_driller"
#
#  restart_lock_time: 10
#
#  # Максимальная задержка между отчетами нод для определения их зависания
#  max_report_time_delay: 1
#
#  special_timeouts:
#    RemoteConnectorNode: 4
#    HalClientNode: 10
#    BagsRecorderNode: 1200
#
#  # Карта выбора действия при получении ошибки от ноды.
#  error_map:
#    # Имя ноды: Тип ошибки: Тип, обработчик которого вызывать
#    PlannerNode:
#      '5': 4
#    DriverNode:
#      '5': 4
#
#  # Временной отрезок для оценки частоты сообщений об ошибках секунды
#  error_rate_check_period: 1
#  # Максимальное допустимое число ошибок полученных за заданный временной отрезок
#  max_errors_rate: 15
#
#SimulatorNode:
#  rate: 50
#
#  x_start: 0
#  y_start: 0
#  yaw_start: 0
#  spindle_start: 1
#
#  max_jack_len: 4
#  min_jack_len: 1.4
#  left_jack_len_start: 1.4
#  right_jack_len_start: 1.4
#  rear_jack_len_start: 1.4
#
#  max_vert_pin_len: 0.3
#  max_incl_pin_len: 0.3
#  # Коэффициент зависимости скорости движения штифта от управляющей величины:
#  pin_c2f: 0.004
#  # Коэффициент зависимости скорости движения мачты от управляющей величины:
#  tilt_c2f: 0.2
#
#  max_tilt: 90
#
#  max_arm_len: 0.3
#  min_arm_len: 0
#  arm_c2f: 0.2
#
#  max_fork_len: 0.3
#  min_fork_len: 0
#  fork_c2f: 0.2
#
#  wrench_move_c2f: 0.2
#  wrench_grip_c2f: 0.2
#  max_wrench_move_len: 0.3
#  max_wrench_grip_len: 0.2
#
#  carousel_move_c2f: 1
#  carousel_rot_c2f: 0.2
#  max_carousel_move_len: 0.3
#  max_carousel_rot_len: 0.3
#
#  max_air_press: 4
#  min_air_press: 0
#  air_c2f: 3
#
#  cat_c2f: 3 # was 2.4
#  jack_c2f: 0.2
#  # Коэффициент зависимости скорости подачи от управляющей величины:
#  head_c2f: 0.6
#  # Коэффициент зависимости скорости вращения от управляющей величины:
#  rot_c2f: 100
#  # Коэффициент зависимости давления подачи от управляющей величины:
#  press_c2f: 0.01
#
#  # Уровень земли под домкратами
#  grnd_left: 0.3
#  grnd_right: 0.2
#  grnd_rear: 0
#
#  # Мин. скорость бурения метры/секунду
#  min_drilling_speed: 0.00028
#  # Макс. давление подачи тонны/квадратный метр
#  max_feed_pressure: 25
#  # Макс. скорость вращения обороты/секунду
#  max_rotation_speed: 2
#
#  layers:
#    # Кол-во слоев земли
#    total_num: 50
#
#    thickness:
#      mu: 2
#      sigma: 2
#      lower_bound: 1
#      upper_bound: 6
#
#StateTracker2Node:
#  rate: 100
#
#  # Параметры нод графа, которые могут быть заданы вручную через ГУИ:
#  # Имя ноды в параметрах графа
#  vibration_estimator:
#    window_size: 30
#
#  state_pub:
#    limit_period: 0.15
#
#  drill_state_pub:
#    limit_period: 0.1
#
#  inc_roll_pitch_align:
#    pitch_offset: 0.497499998658896
#    roll_offset: -0.363750003278255
#
#  tower_imu_roll_pitch_align:
#    pitch_offset: 0.0
#    roll_offset: 0.0
#
#  tower_inc_roll_pitch_align:
#    pitch_offset: -90.0
#    roll_offset: -90.0
#
#  front_imu_roll_pitch_align:
#    pitch_offset: 15.49 # 15.0908966064453
#    roll_offset: 178.43 # 178.31477355957
#
#  back_imu_roll_pitch_align:
#    pitch_offset: 3.65 #3.97684097290039
#    roll_offset: 179.90124130249
#
#  yaw_comp:
#    coefficient: 0.25
#
#  smooth_yaw:
#    time_window: 0.3
#
#  smooth_roll:
#    time_window: 0.01
#    input_rate: 100
#
#  smooth_pitch:
#    time_window: 0.01
#    input_rate: 100
#
#  yaw_update:
#    max_period: 0.2
#
#  x_update:
#    max_period: 0.2
#
#  y_update:
#    max_period: 0.2
#
#  z_update:
#    max_period: 0.2
#
#
#  wrench_st1_extend_len_check:
#    max_val: 25
#    min_val: 21.55
#
#  wrench_st1_retract_len_check:
#    max_val: 3.35
#    min_val: 0
#
#  wrench_st2_extend_len_check:
#    max_val: 0.8
#    min_val: 0.0
#
#  wrench_st2_retract_len_check:
#    max_val: 25
#    min_val: 22.55
#
#  wrench_st3_extend_len_check:
#    max_val: 25
#    min_val: 22.0
#
#  wrench_st3_retract_len_check:
#    max_val: 7.20
#    min_val: 0
#
#  arm_open_check:
#    max_val: 4.325
#    min_val: 0
#
#  arm_close_check:
#    max_val: 24
#    min_val: 21.3
#
#  arm_grip_check:
#    max_val: 24
#    min_val: 16.6
#
#  carousel_open_check:
#    max_val: 24
#    min_val: 21.6
#
#  carousel_closed_check:
#    max_val: 6.26
#    min_val: 0
#
#  fork_len_pred:
#    measure_2: 24
#    measure_1: 0
#    result_2: 0.43
#    result_1: 0
#
#  fork_len_rear_check:
#    max_val: 4.1
#    min_val: 0
#
#  fork_len_front_check:
#    max_val: 15
#    min_val: 8.5
#
#  jack_rl_len_pred:
#    measure_1: 0.268
#    measure_2: 0.926
#    result_1: 0.15
#    result_2: 6.1
#
#  jack_rr_len_pred:
#    measure_1: 0.268
#    measure_2: 0.926
#    result_1: 0.22
#    result_2: 6.4
#
#
#  jack_fl_len_pred:
#    measure_1: 0.268
#    measure_2: 0.926
#    result_1: 0.19
#    result_2: 5.31
#
#  jack_fr_len_pred:
#    measure_1: 0.268
#    measure_2: 0.926
#    result_1: 0.15
#    result_2: 6.20
#
#  jack_rr_and_rl_pulled:
#    quantity: 2
#
#  jack_fl_update:
#    max_period: 0.2
#
#  jack_fl_smooth:
#    time_window: 0.1
#
#  jack_fl_dt_smooth:
#    time_window: 0.4
#
#  jack_rr_update:
#    max_period: 0.2
#
#  jack_rr_smooth:
#    time_window: 0.1
#
#  jack_rl_update:
#    max_period: 0.2
#
#  jack_rl_smooth:
#    time_window: 0.1
#
#  jack_rear_max_len_update:
#    max_period: 0.2
#
#  jack_rear_max_len_smooth:
#    time_window: 0.1
#
#  jack_rear_len_update:
#    max_period: 0.2
#
#  jack_rear_len_smooth:
#    time_window: 0.1
#
#  jack_rear_len_dt_smooth:
#    time_window: 0.4
#
#  jack_fr_update:
#    max_period: 0.2
#
#  jack_fr_smooth:
#    time_window: 0.1
#
#  jack_fr_dt_smooth:
#    time_window: 0.4
#
#  smooth_x:
#    time_window: 0.4
#
#  smooth_y:
#    time_window: 0.4
#
#  smooth_z:
#    time_window: 0.4
#
#  smooth_w:
#    time_window: 0.35
#
#  smooth_vx:
#    time_window: 0.35
#
#  smooth_vy:
#    time_window: 0.35
#
#  smooth_v:
#    time_window: 0.45
#
#  rel_depth_update:
#    max_period: 0.1
#
#  smooth_feed_speed:
#    time_window: 0.45
#
#  head_rangefinder_pred:
#    measure_1: 61
#    result_1: 0
#    measure_2: 27437
#    result_2: 19.963060379
#
#  head_rangefinder_len_check:
#    max_val: 19.97
#    min_val: 0.0
#
#  rel_repth_proc:
#    reset_depth_min_delta: 0.05
#    high_jump_thr: 0.3
#    allowed_reset_depth_error: 0.04
#
#  smooth_depth:
#    time_window: 0.45
#
#  head_angle_proc:
#    encoder_cpr: 60
#    reverse: false
#
#  smooth_tower_inc_angle:
#    'time_window': 0.8
#
#  fl_ground_check:
#    max_val: 999
#    min_val: 5.25  # tune this if not works
#
#  fr_ground_check:
#    max_val: 999
#    min_val: 5.25 # tune this if not works
#
#  rl_ground_check:
#    max_val: 5 # tune this if not works
#    min_val: 0
#
#  rr_ground_check:
#    max_val: 5 # tune this if not works
#    min_val: 0
#
#  #  rot_speed_pred:
#  #    measure_2: 4.8808
#  #    measure_1: 3.3426
#  #    result_2: 110.0
#  #    result_1: 40.0
#
#  rot_speed_pred:
#    measure_2: 4.64
#    measure_1: 2.17
#    result_2: 110.0
#    result_1: 1.0
#
#  smooth_rot_speed:
#    time_window: 2
#
#  rot_press_pred:
#    measure_2: 3.845
#    measure_1: 2.717
#    result_2: 311.72
#    result_1: 209.187
#
#  smooth_rot_press:
#    time_window: 0.5
#
#  feed_press_pred:
#    measure_2: 3.1659
#    measure_1: 2.4497
#    result_2: 248.3492
#    result_1: 185.1242
#
#  smooth_feed_press:
#    time_window: 0.5
#
#  air_press_pred:
#    measure_2: 0.71474
#    measure_1: 0.19379
#    result_2: 3.864537
#    result_1: 0.358527
#
#  smooth_air_press:
#    time_window: 1.5
#
#  water_level_pred:
#    #    measure_2: 13719.0
#    measure_2: 4.186833015
#    #    measure_1: 9469.0
#    measure_1: 2.889797662
#    result_2: 0.8
#    result_1: 0.5625
#
#  fuel_level_pred:
#    measure_2: 4.26380205154
#    measure_1: 1.60258793831
#    result_2: 0.969
#    result_1: 0.287
#
#RodChangerNode:
#  rate: 20
#
#  #ALIGN_FORK
#  align_fork_timeout: 45
#
#  align_fork_angle:
#    buildup:
#      rod_1: 3.14
#      rod_2: 3.14
#    stow:
#      rod_1: 3.14
#      rod_2: 3.14
#
#  align_fork_depth:
#    buildup:
#      rod_1: 19.44
#      rod_2: 19.44
#    stow:
#      rod_1: 11.83
#      rod_2: 11.83
#
#  #ALIGN_CUP
#  align_cup_angle:
#    rod_1: -3.036
#    rod_2: -1.88
#  align_cup_depth:
#    rod_1: 10.80
#    rod_2: 10.80
#  align_cup_timeout: 20
#
#  #ALIGN_COMMON
#  align_feed_speed: 0.2
#  align_feed_press: 0.1
#  align_rotation_speed: 10
#
#  #LOCK\UNLOCK
#  lock_timeout: 20
#  unlock_timeout: 20
#  max_lock_attempts: 8
#
#  #TURN_CW
#  cw_rot_speed: 40
#  turn_cw_timeout: 20.0
#
#  #TURN_CCW
#  ccw_rot_speed: 10
#  turn_ccw_timeout: 20.0
#
#  #TURN_CW_CUP
#  turn_cw_cup_time: 1
#  turn_cw_cup_rotation_speed: 0.1
#
#  #BRAKEOUT
#  brakeout:
#    rotation_speed_ctrl: 120
#  brakeout_timeout: 5
#  max_brakeout_attempts: 5
#  brakeout_rev: 0.208
#
#  #CLOSE_WRENCH
#  close_wrench_timeout: 20.0
#
#  #APPLY_WRENCH
#  apply_wrench_timeout: 55
#  wrench_angle_threshold: 0.104
#  apply_wrench_attempts: 5
#
#  #OPEN_WRENCH
#  open_wrench_timeout: 20
#
#  #DETACH
#  rot_detach_speed: 60
#  detach_rev: 7
#  detach_timeout: 20
#
#  #DETACH_CUP
#  detach_cup_timeout: 20
#
#  #LIFT_TO_CAROUSEL
#  lift_to_carousel_depth: 10.71
#  lift_to_carousel_speed: 0.6
#  lift_to_carousel_timeout: 45.0
#
#  #OPEN_CAROUSEL
#  open_carousel_timeout: 30
#
#  #CLOSE_CAROUSEL
#  close_carousel_timeout: 30
#
#  #APPROACH_CAROUSEL
#  approach_carousel_timeout: 30
#  approach_depth: 10.86
#
#  #INSERT
#  insert_max_pressure: 5.7
#  insert_max_attempts: 8
#  insert_timeout: 20
#  insert_depth:
#    rod_1: 11.03
#    rod_2: 11.03
#
#  #SCREWING
#  feed_press_screwing: 0.05
#  feed_speed_screwing: 0.02
#  rot_screwing_speed: 50
#  screwing_pressure_threshold: 270
#  max_feed_press: 15
#  screwing_timeout: 40.0
#
#  #NEW_ROD_SCREWING
#  new_rod_screwing_timeout: 40.0
#
#  #ARM
#  arm_timeout: 30
#
#  #CLOSE_ARM
#  close_arm_timeout: 30
#
#  #OPEN_ARM
#  open_arm_timeout: 30
#
#  #OPEN_FORKARM
#  open_forkarm_timeout: 20
#
#  #PULL_OUT
#  pull_out_depth: 10.78
#  pull_out_timeout: 10
#
#  #APPROACH_FORK
#  approach_fork_depth:
#    buildup:
#      rod_1: 11.56
#      rod_2: 11.56
#    stow:
#      rod_1: 19.25
#      rod_2: 19.25
#  approach_fork_timeout: 45
#
#  #FINAL_POSITION
#  final_position_timeout: 45
#
#  #COMMON
#  depth_error: 0.03
#  angle_error: 0.052
#  feed_speed_normal: 0.5
#  feed_press_normal: 0.1
#  rotation_speed_normal: 40
#  fix_time: 0.1
#
#  allowed_modes:
#    - "shaft_buildup"
#    - "shaft_stow"
#
#WrenchControllerNode:
#  rate: 20
#  open_speed: 1.0
#  close_speed: 1.0
#  opening_time: 15.0
#  closing_time: 15.0
#  turn_time: 15.0
#  release_time: 15.0
#  allowed_modes:
#    - "shaft_buildup"
#    - "shaft_stow"
#    - "idle"
#    - "drilling"
#
#
#CarouselControllerNode:
#  rate: 5
#  open_speed: 1.0
#  close_speed: 1.0
#  rotate_speed: 1.0
#  max_oc_time: 10
#  max_turn_time: 10
#
#  allowed_modes:
#    - "shaft_buildup"
#    - "shaft_stow"
#    - "idle"
#
#RCSAdapterNode:
#  rate: 30
#
#  rcs_ip: "**************"
#  rcs_port: 3737
#
#  robot_ip: "0.0.0.0"
#  robot_port: 3738
#
#  max_package_size: 1024
#
#VehicleSupervisorNode:
#  rate: 20
#
#  restrictions_off_mode_duration: 60
#  control_check_time_window: 3
#  max_repetitions: 20
#
#  topics_to_monitor:
#    platform_level:
#      msg_type: 'drill_msgs/PlatformState'
#      validation_parameters:
#        roll:
#          min_val: '/Vehicle/restrictions/min_allowed_roll'
#          max_val: '/Vehicle/restrictions/max_allowed_roll'
#          thr_level_0: '/Vehicle/restrictions/crt_angle_thr_1'
#          thr_level_1: '/Vehicle/restrictions/crt_angle_thr_2'
#          allow_level_2: true
#          changeability_check: true
#          event_code: 'roll_critical'
#        pitch:
#          min_val: '/Vehicle/restrictions/min_allowed_pitch'
#          max_val: '/Vehicle/restrictions/max_allowed_pitch'
#          thr_level_0: '/Vehicle/restrictions/crt_angle_thr_1'
#          thr_level_1: '/Vehicle/restrictions/crt_angle_thr_2'
#          allow_level_2: true
#          changeability_check: true
#          event_code: 'pitch_critical'
#        timestamp:
#          timeout: 1.5
#
#    wrench_switch_state_raw:
#      msg_type: 'drill_msgs/WrenchSwitchState'
#      validation_parameters:
#        stage_len_1:
#          changeability_check: true
#          min_val: 15.6
#          max_val: 21.6
#          thr_level_0: 0.05
#          thr_level_1: 0.5
#          allow_level_2: true
#          event_code: 'wrench_sensor1_failure'
#        stage_len_2:
#          changeability_check: true
#          min_val: 12.6
#          max_val: 18.7
#          thr_level_0: 0.05
#          thr_level_1: 0.5
#          allow_level_2: true
#          event_code: 'wrench_sensor2_failure'
#        stage_len_3:
#          changeability_check: true
#          min_val: 12.5
#          max_val: 15.7
#          thr_level_0: 0.05
#          thr_level_1: 0.5
#          allow_level_2: true
#          event_code: 'wrench_sensor3_failure'
#        timestamp:
#          timeout: 1.5
#
#    rotation_speed:
#      msg_type: 'drill_msgs/FloatStamped'
#      validation_parameters:
#        value:
#          changeability_check: true
#          min_val: 2
#          max_val: 4.85
#          thr_level_0: 0.05
#          thr_level_1: 0.5
#          allow_level_2: true
#          event_code: 'rpm_sensor_failure'
#        timestamp:
#          timeout: 1.5
#
#    carousel_switch_state_raw:
#      msg_type: 'drill_msgs/CarouselSwitchState'
#      validation_parameters:
#        main_len:
#          changeability_check: true
#          min_val: 6.1
#          max_val: 21.85
#          thr_level_0: 0.05
#          thr_level_1: 0.5
#          allow_level_2: true
#          event_code: 'carousel_linear_sensor_failure'
#        timestamp:
#          timeout: 1.5
#
#    fork_switch_state_raw:
#      msg_type: 'drill_msgs/ForkSwitchState'
#      validation_parameters:
#        length:
#          changeability_check: true
#          min_val: 6.4
#          max_val: 10.6
#          thr_level_0: 0.05
#          thr_level_1: 0.1
#          allow_level_2: true
#          event_code: 'fork_linear_sensor_failure'
#        timestamp:
#          timeout: 1.5
#
#
#    arm_switch_state_raw:
#      msg_type: 'drill_msgs/ArmState'
#      validation_parameters:
#        stage_1:
#          changeability_check: true
#          min_val: 4.1
#          max_val: 21.65
#          thr_level_0: 0.05
#          thr_level_1: 0.1
#          allow_level_2: true
#          event_code: 'arm_linear_sensor1_failure'
#        stage_2:
#          changeability_check: true
#          min_val: 1.7
#          max_val: 17.55
#          thr_level_0: 0.05
#          thr_level_1: 0.1
#          allow_level_2: true
#          event_code: 'arm_linear_sensor2_failure'
#        timestamp:
#          timeout: 1.5
#
#    jacks_state_raw:
#      msg_type: 'drill_msgs/JacksState'
#      validation_parameters:
#        left_len:
#          changeability_check: true
#          min_val: 1.3
#          max_val: 21.9
#          thr_level_0: 0.05
#          thr_level_1: 0.1
#          allow_level_2: true
#          event_code: 'left_jack_linear_sensor_failure'
#        right_len:
#          changeability_check: true
#          min_val: 1.3
#          max_val: 21.9
#          thr_level_0: 0.05
#          thr_level_1: 0.1
#          allow_level_2: true
#          event_code: 'right_jack_linear_sensor_failure'
#        rear_left_len:
#          changeability_check: true
#          min_val: 1.3
#          max_val: 17.8
#          thr_level_0: 0.05
#          thr_level_1: 0.1
#          allow_level_2: true
#          event_code: 'rear_left_jack_linear_sensor_failure'
#        rear_right_len:
#          changeability_check: true
#          min_val: 1.3
#          max_val: 17.8
#          thr_level_0: 0.05
#          thr_level_1: 0.1
#          allow_level_2: true
#          event_code: 'rear_right_jack_linear_sensor_failure'
#        timestamp:
#          timeout: 1.5
#
#    pressure_state_raw:
#      msg_type: 'drill_msgs/PressureState'
#      validation_parameters:
#        rotation_pressure:
#          changeability_check: true
#          min_val: 0.3
#          max_val: 4.3
#          thr_level_0: 0.05
#          thr_level_1: 0.1
#          allow_level_2: true
#          event_code: 'rotation_pressure_sensor_failure'
#        feed_pressure:
#          changeability_check: true
#          min_val: 0.25
#          max_val: 4.0
#          thr_level_0: 0.05
#          thr_level_1: 0.1
#          allow_level_2: true
#          event_code: 'feed_pressure_sensor_failure'
#        air_pressure:
#          changeability_check: true
#          min_val: 0.05
#          max_val: 2.15
#          thr_level_0: 0.02
#          thr_level_1: 0.1
#          allow_level_2: true
#          event_code: 'air_pressure_sensor_failure'
#        timestamp:
#          timeout: 1.5
#
#    head_rangefinder:
#      msg_type: 'drill_msgs/FloatStamped'
#      validation_parameters:
#        value:
#          changeability_check: true
#        timestamp:
#          timeout: 1.5

#    engine_state:
#      msg_type: "drill_msgs/EngineState"
#      validation_parameters:
#        engine_speed:
#          max_val: 1950
#        coolant_temperature:
#          min_val: -40
#          max_val: 97
#        engine_oil_pressure:
#          min_val: 120
#          max_val: 600


#RoboModeControllerNode:
#  rate: 10
#  can_module_topic: "/can_2/rx"

#DustFlapsControllerNode:
#  rate: 10
#  allowed_modes:
#    - "drilling"
#    - "grounding"
#    - "idle"
#    - "moving"
#    - "wait_after_level"
#
#  # Допустимое время выполнения каждого состояния
#  opening_time: 30
#  closing_time: 30
#  open_push_time: 1.5
#  close_push_time: 1.5
#  no_reaction_time: 4.0
#
#BagsRecorderNode:
#  rate: 5
#  drill_bags_recorder_lidar: "drill_bags_recorder_lidar"
#  drill_bags_recorder_nolidar: "drill_bags_recorder_nolidar"
#  #  record_script_nolidar: "rosbag record --split --duration=1m -a -o drill_nolidar"
#  #  record_script_lidar: "rosbag record --split --duration=1m -a -o drill_withlidar"
#  bags_prefix_nolidar: "drill_nolidar"
#  bags_prefix_withlidar: "drill_withlidar"
#  split_duration_min: 1
#  max_save_minutes: 30
#  min_save_minutes: 3
#  do_recording: true        # starts rosbag record with given params if true or expect it is run externally (if false),
#  # in such a case be sure to sync settings (path, prefix, split duration)
#  extra_duration_min: 2     # extra duration to add to minutes_to_save (from last main action)
#  wait_before_copy_min: 1   # sleep given minutes before copying bags to wait current active bag would be finished
#  # (should correspond to --split --duration param of rosbag record)
#  lidars_minutes_to_save: 3  # always save lidar bags of given lifetime
#  topics_nolidar:
#    - /ArmControllerNodeStatus
#    - /DEBUG_correction_ang
#    - /DEBUG_path_deviation
#    - /DEBUG_rotation_speed
#    - /DEBUG_target_yaw_pose
#    - /DEBUG_yaw
#    - /DEBUG_yaw_sp
#    - /DrillerNodeStatus
#    - /DustFlapsControllerNodeStatus
#    - /LevelerNodeStatus
#    - /MainStateMachineNodeStatus
#    - /SC/orientation
#    - /SC/position
#    - /SC/quality
#    - /SC/speed
#    - /SC/trimble_nmea_gga
#    - /area
#    - /arm_action
#    - /arm_control
#    - /arm_switch_state
#    - /back_imu_data
#    - /bags_save_cmd
#    - /board_inc_angles
#    - /can_1/tx
#    - /can_2/rx
#    - /can_2/tx
#    - /can_5/rx
#    - /can_5/tx
#    - /can_6/tx
#    - /can_node_log
#    - /carousel_switch_state
#    - /cat_control_smart
#    - /cats_control
#    - /compressor_control
#    - /current_robomode
#    - /drill_actuator
#    - /drill_depth_info
#    - /rmo_rock_type
#    - /rmo_hole_water
#    - /drill_propel_mode
#    - /drill_state
#    - /driller_action
#    - /driller_out
#    - /driver_out
#    - /dust_collector_control
#    - /dust_flaps_action
#    - /dust_flaps_control
#    - /dust_flaps_switch_state
#    - /emergency_control
#    - /emergency_status
#    - /engine_ctrl
#    - /engine_state
#    - /epiroc_inclination_calibrated
#    - /epiroc_inclination_raw
#    - /event
#    - /fan_control
#    - /fork_switch_state
#    - /fork_switch_state_raw
#    - /front_imu_data
#    - /fuel_level
#    - /fuel_level_raw
#    - /hal_permission
#    - /head_rangefinder
#    - /internal_error
#    - /internal_report
#    - /jacks_control
#    - /jacks_state
#    - /jacks_state_raw
#    - /lamp_control
#    - /last_finished_holeid
#    - /lights_control_direct
#    - /main_action
#    - /main_mode
#    - /main_sm_drill_out
#    - /maneuver_processing
#    - /manual_state
#    - /moving_error
#    - /permission
#    - /planned_route
#    - /planner_action
#    - /planner_status
#    - /platform_level
#    - /platform_level_epiroc
#    - /pressure_state
#    - /pressure_state_raw
#    - /progress_topic
#    - /recalculate_maneuver
#    - /remote_arm_control
#    - /remote_carousel_control
#    - /remote_cats_control
#    - /remote_compressor_control
#    - /remote_drill_actuator
#    - /remote_drill_propel_mode
#    - /remote_driller_actuator
#    - /remote_drilling_smart
#    - /remote_dust_flaps_control
#    - /remote_fan_control
#    - /remote_fork_control
#    - /remote_jacks_control
#    - /remote_moving
#    - /remote_tower_control
#    - /remote_type
#    - /remote_wrench_control
#    - /rmo_robomode_sp
#    - /rosout
#    - /rosout_agg
#    - /rotation_speed
#    - /selector_robomode_fb
#    - /selector_robomode_sp
#    - /speed_ctrl_stopped
#    - /st/cat_fb
#    - /st/slip
#    - /st/w
#    - /state
#    - /system_status
#    - /target
#    - /tf
#    - /tf_static
#    - /tower_action
#    - /tower_angle_ready
#    - /tower_control
#    - /tower_imu_data
#    - /tower_inc_angle
#    - /tower_state
#    - /tower_switch_state_raw
#    - /vibration_state
#    - /water_injection_auto
#    - /water_injection_control
#    - /water_level
#    - /water_level_raw
#    - /wrench_control
#    - /wrench_switch_state
#    - /wrench_switch_state_raw
#    - /camera_detections_left
#    - /camera_detections_right
#    - /obst_shortest_dist
#
#  topics_lidar: # in addition to topics_nolidar
#    - /segmented_obstacles
#    - /segmented_road
#    - /segmented_tailing
#    - /tailing_edge_flt
#    - /tailing_edge_raw
#    - /tailing_map
#    - /tailing_map_polygons
#    - /tailing_vis_array
#    - /velodyne_points
#    - /os_cloud_node/points
#
#  record_folder: "/media/vist/ADATA777/autobags/nolidar"
#  save_path: "/media/vist/ADATA777/autobags/bags_saved"
#  free_space_treshold_warn_gb: 3
#  free_space_treshold_stop_gb: 2
#  do_tar: false
#
#EngineControllerNode:
#  rate: 100
#  engine_topic: "/can_2/rx"
#  default_rpm: 1200
#
#AiConnectorNode:
#  rate: 10
#  port: 7777
#  cameras:
#    - 'left'
#    - 'right'
#
#ForkControlNode:
#  rate: 10
#
#  allowed_modes:
#    - "shaft_buildup"
#    - "shaft_stow"
#
#  opening_time: 5
#  closing_time: 5
#  start_timeout: 2