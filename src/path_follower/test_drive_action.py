#!/usr/bin/env python3

"""Test script to publish DriveAction with test trajectory."""

import math
import rclpy
from rclpy.node import Node
from std_msgs.msg import Header
from drill_msgs.msg import DriveAction, Path, PathPoint, Point2d, Position


class DriveActionTester(Node):
    """Test node for publishing DriveAction messages."""

    def __init__(self):
        super().__init__('drive_action_tester')
        
        # Publisher for drive actions
        self.drive_action_pub = self.create_publisher(
            DriveAction, '/drive_action', 10)
        
        # Subscriber for current position
        self.position_sub = self.create_subscription(
            Position, '/position', self.position_callback, 10)
        
        self.action_id = 1
        self.current_position = None
        self.position_timestamp = None
        
        # Vehicle parameters from vehicle.yaml
        self.tower2center = 2.8  # Distance from robot center to tower (forward)
        
        self.get_logger().info('Drive Action Tester initialized - waiting for current position...')

    def position_callback(self, msg: Position):
        """Update current position."""
        self.current_position = msg
        self.position_timestamp = self.get_clock().now()
        if hasattr(self, '_first_position'):
            return
        self._first_position = True
        self.get_logger().info(f'Current position received: ({msg.x:.2f}, {msg.y:.2f}, yaw={math.degrees(msg.yaw):.1f}°)')

    def get_current_position(self):
        """Get current position or return default if not available."""
        if self.current_position is None:
            self.get_logger().warn('No current position available, using default (0, 0, 0)')
            return 0.0, 0.0, 0.0
        
        # Check position age
        if hasattr(self, 'position_timestamp'):
            age = (self.get_clock().now() - self.position_timestamp).nanoseconds / 1e9
            if age > 2.0:  # Warn if position is older than 2 seconds
                self.get_logger().warn(f'Position data is {age:.1f}s old - may be stale')
        
        return self.current_position.x, self.current_position.y, self.current_position.yaw

    def update_position_and_log(self):
        """Update position by spinning once and log current state."""
        # Spin to get latest position
        rclpy.spin_once(self, timeout_sec=0.1)
        
        start_x, start_y, start_yaw = self.get_current_position()
        self.get_logger().info(f'Using current position for trajectory: ({start_x:.2f}, {start_y:.2f}, yaw={math.degrees(start_yaw):.1f}°)')
        return start_x, start_y, start_yaw

    def create_straight_segment(self, start_x, start_y, start_yaw, length, speed, is_backward=False):
        """Create straight line segment."""
        points = []
        num_points = max(5, int(length / 0.5))  # Point every 0.5m or minimum 5 points
        
        # For backward movement, robot moves in opposite direction to its heading
        direction_yaw = start_yaw + (math.pi if is_backward else 0)
        
        for i in range(num_points):
            progress = i / (num_points - 1)
            x = start_x + length * progress * math.cos(direction_yaw)
            y = start_y + length * progress * math.sin(direction_yaw)
            
            point = PathPoint()
            point.x = x
            point.y = y
            point.yaw = start_yaw  # Robot orientation stays the same
            point.speed = speed
            points.append(point)
        
        segment = Path()
        segment.points = points
        segment.is_backward = is_backward
        return segment, points[-1].x, points[-1].y, start_yaw  # Return end position

    def create_arc_segment(self, start_x, start_y, start_yaw, radius, arc_angle, speed, is_backward=False):
        """Create arc segment for turning."""
        points = []
        num_points = max(10, int(abs(arc_angle) / 0.2))  # Point every 0.2 rad or minimum 10 points
        
        # For backward movement, the turning logic is inverted
        if is_backward:
            # Backward turning: robot moves backward along the arc
            effective_yaw = start_yaw + math.pi  # Robot faces opposite direction when moving backward
            arc_angle = -arc_angle  # Invert turn direction for backward movement
        else:
            effective_yaw = start_yaw
        
        # Calculate center based on turn direction (positive angle = left turn)
        if arc_angle > 0:  # Left turn
            center_x = start_x - radius * math.sin(effective_yaw)
            center_y = start_y + radius * math.cos(effective_yaw)
        else:  # Right turn
            center_x = start_x + radius * math.sin(effective_yaw)
            center_y = start_y - radius * math.cos(effective_yaw)
        
        for i in range(num_points):
            progress = i / (num_points - 1)
            
            # Calculate current angle from center
            if arc_angle > 0:  # Left turn
                current_angle = effective_yaw - math.pi/2 + arc_angle * progress
            else:  # Right turn
                current_angle = effective_yaw + math.pi/2 + arc_angle * progress
            
            # Calculate point position
            x = center_x + radius * math.cos(current_angle)
            y = center_y + radius * math.sin(current_angle)
            
            # Calculate yaw at this point (robot orientation)
            if is_backward:
                yaw = start_yaw - arc_angle * progress  # Opposite rotation for backward
            else:
                yaw = start_yaw + arc_angle * progress
            
            point = PathPoint()
            point.x = x
            point.y = y
            point.yaw = yaw
            point.speed = speed
            points.append(point)
        
        segment = Path()
        segment.points = points
        segment.is_backward = is_backward
        
        # Return end position and yaw
        if is_backward:
            end_yaw = start_yaw - arc_angle
        else:
            end_yaw = start_yaw + arc_angle
        return segment, points[-1].x, points[-1].y, end_yaw

    def calculate_hole_position(self, robot_x, robot_y, robot_yaw):
        """Calculate hole position considering tower offset from robot center."""
        # Tower is offset forward from robot center by tower2center distance
        hole_x = robot_x + self.tower2center * math.cos(robot_yaw)
        hole_y = robot_y + self.tower2center * math.sin(robot_yaw)
        return hole_x, hole_y

    def publish_test_action(self):
        """Publish simple forward-turn-forward trajectory from current position."""
        # Get current position as starting point
        start_x, start_y, start_yaw = self.update_position_and_log()
        
        action = DriveAction()
        action.header = Header()
        action.header.stamp = self.get_clock().now().to_msg()
        action.id = self.action_id
        
        # Trajectory parameters
        forward_speed = 0.7  # m/s
        turn_speed = 0.3  # m/s
        
        # Segment 1: Forward straight (5m) from current position
        segment1, seg1_end_x, seg1_end_y, seg1_end_yaw = self.create_straight_segment(
            start_x=start_x, start_y=start_y, start_yaw=start_yaw,
            length=5.0, speed=forward_speed, is_backward=False)
        
        # Segment 2: Right turn (90 degrees, radius 3m)
        segment2, seg2_end_x, seg2_end_y, seg2_end_yaw = self.create_arc_segment(
            start_x=seg1_end_x, start_y=seg1_end_y, start_yaw=seg1_end_yaw,
            radius=3.0, arc_angle=-math.pi/2, speed=turn_speed, is_backward=False)
        
        # Segment 3: Forward straight (4m)
        segment3, seg3_end_x, seg3_end_y, seg3_end_yaw = self.create_straight_segment(
            start_x=seg2_end_x, start_y=seg2_end_y, start_yaw=seg2_end_yaw,
            length=4.0, speed=forward_speed, is_backward=False)
        
        # Add segments to action
        action.path_segments = [segment1, segment2, segment3]
        
        # Set hole position considering tower offset
        # Use actual final path point yaw (body orientation) for drill placement
        final_pt = segment3.points[-1]
        hole_x, hole_y = self.calculate_hole_position(final_pt.x, final_pt.y, final_pt.yaw)
        action.hole_position = Point2d()
        action.hole_position.x = hole_x
        action.hole_position.y = hole_y
        
        # Publish action
        self.drive_action_pub.publish(action)
        
        self.get_logger().info(f'Published DriveAction ID={self.action_id} from position ({start_x:.2f}, {start_y:.2f}, {math.degrees(start_yaw):.1f}°):')
        self.get_logger().info(f'  - Segment 1: Forward straight 5m at {forward_speed} m/s')
        self.get_logger().info(f'  - Segment 2: Right turn (π/2 rad, R=3m) at {turn_speed} m/s')
        self.get_logger().info(f'  - Segment 3: Forward straight 4m at {forward_speed} m/s')
        self.get_logger().info(f'  - Final hole position at ({action.hole_position.x:.2f}, {action.hole_position.y:.2f}) [with tower offset]')
        
        total_points = sum(len(seg.points) for seg in action.path_segments)
        self.get_logger().info(f'  - Total path points: {total_points}')
        
        self.action_id += 1

    def publish_simple_test(self):
        """Publish simple test action - just forward straight from current position."""
        # Get current position as starting point
        start_x, start_y, start_yaw = self.update_position_and_log()
        
        action = DriveAction()
        action.header = Header()
        action.header.stamp = self.get_clock().now().to_msg()
        action.id = 999
        
        # Simple forward straight segment from current position
        segment, seg_end_x, seg_end_y, seg_end_yaw = self.create_straight_segment(
            start_x=start_x, start_y=start_y, start_yaw=start_yaw,
            length=7.0, speed=0.5, is_backward=False)
        
        action.path_segments = [segment]
        
        # Set hole position considering tower offset
        final_pt = segment.points[-1]
        hole_x, hole_y = self.calculate_hole_position(final_pt.x, final_pt.y, final_pt.yaw)
        action.hole_position = Point2d()
        action.hole_position.x = hole_x
        action.hole_position.y = hole_y
        
        self.drive_action_pub.publish(action)
        self.get_logger().info(f'Published simple test DriveAction: 7m forward from ({start_x:.2f}, {start_y:.2f}, {math.degrees(start_yaw):.1f}°)')

    def publish_backward_test(self):
        """Publish backward test action."""
        # Get current position as starting point
        start_x, start_y, start_yaw = self.update_position_and_log()
        
        action = DriveAction()
        action.header = Header()
        action.header.stamp = self.get_clock().now().to_msg()
        action.id = 998
        
        # Backward straight segment from current position
        segment, seg_end_x, seg_end_y, seg_end_yaw = self.create_straight_segment(
            start_x=start_x, start_y=start_y, start_yaw=start_yaw,
            length=5.0, speed=0.3, is_backward=True)
        
        action.path_segments = [segment]
        
        # Set hole position considering tower offset
        final_pt = segment.points[-1]
        hole_x, hole_y = self.calculate_hole_position(final_pt.x, final_pt.y, final_pt.yaw)
        action.hole_position = Point2d()
        action.hole_position.x = hole_x
        action.hole_position.y = hole_y
        
        self.drive_action_pub.publish(action)
        self.get_logger().info(f'Published backward test DriveAction: 5m backward from ({start_x:.2f}, {start_y:.2f}, {math.degrees(start_yaw):.1f}°)')

    def publish_s_curve_test(self):
        """Publish S-curve test - left turn, straight, right turn."""
        start_x, start_y, start_yaw = self.update_position_and_log()
        
        action = DriveAction()
        action.header = Header()
        action.header.stamp = self.get_clock().now().to_msg()
        action.id = self.action_id
        
        speed = 0.6
        
        # Segment 1: Left turn (90 degrees)
        segment1, seg1_end_x, seg1_end_y, seg1_end_yaw = self.create_arc_segment(
            start_x=start_x, start_y=start_y, start_yaw=start_yaw,
            radius=3.0, arc_angle=math.pi/2, speed=speed, is_backward=False)
        
        # Segment 2: Straight forward (5m)
        segment2, seg2_end_x, seg2_end_y, seg2_end_yaw = self.create_straight_segment(
            start_x=seg1_end_x, start_y=seg1_end_y, start_yaw=seg1_end_yaw,
            length=5.0, speed=speed, is_backward=False)
        
        # Segment 3: Right turn (90 degrees) to create S-curve
        segment3, seg3_end_x, seg3_end_y, seg3_end_yaw = self.create_arc_segment(
            start_x=seg2_end_x, start_y=seg2_end_y, start_yaw=seg2_end_yaw,
            radius=3.0, arc_angle=-math.pi/2, speed=speed, is_backward=False)
        
        # Segment 4: Final straight (3m)
        segment4, seg4_end_x, seg4_end_y, seg4_end_yaw = self.create_straight_segment(
            start_x=seg3_end_x, start_y=seg3_end_y, start_yaw=seg3_end_yaw,
            length=3.0, speed=speed, is_backward=False)
        
        action.path_segments = [segment1, segment2, segment3, segment4]
        
        # Set hole position considering tower offset
        final_pt = segment4.points[-1]
        hole_x, hole_y = self.calculate_hole_position(final_pt.x, final_pt.y, final_pt.yaw)
        action.hole_position = Point2d()
        action.hole_position.x = hole_x
        action.hole_position.y = hole_y
        
        self.drive_action_pub.publish(action)
        self.get_logger().info(f'Published S-curve test DriveAction ID={self.action_id}: Left turn → Straight → Right turn → Straight')
        
        self.action_id += 1

    def publish_spiral_test(self):
        """Publish spiral test - decreasing radius left turns."""
        start_x, start_y, start_yaw = self.update_position_and_log()
        
        action = DriveAction()
        action.header = Header()
        action.header.stamp = self.get_clock().now().to_msg()
        action.id = self.action_id
        
        speed = 0.4
        segments = []
        current_x, current_y, current_yaw = start_x, start_y, start_yaw
        
        # Create spiral with decreasing radius - all left turns
        radii = [8.0, 6.0, 4.5, 3.5]  # Larger radii for 7m robot
        for i, radius in enumerate(radii):
            # Three-quarter turn (270 degrees) to create inward spiral
            turn_angle = 3*math.pi/2 if i == 0 else math.pi  # First turn bigger, then 180° turns
            segment, current_x, current_y, current_yaw = self.create_arc_segment(
                start_x=current_x, start_y=current_y, start_yaw=current_yaw,
                radius=radius, arc_angle=turn_angle, speed=speed, is_backward=False)
            segments.append(segment)
            
            # Short straight section between turns
            if i < len(radii) - 1:  # Not the last segment
                segment, current_x, current_y, current_yaw = self.create_straight_segment(
                    start_x=current_x, start_y=current_y, start_yaw=current_yaw,
                    length=2.0, speed=speed, is_backward=False)
                segments.append(segment)
        
        # Final straight to hole
        segment, current_x, current_y, current_yaw = self.create_straight_segment(
            start_x=current_x, start_y=current_y, start_yaw=current_yaw,
            length=3.0, speed=speed, is_backward=False)
        segments.append(segment)
        
        action.path_segments = segments
        
        # Set hole position considering tower offset
        # Use last point of the last segment for precise yaw
        last_seg = segments[-1]
        final_pt = last_seg.points[-1]
        hole_x, hole_y = self.calculate_hole_position(final_pt.x, final_pt.y, final_pt.yaw)
        action.hole_position = Point2d()
        action.hole_position.x = hole_x
        action.hole_position.y = hole_y
        
        self.drive_action_pub.publish(action)
        self.get_logger().info(f'Published spiral test DriveAction ID={self.action_id}: Inward spiral with radii {radii}')
        
        self.action_id += 1

    def publish_zigzag_test(self):
        """Publish zigzag test - alternating turns."""
        start_x, start_y, start_yaw = self.update_position_and_log()
        
        action = DriveAction()
        action.header = Header()
        action.header.stamp = self.get_clock().now().to_msg()
        action.id = self.action_id
        
        speed = 0.7
        segments = []
        current_x, current_y, current_yaw = start_x, start_y, start_yaw
        
        # Create zigzag pattern
        angles = [math.pi/4, -math.pi/2, math.pi/2, -math.pi/2, math.pi/4]  # 45°, -90°, 90°, -90°, 45°
        
        for i, angle in enumerate(angles):
            # Turn
            segment, current_x, current_y, current_yaw = self.create_arc_segment(
                start_x=current_x, start_y=current_y, start_yaw=current_yaw,
                radius=2.5, arc_angle=angle, speed=speed*0.7, is_backward=False)
            segments.append(segment)
            
            # Straight section
            length = 4.0 if i % 2 == 0 else 3.0  # Varying lengths
            segment, current_x, current_y, current_yaw = self.create_straight_segment(
                start_x=current_x, start_y=current_y, start_yaw=current_yaw,
                length=length, speed=speed, is_backward=False)
            segments.append(segment)
        
        action.path_segments = segments
        
        # Set hole position considering tower offset
        last_seg = segments[-1]
        final_pt = last_seg.points[-1]
        hole_x, hole_y = self.calculate_hole_position(final_pt.x, final_pt.y, final_pt.yaw)
        action.hole_position = Point2d()
        action.hole_position.x = hole_x
        action.hole_position.y = hole_y
        
        self.drive_action_pub.publish(action)
        self.get_logger().info(f'Published zigzag test DriveAction ID={self.action_id}: Alternating turns with straights')
        
        self.action_id += 1

    def publish_complex_backward_test(self):
        """Publish complex backward maneuver - forward, turn, backward with turn."""
        start_x, start_y, start_yaw = self.update_position_and_log()
        
        action = DriveAction()
        action.header = Header()
        action.header.stamp = self.get_clock().now().to_msg()
        action.id = self.action_id
        
        # Segment 1: Forward straight (4m)
        segment1, seg1_end_x, seg1_end_y, seg1_end_yaw = self.create_straight_segment(
            start_x=start_x, start_y=start_y, start_yaw=start_yaw,
            length=4.0, speed=0.6, is_backward=False)
        
        # Segment 2: Right turn (120 degrees)
        segment2, seg2_end_x, seg2_end_y, seg2_end_yaw = self.create_arc_segment(
            start_x=seg1_end_x, start_y=seg1_end_y, start_yaw=seg1_end_yaw,
            radius=3.0, arc_angle=-2*math.pi/3, speed=0.3, is_backward=False)
        
        # Segment 3: Backward straight (5m)
        segment3, seg3_end_x, seg3_end_y, seg3_end_yaw = self.create_straight_segment(
            start_x=seg2_end_x, start_y=seg2_end_y, start_yaw=seg2_end_yaw,
            length=5.0, speed=0.4, is_backward=True)
        
        # Segment 4: Backward left turn (90 degrees)
        segment4, seg4_end_x, seg4_end_y, seg4_end_yaw = self.create_arc_segment(
            start_x=seg3_end_x, start_y=seg3_end_y, start_yaw=seg3_end_yaw,
            radius=2.5, arc_angle=math.pi/2, speed=0.2, is_backward=True)
        
        action.path_segments = [segment1, segment2, segment3, segment4]
        
        # Set hole position considering tower offset
        final_pt = segment4.points[-1]
        hole_x, hole_y = self.calculate_hole_position(final_pt.x, final_pt.y, final_pt.yaw)
        action.hole_position = Point2d()
        action.hole_position.x = hole_x
        action.hole_position.y = hole_y
        
        self.drive_action_pub.publish(action)
        self.get_logger().info(f'Published complex backward test DriveAction ID={self.action_id}: Forward → Turn → Backward → Backward turn')
        
        self.action_id += 1

    def publish_alternative_test(self):
        """Publish alternative complex test - this is the correct one from original code."""
        # Get current position as starting point
        start_x, start_y, start_yaw = self.update_position_and_log()
        
        action = DriveAction()
        action.header = Header()
        action.header.stamp = self.get_clock().now().to_msg()
        action.id = self.action_id
        
        # Trajectory parameters
        forward_speed = 0.8  # m/s
        backward_speed = 0.5  # m/s
        turn_speed = 0.3  # m/s
        
        # Segment 1: Forward straight (7m) from current position
        segment1, seg1_end_x, seg1_end_y, seg1_end_yaw = self.create_straight_segment(
            start_x=start_x, start_y=start_y, start_yaw=start_yaw,
            length=7.0, speed=forward_speed, is_backward=False)
        
        # Segment 2: U-turn arc (180 degrees, radius 4m)
        segment2, seg2_end_x, seg2_end_y, seg2_end_yaw = self.create_arc_segment(
            start_x=seg1_end_x, start_y=seg1_end_y, start_yaw=seg1_end_yaw,
            radius=4.0, arc_angle=math.pi, speed=turn_speed, is_backward=False)
        
        # Segment 3: Backward straight (6m) - this was the good one
        segment3, seg3_end_x, seg3_end_y, seg3_end_yaw = self.create_straight_segment(
            start_x=seg2_end_x, start_y=seg2_end_y, start_yaw=seg2_end_yaw,
            length=6.0, speed=backward_speed, is_backward=True)
        
        # Add segments to action
        action.path_segments = [segment1, segment2, segment3]
        
        # Set hole position considering tower offset
        final_pt = segment3.points[-1]
        hole_x, hole_y = self.calculate_hole_position(final_pt.x, final_pt.y, final_pt.yaw)
        action.hole_position = Point2d()
        action.hole_position.x = hole_x
        action.hole_position.y = hole_y
        
        # Publish action
        self.drive_action_pub.publish(action)
        
        self.get_logger().info(f'Published Alternative DriveAction ID={self.action_id} from position ({start_x:.2f}, {start_y:.2f}, {math.degrees(start_yaw):.1f}°):')
        self.get_logger().info(f'  - Segment 1: Forward straight 7m at {forward_speed} m/s')
        self.get_logger().info(f'  - Segment 2: U-turn arc (π rad, R=4m) at {turn_speed} m/s')
        self.get_logger().info(f'  - Segment 3: Backward straight 6m at {backward_speed} m/s')
        self.get_logger().info(f'  - Final hole position at ({action.hole_position.x:.2f}, {action.hole_position.y:.2f}) [with tower offset]')
        
        total_points = sum(len(seg.points) for seg in action.path_segments)
        self.get_logger().info(f'  - Total path points: {total_points}')
        
        self.action_id += 1

    def publish_cancel_action(self):
        """Publish cancel action."""
        action = DriveAction()
        action.header = Header()
        action.header.stamp = self.get_clock().now().to_msg()
        action.id = -2  # Cancel ID
        action.path_segments = []
        action.hole_position = Point2d()
        
        self.drive_action_pub.publish(action)
        self.get_logger().info('Published cancel DriveAction (ID=-2)')


def main(args=None):
    """Main entry point."""
    rclpy.init(args=args)
    
    tester = DriveActionTester()
    
    try:
        # Wait for position data
        print("Waiting for current position...")
        start_time = tester.get_clock().now()
        while rclpy.ok() and tester.current_position is None:
            rclpy.spin_once(tester, timeout_sec=0.1)
            # Timeout after 10 seconds
            if (tester.get_clock().now() - start_time).nanoseconds > 10e9:
                print("Warning: No position received after 10s, will use default (0,0,0)")
                break
        
        # Interactive mode
        print("\nDrive Action Tester Commands:")
        print("  1 - Send simple test (7m forward)")
        print("  2 - Send L-shape test (forward-turn-forward)")
        print("  3 - Send backward test (5m backward)")
        print("  4 - Send U-turn test (forward-turn-backward)")
        print("  5 - Send S-curve test (left-straight-right-straight)")
        print("  6 - Send spiral test (inward spiral with decreasing radius)")
        print("  7 - Send zigzag test (alternating turns)")
        print("  8 - Send complex backward test (forward-turn-backward-backward_turn)")
        print("  c - Cancel current action")
        print("  p - Show current position")
        print("  q - Quit")
        
        while rclpy.ok():
            try:
                cmd = input("\nEnter command: ").strip().lower()
                
                if cmd == '1':
                    tester.publish_simple_test()
                elif cmd == '2':
                    tester.publish_test_action()
                elif cmd == '3':
                    tester.publish_backward_test()
                elif cmd == '4':
                    tester.publish_alternative_test()
                elif cmd == '5':
                    tester.publish_s_curve_test()
                elif cmd == '6':
                    tester.publish_spiral_test()
                elif cmd == '7':
                    tester.publish_zigzag_test()
                elif cmd == '8':
                    tester.publish_complex_backward_test()
                elif cmd == 'c':
                    tester.publish_cancel_action()
                    # Spin once to process callbacks for cancel
                    rclpy.spin_once(tester, timeout_sec=0.1)
                elif cmd == 'p':
                    # Update position before showing
                    rclpy.spin_once(tester, timeout_sec=0.1)
                    if tester.current_position:
                        pos = tester.current_position
                        age = (tester.get_clock().now() - tester.position_timestamp).nanoseconds / 1e9 if tester.position_timestamp else float('inf')
                        print(f"Current position: ({pos.x:.2f}, {pos.y:.2f}, yaw={math.degrees(pos.yaw):.1f}°) [age: {age:.1f}s]")
                    else:
                        print("No current position available")
                elif cmd == 'q':
                    break
                else:
                    print("Unknown command. Use 1-8, c, p, or q")
                
            except EOFError:
                break
                
    except KeyboardInterrupt:
        tester.get_logger().info('Test interrupted')
    except Exception as e:
        tester.get_logger().error(f'Test error: {e}')
    finally:
        tester.destroy_node()
        rclpy.shutdown()


if __name__ == '__main__':
    main() 