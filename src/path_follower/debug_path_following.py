#!/usr/bin/env python3
"""
Debug path following to understand why robot doesn't follow trajectory.
"""

import math
import sys
import os
from typing import List, Tuple

# Add path_follower module to path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', 'path_follower'))
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'test'))

from test_mocks import MockPathPoint, MockPath, MockPosition
from test_common import RobotSimulator, create_test_controller

def debug_simple_straight_path():
    """Debug simple straight path following."""
    print("🚗 DEBUGGING STRAIGHT PATH FOLLOWING")
    print("=" * 60)
    
    # Create simple 5m straight path with higher speed
    target_points = [(0.0, 0.0), (1.0, 0.0), (2.0, 0.0), (3.0, 0.0), (4.0, 0.0), (5.0, 0.0)]
    path_points = [MockPathPoint(x=x, y=y, speed=0.5) for x, y in target_points]  # Higher speed
    mock_path = MockPath(path_points)
    
    # Create robot and controller
    robot = RobotSimulator(sensor_offset=0.0)  # No offset first
    controller = create_test_controller()
    controller.set_sensor_offset_mode(False)
    
    # Initialize robot at start
    robot.reset(0.0, 0.0, 0.0)  # Start at (0,0) facing east
    current_point_id = 0
    
    print(f"Target path: {target_points}")
    print(f"Robot start: ({robot.x:.3f}, {robot.y:.3f}, {math.degrees(robot.yaw):.1f}°)")
    print(f"Path points: {len(mock_path.points)}")
    
    print(f"\nStep-by-step debugging:")
    
    for step in range(100):  # More steps to reach points
        position = robot.get_position()
        speed_state = robot.get_speed_state()
        
        # Check if we should advance to next point
        # IMPORTANT: current_point_id must not exceed len(points) - 2 for valid segments
        if current_point_id < len(mock_path.points) - 2:
            current_target = mock_path.points[current_point_id + 1]  # Target is NEXT point
            distance_to_target = math.sqrt(
                (position.x - current_target.x)**2 + 
                (position.y - current_target.y)**2
            )
            
            # Debug point advancement
            if step < 5:  # Show details for first few steps
                print(f"    Check advance: pos=({position.x:.3f},{position.y:.3f}) "
                      f"target=({current_target.x:.3f},{current_target.y:.3f}) "
                      f"dist={distance_to_target:.3f}")
            
            if distance_to_target < 0.08:  # Smaller threshold for advancement
                current_point_id += 1
                print(f"  → Advanced to point {current_point_id}")
                
        # Check completion - robot should reach final point
        if current_point_id >= len(mock_path.points) - 2:
            final_target = mock_path.points[-1]
            distance_to_final = math.sqrt(
                (position.x - final_target.x)**2 + (position.y - final_target.y)**2
            )
            if step % 10 == 0:  # Debug final approach
                print(f"    Final approach: pos=({position.x:.3f},{position.y:.3f}) "
                      f"final=({final_target.x:.3f},{final_target.y:.3f}) "
                      f"dist={distance_to_final:.3f}")
        
        # Calculate control
        try:
            control_result = controller.calculate_path_following_control(
                mock_path, position, current_point_id, speed_state
            )
            left_speed = control_result.left_speed
            right_speed = control_result.right_speed
            
            # Show more control details for first few steps
            if step < 5 or step % 10 == 0:  # Show every 10th step after first 5
                target_point = mock_path.points[min(current_point_id, len(mock_path.points) - 1)]
                print(f"Step {step:2d}: pos=({position.x:.3f},{position.y:.3f}) "
                      f"target_id={current_point_id} target=({target_point.x:.3f},{target_point.y:.3f}) "
                      f"speeds=({left_speed:.3f},{right_speed:.3f}) path_speed={target_point.speed:.3f}")
            elif step < 30:  # Show first 30 steps in short format
                print(f"Step {step:2d}: pos=({position.x:.3f},{position.y:.3f}) "
                      f"target_id={current_point_id} "
                      f"speeds=({left_speed:.3f},{right_speed:.3f})")
            
        except Exception as e:
            print(f"Step {step:2d}: CONTROL ERROR: {e}")
            left_speed = right_speed = 0.0
            
        # Check if control makes sense
        if abs(left_speed) < 0.001 and abs(right_speed) < 0.001:
            print(f"  ⚠️  Zero speeds - robot will not move!")
            
        if abs(left_speed) > 1.0 or abs(right_speed) > 1.0:
            print(f"  ⚠️  Very high speeds - may be unstable!")
        
        # Apply control
        robot.step(left_speed, right_speed, 0.1)
        
        # Check progress
        final_target = mock_path.points[-1]
        distance_to_end = math.sqrt(
            (robot.x - final_target.x)**2 + (robot.y - final_target.y)**2
        )
        
        if distance_to_end < 0.05:
            print(f"  ✅ Reached end point!")
            break
            
        if step > 80:  # Stop if taking too long
            break
    
    print(f"\nFinal position: ({robot.x:.3f}, {robot.y:.3f})")
    print(f"Target end: ({target_points[-1][0]:.3f}, {target_points[-1][1]:.3f})")
    error = math.sqrt((robot.x - target_points[-1][0])**2 + (robot.y - target_points[-1][1])**2)
    print(f"Final error: {error:.3f}m")

def debug_controller_parameters():
    """Debug controller parameters."""
    print(f"\n⚙️  DEBUG CONTROLLER PARAMETERS")  
    print("=" * 60)
    
    controller = create_test_controller()
    
    # Check if controller has necessary attributes
    attrs_to_check = [
        'lookahead_distance', 'lateral_correction_gain', 'heading_error_gain',
        'max_speed', 'min_speed', 'vehicle_width'
    ]
    
    print("Controller parameters:")
    for attr in attrs_to_check:
        if hasattr(controller, attr):
            value = getattr(controller, attr)
            print(f"  {attr}: {value}")
        else:
            print(f"  {attr}: MISSING ❌")
    
    # Test lookahead calculation
    print(f"\nTesting lookahead calculation:")
    target_points = [(0.0, 0.0), (1.0, 0.0), (2.0, 0.0), (3.0, 0.0), (5.0, 0.0)]
    path_points = [MockPathPoint(x=x, y=y, speed=0.3) for x, y in target_points]
    mock_path = MockPath(path_points)
    
    try:
        li = controller.compute_lookahead(mock_path, 0)
        if li.point:
            print(f"  Lookahead point: ({li.point.x:.3f}, {li.point.y:.3f}) d={li.distance:.2f}m")
        else:
            print(f"  Lookahead point: None ❌")
    except Exception as e:
        print(f"  Lookahead error: {e}")

def debug_path_structure():
    """Debug path structure."""
    print(f"\n📍 DEBUG PATH STRUCTURE")
    print("=" * 60)
    
    # Create test path
    target_points = [(0.0, 0.0), (1.0, 0.0), (2.0, 0.0)]
    path_points = [MockPathPoint(x=x, y=y, speed=0.3) for x, y in target_points]
    mock_path = MockPath(path_points)
    
    print(f"Path structure:")
    print(f"  Number of points: {len(mock_path.points)}")
    print(f"  Is backward: {mock_path.is_backward}")
    
    for i, point in enumerate(mock_path.points):
        print(f"  Point {i}: ({point.x:.3f}, {point.y:.3f}, speed={point.speed:.3f})")
    
    # Test position structure
    print(f"\nPosition structure:")
    position = MockPosition(1.0, 2.0, 0.5)
    print(f"  Position: x={position.x}, y={position.y}, yaw={position.yaw}")

if __name__ == "__main__":
    debug_controller_parameters()
    debug_path_structure()
    debug_simple_straight_path()
    
    print(f"\n🔍 SUMMARY")
    print("=" * 60)
    print("Check the debug output above for:")
    print("1. Missing controller parameters")
    print("2. Control calculation errors") 
    print("3. Zero or invalid speeds")
    print("4. Path structure issues")