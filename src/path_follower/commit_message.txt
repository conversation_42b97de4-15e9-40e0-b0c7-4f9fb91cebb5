feat(path_follower): Split approach/control, distance-based advancement, 1-pass lookahead, offset ingress

Architecture:
* Extract `ApproachController`; keep `PathController` ROS-free (trajectory algorithms only)
* Move sensor-offset logic to `SensorOffsetEstimator` with thin wrappers in `PathController`
* Localize `current_segment_id`/`current_point_id` and approach entry decision to `RunningState`
* Centralize publishing in `do_work_finally()`; `stop_control()` only resets targets

Algorithms & behavior:
* Agro-rover style distance-to-segment advancement with hysteresis; curvature-aware skip limits
* Single-pass lookahead: returns point, actual distance, yaw_current, yaw_at_lookahead (angle interpolation)
* Unified pure-rotation threshold for forward/backward
* Fast-forward helper in controller to find current segment/point

Offset:
* Simplified estimator; apply correction at controller ingress; toggle via `set_sensor_offset_mode()`

Logging:
* Reduced noise: RTK reliability at INFO (30s), main-state BLOCKED at DEBUG (10s), advancement debug via `PF_ADVANCE_DEBUG`

Refactor & tests:
* Use named class constants for limits/thresholds; remove legacy/unreferenced code
* Add comprehensive sensor-offset/trajectory tests: `src/path_follower/test_sensor_offset_comprehensive.py` (supports `--viz`)