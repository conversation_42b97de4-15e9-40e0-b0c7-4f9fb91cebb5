#!/usr/bin/env python3
"""Common utilities for path following tests."""

import math
import sys
import os
from typing import List, Tu<PERSON>, Dict, Any

# Add path_follower module to path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..'))
from path_follower.path_controller import PathController
from path_follower.math_utils import point_to_segment_distance
from test_mocks import MockPathPoint, MockPath, MockPosition, MockSpeedState


class RobotSimulator:
    """Simple robot simulation for testing with sensor offset from kinematic center."""
    
    def __init__(self, x: float = 0.0, y: float = 0.0, yaw: float = 0.0, width: float = 2.0, sensor_offset: float = 0.0):
        # Accept yaw in degrees (preferred) or radians (|val|<=π)
        self.yaw = math.radians(yaw) if abs(yaw) > math.pi else yaw
        self.width = width
        self.sensor_offset = sensor_offset
        
        # Input x,y is sensor position (what we measure with RTK)
        self.x_sensor = x
        self.y_sensor = y
        
        # Calculate kinematic center from sensor position
        self.x_kinematic = x - sensor_offset * math.cos(self.yaw)
        self.y_kinematic = y - sensor_offset * math.sin(self.yaw)
        
        
        # History tracking
        self.x_sensor_history = [self.x_sensor]
        self.y_sensor_history = [self.y_sensor]
        self.yaw_history = [self.yaw]
        self.x_kinematic_history = [self.x_kinematic]
        self.y_kinematic_history = [self.y_kinematic]
        
        # Speed state for sensor position
        self.speed_state = MockSpeedState()
    
    def reset(self, x: float = 0.0, y: float = 0.0, yaw: float = 0.0):
        """Reset robot to new position (x,y is sensor position)."""
        # Reset kinematic center based on sensor position
        self.yaw = math.radians(yaw) if abs(yaw) > math.pi else yaw
        self.x_kinematic = x - self.sensor_offset * math.cos(self.yaw)
        self.y_kinematic = y - self.sensor_offset * math.sin(self.yaw)
        
        # Sensor position
        self.x_sensor = x
        self.y_sensor = y
        
        # Reset histories
        self.x_sensor_history = [x]
        self.y_sensor_history = [y]
        self.yaw_history = [self.yaw]
        self.x_kinematic_history = [self.x_kinematic]
        self.y_kinematic_history = [self.y_kinematic]
        
        # Reset speed state
        self.speed_state = MockSpeedState()
    
    def step(self, left_speed: float, right_speed: float, dt: float = 0.1):
        """Simulate one step of robot movement using differential drive."""
        # Differential drive kinematics for kinematic center
        v_kinematic = (left_speed + right_speed) / 2  # Linear velocity at kinematic center
        omega = (right_speed - left_speed) / self.width  # Angular velocity
        
        # Update kinematic center position
        self.x_kinematic += v_kinematic * math.cos(self.yaw) * dt
        self.y_kinematic += v_kinematic * math.sin(self.yaw) * dt
        self.yaw += omega * dt
        
        # Normalize yaw to [-π, π]
        while self.yaw > math.pi:
            self.yaw -= 2 * math.pi
        while self.yaw < -math.pi:
            self.yaw += 2 * math.pi
        
        # Calculate sensor position from kinematic center
        self.x_sensor = self.x_kinematic + self.sensor_offset * math.cos(self.yaw)  
        self.y_sensor = self.y_kinematic + self.sensor_offset * math.sin(self.yaw)
        
        
        # Calculate sensor velocities (what SpeedState would report)
        # Sensor has both translational and rotational components
        v_sensor_forward = v_kinematic  # Forward speed same as kinematic center
        v_sensor_lateral = omega * self.sensor_offset  # Lateral speed due to rotation
        
        # Update speed state
        self.speed_state.forward = v_sensor_forward
        self.speed_state.lateral = v_sensor_lateral  
        self.speed_state.angular = omega
        self.speed_state.is_reliable = True
        
        # Store histories
        self.x_sensor_history.append(self.x_sensor)
        self.y_sensor_history.append(self.y_sensor)
        self.yaw_history.append(self.yaw)
        self.x_kinematic_history.append(self.x_kinematic)
        self.y_kinematic_history.append(self.y_kinematic)
    
    def get_position(self) -> MockPosition:
        """Get current position as MockPosition object (yaw in rad)."""
        return MockPosition(self.x_sensor, self.y_sensor, self.yaw)

    
    def get_trajectory(self) -> List[Tuple[float, float]]:
        """Get sensor trajectory as list of (x, y) tuples."""
        return list(zip(self.x_sensor_history, self.y_sensor_history))
    
    def get_kinematic_trajectory(self) -> List[Tuple[float, float]]:
        """Get kinematic center trajectory as list of (x, y) tuples."""
        return list(zip(self.x_kinematic_history, self.y_kinematic_history))
    
    def get_speed_state(self) -> MockSpeedState:
        """Get current speed state."""
        return self.speed_state

def load_production_parameters():
    """Load actual production parameters from nodes.yaml file."""
    try:
        import yaml
        
        # Load the production configuration
        nodes_yaml_path = os.path.join(os.path.dirname(__file__), '..', '..', 'params_server', 'params_server', 'base_config', 'nodes.yaml')
        
        with open(nodes_yaml_path, 'r') as f:
            config = yaml.safe_load(f)
        
        # Extract path_follower parameters - use directly as node_params
        node_params = config.get('path_follower', {})

        # Vehicle parameters (attempt to read from nodes.yaml; fallback to defaults)
        vehicle_cfg = config.get('Vehicle', {})
        geom_cfg = vehicle_cfg.get('geometry', {})
        vehicle_params = {
            'geometry': {
                'platform_width': geom_cfg.get('platform_width', 2.0),
                'tower2center': geom_cfg.get('tower2center', 0.0),
            },
            'max_speed': vehicle_cfg.get('max_speed', 0.6),
            'min_speed': vehicle_cfg.get('min_speed', 0.01),
        }
        
        return node_params, vehicle_params
    
    except Exception as e:
        print(f"Warning: Could not load production parameters from nodes.yaml: {e}")
        print("Using fallback test parameters...")
        return create_fallback_test_parameters()


def create_fallback_test_parameters():
    """Create fallback parameter set if production config cannot be loaded."""
    # Fallback path_follower configuration
    node_params = {
        'lookahead_distance': 2.0,
        'lateral_correction_gain': 0.5,
        'lateral_correction_limit': 0.3,
        'heading_error_gain': 1.0,
        'path_curvature_feedforward_gain': 0.3,
        'turn_rate_base_limit': 2.0,
        'turn_rate_speed_factor': 0.5,
        'max_heading_error_for_full_speed': 1.047,
        'max_approach_speed': 0.078,
        'max_approach_angular_error_deg': 9,
        'goal_achievement_radius': 0.06,
        'approach_max_turn_rate': 0.015,
        'max_allowed_distance_to_target': 5.0,
        'approach_timeout': 60.0,
        'approach_pid': {
            'p': 0.35,
            'i': 0.00,
            'd': 0.00,
            'i_saturation': 0.01
        },
        'max_heading_error_for_motion': 1.57,
        'message_timeout': 0.5,
        'output_smoothing_time_constant': 0.1,
        'rms_recent_time_constant': 7.0,
        'sensor_offset_smoothing_alpha': 0.15,
        'sensor_offset_min_angular_velocity': 0.05,
        'sensor_offset_max_reasonable_offset': 2.0,
        'sensor_offset_quality_log_period': 10.0,
        'sensor_offset_min_updates_for_reliability': 15,
        'sensor_offset_max_stability_variance': 0.01,
        'sensor_offset_reliability_threshold': 0.7
    }
    
    vehicle_params = {
        'geometry': {'platform_width': 2.0},
        'max_speed': 0.6,
        'min_speed': 0.01
    }
    
    return node_params, vehicle_params


def create_test_controller() -> PathController:
    """Create PathController with actual production parameters from nodes.yaml."""
    controller = PathController()
    node_params, vehicle_params = load_production_parameters()
    controller.load_parameters(node_params, vehicle_params)
    return controller


def create_test_paths() -> Dict[str, MockPath]:
    """Create basic test paths."""
    paths = {}
    
    # Straight line (0..5.0 with 0.5 step)
    points = [MockPathPoint(x=i*0.5, y=0.0, speed=0.5) for i in range(11)]
    paths['straight'] = MockPath(points)
    
    # Simple curve  
    points = []
    for i in range(21):
        x = i * 0.3
        y = 0.8 * math.sin(x * 0.3)
        points.append(MockPathPoint(x=x, y=y, speed=0.4))
    paths['curve'] = MockPath(points)
    
    # L-turn (denser sampling for stability on sharp corner)
    points = []
    step = 0.25
    n_h = int(2.0 / step) + 1
    for i in range(n_h):
        points.append(MockPathPoint(x=i*step, y=0.0, speed=0.25))
    n_v = int(2.0 / step)
    for i in range(1, n_v + 1):
        points.append(MockPathPoint(x=2.0, y=i*step, speed=0.25))
    paths['corner'] = MockPath(points)
    
    return paths


def calculate_path_errors(trajectory: List[Tuple[float, float]], 
                         path_points: List[Any]) -> Dict[str, float]:
    """Calculate trajectory error metrics."""
    if not trajectory or not path_points:
        return {'rms_error': float('inf'), 'max_error': float('inf')}
    
    errors = []
    
    # Calculate error at each robot position
    for robot_x, robot_y in trajectory:
        min_error = float('inf')
        
        # Find minimum distance to any path segment
        for i in range(len(path_points) - 1):
            p1, p2 = path_points[i], path_points[i + 1]
            error = point_to_segment_distance(robot_x, robot_y, p1.x, p1.y, p2.x, p2.y)
            min_error = min(min_error, error)
        
        errors.append(min_error)
    
    # Calculate metrics
    rms_error = math.sqrt(sum(e**2 for e in errors) / len(errors)) if errors else float('inf')
    max_error = max(errors) if errors else float('inf')
    
    return {'rms_error': rms_error, 'max_error': max_error}


def run_path_simulation(controller: PathController, path: MockPath, 
                       max_steps: int = 1000, sensor_offset: float = 0.0) -> Dict[str, Any]:
    """Run path following simulation."""
    if not path.points or len(path.points) < 2:
        return {'success': False, 'error': 'Invalid path'}
    
    robot = RobotSimulator(sensor_offset=sensor_offset)
    
    # Initialize robot at path start
    start_point = path.points[0]
    if len(path.points) > 1:
        dx = path.points[1].x - start_point.x
        dy = path.points[1].y - start_point.y
        initial_yaw = math.atan2(dy, dx)
        if path.is_backward:
            initial_yaw += math.pi
    else:
        initial_yaw = 0.0
        
    robot.reset(start_point.x, start_point.y, initial_yaw)
    current_point_id = 0
    
    # Precompute total path length
    path_len = 0.0
    for i in range(len(path.points) - 1):
        x0, y0 = path.points[i].x, path.points[i].y
        x1, y1 = path.points[i + 1].x, path.points[i + 1].y
        path_len += math.hypot(x1 - x0, y1 - y0)

    # Simulation loop
    travelled = 0.0
    last_pose = None
    for step in range(max_steps):
        position = robot.get_position()

        # Update current point: advance while distance decreases (may skip multiple)
        while current_point_id < len(path.points) - 1:
            new_point_id = controller.choose_next_point_id_by_distance(path, position, current_point_id)
            if new_point_id == current_point_id:
                break
            current_point_id = new_point_id

        # Always check distance to end and stop when close enough
        final_point = path.points[-1]
        distance_to_end = math.sqrt(
            (position.x - final_point.x)**2 + (position.y - final_point.y)**2
        )
        if distance_to_end < 0.03:  # 3cm
            break

        # Calculate control
        result = controller.calculate_path_following_control(path, position, current_point_id)
        left_speed, right_speed = result.left_speed, result.right_speed

        # Update robot
        robot.step(left_speed, right_speed, 0.1)
        if last_pose is not None:
            travelled += math.hypot(robot.x_sensor - last_pose[0], robot.y_sensor - last_pose[1])
        last_pose = (robot.x_sensor, robot.y_sensor)
    
    # Calculate results
    # Use KINEMATIC trajectory for error calculation to reflect true control frame
    trajectory = robot.get_kinematic_trajectory()
    errors = calculate_path_errors(trajectory, path.points)
    
    return {
        'success': True,
        'trajectory': trajectory,
        'robot_history': {
            'x_sensor': robot.x_sensor_history,
            'y_sensor': robot.y_sensor_history,
            'x_kinematic': robot.x_kinematic_history,
            'y_kinematic': robot.y_kinematic_history,
            'yaw': robot.yaw_history
        },
        'errors': errors,
        'steps': step + 1,
        'travelled': travelled,
        'path_len': path_len
    } 