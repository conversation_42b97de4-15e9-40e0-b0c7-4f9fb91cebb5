#!/usr/bin/env python3
"""Unit tests for point advancement logic.

Checks `choose_next_point_id_by_distance` behavior on straight, curve and L-turn.
"""

import math
import sys
import os
import unittest

# Test imports path setup
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..'))

from path_follower.path_controller import PathController  # noqa: E402
from test_common import create_test_paths, load_production_parameters  # noqa: E402
from test_mocks import MockPosition  # noqa: E402


class TestPointAdvancement(unittest.TestCase):
    def setUp(self):
        self.controller = PathController()
        params, vehicle = load_production_parameters()
        self.controller.load_parameters(params, vehicle)
        self.paths = create_test_paths()

    def _max_allowed_advance_segments(self, points):
        # Compute how many segments fit into cap distance (~1.5 * lookahead)
        cap = max(0.5, self.controller.lookahead_distance * 1.5)
        acc = 0.0
        cnt = 0
        for i in range(len(points) - 1):
            seg = math.hypot(points[i+1].x - points[i].x, points[i+1].y - points[i].y)
            if acc + seg > cap:
                break
            acc += seg
            cnt += 1
        return max(1, cnt)

    def test_straight_monotonic_and_capped(self):
        path = self.paths['straight']
        points = path.points
        # Place robot slightly off the path, near start
        pos = MockPosition(x=0.7, y=0.1, yaw=0.0)
        cur = 0
        nxt = self.controller.choose_next_point_id_by_distance(path, pos, cur)
        self.assertGreaterEqual(nxt, cur)
        self.assertLess(nxt, len(points) - 1)

        # Advancement should not exceed cap
        max_adv = self._max_allowed_advance_segments(points)
        self.assertLessEqual(nxt - cur, max_adv)

        # Subsequent call after small forward motion should be non-decreasing
        # Integrate a small step forward along yaw
        v = 0.2
        dt = 0.5
        pos = MockPosition(x=pos.x + v*math.cos(pos.yaw)*dt,
                           y=pos.y + v*math.sin(pos.yaw)*dt,
                           yaw=pos.yaw)
        nxt2 = self.controller.choose_next_point_id_by_distance(path, pos, nxt)
        self.assertGreaterEqual(nxt2, nxt)

    def test_corner_no_overshoot_and_not_last(self):
        # Construct an L-turn similar to test_common but concise
        from test_mocks import MockPath, MockPathPoint
        pts = []
        step = 0.25
        # Horizontal 0..2.0
        for i in range(int(2.0/step) + 1):
            pts.append(MockPathPoint(x=i*step, y=0.0, speed=0.25))
        # Vertical 0.25..2.0 at x=2.0
        for i in range(1, int(2.0/step) + 1):
            pts.append(MockPathPoint(x=2.0, y=i*step, speed=0.25))
        path = MockPath(pts)
        points = path.points

        # Robot just before the corner, slightly inside
        pos = MockPosition(x=1.8, y=0.05, yaw=0.0)
        cur = 4  # somewhere before turning
        nxt = self.controller.choose_next_point_id_by_distance(path, pos, cur)

        # Should not jump beyond cap
        max_adv = self._max_allowed_advance_segments(points)
        self.assertLessEqual(nxt - cur, max_adv)
        # And must never return the last index
        self.assertLess(nxt, len(points) - 1)

    def test_near_end_never_returns_last(self):
        path = self.paths['straight']
        last_valid = len(path.points) - 2
        # Place robot close to end
        end = path.points[-1]
        pos = MockPosition(x=end.x - 0.05, y=end.y, yaw=0.0)
        cur = last_valid - 1
        nxt = self.controller.choose_next_point_id_by_distance(path, pos, cur)
        self.assertLess(nxt, len(path.points) - 1)


if __name__ == '__main__':
    unittest.main()


