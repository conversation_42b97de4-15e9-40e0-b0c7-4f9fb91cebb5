#!/usr/bin/env python3
"""Unit tests for path following algorithms."""

import unittest
import math
import sys
import os

# Add path_follower module to path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..'))

from path_follower.math_utils import norm_angle_minusPI_plusPI
from test_mocks import (
    MockPathPoint, MockPath, MockPosition, MockDriveAction,
    create_straight_path, create_curved_path
)
from test_common import create_test_controller, load_production_parameters
from path_follower.approach_controller import ApproachController


class TestLookaheadAlgorithm(unittest.TestCase):
    """Test lookahead point finding algorithm using real PathController."""
    
    def setUp(self):
        """Set up PathController."""
        self.controller = create_test_controller()
        
    def test_find_lookahead_straight_path(self):
        """Test lookahead point on straight path."""
        path = create_straight_path(length=10, spacing=1.0)
        
        # Lookahead distance of 2.0 from start should give point at (2,0)
        li = self.controller.compute_lookahead(path, 0)
        self.assertIsNotNone(li.point)
        self.assertAlmostEqual(li.point.x, 2.0, delta=0.1)
        self.assertAlmostEqual(li.point.y, 0.0, delta=0.1)
        
        # Lookahead from middle of path
        li = self.controller.compute_lookahead(path, 3)
        self.assertIsNotNone(li.point)
        self.assertAlmostEqual(li.point.x, 5.0, delta=0.5)  # Approximately 3 + 2
        self.assertAlmostEqual(li.point.y, 0.0, delta=0.1)
        
        # Lookahead beyond end of path should return last point
        li = self.controller.compute_lookahead(path, 8)
        self.assertIsNotNone(li.point)
        self.assertAlmostEqual(li.point.x, 10.0, delta=0.1)
        self.assertAlmostEqual(li.point.y, 0.0, delta=0.1)
        
    def test_find_lookahead_curved_path(self):
        """Test lookahead point on curved path."""
        path = create_curved_path(radius=5.0, num_points=10)
        
        li = self.controller.compute_lookahead(path, 0)
        self.assertIsNotNone(li.point)
        
        # Lookahead point should be somewhere along the curve
        self.assertGreater(li.point.x, 0.0)
        self.assertGreater(li.point.y, 0.0)
        
    def test_empty_path_handling(self):
        """Test handling of empty or invalid paths."""
        # Empty path
        empty_path = MockPath([])
        li = self.controller.compute_lookahead(empty_path, 0)
        # For empty path, controller returns (None, 0.0, 0.0, 0.0)
        self.assertIsNone(li.point)
        
        # Single point path: treated as degenerate, last point returned
        single_point_path = MockPath([MockPathPoint(1.0, 1.0)])
        li = self.controller.compute_lookahead(single_point_path, 0)
        self.assertIsNotNone(li.point)
        self.assertAlmostEqual(li.point.x, 1.0, delta=1e-6)
        self.assertAlmostEqual(li.point.y, 1.0, delta=1e-6)


class TestFastForwardAlgorithm(unittest.TestCase):
    """Test fast forward to current position algorithm using real PathController."""
    
    def setUp(self):
        """Set up PathController."""
        self.controller = create_test_controller()
    
    def test_fast_forward_straight_path(self):
        """Test fast forward on straight path."""
        # Create straight path from (0,0) to (10,0)
        path = create_straight_path(length=10, spacing=1.0)
        path_segments = [path]
        
        # Robot at position (5,0) should fast forward to around point 5
        current_pos = MockPosition(x=5.0, y=0.0)
        seg_id, pt_id = self.controller.fast_forward_to_current_position(
            path_segments, current_pos)
        
        self.assertEqual(seg_id, 0)
        self.assertGreaterEqual(pt_id, 4)  # Should be at or past point 4
        self.assertLessEqual(pt_id, 6)     # But not too far ahead
        
        # Robot ahead of path should fast forward to end
        current_pos = MockPosition(x=12.0, y=0.0)
        seg_id, pt_id = self.controller.fast_forward_to_current_position(
            path_segments, current_pos)
        
        # Should stay in current segment but advance to end
        self.assertEqual(seg_id, 0)
        self.assertGreaterEqual(pt_id, 8)  # Should be near end of path
        
    def test_fast_forward_multiple_segments(self):
        """Test fast forward across multiple path segments."""
        # Create two segments
        seg1 = create_straight_path(length=5, spacing=1.0)
        seg2 = create_straight_path(length=5, spacing=1.0)
        # Offset second segment
        for point in seg2.points:
            point.x += 5.0
            point.y += 1.0
        
        path_segments = [seg1, seg2]
        
        # Robot near end of first segment
        current_pos = MockPosition(x=4.5, y=0.1)
        seg_id, pt_id = self.controller.fast_forward_to_current_position(
            path_segments, current_pos)
        
        # Should advance appropriately
        self.assertGreaterEqual(seg_id, 0)


class TestControlCalculations(unittest.TestCase):
    """Test control calculation algorithms using real PathController."""
    
    def setUp(self):
        """Set up PathController."""
        self.controller = create_test_controller()
        
    def test_lateral_error_correction(self):
        """Test lateral error correction via PathController."""
        path = create_straight_path(length=10, spacing=1.0, speed=0.5)
        
        # Robot ABOVE the path (LEFTWARD) should turn RIGHT
        position = MockPosition(x=1.0, y=1.0, yaw=0.0)  # y=1 means LEFTWARD of path
        result = self.controller.calculate_path_following_control(
            path, position, current_point_id=0)
        left, right = result.left_speed, result.right_speed
        
        # Left wheel should be faster (turning right to correct)
        self.assertGreater(left, right, 
                         f"Robot LEFTWARD should turn RIGHT: left={left:.3f} > right={right:.3f}")
        
        # Robot BELOW the path (RIGHTWARD) should turn LEFT
        position = MockPosition(x=1.0, y=-1.0, yaw=0.0)  # y=-1 means RIGHTWARD of path
        result = self.controller.calculate_path_following_control(
            path, position, current_point_id=0)
        left, right = result.left_speed, result.right_speed
        
        # Right wheel should be faster (turning left to correct)
        self.assertGreater(right, left,
                         f"Robot RIGHTWARD should turn LEFT: right={right:.3f} > left={left:.3f}")
        
    def test_backward_movement_handling(self):
        """Test backward movement via PathController."""
        path = create_straight_path(length=10, spacing=1.0, speed=0.5)
        
        # Forward movement
        path.is_backward = False
        position = MockPosition(x=0.0, y=0.0, yaw=0.0)
        result = self.controller.calculate_path_following_control(
            path, position, current_point_id=0)
        left, right = result.left_speed, result.right_speed
        
        # Should move forward
        self.assertGreater(left, 0.0)
        self.assertGreater(right, 0.0)
        
        # Backward movement
        path.is_backward = True
        position = MockPosition(x=0.0, y=0.0, yaw=math.pi)  # Robot faces opposite to path direction
        result = self.controller.calculate_path_following_control(
            path, position, current_point_id=0)
        left, right = result.left_speed, result.right_speed
        
        # Should move backward
        self.assertLess(left, 0.0)
        self.assertLess(right, 0.0)


class TestApproachMode(unittest.TestCase):
    """Test approach mode calculations using real PathController."""
    
    def setUp(self):
        """Set up controllers."""
        self.controller = create_test_controller()
        self.approach = ApproachController()
        params, vehicle = load_production_parameters()
        self.approach.load_parameters(params, vehicle)
    
    def test_approach_distance_calculation(self):
        """Test approach control via PathController."""
        drill_pos = MockPosition(x=0.0, y=0.0, yaw=0.0)
        hole_pos = MockPathPoint(x=1.0, y=0.0)
        
        # Normal approach via dedicated controller
        left, right, reached = self.approach.calculate_control(
            drill_pos, hole_pos)
        
        self.assertFalse(reached)
        self.assertGreater(left, 0.0)
        self.assertGreater(right, 0.0)
        self.assertAlmostEqual(left, right, delta=0.1)  # Straight approach
        
        # Very close to target
        drill_pos = MockPosition(x=0.95, y=0.0, yaw=0.0)
        left, right, reached = self.approach.calculate_control(
            drill_pos, hole_pos)
        
        # Should either reach target or move slowly
        if not reached:
            self.assertLessEqual(abs(left), self.controller.max_approach_speed)
            self.assertLessEqual(abs(right), self.controller.max_approach_speed)


if __name__ == '__main__':
    unittest.main()