#!/usr/bin/env python3
"""Accuracy tests using simplified utilities."""

import math
from typing import List, Tuple

from test_common import (
    create_test_controller,
    create_test_paths, 
    run_path_simulation,
    RobotSimulator,
    load_production_parameters,
)
from test_mocks import MockPosition


def test_path_following_accuracy(target_rms_error_cm: float = 5.0) -> dict:
    """Test path following accuracy."""
    print(f"Testing path following accuracy (target RMS < {target_rms_error_cm}cm)...")
    
    controller = create_test_controller()
    test_paths = create_test_paths()
    
    results = {}
    
    for path_name, path in test_paths.items():
        print(f"\nTesting {path_name}:")
        result = run_path_simulation(controller, path)
        
        if not result['success']:
            print(f"  Error: {result['error']}")
            continue
        
        errors = result['errors']
        success = errors['rms_error'] * 100 < target_rms_error_cm
        
        result_dict = {
            'rms_error_cm': errors['rms_error'] * 100,
            'max_error_cm': errors['max_error'] * 100,
            'success': success
        }
        
        print(f"  RMS Error: {result_dict['rms_error_cm']:.1f}cm")
        print(f"  Max Error: {result_dict['max_error_cm']:.1f}cm")
        # Progress diagnostics
        if 'travelled' in result and 'path_len' in result:
            prog = 0.0 if result['path_len'] == 0 else (result['travelled'] / result['path_len'] * 100)
            print(f"  Progress: {prog:.0f}% (travelled {result['travelled']:.2f}m / path {result['path_len']:.2f}m)")
        print(f"  Result: {'PASS' if success else 'FAIL'}")
        
        results[path_name] = result_dict
    
    return results


def test_approach_accuracy(start_positions: List[Tuple[float, float, float]], 
                          target_pos: Tuple[float, float], 
                          target_error_cm: float = 5.0,
                          sensor_offset: float = 0.0) -> dict:
    """Test approach control accuracy.
    sensor_offset: simulate sensor ahead/behind kinematic center.
    """
    print(f"\nTesting approach accuracy (target < {target_error_cm}cm, offset={sensor_offset}m)...")
    
    controller = create_test_controller()
    robot = RobotSimulator(sensor_offset=sensor_offset)
    # Dedicated approach controller
    from path_follower.approach_controller import ApproachController
    approach = ApproachController()
    params, vehicle = load_production_parameters()
    approach.load_parameters(params, vehicle)
    
    results = []
    
    for i, start_pos in enumerate(start_positions):
        print(f"  Test {i+1}: Start ({start_pos[0]:.1f}, {start_pos[1]:.1f}, {math.degrees(start_pos[2]):.0f}°)")
        
        robot.reset(start_pos[0], start_pos[1], start_pos[2])
        target_position = MockPosition(target_pos[0], target_pos[1], 0.0)
        
        step_history = []
        for step in range(3000):  # Increased from 1000 to ensure convergence
            drill_position = robot.get_position()
            
            left_speed, right_speed, goal_reached = approach.calculate_control(
                drill_position, target_position)
            
            # Store debug info
            if step % 50 == 0:
                distance = math.sqrt((robot.x_sensor - target_pos[0])**2 + (robot.y_sensor - target_pos[1])**2)
                step_history.append((step, distance*100, left_speed, right_speed))
            
            if goal_reached:
                break
            
            robot.step(left_speed, right_speed, 0.1)  # Match debug test dt for stability
        
        # Calculate final error
        final_error = math.sqrt((robot.x_sensor - target_pos[0])**2 + (robot.y_sensor - target_pos[1])**2)
        final_error_cm = final_error * 100
        success = final_error_cm < target_error_cm
        
        results.append({
            'final_error_cm': final_error_cm,
            'success': success,
            'steps': step + 1,
            'step_history': step_history
        })
        
        print(f"    Final error: {final_error_cm:.1f}cm, Steps: {step + 1}, {'PASS' if success else 'FAIL'}")
        
        # Print debug info for failed cases
        if not success and step_history:
            print(f"    Debug - Step progression:")
            for s, d, ls, rs in step_history[:5]:  # Show first 5 steps only
                print(f"      Step {s:3d}: distance={d:5.1f}cm, speeds=({ls:5.3f}, {rs:5.3f})")
    
    # Overall statistics
    all_errors = [r['final_error_cm'] for r in results]
    success_rate = sum(1 for r in results if r['success']) / len(results) * 100
    
    overall_result = {
        'avg_error_cm': sum(all_errors) / len(all_errors),
        'max_error_cm': max(all_errors),
        'success_rate': success_rate,
        'individual_results': results
    }
    
    print(f"  Average error: {overall_result['avg_error_cm']:.1f}cm")
    print(f"  Max error: {overall_result['max_error_cm']:.1f}cm")
    print(f"  Success rate: {success_rate:.0f}%")
    
    return overall_result


def main():
    """Run comprehensive accuracy tests."""
    print("=" * 60)
    print("PATH FOLLOWING AND APPROACH ACCURACY TESTS")
    print("=" * 60)
    
    # Test 1: Path Following Accuracy
    path_results = test_path_following_accuracy(target_rms_error_cm=5.0)
    
    # Test 2: Approach Control Accuracy (no offset)
    target_position = (5.0, 5.0)
    start_positions = [
        (4.0, 5.0, 0.0),         # 0°   forward approach
        (6.0, 5.0, 0.0),         # 0°   backward (target behind)
        (5.0, 4.0, math.pi/2),   # 90°  forward
        (5.0, 6.0, math.pi/2),   # 90°  backward
        (4.5, 4.5, math.pi/4),   # 45°  forward
        (5.5, 5.5, math.pi/4),   # 45°  backward
    ]
    
    approach_result = test_approach_accuracy(start_positions, target_position, target_error_cm=10.0, sensor_offset=0.0)

    # Test 3: Approach Control Accuracy with sensor offset
    approach_result_with_offset = test_approach_accuracy(start_positions, target_position, target_error_cm=10.0, sensor_offset=0.8)
    
    # Summary
    print("\n\nSUMMARY")
    print("=" * 60)
    
    print("\nPath Following Results:")
    for path_name, result in path_results.items():
        status = "PASS" if result['success'] else "FAIL"
        print(f"  {path_name:<12}: RMS {result['rms_error_cm']:5.1f}cm, Max {result['max_error_cm']:5.1f}cm [{status}]")
    
    print(f"\nApproach Control Results (no offset):")
    print(f"  Average error: {approach_result['avg_error_cm']:5.1f}cm")
    print(f"  Success rate:  {approach_result['success_rate']:5.0f}%")

    print(f"\nApproach Control Results (offset=0.8m):")
    print(f"  Average error: {approach_result_with_offset['avg_error_cm']:5.1f}cm")
    print(f"  Success rate:  {approach_result_with_offset['success_rate']:5.0f}%")
    
    # Overall assessment
    path_success_rate = sum(1 for r in path_results.values() if r['success']) / len(path_results) * 100
    overall_success = path_success_rate >= 80 and approach_result['success_rate'] >= 80 and approach_result_with_offset['success_rate'] >= 80
    
    print(f"\nOVERALL ASSESSMENT: {'PASS' if overall_success else 'NEEDS IMPROVEMENT'}")
    print(f"  Path following success rate: {path_success_rate:.0f}%")
    print(f"  Approach control success rate (no offset): {approach_result['success_rate']:.0f}%")
    print(f"  Approach control success rate (offset): {approach_result_with_offset['success_rate']:.0f}%")


if __name__ == '__main__':
    main() 