#!/usr/bin/env python3
"""
PathController Sensor Offset Test Suite

Key Components:
- SensorOffsetTestSuite: Main test suite class
- Real PathController integration testing  
- Trajectory comparison and RMS analysis
- Visual trajectory comparison with matplotlib

Usage:
    python3 src/path_follower/test_sensor_offset_comprehensive.py
    
    # Run test suite with visualization
    python3 src/path_follower/test_sensor_offset_comprehensive.py --viz

"""

import math
import sys
import os
from typing import List, Tuple, Dict, Any

# Optional matplotlib import for visualization
try:
    import matplotlib.pyplot as plt
    MATPLOTLIB_AVAILABLE = True
except ImportError:
    MATPLOTLIB_AVAILABLE = False

# Add test directory to path for imports
test_dir = os.path.join(os.path.dirname(__file__), 'test')
sys.path.insert(0, test_dir)

from test_mocks import MockPathPoint, MockPath, MockPosition, MockSpeedState

# Import real controller and use existing test utilities
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', 'path_follower'))
from path_follower.path_controller import PathController
from test_common import RobotSimulator, load_production_parameters


class SensorOffsetTestSuite:
    """Comprehensive test suite for PathController sensor offset functionality.
    
    Usage:
        suite = SensorOffsetTestSuite(sensor_offset=0.8)
        results = suite.run_all_tests()
        suite.visualize_trajectories(save_plots=True)
    """

    def __init__(self, sensor_offset: float = 0.8):
        """Initialize test suite with specified sensor offset."""
        self.sensor_offset = sensor_offset
        self.results = {}

    def _create_controller(self, use_sensor_offset: bool):
        """Create PathController with production parameters."""
        controller = PathController()
        node_params, vehicle_params = load_production_parameters()
        controller.load_parameters(node_params, vehicle_params)
        controller.set_sensor_offset_mode(use_sensor_offset)
        return controller

    def test_sensor_offset_calculation(self) -> Dict[str, Any]:
        """Test REAL PathController sensor offset calculation algorithm."""
        print("🧮 Testing real-time sensor offset calculation...")

        controller = self._create_controller(use_sensor_offset=True)

        test_cases = [
            (0.1, 0.2, 0.5, "Simple case: 0.5m offset"),
            (0.16, 0.2, 0.8, "Standard case: 0.8m offset"),
            (0.24, 0.2, 1.2, "Large offset: 1.2m offset"),
            (-0.06, 0.2, -0.3, "Negative offset: sensor behind kinematic center"),
        ]

        results = []
        for lateral, angular, expected, description in test_cases:
            # Reset controller state
            controller._sensor_offset = 0.0
            controller._offset_update_count = 0

            # Create mock speed state
            speed_state = MockSpeedState()
            speed_state.lateral = lateral
            speed_state.angular = angular
            speed_state.is_reliable = True

            # Feed multiple samples to let it converge
            for _ in range(50):
                controller._update_sensor_offset_from_speed(speed_state)

            calculated = controller._sensor_offset
            error = abs(calculated - expected)
            success = error < 0.1  # 10cm tolerance for real controller

            print(f"  {description}: calculated={calculated:.3f}m, error={error*1000:.1f}mm {'✓' if success else '✗'}")
            results.append({
                'description': description,
                'calculated': calculated,
                'expected': expected,
                'error_mm': error * 1000,
                'success': success
            })

        return {'test_cases': results, 'all_passed': all(r['success'] for r in results)}

    def test_kinematic_center_calculation(self) -> Dict[str, Any]:
        """Test REAL PathController kinematic center calculation."""
        print("🎯 Testing kinematic center position calculation...")

        controller = self._create_controller(use_sensor_offset=True)

        # Kinematic center is behind sensor by offset distance
        test_cases = [
            (MockPosition(x=0.0, y=0.0, yaw=0.0), 0.8, (-0.8, 0.0), "Forward "),
            (MockPosition(x=0.0, y=0.0, yaw=1.57), 0.8, (0.0, -0.8), "Right "),
            (MockPosition(x=0.0, y=0.0, yaw=3.14), 0.8, (0.8, 0.0), "Backward "),
            (MockPosition(x=0.0, y=0.0, yaw=-1.57), 0.8, (0.0, 0.8), "Left "),
        ]

        results = []
        for position, offset, expected, description in test_cases:
            controller._sensor_offset = offset
            kinematic_x, kinematic_y = controller._calculate_kinematic_center_position(position)

            error_x = abs(kinematic_x - expected[0])
            error_y = abs(kinematic_y - expected[1])
            total_error = math.sqrt(error_x**2 + error_y**2)
            success = total_error < 0.01  # 1cm tolerance

            print(f"  {description}: calculated=({kinematic_x:.3f}, {kinematic_y:.3f}), "
                  f"expected={expected}, error={total_error*1000:.1f}mm {'✓' if success else '✗'}")

            results.append({
                'description': description,
                'calculated': (kinematic_x, kinematic_y),
                'expected': expected,
                'error_mm': total_error * 1000,
                'success': success
            })

        return {'test_cases': results, 'all_passed': all(r['success'] for r in results)}

    def test_safety_checks_and_validation(self) -> Dict[str, Any]:
        """Test REAL PathController safety checks and input validation."""
        print("🛡️  Testing safety checks and input validation...")

        controller = self._create_controller(use_sensor_offset=True)

        test_cases = [
            # (lateral, angular, is_reliable, should_update, description)
            (0.1, 0.2, True, True, "Valid reliable data"),
            (0.1, 0.2, False, False, "Unreliable data should be rejected"),
            (0.0, 0.0, True, False, "Stationary robot should not update"),
            (0.1, 0.01, True, False, "Low angular velocity should be rejected"),
        ]

        results = []
        for lateral, angular, is_reliable, should_update, description in test_cases:
            # Reset controller state
            initial_offset = controller._sensor_offset
            initial_count = controller._offset_update_count

            # Create mock speed state
            speed_state = MockSpeedState()
            speed_state.lateral = lateral
            speed_state.angular = angular
            speed_state.is_reliable = is_reliable

            # Call REAL PathController method
            controller._update_sensor_offset_from_speed(speed_state)

            # Check if update occurred
            offset_changed = controller._sensor_offset != initial_offset
            count_changed = controller._offset_update_count != initial_count
            actually_updated = offset_changed or count_changed

            success = actually_updated == should_update

            print(f"  {description}: updated={actually_updated}, expected={should_update} {'✓' if success else '✗'}")

            results.append({
                'description': description,
                'actually_updated': actually_updated,
                'should_update': should_update,
                'success': success
            })

        return {'test_cases': results, 'all_passed': all(r['success'] for r in results)}

    def _create_all_test_trajectories(self) -> Dict[str, List[Tuple[float, float]]]:
        """Create all test trajectory patterns."""
        trajectories = {}
        
        # Straight forward
        trajectories['straight_forward'] = [(i * 0.5, 0.0) for i in range(10)]
        
        # Straight backward  
        trajectories['straight_backward'] = [(5.0 - i * 0.5, 0.0) for i in range(10)]
        
        # Circle forward
        circle_points = []
        for i in range(25):
            angle = i * 2 * math.pi / 24
            x = 3.0 + 3.0 * math.cos(angle)
            y = 3.0 * math.sin(angle)
            circle_points.append((x, y))
        trajectories['circle_forward'] = circle_points
        
        # Circle backward
        trajectories['circle_backward'] = list(reversed(circle_points))
        
        # Circle with high resolution (smaller step)
        circle_dense_points = []
        for i in range(100):  # 4x more points
            angle = i * 2 * math.pi / 99
            x = 3.0 + 3.0 * math.cos(angle)
            y = 3.0 * math.sin(angle)
            circle_dense_points.append((x, y))
        trajectories['circle_dense'] = circle_dense_points
        
        # S-curve forward
        s_curve_points = []
        for i in range(20):
            x = i * 0.5
            y = 2.0 * math.sin(x * 0.5)
            s_curve_points.append((x, y))
        trajectories['s_curve_forward'] = s_curve_points
        
        # S-curve backward
        trajectories['s_curve_backward'] = list(reversed(s_curve_points))
        
        # L-turn (denser sampling for stability on sharp corner)
        l_turn_points = []
        step = 0.25
        # Horizontal leg 0..2.0
        n_h = int(2.0 / step) + 1
        for i in range(n_h):
            l_turn_points.append((i * step, 0.0))
        # Vertical leg 0.25..2.0 at x=2.0 (avoid duplicating corner point)
        n_v = int(2.0 / step)
        for i in range(1, n_v + 1):
            l_turn_points.append((2.0, i * step))
        trajectories['l_turn'] = l_turn_points
        
        # L-turn backward
        trajectories['l_turn_backward'] = list(reversed(l_turn_points))
        
        # Zigzag
        zigzag_points = []
        direction = 1
        for i in range(15):
            x = i * 0.5
            y = direction * (i % 3) * 0.5
            if i % 3 == 0:
                direction *= -1
            zigzag_points.append((x, y))
        trajectories['zigzag'] = zigzag_points
        
        return trajectories

    def test_real_trajectory_rms(self, with_visualization: bool = False,
                                 rms_threshold_m: float = 0.06,
                                 initial_lateral_displacement_m: float = 0.1,
                                 quick: bool = False) -> Dict[str, Any]:
        """Test real trajectory following with actual PathController.

        Adds absolute tracking checks and progress validation:
        - Absolute RMS error must be below threshold (meters)
        - Robot must progress along the path and approach the end point
        - Start position is slightly offset normal to the initial heading
        """
        print("Testing real trajectory RMS with actual PathController...")

        trajectories = self._create_all_test_trajectories()
        all_results = []

        # Trajectory titles for visualization
        trajectory_titles = {
            'straight_forward': 'Straight Forward',
            'straight_backward': 'Straight Backward',
            'circle_forward': 'Circle Forward',
            'circle_backward': 'Circle Backward',
            'circle_dense': 'Circle Dense',
            's_curve_forward': 'S-Curve Forward',
            's_curve_backward': 'S-Curve Backward',
            'l_turn': 'L-Turn',
            'l_turn_backward': 'L-Turn Backward',
            'zigzag': 'Zigzag'
        }

        # Optionally reduce set for quick runs
        if quick:
            trajectories = {k: v for k, v in trajectories.items()
                            if k in ("straight_forward", "circle_forward", "l_turn")}

        for traj_name, target_points in trajectories.items():
            print(f"  Testing trajectory: {traj_name}")
            traj_title = trajectory_titles.get(traj_name, traj_name.replace('_', ' ').title())

            def run_trajectory_simulation(use_sensor_offset: bool):
                """Run single trajectory simulation with PathController."""
                controller = self._create_controller(use_sensor_offset)
                robot = RobotSimulator(sensor_offset=self.sensor_offset)

                # Create path
                path_points = [MockPathPoint(x=x, y=y, speed=0.3) for x, y in target_points]

                # Assign yaw using true tangents where analytic form is known.
                # For polylines, use segment direction (piecewise tangent).
                def _assign_yaw_true_tangent(name: str, pts: list):
                    n_local = len(pts)
                    if n_local == 0:
                        return
                    # Circle family: center (3, 0), radius 3
                    if name in ("circle_forward", "circle_backward", "circle_dense"):
                        cx, cy = 3.0, 0.0
                        # Tangent for CCW: atan2(y-cy, x-cx) + pi/2
                        # Orientation along traversal is handled at robot init for backward via +pi
                        for i in range(n_local):
                            dxr = pts[i].x - cx
                            dyr = pts[i].y - cy
                            yaw_t = math.atan2(dyr, dxr) + math.pi * 0.5
                            pts[i].yaw = yaw_t
                        # If last duplicates first, copy yaw to keep continuity
                        if n_local >= 2 and abs(pts[-1].x - pts[0].x) < 1e-6 and abs(pts[-1].y - pts[0].y) < 1e-6:
                            pts[-1].yaw = pts[0].yaw
                        return

                    # S-curve: y = 2 * sin(0.5 * x) -> dy/dx = cos(0.5 * x)
                    if name in ("s_curve_forward", "s_curve_backward"):
                        for i in range(n_local):
                            x_i = pts[i].x
                            dydx = math.cos(0.5 * x_i)
                            pts[i].yaw = math.atan2(dydx, 1.0)
                        return

                    # L-turn: horizontal then vertical segments
                    if name in ("l_turn", "l_turn_backward"):
                        # Use central difference at interior points to smooth the sharp corner
                        for i in range(n_local):
                            if 0 < i < n_local - 1:
                                dx = pts[i + 1].x - pts[i - 1].x
                                dy = pts[i + 1].y - pts[i - 1].y
                                pts[i].yaw = math.atan2(dy, dx)
                            else:
                                # Endpoints: use segment direction
                                j = i + 1 if i == 0 else i
                                k = i if i == 0 else i - 1
                                dx = pts[j].x - pts[k].x
                                dy = pts[j].y - pts[k].y
                                pts[i].yaw = math.atan2(dy, dx)
                        return

                    # Zigzag and straight lines: use central difference at interior points
                    # Endpoints fall back to segment direction
                    for i in range(n_local):
                        if 0 < i < n_local - 1:
                            dx = pts[i + 1].x - pts[i - 1].x
                            dy = pts[i + 1].y - pts[i - 1].y
                        else:
                            j = i + 1 if i == 0 else i
                            k = i if i == 0 else i - 1
                            dx = pts[j].x - pts[k].x
                            dy = pts[j].y - pts[k].y
                        pts[i].yaw = math.atan2(dy, dx)

                _assign_yaw_true_tangent(traj_name, path_points)
                mock_path = MockPath(path_points)
                if 'backward' in traj_name:
                    mock_path.is_backward = True

                # Initialize robot (start slightly offset from the path normal)
                start_x, start_y = target_points[0]
                initial_yaw = path_points[0].yaw if path_points else 0.0
                if 'backward' in traj_name:
                    initial_yaw += math.pi

                # Apply a small lateral start displacement relative to initial heading (desired for KINEMATIC center)
                nx = -math.sin(initial_yaw)
                ny = math.cos(initial_yaw)
                kin_start_x = start_x + initial_lateral_displacement_m * nx
                kin_start_y = start_y + initial_lateral_displacement_m * ny

                # Pre-set sensor offset for NEW mode and place SENSOR such that KINEMATIC center is on the path
                if use_sensor_offset:
                    controller._sensor_offset = self.sensor_offset
                    controller._offset_update_count = 50
                    sens_start_x = kin_start_x + self.sensor_offset * math.cos(initial_yaw)
                    sens_start_y = kin_start_y + self.sensor_offset * math.sin(initial_yaw)
                else:
                    sens_start_x = kin_start_x
                    sens_start_y = kin_start_y

                robot.reset(sens_start_x, sens_start_y, initial_yaw)
                current_point_id = 0

                # Run simulation
                path_len = 0.0
                for i in range(len(mock_path.points) - 1):
                    x0, y0 = mock_path.points[i].x, mock_path.points[i].y
                    x1, y1 = mock_path.points[i+1].x, mock_path.points[i+1].y
                    path_len += math.hypot(x1 - x0, y1 - y0)

                travelled = 0.0
                last_pose = None
                for _ in range(800):
                    position = robot.get_position()
                    speed_state = robot.get_speed_state()
                    
                    # Advance points (may skip multiple)
                    if current_point_id < len(mock_path.points) - 1:
                        new_point_id = controller.choose_next_point_id_by_distance(
                            mock_path, position, current_point_id
                        )
                        current_point_id = new_point_id

                    # Check completion using the same controller rule as the node
                    if controller.should_complete_segment(mock_path, position, current_point_id):
                        break

                    # Control calculation
                    try:
                        control_result = controller.calculate_path_following_control(
                            mock_path, position, current_point_id, speed_state
                        )
                        left_speed = control_result.left_speed
                        right_speed = control_result.right_speed
                    except Exception:
                        left_speed = right_speed = 0.0

                    robot.step(left_speed, right_speed, 0.05)
                    # Track progress by travelled distance
                    if last_pose is not None:
                        travelled += math.hypot(robot.x_sensor - last_pose[0], robot.y_sensor - last_pose[1])
                    last_pose = (robot.x_sensor, robot.y_sensor)

                # Get both sensor and kinematic trajectories
                sensor_trajectory = robot.get_trajectory()
                kinematic_trajectory = robot.get_kinematic_trajectory()
                
                # Calculate RMS using KINEMATIC trajectory (absolute distance to path segments)
                try:
                    from test_common import calculate_path_errors
                    class _Tmp:
                        def __init__(self, pts):
                            self.points = pts
                    abs_errors = calculate_path_errors(kinematic_trajectory, _Tmp(path_points).points)
                    rms_abs = abs_errors['rms_error']
                except Exception:
                    rms_abs = float('inf')

                final_x, final_y = target_points[-1]
                # Measure final distance in the control frame (kinematic center)
                final_dist = math.hypot(robot.x_kinematic - final_x, robot.y_kinematic - final_y)

                return {
                    'sensor_traj': sensor_trajectory,
                    'kinematic_traj': kinematic_trajectory,
                    'yaw_hist': robot.yaw_history,
                    'rms_abs': rms_abs,
                    'final_dist': final_dist,
                    'travelled': travelled,
                    'path_len': path_len,
                    'controller': controller,
                }

            # Run both OLD and NEW tests
            old_res = run_trajectory_simulation(use_sensor_offset=False)
            new_res = run_trajectory_simulation(use_sensor_offset=True)
            # Calculate improvement percentage - if both values are tiny, treat as no change
            old_rms = old_res['rms_abs']
            new_rms = new_res['rms_abs']
            if old_rms < 1e-3 and new_rms < 1e-3:  # Both essentially zero
                improvement_pct = 0.0
            elif old_rms < 1e-6:  # Old is zero but new is not
                improvement_pct = -100.0
            else:
                improvement_pct = ((old_rms - new_rms) / old_rms * 100)

            # Evaluate absolute criteria: must meet centimeter-level RMS regardless of delta
            progress_frac_new = (new_res['travelled'] / new_res['path_len']) if new_res['path_len'] > 1e-9 else 0.0
            progress_ok = (new_res['final_dist'] < 0.1) or (progress_frac_new >= 0.65)
            rms_ok = (new_rms <= rms_threshold_m)
            delta_ok = (improvement_pct > 5.0)
            standstill = (progress_frac_new < 0.05)

            result = {
                'trajectory_name': traj_name,
                'old_rms': old_rms,
                'new_rms': new_rms,
                'improvement_pct': improvement_pct,
                'significant_improvement': delta_ok,
                'rms_ok': rms_ok,
                'progress_ok': progress_ok,
                'standstill': standstill,
                'progress_frac_new': progress_frac_new,
                'final_dist': new_res['final_dist'],
                'travelled': new_res['travelled'],
                'path_len': new_res['path_len'],
            }
            all_results.append(result)

            # Print individual result with RMS/PROG/Δ ticks (plain symbols)
            prog_frac = (new_res['travelled'] / new_res['path_len']) if new_res['path_len'] > 1e-9 else 0.0
            tick_rms = "✓" if new_rms <= rms_threshold_m else "x"
            tick_prog = "✓" if (new_res['final_dist'] < 0.1 or prog_frac >= 0.70) else ("x" if prog_frac < 0.05 else "-")
            tick_delta = "✓" if improvement_pct > 5 else ("-" if improvement_pct > -5 else "x")
            print(
                f"  {traj_name:15s}: RMS {tick_rms} {new_rms:.3f}m | PROG {tick_prog} {prog_frac*100:.0f}% | Δ {tick_delta} {improvement_pct:+.1f}% "
                f"(OLD {old_rms:.3f}m)"
            )

            # Generate visualization if requested
            if with_visualization and MATPLOTLIB_AVAILABLE:
                old_trajectory_data = {
                    'sensor': old_res['sensor_traj'],
                    'kinematic': old_res['kinematic_traj'],
                    'yaw': old_res['yaw_hist']
                }
                new_trajectory_data = {
                    'sensor': new_res['sensor_traj'],
                    'kinematic': new_res['kinematic_traj'],
                    'yaw': new_res['yaw_hist']
                }
                self._generate_trajectory_plot(traj_name, traj_title, target_points,
                                              old_trajectory_data, new_trajectory_data,
                                              old_rms, new_rms, improvement_pct)

        # Calculate summary statistics
        avg_improvement = sum(r['improvement_pct'] for r in all_results) / len(all_results)
        significant_improvements = sum(1 for r in all_results if r['significant_improvement'])

        # Absolute success requires ALL trajectories to pass RMS threshold (independent of delta)
        key_ok = all(r.get('rms_ok', False) for r in all_results)

        return {
            'trajectory_results': all_results,
            'average_improvement_pct': avg_improvement,
            'significant_improvements': significant_improvements,
            'total_trajectories': len(all_results),
            'significant_improvement': avg_improvement > 5.0 and significant_improvements >= 2,
            'absolute_success': key_ok
        }

    def _generate_trajectory_plot(self, traj_name: str, traj_title: str, target_points: List[Tuple[float, float]],
                                 old_trajectory_data: dict, new_trajectory_data: dict,
                                 old_rms: float, new_rms: float, improvement_pct: float):
        """Generate single trajectory comparison plot."""
        if not MATPLOTLIB_AVAILABLE:
            return

        # Create side-by-side comparison plot with higher resolution
        fig, (ax_old, ax_new) = plt.subplots(1, 2, figsize=(20, 10))

        # Target path data
        target_x = [p[0] for p in target_points]
        target_y = [p[1] for p in target_points]

        def _plot_trajectory_data(ax, trajectory_data, target_x, target_y):
            """Plot trajectory data on given axis."""
            # Target path
            ax.plot(target_x, target_y, 'g--', linewidth=3, label='Target Path', alpha=0.8, zorder=1)
            
            # Kinematic center trajectory
            kinematic = trajectory_data['kinematic']
            if kinematic:
                kin_x = [p[0] for p in kinematic]
                kin_y = [p[1] for p in kinematic]
                ax.plot(kin_x, kin_y, 'b-', linewidth=2, label='Kinematic Center', alpha=0.7, zorder=2)
            
            # Sensor trajectory
            sensor = trajectory_data['sensor']
            if sensor:
                sens_x = [p[0] for p in sensor]
                sens_y = [p[1] for p in sensor]
                ax.plot(sens_x, sens_y, 'c-', linewidth=2, label='Sensor Position', alpha=0.8, zorder=3)
            
            # Robot orientation arrows
            if kinematic and sensor and trajectory_data.get('yaw'):
                yaw_history = trajectory_data['yaw']
                step = max(1, len(kinematic) // 15)  # ~15 arrows max
                for i in range(0, len(kinematic), step):
                    if i < len(yaw_history):
                        kin_x, kin_y = kinematic[i]
                        yaw = yaw_history[i]
                        # Calculate sensor position from kinematic center using yaw and offset
                        arrow_length = abs(self.sensor_offset) * 0.8  # Scale for visibility
                        sens_x_theoretical = kin_x + arrow_length * math.cos(yaw)
                        sens_y_theoretical = kin_y + arrow_length * math.sin(yaw)
                        # Draw arrow showing robot orientation and sensor offset direction
                        ax.annotate('', xy=(sens_x_theoretical, sens_y_theoretical), xytext=(kin_x, kin_y),
                                  arrowprops=dict(arrowstyle='->', color='red', alpha=0.6, lw=1.5),
                                  zorder=4)
            
            # Start/end markers
            ax.plot(target_x[0], target_y[0], 'go', markersize=12, label='Start', zorder=5)
            ax.plot(target_x[-1], target_y[-1], 'ro', markersize=12, label='End', zorder=5)

        # Plot both subplots using the same function
        if old_trajectory_data:
            _plot_trajectory_data(ax_old, old_trajectory_data, target_x, target_y)
        if new_trajectory_data:
            _plot_trajectory_data(ax_new, new_trajectory_data, target_x, target_y)

        # Configure subplots
        ax_old.set_title(f'OLD Algorithm\nRMS Error: {old_rms:.3f}m', fontsize=14, fontweight='bold')
        ax_old.set_xlabel('X Position (m)')
        ax_old.set_ylabel('Y Position (m)')
        ax_old.legend(loc='best')
        ax_old.grid(True, alpha=0.3)
        ax_old.axis('equal')

        ax_new.set_title(f'NEW Algorithm (Sensor Offset)\nRMS Error: {new_rms:.3f}m', fontsize=14, fontweight='bold')
        ax_new.set_xlabel('X Position (m)')
        ax_new.set_ylabel('Y Position (m)')
        ax_new.legend(loc='best')
        ax_new.grid(True, alpha=0.3)
        ax_new.axis('equal')

        # Overall title with improvement (using ASCII symbols for font compatibility)
        if improvement_pct > 5:
            status_symbol = "[PASS]"
        elif improvement_pct > -5:
            status_symbol = "[SAME]"
        else:
            status_symbol = "[FAIL]"
        
        fig.suptitle(f'{traj_title} - Sensor Offset Comparison (offset={self.sensor_offset}m)\n'
                    f'{status_symbol} RMS Improvement: {improvement_pct:+.1f}% '
                    f'({old_rms:.3f}m -> {new_rms:.3f}m)',
                    fontsize=16, fontweight='bold')

        plt.tight_layout()

        # Save plot
        output_dir = os.path.join(os.path.dirname(__file__), 'trajectory_plots')
        os.makedirs(output_dir, exist_ok=True)
        filename = f"{traj_name}_comparison_{self.sensor_offset}m.png"
        filepath = os.path.join(output_dir, filename)
        plt.savefig(filepath, dpi=300, bbox_inches='tight')
        print(f"    📊 Saved plot: {filepath}")
        plt.close()


    def run_all_tests(self, quick: bool = False) -> Dict[str, Any]:
        """Run all tests and return comprehensive results."""
        print(f"🧪 Running sensor offset tests (offset={self.sensor_offset}m)")

        # Run REAL algorithm tests
        offset_calc_results = self.test_sensor_offset_calculation()
        kinematic_calc_results = self.test_kinematic_center_calculation()
        safety_results = self.test_safety_checks_and_validation()

        # Run trajectory RMS test
        trajectory_results = self.test_real_trajectory_rms(quick=quick)

        # Check if all core algorithms pass
        algorithms_pass = (offset_calc_results['all_passed'] and
                          kinematic_calc_results['all_passed'] and
                          safety_results['all_passed'])

        # Compile summary
        summary = {
            'overall_success': algorithms_pass and trajectory_results['significant_improvement'] and trajectory_results.get('absolute_success', False),
            'algorithms_pass': algorithms_pass,
            'trajectory_improvement': trajectory_results['significant_improvement'],
            'average_improvement_pct': trajectory_results['average_improvement_pct'],
            'significant_improvements': trajectory_results['significant_improvements'],
            'total_trajectories': trajectory_results['total_trajectories'],
            'absolute_success': trajectory_results.get('absolute_success', False)
        }

        return {
            'summary': summary,
            'sensor_offset_calculation': offset_calc_results,
            'kinematic_center_calculation': kinematic_calc_results,
            'safety_checks': safety_results,
            'trajectory_rms': trajectory_results
        }


def print_test_summary(results: Dict[str, Any]):
    """Print formatted test summary."""
    print("\n📊 ALGORITHM TEST RESULTS:")

    # Core algorithm results
    algo_status = "✅" if results['summary']['algorithms_pass'] else "❌"
    print(f"  {algo_status} Core Algorithms: {'PASS' if results['summary']['algorithms_pass'] else 'FAIL'}")

    offset_status = "✅" if results['sensor_offset_calculation']['all_passed'] else "❌"
    print(f"    {offset_status} Sensor offset calculation: {len([r for r in results['sensor_offset_calculation']['test_cases'] if r['success']])}/{len(results['sensor_offset_calculation']['test_cases'])} tests passed")

    kinematic_status = "✅" if results['kinematic_center_calculation']['all_passed'] else "❌"
    print(f"    {kinematic_status} Kinematic center calculation: {len([r for r in results['kinematic_center_calculation']['test_cases'] if r['success']])}/{len(results['kinematic_center_calculation']['test_cases'])} tests passed")

    safety_status = "✅" if results['safety_checks']['all_passed'] else "❌"
    print(f"    {safety_status} Safety checks and validation: {len([r for r in results['safety_checks']['test_cases'] if r['success']])}/{len(results['safety_checks']['test_cases'])} tests passed")

    print("\n📊 TRAJECTORY IMPROVEMENT RESULTS:")
    for r in results['trajectory_rms']['trajectory_results']:
        tick_rms = "✓" if r.get('rms_ok') else "x"
        tick_prog = "✓" if r.get('progress_ok') else ("x" if r.get('standstill') else "-")
        tick_delta = "✓" if r.get('significant_improvement') else ("-" if r['improvement_pct'] > -5 else "x")
        print(f"  {r['trajectory_name']:15s}: RMS {tick_rms} {r['new_rms']:.3f}m | PROG {tick_prog} {r.get('progress_frac_new',0.0)*100:.0f}% | Δ {tick_delta} {r['improvement_pct']:+.1f}% "
              f"(OLD {r['old_rms']:.3f}m)")

    traj_status = "✅" if results['summary']['trajectory_improvement'] else "❌"
    print(f"\n  {traj_status} Overall trajectory improvement: {'SIGNIFICANT' if results['summary']['trajectory_improvement'] else 'INSUFFICIENT'}")
    print(f"      Average improvement: {results['summary']['average_improvement_pct']:+.1f}%")
    print(f"      Significant improvements: {results['summary']['significant_improvements']}/{results['summary']['total_trajectories']}")



def run_test_suite(with_visualization: bool = False):
    """Run comprehensive tests with multiple sensor offsets."""
    print("🚀 SENSOR OFFSET TEST SUITE")
    print("=" * 60)

    # Run algorithm tests once (they don't depend on sensor offset)
    print("\n🧠 TESTING CORE ALGORITHMS")
    print("-" * 40)
    suite = SensorOffsetTestSuite(sensor_offset=0.8)  # Use standard offset for algorithm tests
    offset_calc_results = suite.test_sensor_offset_calculation()
    kinematic_calc_results = suite.test_kinematic_center_calculation()
    safety_results = suite.test_safety_checks_and_validation()
    
    algorithms_pass = (offset_calc_results['all_passed'] and
                      kinematic_calc_results['all_passed'] and
                      safety_results['all_passed'])
    
    print(f"\n📊 CORE ALGORITHM RESULTS:")
    algo_status = "✅" if algorithms_pass else "❌"
    print(f"  {algo_status} Core Algorithms: {'PASS' if algorithms_pass else 'FAIL'}")

    # Run trajectory tests for multiple offsets
    print(f"\n🛤️ TESTING TRAJECTORIES WITH DIFFERENT OFFSETS")
    print("-" * 40)
    
    test_offsets = [0.5, 0.8, 1.2, 0, -0.3]
    all_trajectory_results = []

    for offset in test_offsets:
        print(f"\n🔧 Testing sensor offset: {offset}m")
        
        suite = SensorOffsetTestSuite(sensor_offset=offset)
        trajectory_results = suite.test_real_trajectory_rms(with_visualization=with_visualization)
        all_trajectory_results.append((offset, trajectory_results))
        
        # Print summary for this offset
        avg_improvement = trajectory_results['average_improvement_pct']
        significant_improvements = trajectory_results['significant_improvements']
        total_trajectories = trajectory_results['total_trajectories']
        
        print(f"  Average improvement: {avg_improvement:+.1f}%")
        print(f"  Significant improvements: {significant_improvements}/{total_trajectories}")

    # Overall assessment
    any_significant = any(results['significant_improvement'] for _, results in all_trajectory_results)
    overall_success = algorithms_pass and any_significant

    print(f"\n🏁 FINAL RESULT: {'PASS' if overall_success else 'SOME ISSUES'}")
    return overall_success


if __name__ == "__main__":
    import sys

    # Parse command line arguments
    with_viz = "--viz" in sys.argv or "--visualize" in sys.argv

    if with_viz:
        print("📊 Visualization enabled - plots will be generated")
    
    # Support quick mode
    quick = any(arg in ("--quick", "-q") for arg in sys.argv)
    if quick:
        print("⚡ Quick mode enabled: subset of trajectories, faster checks")
    success = run_test_suite(with_visualization=with_viz) if not quick else SensorOffsetTestSuite().run_all_tests(quick=True)['summary']['overall_success']

    if success:
        print("\n🎉 All tests passed!")
        if with_viz:
            print("📊 Trajectory plots saved to src/path_follower/trajectory_plots/")
    else:
        print("\n❌ Some tests failed.")

    sys.exit(0 if success else 1)
