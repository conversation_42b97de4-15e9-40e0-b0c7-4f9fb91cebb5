#!/usr/bin/env python3
"""
Ideal circle visualization and animation utilities.

- visualize_ideal_circle_geometry: static plot of ideal sensor and center paths
- animate_ideal_circle_geometry: animation with robot body, tracks, trail, and direction arrows

Comments in English. Documentation elsewhere should be in Russian per project policy.
"""

import os
import math
from typing import Tuple, List

try:
    import matplotlib.pyplot as plt
    from matplotlib.animation import FFMpegWriter, PillowWriter, FuncAnimation
    MATPLOTLIB_AVAILABLE = True
except Exception:
    MATPLOTLIB_AVAILABLE = False


def _compute_ideal_paths(sensor_offset: float,
                         radius: float,
                         center: Tuple[float, float],
                         direction: str,
                         num_points: int) -> Tuple[List[float], List[float], List[float], List[float], List[float]]:
    cx, cy = center
    clockwise = (str(direction).lower() in ['cw', 'clockwise', '-1'])

    if abs(sensor_offset) >= radius:
        raise ValueError("|sensor_offset| must be < radius")

    R_s = radius
    R_c = math.sqrt(max(1e-9, R_s * R_s - sensor_offset * sensor_offset))
    delta = math.atan2(sensor_offset, R_c)

    t_s_values = [i * 2.0 * math.pi / (max(1, num_points - 1)) for i in range(num_points)]

    sx, sy, kx, ky, yaw = [], [], [], [], []
    for t_s in t_s_values:
        sx_t = cx + R_s * math.cos(t_s)
        sy_t = cy + R_s * math.sin(t_s)
        t_c = t_s - delta
        yaw_t = t_c + (math.pi / 2.0) * (1.0 if not clockwise else -1.0)
        kx_t = cx + R_c * math.cos(t_c)
        ky_t = cy + R_c * math.sin(t_c)
        sx.append(sx_t); sy.append(sy_t)
        kx.append(kx_t); ky.append(ky_t)
        yaw.append(yaw_t)

    return sx, sy, kx, ky, yaw


def visualize_ideal_circle_geometry(sensor_offset: float = 1.0,
                                     radius: float = 3.0,
                                     center: Tuple[float, float] = (3.0, 0.0),
                                     direction: str = 'ccw',
                                     num_points: int = 200):
    if not MATPLOTLIB_AVAILABLE:
        print("Matplotlib is not available; skip ideal circle visualization")
        return

    try:
        sx, sy, kx, ky, yaw = _compute_ideal_paths(sensor_offset, radius, center, direction, num_points)
    except ValueError as e:
        print(f"Error: {e}")
        return

    cx, cy = center
    clockwise = (str(direction).lower() in ['cw', 'clockwise', '-1'])

    fig, ax = plt.subplots(1, 1, figsize=(9, 9))
    ax.plot(sx, sy, 'c-', linewidth=2, label='Sensor Position (given)')
    ax.plot(kx, ky, 'b-', linewidth=2, label='Kinematic Center')

    step = max(1, len(sx) // 20)
    for i in range(0, len(sx), step):
        ax.annotate('', xy=(sx[i], sy[i]), xytext=(kx[i], ky[i]),
                    arrowprops=dict(arrowstyle='->', color='red', alpha=0.7, lw=1.5))

    ax.set_aspect('equal', adjustable='box')
    ax.grid(True, alpha=0.3)
    ax.set_xlabel('X Position (m)')
    ax.set_ylabel('Y Position (m)')
    dir_name = 'CCW' if not clockwise else 'CW'
    ax.set_title(f'Ideal Circle: sensor follows circle\nR={radius:.2f} m, offset={sensor_offset:.2f} m, dir={dir_name}')
    ax.legend(loc='best')

    output_dir = os.path.join(os.path.dirname(__file__), 'trajectory_plots')
    os.makedirs(output_dir, exist_ok=True)
    filename = f"ideal_circle_sensor_vs_center_R{radius:.2f}m_offset{sensor_offset:.2f}m_{dir_name}.png"
    filepath = os.path.join(output_dir, filename)
    plt.savefig(filepath, dpi=300, bbox_inches='tight')
    print(f"    📊 Saved ideal circle plot: {filepath}")
    plt.close()


def animate_ideal_circle_geometry(sensor_offset: float = 1.0,
                                  radius: float = 3.0,
                                  center: Tuple[float, float] = (3.0, 0.0),
                                  direction: str = 'ccw',
                                  num_frames: int = 360,
                                  robot_length: float = 3.0,
                                  robot_width: float = 2.0,
                                  fps: int = 30):
    if not MATPLOTLIB_AVAILABLE:
        print("Matplotlib is not available; skip ideal circle animation")
        return

    try:
        sx, sy, kx, ky, yaw = _compute_ideal_paths(sensor_offset, radius, center, direction, num_frames)
    except ValueError as e:
        print(f"Error: {e}")
        return

    import math as _math
    fig, ax = plt.subplots(1, 1, figsize=(9, 9))
    cx, cy = center
    R_s = radius
    circle_theta = [i * 2.0 * _math.pi / 200 for i in range(201)]
    ax.plot([cx + R_s * _math.cos(tt) for tt in circle_theta],
            [cy + R_s * _math.sin(tt) for tt in circle_theta], 'g--', lw=2, label='Target Sensor Circle')
    ax.set_aspect('equal', adjustable='box')
    ax.grid(True, alpha=0.3)
    ax.set_xlabel('X Position (m)')
    ax.set_ylabel('Y Position (m)')
    dir_name = 'CCW' if str(direction).lower() not in ['cw', 'clockwise', '-1'] else 'CW'
    ax.set_title(f'Animation: sensor on circle, center motion\nR={radius:.2f} m, offset={sensor_offset:.2f} m, dir={dir_name}')

    # Artists
    sensor_path_line, = ax.plot([], [], 'c-', lw=2, alpha=0.7, label='Sensor Path')
    center_path_line, = ax.plot([], [], 'b-', lw=2, alpha=0.7, label='Kinematic Center Path')
    sensor_point, = ax.plot([], [], 'co', ms=6)
    center_point, = ax.plot([], [], 'bo', ms=6)
    orientation_arrow = ax.annotate('', xy=(0, 0), xytext=(0, 0),
                                    arrowprops=dict(arrowstyle='->', color='red', lw=1.5))
    ax.legend(loc='best')

    # Limits
    all_x = sx + kx
    all_y = sy + ky
    pad = max(robot_length, robot_width)
    ax.set_xlim(min(all_x) - pad, max(all_x) + pad)
    ax.set_ylim(min(all_y) - pad, max(all_y) + pad)

    # Arrows along sensor path showing heading; tips lie on trajectory
    max_arrows = 18
    arrow_step = max(2, len(sx) // max_arrows)
    arrow_indices = list(range(0, len(sx), arrow_step))
    if len(arrow_indices) > 2:
        arrow_indices = arrow_indices[1:-1]
    sensor_dir_arrows = []
    for _ in arrow_indices:
        ar = ax.annotate('', xy=(0, 0), xytext=(0, 0),
                         arrowprops=dict(arrowstyle='->', color='0.35', alpha=0.7, lw=1.2))
        ar.set_visible(False)
        sensor_dir_arrows.append(ar)

    # Geometry helpers
    def _body_polygon(cx_: float, cy_: float, yaw_: float) -> Tuple[list, list]:
        half_l = robot_length / 2.0
        half_w = robot_width / 2.0
        nose_margin = max(0.1, 0.15 * robot_length)
        sign_dir = 1.0 if sensor_offset >= 0.0 else -1.0
        tip_x = sensor_offset + sign_dir * nose_margin
        forward_extent = max(half_l, max(0.0, tip_x))
        backward_extent = max(half_l, max(0.0, -tip_x))
        local = [
            (-backward_extent, +half_w),
            (-backward_extent, -half_w),
            (+forward_extent, -half_w),
            (tip_x, 0.0),
            (+forward_extent, +half_w),
            (-backward_extent, +half_w),
        ]
        cos_y = _math.cos(yaw_)
        sin_y = _math.sin(yaw_)
        xs = [cx_ + px * cos_y - py * sin_y for px, py in local]
        ys = [cy_ + px * sin_y + py * cos_y for px, py in local]
        return xs, ys

    def _track_corners(cx_: float, cy_: float, yaw_: float, side: int) -> Tuple[list, list]:
        half_l = robot_length / 2.0
        half_w = robot_width / 2.0
        track_width = max(0.05, min(0.5, robot_width * 0.25))
        offset_y = half_w - track_width / 2.0
        y_center = side * offset_y
        local = [
            (+half_l, y_center + track_width / 2.0),
            (+half_l, y_center - track_width / 2.0),
            (-half_l, y_center - track_width / 2.0),
            (-half_l, y_center + track_width / 2.0),
            (+half_l, y_center + track_width / 2.0),
        ]
        cos_y = _math.cos(yaw_)
        sin_y = _math.sin(yaw_)
        xs = [cx_ + px * cos_y - py * sin_y for px, py in local]
        ys = [cy_ + px * sin_y + py * cos_y for px, py in local]
        return xs, ys

    # Robot artists
    body_line, = ax.plot([], [], color='k', lw=1.5)
    left_track_line, = ax.plot([], [], color='0.3', lw=2)
    right_track_line, = ax.plot([], [], color='0.3', lw=2)
    trail_length = min(120, max(40, num_frames // 3))
    trail_body_lines = [ax.plot([], [], color='0.7', lw=1,
                                 alpha=max(0.08, 0.6 * (1.0 - (i + 1) / (trail_length + 1))))[0]
                        for i in range(trail_length)]

    def init():
        sensor_path_line.set_data([], [])
        center_path_line.set_data([], [])
        sensor_point.set_data([], [])
        center_point.set_data([], [])
        body_line.set_data([], [])
        left_track_line.set_data([], [])
        right_track_line.set_data([], [])
        for tl in trail_body_lines:
            tl.set_data([], [])
        for ar in sensor_dir_arrows:
            ar.set_visible(False)
        orientation_arrow.set_position((0, 0))
        orientation_arrow.xy = (0, 0)
        return (sensor_path_line, center_path_line, sensor_point, center_point,
                body_line, left_track_line, right_track_line, *trail_body_lines, *sensor_dir_arrows, orientation_arrow)

    def update(frame: int):
        sensor_path_line.set_data(sx[:frame+1], sy[:frame+1])
        center_path_line.set_data(kx[:frame+1], ky[:frame+1])
        sensor_point.set_data([sx[frame]], [sy[frame]])
        center_point.set_data([kx[frame]], [ky[frame]])
        rx, ry = _body_polygon(kx[frame], ky[frame], yaw[frame])
        body_line.set_data(rx, ry)
        ltx, lty = _track_corners(kx[frame], ky[frame], yaw[frame], side=+1)
        rtx, rty = _track_corners(kx[frame], ky[frame], yaw[frame], side=-1)
        left_track_line.set_data(ltx, lty)
        right_track_line.set_data(rtx, rty)
        for i in range(trail_length):
            idx = frame - i - 1
            if idx >= 0:
                rxi, ryi = _body_polygon(kx[idx], ky[idx], yaw[idx])
                trail_body_lines[i].set_data(rxi, ryi)
            else:
                trail_body_lines[i].set_data([], [])
        orientation_arrow.set_position((kx[frame], ky[frame]))
        orientation_arrow.xy = (sx[frame], sy[frame])
        # Direction arrows on the sensor path (tips on trajectory)
        arrow_len = max(0.2, min(0.6, robot_length * 0.25))
        for j, idx in enumerate(arrow_indices):
            if idx <= frame:
                sx0, sy0 = sx[idx], sy[idx]
                yaw0 = yaw[idx]
                x_end = sx0
                y_end = sy0
                x_start = sx0 - arrow_len * _math.cos(yaw0)
                y_start = sy0 - arrow_len * _math.sin(yaw0)
                sensor_dir_arrows[j].set_position((x_start, y_start))
                sensor_dir_arrows[j].xy = (x_end, y_end)
                sensor_dir_arrows[j].set_visible(True)
            else:
                sensor_dir_arrows[j].set_visible(False)
        return (sensor_path_line, center_path_line, sensor_point, center_point,
                body_line, left_track_line, right_track_line, *trail_body_lines, *sensor_dir_arrows, orientation_arrow)

    anim = FuncAnimation(fig, update, init_func=init, frames=num_frames, interval=1000/max(1, fps), blit=False)

    output_dir = os.path.join(os.path.dirname(__file__), 'trajectory_plots')
    os.makedirs(output_dir, exist_ok=True)
    base_name = f"ideal_circle_animation_R{radius:.2f}m_offset{sensor_offset:.2f}m_{dir_name}"
    mp4_path = os.path.join(output_dir, base_name + '.mp4')
    gif_path = os.path.join(output_dir, base_name + '.gif')
    saved = False
    try:
        writer = FFMpegWriter(fps=fps, bitrate=1800)
        anim.save(mp4_path, writer=writer, dpi=200)
        print(f"    🎬 Saved animation (mp4): {mp4_path}")
        saved = True
    except Exception as e:
        print(f"Warning: MP4 save failed ({e}). Falling back to GIF…")
    if not saved:
        try:
            anim.save(gif_path, writer=PillowWriter(fps=min(fps, 20)), dpi=200)
            print(f"    🎞️ Saved animation (gif): {gif_path}")
            saved = True
        except Exception as e:
            print(f"Error: Could not save animation as GIF either: {e}")
    plt.close()


