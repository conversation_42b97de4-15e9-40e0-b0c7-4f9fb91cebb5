"""Sensor offset estimation and kinematic center utilities.

This module isolates the offset estimation logic from the path controller.

Comments are in English as per project convention.
"""

from __future__ import annotations

import math
from typing import Any, Tuple


class SensorOffsetEstimator:
    """Estimate sensor-to-kinematic-center offset and apply it to positions."""

    # Implementation constants (not tunable via params server)
    _OFFSET_HISTORY_SIZE_LIMIT = 10
    _OFFSET_MIN_SAMPLES_FOR_STATS = 3

    def __init__(self, *, node=None):
        self.node = node

        # Runtime flags
        self._enabled: bool = True

        # Parameters (config via load_parameters)
        self._alpha: float = 0.15
        self._min_omega: float = 0.05
        self._max_abs_offset: float = 2.0
        # No periodic quality logs in production
        self._min_updates_for_reliability: int = 10
        self._max_stability_variance: float = 0.01
        self._reliability_threshold: float = 0.7  # not used anymore

        # State
        self._offset: float = 0.0
        self._update_count: int = 0
        self._is_reliable: bool = False
        self._history = []  # type: list[float]

    # --- Public configuration ---

    def set_node(self, node) -> None:
        self.node = node

    def load_parameters(self, node_params: dict) -> None:
        self._alpha = node_params['sensor_offset_smoothing_alpha']
        self._min_omega = node_params['sensor_offset_min_angular_velocity']
        self._max_abs_offset = node_params['sensor_offset_max_reasonable_offset']
        self._min_updates_for_reliability = node_params['sensor_offset_min_updates_for_reliability']
        self._max_stability_variance = node_params['sensor_offset_max_stability_variance']
        # No periodic quality logs

    def set_enabled(self, enabled: bool) -> None:
        self._enabled = enabled
        if not enabled:
            # Reset for clean comparison when disabled
            self._offset = 0.0
            self._update_count = 0
            self._history.clear()

    # --- Estimation and application ---

    def update_from_speed(self, speed_state) -> bool:
        """Update offset estimate from speed observation.

        Returns True when an update was applied.
        """
        if not self._enabled:
            return False
        if not speed_state:
            return False
        if not (math.isfinite(speed_state.lateral) and math.isfinite(speed_state.angular)):
            return False
        if not speed_state.is_reliable:
            return False
        if abs(speed_state.angular) < self._min_omega:
            return False

        raw_offset = speed_state.lateral / speed_state.angular

        if abs(raw_offset) > self._max_abs_offset:
            if self.node:
                self.node.log(
                    f"OFFSET_REJECTED: raw_offset={raw_offset:.3f}m exceeds limit {self._max_abs_offset:.3f}m",
                    level=self.node.DEBUG,
                    period=5.0,
                )
            return False

        # Track history and smooth
        self._history.append(raw_offset)
        if len(self._history) > self._OFFSET_HISTORY_SIZE_LIMIT:
            self._history.pop(0)

        prev = self._offset
        self._offset = (1.0 - self._alpha) * self._offset + self._alpha * raw_offset
        self._update_count += 1

        # Simple reliability flag
        _, variance = self._calc_stats()
        has_enough = self._update_count >= self._min_updates_for_reliability
        is_stable = variance <= self._max_stability_variance
        self._is_reliable = has_enough and is_stable

        return True

    def apply_to_position(self, position: Any) -> Tuple[float, float]:
        """Return kinematic center world coordinates based on current estimate.

        If disabled, returns the input sensor position unchanged.
        """
        if not self._enabled:
            return position.x, position.y

        x = position.x - self._offset * math.cos(position.yaw)
        y = position.y - self._offset * math.sin(position.yaw)
        return x, y

    # --- Introspection ---

    def get_offset(self) -> float:
        return self._offset

    def reset(self) -> None:
        """Reset estimator state."""
        self._offset = 0.0
        self._update_count = 0
        self._history.clear()
        self._is_reliable = False

    def set_offset_for_testing(self, value: float, mark_reliable: bool = True) -> None:
        """Seed estimator with a given offset for tests/debug.

        Not intended for production use.
        """
        self._offset = float(value)
        self._update_count = max(self._update_count, self._min_updates_for_reliability)
        self._is_reliable = bool(mark_reliable)

    def get_quality_info(self) -> dict:
        return {
            'offset': self._offset,
            'is_reliable': self._is_reliable,
            'update_count': self._update_count,
        }

    # --- Internal helpers ---

    def _calc_stats(self) -> Tuple[float, float]:
        if len(self._history) < self._OFFSET_MIN_SAMPLES_FOR_STATS:
            return 0.0, 0.0
        mean = sum(self._history) / len(self._history)
        var = sum((x - mean) ** 2 for x in self._history) / len(self._history)
        return mean, var

    def _log_quality(self, raw_offset: float, change: float, omega: float) -> None:
        return


