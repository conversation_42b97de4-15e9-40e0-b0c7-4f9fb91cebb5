# Copyright 2025 <PERSON> <<EMAIL>>
# Copyright 2025 Zyfra Robotics
#
# This software is proprietary and confidential.
# Unauthorized copying of this file is strictly prohibited.

"""Path follower ROS2 node with FSM for robotic drilling rig."""

import math
from typing import Optional, List

import rclpy
from base_node.base_fsm import BaseFSM, BaseState
from drill_msgs.msg import (DriveAction, DriveStatus, Position, 
                            StateMachineStatus, TracksCtrl, SpeedState)
from drill_msgs.srv import GetCurrentDriveAction
from std_msgs.msg import Header

from .path_controller import PathController
from .approach_controller import ApproachController


class PathFollowerStates:
    """State name constants."""
    IDLE = 'idle'
    RUNNING = 'running'
    APPROACH = 'approach'


class IdleState(BaseState):
    """Idle state - waiting for drive action."""

    def __init__(self, node: 'PathFollowerNode'):
        super().__init__(
            name=PathFollowerStates.IDLE,
            node=node,
            remember_as_prev=True,
            ignore_permission=False,
            ignore_robomode=False,
            ignore_outdated_subs=True,  # Allow outdated position in idle
            ignore_safety_check=True    # No movement in idle
        )

    def on_transition_to(self):
        """Enter idle state and reset control."""
        self.log('STATE: idle - waiting for drive action')
        self.node.stop_control()
        
        # Mark action as completed if one was active
        if self.node.current_drive_action is not None:
            self.node.last_action_id = self.node.current_drive_action.id
            self.node.cur_action_id = -1  # No active action
            self.log(f'ACTION_COMPLETE: {self.node.current_drive_action.id}')
        
        self.node.current_drive_action = None
        # Indices are maintained locally in RunningState only

    def do_work(self):
        """Check for new drive action and transition to running."""
        if self.node.current_drive_action is not None:
            self.log('STATE_CHANGE: idle → running')
            self.node.set_state(PathFollowerStates.RUNNING)


class RunningState(BaseState):
    """Running state - following the path segments."""

    def __init__(self, node: 'PathFollowerNode'):
        super().__init__(
            name=PathFollowerStates.RUNNING,
            node=node,
            remember_as_prev=True,
            ignore_permission=False,
            ignore_robomode=False,
            ignore_outdated_subs=False,
            ignore_safety_check=False
        )

    def on_transition_to(self):
        """Enter running state and initialize path following."""
        action_id = self.node.current_drive_action.id if self.node.current_drive_action else "None"
        self.log(f'STATE: running - following path for action {action_id}')

        # Localize running state progression variables
        self.current_segment_id = 0
        self.current_point_id = 0
        self.last_point_advance_time = self.node.get_time()
        self._last_dist_to_next = None

        if self.node.current_drive_action and self.node.subs.position:
            # Fast-forward to current position using controller
            self.current_segment_id, self.current_point_id = \
                self.node.path_controller.fast_forward_to_current_position(
                    self.node.current_drive_action.path_segments,
                    self.node.subs.position,
                )
            self.last_point_advance_time = self.node.get_time()

            # Log path summary
            total_segments = len(self.node.current_drive_action.path_segments)
            total_points = sum(len(seg.points) for seg in self.node.current_drive_action.path_segments)
            current_seg = self.node.current_drive_action.path_segments[self.current_segment_id] if self.current_segment_id < total_segments else None
            seg_info = f"({len(current_seg.points)}pts {'REV' if current_seg.is_backward else 'FWD'})" if current_seg else "N/A"
            self.log(f"PATH_INIT: {total_segments}seg {total_points}pts, fast_forward→seg{self.current_segment_id}/pt{self.current_point_id} {seg_info}")

    def do_work(self):
        """Execute path following with state transitions."""
        self.log('RUN_CYCLE', level=self.DEBUG, period=5.0)
        
        # Check if we should stop
        if not self.node.should_continue_movement():
            self.log('STATE_CHANGE: running → idle (conditions not met)')
            self.node.set_state(PathFollowerStates.IDLE)
            return

        # Decide if should enter approach mode here (local logic)
        if self.node.current_drive_action is not None:
            if self.current_segment_id >= len(self.node.current_drive_action.path_segments):
                hole_pos = self.node.current_drive_action.hole_position
                # Validate hole position (>= 1cm from origin)
                if math.hypot(hole_pos.x, hole_pos.y) > 0.01:
                    self.log('STATE_CHANGE: running → approach')
                    self.node.set_state(PathFollowerStates.APPROACH)
                    return
                else:
                    self.log('PATH_COMPLETE: no hole position - mission done')

        # Follow the path (localized execution)
        if (self.node.current_drive_action is None or self.node.subs.position is None):
            self.log(f"PATH_EXEC: Missing data - action:{bool(self.node.current_drive_action)}, pos:{bool(self.node.subs.position)}",
                     level=self.DEBUG, period=2.0)
            self.node.stop_control()
            return

        # All segments completed -> nothing to do here
        if self.current_segment_id >= len(self.node.current_drive_action.path_segments):
            self.node.stop_control()
            return

        current_segment = self.node.current_drive_action.path_segments[self.current_segment_id]

        # Segment completion (delegated to controller for clarity and testability)
        if self.node.path_controller.should_complete_segment(current_segment, self.node.subs.position, self.current_point_id):
            old_segment = self.current_segment_id
            self.current_segment_id += 1
            self.current_point_id = 0
            self.last_point_advance_time = self.node.get_time()

            if self.current_segment_id < len(self.node.current_drive_action.path_segments):
                new_segment = self.node.current_drive_action.path_segments[self.current_segment_id]
                self.log(
                    f"SEG_ADVANCE: {old_segment}→{self.current_segment_id}/{len(self.node.current_drive_action.path_segments)-1} "
                    f"({len(new_segment.points)}pts, {'REV' if new_segment.is_backward else 'FWD'})",
                    level=self.INFO,
                )
            else:
                self.log(
                    f"TRAJECTORY_COMPLETE: {self.node.path_controller.get_rms_summary_for_logging()}",
                    level=self.INFO,
                )
            return

        # Point advancement logic (scan forward while distance decreases)
        if self.current_point_id + 1 < len(current_segment.points):
            old_point_id = self.current_point_id
            new_point_id = self.node.path_controller.choose_next_point_id_by_distance(
                current_segment, self.node.subs.position, self.current_point_id
            )

            if new_point_id > old_point_id:
                target_point = current_segment.points[new_point_id]
                dist_to_next = math.hypot(
                    self.node.subs.position.x - target_point.x,
                    self.node.subs.position.y - target_point.y,
                )
                self.current_point_id = new_point_id
                self.last_point_advance_time = self.node.get_time()
                direction = "REV" if current_segment.is_backward else "FWD"
                skipped = new_point_id - old_point_id
                self.log(
                    f"PT_ADVANCE: {self.current_point_id}/{len(current_segment.points)-1} "
                    f"skip={skipped} d={dist_to_next:.2f}m {direction}",
                    level=self.INFO,
                )
                # If reached the last point index, avoid computing control this cycle;
                # let the next loop perform the segment completion branch above.
                if self.current_point_id >= len(current_segment.points) - 1:
                    return
            else:
                # Stuck warning and optional controller-driven force advance
                now = self.node.get_time()
                forced_id = self.node.path_controller.compute_forced_advance_index(
                    current_segment,
                    self.node.subs.position,
                    self.current_point_id,
                    self.last_point_advance_time,
                    now,
                )
                if forced_id is not None and forced_id > self.current_point_id:
                    self.current_point_id = forced_id
                    self.last_point_advance_time = now
                    direction = "REV" if current_segment.is_backward else "FWD"
                    self.log(
                        f"PT_FORCE_ADVANCE: →{self.current_point_id}/{len(current_segment.points)-1} {direction}",
                        level=self.WARN,
                    )
                    return

                time_since_advance = now - self.last_point_advance_time
                if time_since_advance > 10.0:
                    target_point = current_segment.points[self.current_point_id + 1]
                    direction = "REV" if current_segment.is_backward else "FWD"
                    # Compute distance just for logging
                    d = math.hypot(
                        self.node.subs.position.x - target_point.x,
                        self.node.subs.position.y - target_point.y,
                    ) if self.node.subs.position is not None else float('inf')
                    self.log(
                        f"STUCK_WARNING: pt={self.current_point_id} t={time_since_advance:.1f}s "
                        f"d={d:.2f}m→({target_point.x:.1f},{target_point.y:.1f}) {direction}",
                        level=self.WARN,
                        period=10.0,
                    )

        # Control computation
        control_result = self.node.path_controller.calculate_path_following_control(
            current_segment, self.node.subs.position, self.current_point_id,
            speed_state=self.node.subs.speed_state,
        )

        if control_result.is_blocked:
            self.node.stop_control()
            return

        if control_result.left_speed == 0.0 and control_result.right_speed == 0.0:
            self.log(
                f"CTRL_ERROR: Zero speeds but not blocked! {control_result}",
                level=self.ERROR,
                period=1.0,
            )

        # Update smoothed targets; publishing occurs in do_work_finally
        self.node.update_smoothed_targets(control_result.left_speed, control_result.right_speed)


class ApproachState(BaseState):
    """Approach state - precise positioning for drilling."""

    def __init__(self, node: 'PathFollowerNode'):
        super().__init__(
            name=PathFollowerStates.APPROACH,
            node=node,
            remember_as_prev=True,
            ignore_permission=False,
            ignore_robomode=False,
            ignore_outdated_subs=False,
            ignore_safety_check=False
        )

    def on_transition_to(self):
        """Enter approach state for precise positioning."""
        # Reset PID controller state for new approach session
        self.node.approach_controller.reset_pid()
        
        if self.node.current_drive_action and self.node.subs.drill_position:
            hole_pos = self.node.current_drive_action.hole_position
            drill_pos = self.node.subs.drill_position
            distance = math.sqrt((drill_pos.x - hole_pos.x)**2 + (drill_pos.y - hole_pos.y)**2)
            self.log(f'STATE: approach - positioning drill at ({hole_pos.x:.2f},{hole_pos.y:.2f}), dist={distance:.2f}m')
        else:
            self.log('STATE: approach - precise drill positioning')

    def do_work(self):
        """Execute approach control with state transitions."""
        # Check approach timeout 
        approach_duration = self.node.get_current_state_duration()
        if approach_duration > self.node.approach_timeout:
            self.handle_error(
                f"APPROACH_TIMEOUT: Failed to reach target in {approach_duration:.1f}s active time (limit: {self.node.approach_timeout:.1f}s)",
                level=self.ERROR,
                event_code=self.node.events.APPROACH_TIMEOUT,
                require_remote=True
            )
            return  # handle_error triggers failure state transition
        
        # Check for distance error during approach execution
        if (self.node.current_drive_action is not None and 
            self.node.subs.drill_position is not None):
            hole_pos = self.node.current_drive_action.hole_position
            drill_pos = self.node.subs.drill_position
            dist_to_hole = math.sqrt((drill_pos.x - hole_pos.x)**2 + (drill_pos.y - hole_pos.y)**2)
            
            if dist_to_hole > self.node.path_controller.max_allowed_distance_to_target:
                self.handle_error(
                    f"APPROACH_DISTANCE_ERROR: Distance increased during approach {dist_to_hole:.2f}m > {self.node.path_controller.max_allowed_distance_to_target:.2f}m",
                    level=self.ERROR,
                    event_code=self.node.events.APPROACH_DISTANCE_ERROR,
                    require_remote=True
                )
                return  # handle_error triggers failure state transition
        
        # Check if we should stop
        if not self.node.should_continue_movement():
            self.log('STATE_CHANGE: approach → idle (conditions not met)')
            self.node.set_state(PathFollowerStates.IDLE)
            return

        # Execute approach control using dedicated controller
        if (self.node.current_drive_action is None or self.node.subs.drill_position is None):
            self.node.stop_control()
            return

        hole_pos = self.node.current_drive_action.hole_position
        drill_pos = self.node.subs.drill_position

        left_speed, right_speed, goal_reached = self.node.approach_controller.calculate_control(
            drill_pos, hole_pos,
        )

        if goal_reached:
            final_distance = math.hypot(drill_pos.x - hole_pos.x, drill_pos.y - hole_pos.y)
            self.log(
                f"APPROACH_COMPLETE: precision={final_distance*100:.1f}cm "
                f"final_robot=({self.node.subs.position.x:.2f},{self.node.subs.position.y:.2f}) "
                f"final_drill=({drill_pos.x:.2f},{drill_pos.y:.2f}) "
                f"target_hole=({hole_pos.x:.2f},{hole_pos.y:.2f})",
            )
            self.node.stop_control()
            self.log('STATE_CHANGE: approach → idle (target reached)')
            self.node.set_state(PathFollowerStates.IDLE)
            return

        self.node.update_smoothed_targets(left_speed, right_speed)


class PathFollowerNode(BaseFSM):
    """Main path follower FSM node."""

    def __init__(self):
        """Initialize path follower node."""
        super().__init__('path_follower')

        # Publishers
        self.tracks_pub = self.create_publisher(
            TracksCtrl, '/tracks_target_speed', 10)
        self.drive_status_pub = self.create_publisher(
            DriveStatus, '/driver_status', 10)

        # Service servers
        self.get_drive_action_service = self.create_service(
            GetCurrentDriveAction,
            'get_current_drive_action',
            self.handle_get_drive_action_request
        )

        # Subscribers for path following
        self.create_subscription(
            DriveAction, '/drive_action', self._drive_action_callback, 10)
        self.add_subscriber(
            '/position', Position, 'position', timeout=0.5)
        self.add_subscriber(
            '/drill_position', Position, 'drill_position', timeout=0.5)
        self.add_subscriber(
            '/main_state_machine_status', StateMachineStatus,
            'main_status', timeout=2.0)
        self.add_subscriber(
            '/speed_state', SpeedState, 'speed_state', timeout=1.0)

        # Controllers (algorithmic, ROS-free)
        self.path_controller = PathController()
        self.path_controller.set_node(self)
        self.approach_controller = ApproachController(node=self)

        # Action state
        self.current_drive_action: Optional[DriveAction] = None
        # Note: we use cur_action_id and last_action_id inherited from BaseFSM

        # Control timing parameters
        self.message_timeout = 0.5                        # Message time-to-live for safety (s)
        self.output_smoothing_time_constant = 0.1         # Output time constant for control smoothing (s)
        self.approach_timeout = 60.0                      # Maximum time for approach mode (s)

        # Control state
        self.left_prev = 0.0
        self.right_prev = 0.0
        self.left_smoothed = 0.0
        self.right_smoothed = 0.0
        self.last_control_time = self.get_time()
        
        # Diagnostic tracking
        self.last_point_advance_time = self.get_time()

        # Add states
        self.add_states(
            IdleState(self),
            RunningState(self),
            ApproachState(self)
        )

        # Set initial state
        self.set_state(PathFollowerStates.IDLE)

        self.log('PATH_FOLLOWER: Initialized successfully')

    def initialize(self):
        """Initialize FSM - load parameters."""

        # Set logger level to DEBUG programmatically
        rclpy.logging.set_logger_level(self.get_name(), rclpy.logging.LoggingSeverity.DEBUG)
        
        self.load_parameters()

    def load_parameters(self):
        """Load control parameters from parameter server."""
        params = self.node_params
        vehicle_params = self.vehicle_params
        global_params = self.global_params

        # Load parameters into controllers
        self.path_controller.load_parameters(params, vehicle_params)
        self.approach_controller.load_parameters(params, vehicle_params)

        # Control timing parameters
        self.message_timeout = params['message_timeout']
        self.output_smoothing_time_constant = params['output_smoothing_time_constant']
        self.approach_timeout = params['approach_timeout']
        
        # Override with global parameters if available
        if global_params:
            self.message_timeout = global_params.get('MSG_TTL', self.message_timeout)

        self.log(
            f'PARAMS_LOADED:\n'
            f'  lookahead={self.path_controller.lookahead_distance:.2f}m\n'
            f'  lat_corr_gain={self.path_controller.lateral_correction_gain}\n'
            f'  head_err_gain={self.path_controller.heading_error_gain}\n'
            f'  ff_gain={self.path_controller.path_curvature_feedforward_gain}\n'
            f'  max_head_err={math.degrees(self.path_controller.max_heading_error_for_motion):.0f}°\n'
            f'  max_spd={self.path_controller.max_speed}\n'
            f'  veh_width={self.path_controller.vehicle_width}m\n'
            f'  --- APPROACH ---\n'
            f'  max_approach_speed={self.approach_controller.max_approach_speed}\n'
            f'  max_approach_ang_err={math.degrees(self.approach_controller.max_approach_angular_error):.1f}°\n'
            f'  goal_achievement_radius={self.approach_controller.goal_achievement_radius}\n'
            f'  approach_max_turn_rate={self.approach_controller.approach_max_turn_rate}\n'
            f'  max_allowed_distance_to_target={self.approach_controller.max_allowed_distance_to_target}\n'
            f'  approach_timeout={self.approach_timeout:.1f}s\n'
            f'  approach_pid: p={self.approach_controller._pid_p} i={self.approach_controller._pid_i} d={self.approach_controller._pid_d} i_sat={self.approach_controller._pid_i_sat}\n'
            f'  output_smoothing_time_constant={getattr(self, "output_smoothing_time_constant", "N/A")}\n'
        )

    def on_params_update(self, interesting_updated_keys):
        """Handle parameter updates."""
        self.load_parameters()
        self.log(f"PARAMS_UPDATE: {interesting_updated_keys}")
        
        if self.get_name() in interesting_updated_keys:
            self.path_controller.load_parameters(self.node_params, self.vehicle_params)
            self.approach_controller.load_parameters(self.node_params, self.vehicle_params)
            self.log("CTRL_PARAMS: Parameters reloaded")

    def stop_control(self):
        """Stop all movement - safety function."""
        # Only reset targets; publishing is centralized in do_work_finally
        self.left_prev = 0.0
        self.right_prev = 0.0
        self.left_smoothed = 0.0
        self.right_smoothed = 0.0


    def safety_check(self) -> bool:
        """Perform safety checks."""
        # Position data reliability: do not escalate to failure; just wait
        if (self.current_state.name != PathFollowerStates.IDLE and
            self.subs.position is not None and
            not self.subs.position.is_reliable):
            self.log("Position not reliable (waiting)", level=self.INFO, period=30.0)
            return False

        # Check main state machine status
        if (self.subs.main_status is None or
            self.subs.main_status.current_state not in ["MOVING", "moving"]):
            self.log(f"BLOCKED: main_state={self.subs.main_status.current_state if self.subs.main_status else 'None'}", 
                    level=self.DEBUG, 
                    period=10.0)
            return False

        return True

    def should_continue_movement(self) -> bool:
        """Check if movement should continue - only action-specific checks."""
        # Check for cancel action (id = -2)
        if (self.current_drive_action is not None and
            self.current_drive_action.id == -2):
            self.log("MOVEMENT_STOPPED: action_cancelled", period=1.0)
            return False

        return True

    # should_enter_approach_mode moved to RunningState

    # initialize_path_following moved into RunningState

    # execute_path_following localized to RunningState

    # execute_approach_control removed; handled in ApproachState

    def update_smoothed_targets(self, left_target: float, right_target: float) -> None:
        """Update smoothed control targets using a linear smoothing model."""
        current_time = self.get_time()
        dt = current_time - self.last_control_time
        self.last_control_time = current_time

        if dt > 0 and self.output_smoothing_time_constant > 0:
            alpha = min(1.0, dt / self.output_smoothing_time_constant)
            self.left_smoothed = self.left_prev + alpha * (left_target - self.left_prev)
            self.right_smoothed = self.right_prev + alpha * (right_target - self.right_prev)
        else:
            self.left_smoothed = left_target
            self.right_smoothed = right_target

        self.left_prev = self.left_smoothed
        self.right_prev = self.right_smoothed

    def publish_status(self):
        """Publish drive status."""
        status = DriveStatus()
        status.header = Header()
        status.header.stamp = self.get_rostime()

        if self.current_drive_action is not None:
            status.cur_action_id = self.cur_action_id
            # Provide segment/point only if in running state
            if isinstance(self.current_state, RunningState):
                status.cur_segment_id = self.current_state.current_segment_id
                status.cur_point_id = self.current_state.current_point_id
        else:
            status.cur_action_id = self.cur_action_id

        status.last_action_id = self.last_action_id
        status.status = self.current_state.name if self.current_state else "unknown"

        self.drive_status_pub.publish(status)

    def handle_get_drive_action_request(self, request, response):
        """Handle GetCurrentDriveAction service request."""
        self.log("SVC_REQ: GetCurrentDriveAction", level=self.DEBUG)
        _ = request  # Unused parameter
        
        response.header.stamp = self.get_rostime()
        
        if self.current_drive_action is not None:
            response.action_id = self.cur_action_id
            response.path_segments = self.current_drive_action.path_segments
            response.success = True
            response.message = f"Current drive action {self.cur_action_id}"
            
            self.log(f"SVC_RESP: action_id={self.cur_action_id} "
                    f"segments={len(self.current_drive_action.path_segments)}", level=self.DEBUG)
        else:
            response.action_id = self.cur_action_id
            response.path_segments = []
            response.success = True
            response.message = "No current drive action"
            
            self.log("SVC_RESP: no current action", level=self.DEBUG)
        
        return response

    def do_work_prevented(self) -> None:
        """Called when regular work cannot proceed – stop hardware safely."""
        self.log("FSM_BLOCKED: Work prevented - stopping control", level=self.WARN, period=1.0)
        self.stop_control()
        
    def on_subscribers_timeout(self, expired: List[str]):
        """Called when any custom subscribers time out."""
        self.log(f"FSM_BLOCKED: Timeout - {','.join(expired)}", 
                level=self.WARN, period=1.0)

    def _drive_action_callback(self, msg: DriveAction):
        """Callback for drive action subscription."""
        self.log(f"DriveAction received with ID: {msg.id}", level=self.DEBUG)

        # Handle cancel action separately
        if msg.id == -2:
            self.log("ACTION_CANCEL received", period=10.0)
            self.current_drive_action = None
            self.cur_action_id = -1  # No active action
            if self.current_state.name != PathFollowerStates.IDLE:
                self.set_state(PathFollowerStates.IDLE)
            return

        # Check for duplicate/repeated action ID and log a warning
        if self.current_drive_action is not None and msg.id == self.cur_action_id:
            self.log(f"Received DriveAction with the same ID as the current one ({msg.id}). Restarting action.", level=self.WARN)
        elif self.current_state.name == PathFollowerStates.IDLE and self.last_action_id != 0 and msg.id == self.last_action_id:
             self.log(f"Received DriveAction with the same ID as the last completed one ({msg.id}). Re-running action.", level=self.WARN)

        # Process the action (new or re-issued)
        self.log(f"ACTION_NEW: ID={msg.id}")
        
        num_segments = len(msg.path_segments) if msg.path_segments else 0
        total_points = sum(len(seg.points) for seg in msg.path_segments) if msg.path_segments else 0
        
        self.log(f"ACTION_DETAILS: {num_segments}seg {total_points}pts")
        
        self.last_point_advance_time = self.get_time()
        self.path_controller.reset_rms_tracking()
        
        self.current_drive_action = msg
        self.cur_action_id = msg.id  # Set current action ID

    def do_work_finally(self):
        """Called at end of each work cycle - publish control and status."""
        # Centralized publish of current smoothed control
        tracks_cmd = TracksCtrl()
        tracks_cmd.header = Header()
        tracks_cmd.header.stamp = self.get_rostime()
        tracks_cmd.left = self.left_smoothed
        tracks_cmd.right = self.right_smoothed
        self.tracks_pub.publish(tracks_cmd)

        # Publish status each cycle
        self.publish_status()


def main(args=None):
    """Main entry point."""
    rclpy.init(args=args)

    try:
        node = PathFollowerNode()
        node.run()
    except KeyboardInterrupt:
        pass
    except Exception as e:
        print(f"Error in path follower: {e}")
    finally:
        rclpy.shutdown()


if __name__ == '__main__':
    main()
