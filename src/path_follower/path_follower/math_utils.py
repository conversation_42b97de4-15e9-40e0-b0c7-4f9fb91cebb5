# Copyright 2025 <PERSON> <<EMAIL>>
# Copyright 2025 Zyfra Robotics
#
# This software is proprietary and confidential.
# Unauthorized copying of this file is strictly prohibited.
"""Mathematical utility functions for path follower."""

import math
from typing import Tuple


def norm_angle_minusPI_plusPI(angle: float) -> float:
    """Normalize angle to [-π, π] range."""
    while angle > math.pi:
        angle -= 2.0 * math.pi
    while angle < -math.pi:
        angle += 2.0 * math.pi
    return angle


def lerp(a: float, b: float, t: float) -> float:
    """Linear interpolation between a and b by factor t in [0,1]."""
    if t <= 0.0:
        return a
    if t >= 1.0:
        return b
    return a + (b - a) * t


def interpolate_xy(x0: float, y0: float, x1: float, y1: float, t: float) -> Tuple[float, float]:
    """Interpolate coordinates along segment using linear interpolation."""
    return lerp(x0, x1, t), lerp(y0, y1, t)


def interpolate_angle(a0: float, a1: float, t: float) -> float:
    """Shortest-arc interpolation between angles a0 and a1 (radians).

    Handles wrap-around by normalizing the delta to [-pi, pi].
    """
    delta = norm_angle_minusPI_plusPI(a1 - a0)
    return norm_angle_minusPI_plusPI(a0 + delta * max(0.0, min(1.0, t)))



def point_to_segment_distance(px: float, py: float, x1: float, y1: float,
                              x2: float, y2: float) -> float:
    """Calculate distance from point to line segment."""
    dx = x2 - x1
    dy = y2 - y1
    if dx == 0 and dy == 0:
        return math.sqrt((px - x1) ** 2 + (py - y1) ** 2)

    t = max(0, min(1, ((px - x1) * dx + (py - y1) * dy) / (dx * dx + dy * dy)))
    proj_x = x1 + t * dx
    proj_y = y1 + t * dy
    return math.sqrt((px - proj_x) ** 2 + (py - proj_y) ** 2)


def signed_distance_from_point_to_line(px: float, py: float,
                                        x1: float, y1: float,
                                        x2: float, y2: float) -> float:
    """Calculate signed distance from point to infinite line."""
    return ((x2 - x1) * (y1 - py) - (x1 - px) * (y2 - y1)) / \
           math.sqrt((x2 - x1) ** 2 + (y2 - y1) ** 2)


def interpolate_speed(pt0_x: float, pt0_y: float, pt0_speed: float,
                      pt1_x: float, pt1_y: float, pt1_speed: float,
                      pos_x: float, pos_y: float) -> float:
    """Interpolate speed between two path points based on robot position.
    
    Uses projection of robot position onto the path segment to determine
    interpolation ratio, ensuring correct speed calculation even when robot
    is not exactly on the path.
    """
    dx = pt1_x - pt0_x
    dy = pt1_y - pt0_y
    segment_length_sq = dx * dx + dy * dy
    
    if segment_length_sq == 0:
        return pt0_speed

    # Calculate projection parameter t (0 = pt0, 1 = pt1)
    t = ((pos_x - pt0_x) * dx + (pos_y - pt0_y) * dy) / segment_length_sq
    
    # Clamp t to [0, 1] to stay within segment bounds
    t = max(0.0, min(1.0, t))

    return pt0_speed + t * (pt1_speed - pt0_speed)




def differential_drive_kinematics(linear_vel: float, angular_vel: float,
                                  track_width: float) -> Tuple[float, float]:
    """Calculate left and right track velocities from linear/angular.
    
    Standard differential drive kinematics:
    - Positive angular_vel -> turn left (right track faster)
    - Negative angular_vel -> turn right (left track faster)
    """
    half_width = track_width / 2.0
    # Standard kinematics formulas
    left_vel = linear_vel - angular_vel * half_width
    right_vel = linear_vel + angular_vel * half_width
    return left_vel, right_vel


def apply_speed_limits(left_vel: float, right_vel: float,
                       max_speed: float, min_speed: float = 0.0) -> Tuple[float, float]:
    """Apply speed limits while maintaining velocity ratio."""
    max_abs_vel = max(abs(left_vel), abs(right_vel))
    if max_abs_vel > max_speed:
        scale = max_speed / max_abs_vel
        left_vel *= scale
        right_vel *= scale
    
    # Apply minimum speed threshold
    if abs(left_vel) < min_speed and abs(left_vel) > 0:
        left_vel = math.copysign(min_speed, left_vel)
    if abs(right_vel) < min_speed and abs(right_vel) > 0:
        right_vel = math.copysign(min_speed, right_vel)
    
    return left_vel, right_vel


def calculate_cross_track_error(robot_x: float, robot_y: float,
                               current_point_x: float, current_point_y: float,
                               next_point_x: float, next_point_y: float) -> float:
    """Calculate cross-track error (perpendicular distance from robot to path segment).
    
    Args:
        robot_x, robot_y: Robot position
        current_point_x, current_point_y: Current path point
        next_point_x, next_point_y: Next path point
        
    Returns:
        Signed distance to path segment (positive = right of path, negative = left)
    """
    # Check for degenerate case
    dx = next_point_x - current_point_x
    dy = next_point_y - current_point_y
    
    if abs(dx) < 1e-6 and abs(dy) < 1e-6:
        # Points are the same, return distance to point
        return math.sqrt((robot_x - current_point_x)**2 + (robot_y - current_point_y)**2)
    
    # Use signed distance to infinite line (more appropriate for cross-track error)
    return signed_distance_from_point_to_line(robot_x, robot_y,
                                             current_point_x, current_point_y,
                                             next_point_x, next_point_y)


class DualRMSCalculator:
    """Combined RMS calculator: total trajectory + recent performance.
    
    Calculates two types of RMS errors:
    1. Total RMS - exact RMS over entire trajectory (cumulative)
    2. Recent RMS - exponentially smoothed RMS over recent time window (~5-10 seconds)
    
    The recent RMS responds faster to quality changes while total RMS provides
    overall trajectory accuracy assessment.
    """
    
    def __init__(self, recent_time_constant: float = 7.0, update_rate: float = 50.0):
        """Initialize dual RMS calculator.
        
        Args:
            recent_time_constant: Time constant for recent RMS (seconds).
                                Time for recent RMS to reach ~63% of new steady-state value.
                                Smaller = faster response, larger = more smoothing.
            update_rate: Expected update rate (Hz) for proper alpha calculation.
        """
        self.recent_time_constant = recent_time_constant
        
        # Calculate alpha for exponential smoothing
        # α = 1 - exp(-dt/τ) where dt = 1/update_rate, τ = time_constant
        dt = 1.0 / update_rate
        self.alpha = 1.0 - math.exp(-dt / recent_time_constant)
        
        self.reset()
    
    def reset(self):
        """Reset calculator for new trajectory."""
        # Total trajectory RMS (exact cumulative calculation)
        self.total_sum_lateral_sq = 0.0
        self.total_sum_yaw_sq = 0.0
        self.total_count = 0
        
        # Recent RMS (exponential smoothing)
        self.recent_rms_lateral_sq = 0.0
        self.recent_rms_yaw_sq = 0.0
        self.recent_initialized = False
        
        # Statistics
        self.max_lateral_error = 0.0
        self.max_yaw_error = 0.0
    
    def add_sample(self, lateral_error: float, yaw_error: float):
        """Add new sample to both RMS calculations.
        
        Args:
            lateral_error: Lateral deviation from path (m)
            yaw_error: Heading error (rad)
        """
        lat_sq = lateral_error ** 2
        yaw_sq = yaw_error ** 2
        
        # Update total RMS (exact calculation)
        self.total_sum_lateral_sq += lat_sq
        self.total_sum_yaw_sq += yaw_sq
        self.total_count += 1
        
        # Update recent RMS (exponential smoothing)
        if not self.recent_initialized:
            self.recent_rms_lateral_sq = lat_sq
            self.recent_rms_yaw_sq = yaw_sq
            self.recent_initialized = True
        else:
            # Exponential moving average: new_avg = (1-α)*old_avg + α*new_value
            self.recent_rms_lateral_sq = (1 - self.alpha) * self.recent_rms_lateral_sq + self.alpha * lat_sq
            self.recent_rms_yaw_sq = (1 - self.alpha) * self.recent_rms_yaw_sq + self.alpha * yaw_sq
        
        # Update statistics
        abs_lateral = abs(lateral_error)
        abs_yaw = abs(yaw_error)
        if abs_lateral > self.max_lateral_error:
            self.max_lateral_error = abs_lateral
        if abs_yaw > self.max_yaw_error:
            self.max_yaw_error = abs_yaw
    
    def get_total_rms_lateral(self) -> float:
        """Get total trajectory RMS lateral error (m)."""
        if self.total_count == 0:
            return 0.0
        return math.sqrt(self.total_sum_lateral_sq / self.total_count)
    
    def get_total_rms_yaw(self) -> float:
        """Get total trajectory RMS yaw error (rad)."""
        if self.total_count == 0:
            return 0.0
        return math.sqrt(self.total_sum_yaw_sq / self.total_count)
        
    def get_recent_rms_lateral(self) -> float:
        """Get recent RMS lateral error (m)."""
        if not self.recent_initialized:
            return 0.0
        return math.sqrt(self.recent_rms_lateral_sq)
        
    def get_recent_rms_yaw(self) -> float:
        """Get recent RMS yaw error (rad)."""
        if not self.recent_initialized:
            return 0.0
        return math.sqrt(self.recent_rms_yaw_sq)
    
    def get_sample_count(self) -> int:
        """Get total number of samples processed."""
        return self.total_count
    
    def get_max_lateral_error(self) -> float:
        """Get maximum lateral error encountered (m)."""
        return self.max_lateral_error
    
    def get_max_yaw_error(self) -> float:
        """Get maximum yaw error encountered (rad)."""
        return self.max_yaw_error
    
    def get_statistics_summary(self) -> dict:
        """Get comprehensive statistics summary."""
        return {
            'total_rms_lateral': self.get_total_rms_lateral(),
            'total_rms_yaw': self.get_total_rms_yaw(),
            'recent_rms_lateral': self.get_recent_rms_lateral(),
            'recent_rms_yaw': self.get_recent_rms_yaw(),
            'max_lateral_error': self.max_lateral_error,
            'max_yaw_error': self.max_yaw_error,
            'sample_count': self.total_count,
            'time_constant': self.recent_time_constant
        }
