"""Approach control (precise positioning) algorithms.

Separated from path following controller. Pure algorithmic logic, no ROS.
"""

from __future__ import annotations

import math
import time
from typing import Any, Optional, Tuple

from base_node.pid import PID
from .math_utils import differential_drive_kinematics, apply_speed_limits


class ApproachController:
    """Precise drill positioning controller."""

    def __init__(self, *, node=None):
        self.node = node

        # Parameters (set via load_parameters)
        self.max_approach_speed: float = 0.078
        self.max_approach_angular_error: float = math.radians(9.0)
        self.goal_achievement_radius: float = 0.06
        self.approach_max_turn_rate: float = 0.015
        self.max_allowed_distance_to_target: float = 0.15
        self.vehicle_width: float = 2.0

        # PID params
        self._pid_p: float = 0.035
        self._pid_i: float = 0.0
        self._pid_d: float = 0.0
        self._pid_i_sat: float = 0.01

        # State
        self._pid: Optional[PID] = None
        self._active: bool = False

    def set_node(self, node) -> None:
        self.node = node

    def load_parameters(self, node_params: dict, vehicle_params: dict) -> None:
        # Approach-specific params
        self.max_approach_speed = node_params['max_approach_speed']
        self.max_approach_angular_error = math.radians(node_params['max_approach_angular_error_deg'])
        self.goal_achievement_radius = node_params['goal_achievement_radius']
        self.approach_max_turn_rate = node_params['approach_max_turn_rate']
        self.max_allowed_distance_to_target = node_params['max_allowed_distance_to_target']
        self.vehicle_width = vehicle_params['geometry']['platform_width']

        # PID params
        pid_params = node_params['approach_pid']
        self._pid_p = pid_params['p']
        self._pid_i = pid_params['i']
        self._pid_d = pid_params['d']
        self._pid_i_sat = pid_params['i_saturation']

        # Initialize PID
        self._pid = PID(
            get_time=lambda: self.node.get_time() if self.node else time.time(),
            node=self.node,
            p=self._pid_p,
            i=self._pid_i,
            d=self._pid_d,
            ff=0.0,
            i_saturation=self._pid_i_sat,
            out_min=-self.approach_max_turn_rate,
            out_max=self.approach_max_turn_rate,
            d_term_tc=0.1,
            out_tc=0.05,
            force_zero=False,
            name="approach_pid",
        )
        if self.node:
            self.node.log("[DEBUG] Approach PID initialized", level=self.node.DEBUG)

    def reset_pid(self) -> None:
        if self._pid is not None:
            self._pid.reset()
        if self.node:
            self.node.log("Approach PID state reset", level=self.node.DEBUG)

    def calculate_control(self, drill_position: Any, hole_position: Any) -> Tuple[float, float, bool]:
        """Compute differential wheel speeds for precise positioning.

        Returns (left_speed, right_speed, goal_reached).
        """
        dx = hole_position.x - drill_position.x
        dy = hole_position.y - drill_position.y
        distance = math.sqrt(dx * dx + dy * dy)

        # Goal check
        if distance <= self.goal_achievement_radius:
            if self.node:
                self.node.log(
                    f"APPROACH_COMPLETE: precision={distance*100:.1f}cm",
                    level=self.node.INFO,
                    period=1.0,
                )
            self._active = False
            return 0.0, 0.0, True

        # Distance limit
        if distance > self.max_allowed_distance_to_target:
            if self.node:
                self.node.log(
                    f"APPROACH_ABORT: distance {distance:.2f}m > limit {self.max_allowed_distance_to_target:.2f}m",
                    level=self.node.WARN,
                    period=2.0,
                )
            return 0.0, 0.0, False

        # Target orientation
        target_yaw = math.atan2(dy, dx)
        yaw_error = ((drill_position.yaw - target_yaw + math.pi) % (2 * math.pi)) - math.pi
        abs_yaw_error = abs(yaw_error)

        # Speed schedule based on angular error
        if self.max_approach_angular_error > 0:
            speed_scale = max(0.2, 1.0 - abs_yaw_error / self.max_approach_angular_error)
        else:
            speed_scale = 1.0
        target_speed = self.max_approach_speed * speed_scale

        # Backward logic if target is behind
        if abs_yaw_error > math.pi / 2:
            yaw_error = (math.pi - abs_yaw_error) * (1 if yaw_error >= 0 else -1)
            abs_yaw_error = abs(yaw_error)
            if self.max_approach_angular_error > 0:
                speed_scale = max(0.2, 1.0 - abs_yaw_error / self.max_approach_angular_error)
            else:
                speed_scale = 1.0
            target_speed = -self.max_approach_speed * speed_scale

        rotation_speed = self._pid.update(0.0, yaw_error)
        rotation_speed = max(-self.approach_max_turn_rate, min(self.approach_max_turn_rate, rotation_speed))

        # Differential speeds via common kinematics and unified limiting
        left_speed, right_speed = differential_drive_kinematics(
            target_speed, rotation_speed, self.vehicle_width
        )
        left_speed, right_speed = apply_speed_limits(
            left_speed, right_speed, self.max_approach_speed, 0.0
        )

        # Ensure minimal forward motion when turning with large errors
        if abs_yaw_error > math.radians(30) and target_speed > 0:
            min_forward = self.max_approach_speed * 0.1
            if left_speed < min_forward:
                left_speed = min_forward
            if right_speed < min_forward:
                right_speed = min_forward

        if self.node:
            self.node.log(
                f"APPROACH_DEBUG: dir={'BACK' if target_speed < 0 else 'FWD'} "
                f"dist={distance:.2f}m yaw_err={math.degrees(yaw_error):.0f}° \n"
                f"target_yaw={math.degrees(target_yaw):.1f}° drill_yaw={math.degrees(drill_position.yaw):.1f}° "
                f"target_speed={target_speed:.3f} rotation_speed={rotation_speed:.3f} "
                f"ctrl=({left_speed:.3f},{right_speed:.3f}) ",
                level=self.node.DEBUG,
                period=0.5,
            )

        return left_speed, right_speed, False



