# Copyright 2025 Zyfra Robotics
# Copyright 2025 <PERSON> <<EMAIL>>
#
# This software is proprietary and confidential.
# Unauthorized copying of this file is strictly prohibited.
"""Path following control algorithms."""

import math
from typing import Any, NamedTuple, Optional, Tuple

from .math_utils import (
    DualRMSCalculator,
    apply_speed_limits,
    differential_drive_kinematics,
    interpolate_speed,
    interpolate_xy,
    interpolate_angle,
    norm_angle_minusPI_plusPI,
    point_to_segment_distance,
    signed_distance_from_point_to_line
)

from .sensor_offset_estimator import SensorOffsetEstimator


class ControlResult(NamedTuple):
    """Result of path following control calculation."""

    left_speed: float
    right_speed: float
    is_blocked: bool
    block_reason: str


class PathController:
    """Path following control algorithms for differential drive robot."""

    # Algorithm constants (not user-configurable parameters)
    _ROTATION_SPEED_FACTOR = 0.6                      # Max rotation wheel speed as factor of max_speed
    _MAX_PURE_ROTATION_WHEEL_SPEED = 0.8              # Cap for wheel linear speed during pure rotation (m/s)
    _SHARP_CORNER_MIN_ADVANCEMENT = 0.3               # Min advancement distance for sharp corners (m)
    _SHARP_CORNER_LOOKAHEAD_FACTOR = 0.6              # Lookahead factor for sharp corners (>60°)
    _MODERATE_CORNER_MIN_ADVANCEMENT = 0.4            # Min advancement distance for moderate corners (m)
    _MODERATE_CORNER_LOOKAHEAD_FACTOR = 0.9           # Lookahead factor for moderate corners (>30°)
    _STRAIGHT_SEGMENT_YAW_THRESHOLD = math.radians(3.0) # Yaw difference threshold for straight segments (rad)
    _STRAIGHT_SEGMENT_DISTANCE_TOLERANCE = 0.02       # Distance tolerance for straight segment promotion (m)
    _MIN_FORWARD_PROGRESS_M = 0.05                    # Minimal forward progress along current segment (m)
    _ADV_DISTANCE_BASE_CAP_M = 0.5                    # Base cap for advancement distance (m)
    _ADV_DISTANCE_LOOKAHEAD_FACTOR = 1.5              # Factor for advancement cap vs lookahead distance

    def __init__(self):
        """Initialize path controller with default parameters."""

        # Offset estimator (can be disabled for comparison)
        self._offset_estimator = SensorOffsetEstimator()

        # Path following control parameters (loaded via load_parameters)
        self.lookahead_distance = 2.0                         # Distance to look ahead on path (m)
        self.lateral_correction_gain = 0.5                    # Proportional gain for lateral error correction
        self.lateral_correction_limit = 0.3                   # Maximum lateral correction angle (rad)
        self.heading_error_gain = 1.0                         # Proportional gain for heading error control
        self.path_curvature_feedforward_gain = 0.3            # Feedforward gain for path curvature compensation

        # Turn rate limiting parameters
        self.turn_rate_base_limit = 2.0                       # Base limit for turn rate (rad/s)
        self.turn_rate_speed_factor = 0.5                     # Speed-dependent turn rate factor (rad/s per m/s)
        self.max_heading_error_for_full_speed = math.pi / 3   # Max heading error for full speed operation (rad)

        # Approach control moved to ApproachController

        # Safety parameters
        self.max_allowed_distance_to_target = 0.15
        self.max_heading_error_for_motion = math.pi / 2       # Maximum heading error for motion (rad, 90°)
        
        # Vehicle parameters (loaded via load_parameters)
        self.vehicle_width = 2.0                              # Vehicle width (m)
        self.max_speed = 0.6                                  # Maximum speed (m/s)
        self.min_speed = 0.01                                 # Minimum speed (m/s)

        # Internal state
        self.node = None                                      # Set by set_node()
        self.rms_calculator = None                            # Set by set_rms_calculator()

        # Approach controller is separate; no PID here

    def set_node(self, node):
        """Set parent node for logging and constants."""
        self.node = node
        # Propagate node to estimator
        self._offset_estimator.set_node(node)

    def _log(self, message: str, level=None, period: float = 1.0):
        """Log message using parent node."""
        if not self.node:
            return
        if level is None:
            level = self.node.DEBUG
        self.node.log(message, level=level, period=period)

    def load_parameters(self, node_params: dict, vehicle_params: dict):
        """Load control parameters from parameter server."""
        # Path following control parameters
        self.lookahead_distance = node_params['lookahead_distance']
        self.lateral_correction_gain = node_params['lateral_correction_gain']
        self.lateral_correction_limit = node_params['lateral_correction_limit']
        self.heading_error_gain = node_params['heading_error_gain']
        self.path_curvature_feedforward_gain = node_params['path_curvature_feedforward_gain']

        # Turn rate limiting parameters
        self.turn_rate_base_limit = node_params['turn_rate_base_limit']
        self.turn_rate_speed_factor = node_params['turn_rate_speed_factor']
        self.max_heading_error_for_full_speed = node_params['max_heading_error_for_full_speed']

        # Approach parameters are handled in ApproachController
        self.max_allowed_distance_to_target = node_params['max_allowed_distance_to_target']

        # Safety parameters
        self.max_heading_error_for_motion = node_params['max_heading_error_for_motion']

        # Vehicle parameters
        self.vehicle_width = vehicle_params['geometry']['platform_width']
        self.max_speed = vehicle_params['max_speed']
        self.min_speed = vehicle_params['min_speed']

        # No PID init here (moved to ApproachController)

        # Initialize RMS tracking
        rms_time_constant = node_params['rms_recent_time_constant']
        self.rms_calculator = DualRMSCalculator(
            recent_time_constant=rms_time_constant
        )

        # Load sensor offset parameters into estimator
        self._offset_estimator.load_parameters(node_params)

    def set_sensor_offset_mode(self, enabled: bool):
        """Enable/disable sensor offset correction for testing purposes.

        Args:
            enabled: True for NEW mode (kinematic center), False for OLD mode (sensor position)
        """
        self._offset_estimator.set_enabled(enabled)

    def fast_forward_to_current_position(self, path_segments: list, current_pos: Any) -> Tuple[int, int]:
        """Skip completed path segments and points to robot's current position.

        Returns:
            Tuple of (segment_id, point_id) representing current position on path
        """
        if not path_segments or current_pos is None:
            return 0, 0

        # Always work in kinematic center frame
        kx, ky = self._offset_estimator.apply_to_position(current_pos)

        min_global_dist = float('inf')
        best_segment_id = 0
        best_point_id = 0

        # Find globally closest point across all segments
        for seg_idx, segment in enumerate(path_segments):
            if len(segment.points) < 2:
                continue

            for i in range(len(segment.points) - 1):
                dist = point_to_segment_distance(
                    kx, ky,
                    segment.points[i].x, segment.points[i].y,
                    segment.points[i + 1].x, segment.points[i + 1].y
                )
                if dist < min_global_dist:
                    min_global_dist = dist
                    best_segment_id = seg_idx
                    best_point_id = i

        # Refine point advancement along best segment
        if best_segment_id < len(path_segments):
            segment = path_segments[best_segment_id]

            if best_point_id + 1 < len(segment.points):
                pt0 = segment.points[best_point_id]
                pt1 = segment.points[best_point_id + 1]

                # Project robot position onto path segment
                path_vec_x = pt1.x - pt0.x
                path_vec_y = pt1.y - pt0.y
                robot_vec_x = kx - pt0.x
                robot_vec_y = ky - pt0.y

                path_length_sq = path_vec_x**2 + path_vec_y**2
                if path_length_sq > 0:
                    projection_ratio = (robot_vec_x * path_vec_x + robot_vec_y * path_vec_y) / path_length_sq

                    # Conservative advancement
                    if projection_ratio > 0.7:
                        best_point_id = min(best_point_id + 1, len(segment.points) - 2)
                    elif projection_ratio < 0.1 and best_point_id > 0 and min_global_dist > 0.2:
                        best_point_id = max(0, best_point_id - 1)

        return best_segment_id, best_point_id

    # find_lookahead_point removed in favor of compute_lookahead

    def choose_next_point_id_by_distance(self, segment: Any, current_pos: Any, current_point_id: int,
                                         max_skip: int = 25, hysteresis: float = 0.10) -> int:
        """Advance to the next path point by scanning forward while segment distance decreases.

        Uses distance from robot to path segments [i, i+1] instead of discrete points, which
        is more robust on curves and corners. Stops on first increase (with hysteresis),
        caps advancement by cumulative path length and by max_skip to avoid overjumping.
        """
        points = segment.points
        if current_point_id >= len(points) - 1:
            return current_point_id

        # Use KINEMATIC center for advancement to match control frame and true robot center.
        kin_x, kin_y = self._offset_estimator.apply_to_position(current_pos)

        # Start evaluating from current segment [cur, cur+1]
        start_i = max(0, min(current_point_id, len(points) - 2))
        best_i = start_i
        best_d = point_to_segment_distance(
            kin_x, kin_y,
            points[start_i].x, points[start_i].y,
            points[start_i + 1].x, points[start_i + 1].y
        )

        # Ensure minimal forward progress along the current segment to avoid oscillations
        # around the same point due to tiny distance fluctuations.
        min_forward_progress = self._MIN_FORWARD_PROGRESS_M
        seg0_len = self._segment_len(points[start_i], points[start_i + 1])
        if seg0_len > 1e-9:
            vx = points[start_i + 1].x - points[start_i].x
            vy = points[start_i + 1].y - points[start_i].y
            rx = kin_x - points[start_i].x
            ry = kin_y - points[start_i].y
            proj = (rx * vx + ry * vy) / seg0_len  # signed distance along segment
            if proj >= min_forward_progress:
                cand_i = min(start_i + 1, len(points) - 2)
                cand_d = point_to_segment_distance(
                    kin_x, kin_y,
                    points[cand_i].x, points[cand_i].y,
                    points[cand_i + 1].x, points[cand_i + 1].y
                )
                # Promote if it does not significantly worsen the distance (keep small hysteresis)
                if cand_d <= best_d + hysteresis:
                    best_i = cand_i
                    best_d = cand_d

        # Advancement limits (curvature-aware)
        max_adv_distance = max(self._ADV_DISTANCE_BASE_CAP_M,
                               self.lookahead_distance * self._ADV_DISTANCE_LOOKAHEAD_FACTOR)
        # If an upcoming corner is sharp, reduce how far we can advance to avoid over-jumping the hinge
        allow_straight_promote = True
        yaw_cur = math.atan2(points[start_i + 1].y - points[start_i].y,
                             points[start_i + 1].x - points[start_i].x)
        if start_i + 2 < len(points):
            yaw_next = math.atan2(points[start_i + 2].y - points[start_i + 1].y,
                                  points[start_i + 2].x - points[start_i + 1].x)
            corner = abs(norm_angle_minusPI_plusPI(yaw_next - yaw_cur))
            if corner > math.radians(60):  # very sharp
                max_adv_distance = max(self._SHARP_CORNER_MIN_ADVANCEMENT, self.lookahead_distance * self._SHARP_CORNER_LOOKAHEAD_FACTOR)
                allow_straight_promote = False
            elif corner > math.radians(30):  # moderate
                max_adv_distance = max(self._MODERATE_CORNER_MIN_ADVANCEMENT, self.lookahead_distance * self._MODERATE_CORNER_LOOKAHEAD_FACTOR)
                allow_straight_promote = False
        acc_path_dist = 0.0
        steps = 0

        # Debug prints removed in production; use node logs where necessary

        # Reference yaw for straightness check
        yaw_ref = math.atan2(points[start_i + 1].y - points[start_i].y,
                              points[start_i + 1].x - points[start_i].x)

        i = start_i + 1
        while i < len(points) - 1 and steps < max_skip:
            seg_len = self._segment_len(points[i], points[i + 1])
            if acc_path_dist + seg_len > max_adv_distance:
                break
            acc_path_dist += seg_len

            d = point_to_segment_distance(
                kin_x, kin_y,
                points[i].x, points[i].y,
                points[i + 1].x, points[i + 1].y
            )

            # Primary condition: distance improves with hysteresis
            if d + hysteresis <= best_d:
                best_d = d
                best_i = i
                i += 1
                steps += 1
            else:
                # Secondary condition: allow progress on near-straight path when distance is nearly the same
                yaw_i = math.atan2(points[i + 1].y - points[i].y, points[i + 1].x - points[i].x)
                yaw_diff = abs(norm_angle_minusPI_plusPI(yaw_i - yaw_ref))
                if allow_straight_promote and yaw_diff < self._STRAIGHT_SEGMENT_YAW_THRESHOLD and d <= best_d + self._STRAIGHT_SEGMENT_DISTANCE_TOLERANCE:
                    best_d = d
                    best_i = i
                    i += 1
                    steps += 1
                else:
                    break

        # Detailed advancement debug removed to reduce noise

        # Special handling for last segment: allow promotion up to the last point index
        # even if distance starts increasing, bounded by advancement limits.
        if start_i >= len(points) - 2 or best_i >= len(points) - 2:
            j = max(start_i, best_i)
            acc2 = 0.0
            steps2 = 0
            while j < len(points) - 1 and steps2 < max_skip:
                seg_len = self._segment_len(points[j], points[j + 1])
                if acc2 + seg_len > max_adv_distance:
                    break
                acc2 += seg_len
                j += 1
                steps2 += 1
            # Last-segment promotion debug removed
            return min(j, len(points) - 2)

        return best_i

    def should_complete_segment(self, segment: Any, position: Any, current_point_id: int) -> bool:
        """Decide if the current path segment is complete.

        Rules:
        - If index is already at or beyond the last point, segment is complete.
        - If we are on the last segment [last_idx-1, last_idx], consider completion when
          we are either close enough to the final point or have passed it along the
          segment direction (projection >= 1.0).
        """
        points = segment.points
        if len(points) < 2:
            return True
        last_idx = len(points) - 1
        if current_point_id >= last_idx:
            return True

        # Last segment completion heuristic
        if current_point_id == last_idx - 1:
            p0 = points[last_idx - 1]
            p1 = points[last_idx]

            # Use kinematic center for consistency
            px, py = self._offset_estimator.apply_to_position(position)

            rx = px - p0.x
            ry = py - p0.y
            sx = p1.x - p0.x
            sy = p1.y - p0.y
            seg_len_sq = sx * sx + sy * sy
            proj = (rx * sx + ry * sy) / seg_len_sq if seg_len_sq > 1e-9 else 0.0
            dist_to_final = math.hypot(px - p1.x, py - p1.y)

            last_point_completion_threshold = 0.20  # meters
            if dist_to_final <= last_point_completion_threshold or proj >= 1.0:
                return True

        return False

    def compute_forced_advance_index(
        self,
        segment: Any,
        position: Any,
        current_point_id: int,
        last_point_advance_time: float,
        now: float,
    ) -> Optional[int]:
        """Compute an optional forced advancement index to prevent oscillations near a point.

        Returns next index (typically +1) when we've been stuck longer than a threshold
        and are near the next point; otherwise returns None.
        """
        points = segment.points
        if current_point_id >= len(points) - 1:
            return None

        time_since_advance = now - last_point_advance_time
        next_point = points[current_point_id + 1]

        px, py = self._offset_estimator.apply_to_position(position)

        dist_to_next = math.hypot(px - next_point.x, py - next_point.y)

        forward_progress_threshold = 2.0  # seconds
        near_next_threshold = 0.20        # meters

        if time_since_advance > forward_progress_threshold and dist_to_next < near_next_threshold:
            return min(current_point_id + 1, len(points) - 2)

        return None

    def _update_sensor_offset_from_speed(self, speed_state) -> bool:
        """Compatibility wrapper for tests; delegates to estimator."""
        return self._offset_estimator.update_from_speed(speed_state)

    def _calculate_kinematic_center_position(self, sensor_position) -> Tuple[float, float]:
        """Compatibility wrapper; delegates to estimator."""
        return self._offset_estimator.apply_to_position(sensor_position)

    def _calculate_offset_statistics(self) -> Tuple[float, float]:
        """Deprecated; kept for compatibility where used for logs only."""
        info = self._offset_estimator.get_quality_info()
        # Reconstruct stats approximately
        return info['offset'], info['stability'] ** 2

    def _log_offset_quality(self, raw_offset: float, offset_change: float, angular_velocity: float):
        # Logging handled in estimator now; keep no-op for compatibility
        return


    def calculate_path_following_control(
        self,
        segment: Any,
        position: Any,
        current_point_id: int,
        speed_state=None
    ) -> ControlResult:
        """Calculate differential drive speeds using hybrid lookahead and lateral correction.

        Control Components:
        1. Lateral Correction = lateral_error × correction_p
           Corrects robot deviation from path centerline

        2. Proportional Control = yaw_error × turn_rate_p
           Steers proportionally to heading error

        3. Feedforward Control = path_curvature × turn_rate_ff × computed_target_speed
           Anticipates turns based on upcoming path curvature
           path_curvature = lookahead_heading_change / lookahead_distance

        Algorithm:
        1. Find current path segment between points and lookahead point at specified distance
        2. Calculate path curvature = (lookahead_heading - current_segment_heading) / lookahead_distance
        3. Correct current segment heading toward path centerline:
           corrected_heading = segment_heading + lateral_error × correction_p
        4. Calculate yaw error = corrected_heading - robot_heading
        5. Compute target speed with kinematic constraints:
           - Kinematic limit: ensures no wheel exceeds max_speed considering turn rates
           - Path speed: interpolated from current segment points
           - Yaw error limit: speed reduction for large heading errors
        6. Calculate turn rate components:
           - Proportional: e = yaw_error × turn_rate_p
           - Feedforward: D = path_curvature × turn_rate_ff
           - Total: turn_rate = e + D × target_speed
        7. Apply turn rate limits and convert to differential wheel speeds:
           - Limit turn rate based on speed-dependent thresholds
           - Calculate: left = target_speed - turn_rate × width/2, right = target_speed + turn_rate × width/2
           - Enforce minimum speed while preserving steering direction

        Returns:
            ControlResult with left/right speeds and blocking status
        """
        # Input validation
        if len(segment.points) < 2:
            reason = "segment has < 2 points"
            if self.node:
                self.node.handle_error(
                    f"PATH_BLOCKED: {reason}",
                    level=self.node.ERROR,
                    event_code=self.node.events.SW_ERROR,
                    require_remote=True,
                    period=2.0
                )
            return ControlResult(0.0, 0.0, True, reason)

        if current_point_id > len(segment.points) - 2:
            reason = f"point_id ({current_point_id}) > max_valid_index ({len(segment.points) - 2})"
            if self.node:
                self.node.handle_error(
                    f"PATH_BLOCKED: {reason}",
                    level=self.node.ERROR,
                    event_code=self.node.events.SW_ERROR,
                    require_remote=True,
                    period=2.0
                )
            return ControlResult(0.0, 0.0, True, reason)

        # Update sensor offset and apply to input position at controller ingress
        offset_updated = self._offset_estimator.update_from_speed(speed_state)
        kinematic_x, kinematic_y = self._offset_estimator.apply_to_position(position)

        # Current segment endpoints
        cpt0 = segment.points[current_point_id]
        cpt1 = segment.points[current_point_id + 1]

        # Safety check: verify robot position (use min distance to current/next segments)
        distance_curr = point_to_segment_distance(
            kinematic_x, kinematic_y, cpt0.x, cpt0.y, cpt1.x, cpt1.y)
        distance_next = float('inf')
        if current_point_id + 2 < len(segment.points):
            n0 = segment.points[current_point_id + 1]
            n1 = segment.points[current_point_id + 2]
            distance_next = point_to_segment_distance(
                kinematic_x, kinematic_y, n0.x, n0.y, n1.x, n1.y)
        distance_to_path = min(distance_curr, distance_next)

        if distance_to_path > self.max_allowed_distance_to_target:
            reason = f"distance_to_path={distance_to_path:.2f}m > limit={self.max_allowed_distance_to_target:.2f}m"
            if self.node:
                self.node.handle_error(
                    f"DIST_BLOCKED: {reason}",
                    level=self.node.ERROR,
                    event_code=self.node.events.SW_ERROR,
                    require_remote=True,
                    period=1.0
                )
            return ControlResult(0.0, 0.0, True, reason)

        # Lookahead info in single pass
        lai = self.compute_lookahead(segment, current_point_id)
        lookahead_point = lai.point
        lookahead_distance = lai.distance
        current_segment_yaw = lai.yaw_current
        lookahead_segment_yaw = lai.yaw_at_lookahead

        # Path curvature (heading delta per lookahead distance)
        dyaw = norm_angle_minusPI_plusPI(lookahead_segment_yaw - current_segment_yaw)

        # Lateral error correction (use kinematic center position for accurate control)
        lateral_error = signed_distance_from_point_to_line(
            kinematic_x, kinematic_y, cpt0.x, cpt0.y, cpt1.x, cpt1.y)

        correction_angle = lateral_error * self.lateral_correction_gain
        if abs(correction_angle) > self.lateral_correction_limit:
            correction_angle = self.lateral_correction_limit * (1 if correction_angle >= 0 else -1)

        # Calculate corrected heading and yaw error
        corrected_yaw = norm_angle_minusPI_plusPI(current_segment_yaw + correction_angle)

        # Calculate yaw error; when moving backward align by using robot_yaw + π
        robot_yaw = position.yaw + (math.pi if segment.is_backward else 0.0)
        yaw_error = norm_angle_minusPI_plusPI(corrected_yaw - robot_yaw)
        abs_yaw_error = abs(yaw_error)

        # Add sample to RMS tracking
        if self.rms_calculator:
            self.rms_calculator.add_sample(lateral_error, yaw_error)

        # Pure rotation for large heading errors (same threshold for fwd/back)
        rotation_threshold = self.max_heading_error_for_motion
        if abs_yaw_error > rotation_threshold:
            self._log(f"LARGE_YAW_ERR: {math.degrees(abs_yaw_error):.0f}° - pure rotation",
                     level=self.node.WARN if self.node else None, period=2.0)

            turn_rate = yaw_error * self.heading_error_gain
            left_speed, right_speed = differential_drive_kinematics(
                0.0, turn_rate, self.vehicle_width)

            max_rot_speed = min(self.max_speed * self._ROTATION_SPEED_FACTOR, self._MAX_PURE_ROTATION_WHEEL_SPEED)
            left_speed, right_speed = apply_speed_limits(
                left_speed, right_speed, max_rot_speed, self.min_speed)

            return ControlResult(left_speed, right_speed, False, "")

        # Target speed interpolation
        target_speed = interpolate_speed(
            cpt0.x, cpt0.y, cpt0.speed,
            cpt1.x, cpt1.y, cpt1.speed,
            kinematic_x, kinematic_y
        )

        # Proportional steering control component
        yaw_err_term = yaw_error * self.heading_error_gain

        # Feedforward steering control component (curvature coefficient)
        if lookahead_distance > 0.1:
            curv_ff_term = self.path_curvature_feedforward_gain * dyaw / lookahead_distance
        else:
            curv_ff_term = 0.0

        # Speed limiting for kinematic constraints
        half_width = self.vehicle_width / 2
        max_speed_for_kinematics = max(0, (self.max_speed - abs(yaw_err_term) * half_width) / (1 + half_width * abs(curv_ff_term)))

        # Speed limiting for heading errors
        if self.max_heading_error_for_full_speed <= 0:
            yaw_error_speed_limit = float('inf')
        else:
            yaw_error_speed_limit = max(0, self.max_speed - abs_yaw_error * self.max_speed / self.max_heading_error_for_full_speed)

        # Final speed selection
        final_speed = min(max_speed_for_kinematics, target_speed, yaw_error_speed_limit)

        # No special near-end handling in simulation (no inertia)
        near_final_segment = (current_point_id >= len(segment.points) - 2)
        distance_to_end = None

        # Turn rate limiting
        turn_rate_limit = self.turn_rate_base_limit + final_speed * self.turn_rate_speed_factor

        if abs(yaw_err_term) > turn_rate_limit:
            yaw_err_term = turn_rate_limit * (1 if yaw_err_term >= 0 else -1)

        # Combine steering components
        total_turn_rate = yaw_err_term + curv_ff_term * final_speed

        # Backward movement handling - only invert linear speed, steering as usual
        if segment.is_backward:
            final_speed = -abs(final_speed)

        # Convert to differential wheel speeds and enforce limits
        left_speed, right_speed = differential_drive_kinematics(final_speed, total_turn_rate, self.vehicle_width)
        # Disable min-speed enforcement globally to allow precise low-speed control and exact stop
        left_speed, right_speed = apply_speed_limits(left_speed, right_speed, self.max_speed, 0.0)

        # Diagnostic logging
        direction = "REV" if segment.is_backward else "FWD"
        rms_info = self.get_rms_summary_for_logging() if self.rms_calculator else ""
        saturation = (
            abs(yaw_err_term) >= turn_rate_limit or
            abs(left_speed) >= self.max_speed or
            abs(right_speed) >= self.max_speed
        )
        # Sensor offset info for debugging with quality indicators
        qi = self._offset_estimator.get_quality_info()
        ci = "[REL]" if qi.get('is_reliable', False) else "[NR]"
        offset_info = (f"sensor_offset={qi['offset']:.3f}m "
                       f"{'[UPD]' if offset_updated else '[OLD]'}{ci}")
        
        self._log(
            f"TRAJ_CTRL: pt={current_point_id} "
            f"dir={direction} "
            f"{' [SAT]' if saturation else ''} \n"
            f"lat_err={lateral_error:.2f} yaw_err={math.degrees(yaw_error):.1f}° \n" 
            f"curv={math.degrees(dyaw):.1f}°/{lookahead_distance:.2f}m \n"
            f"P={yaw_err_term:.3f} FF={curv_ff_term:.3f} \n"
            f"turn={total_turn_rate:.3f} "
            f"tgt_spd={target_speed:.2f} fin_spd={final_speed:.2f} \n"
            f"out=({left_speed:.2f},{right_speed:.2f}) \n"
            f"lim_kin={max_speed_for_kinematics:.2f} lim_yaw={yaw_error_speed_limit:.2f} turn_lim={turn_rate_limit:.2f} \n"
            f"{offset_info} {rms_info} ",
            level=self.node.DEBUG if self.node else None, period=0.5
        )
        return ControlResult(left_speed, right_speed, False, "")

    class LookaheadInfo(NamedTuple):
        point: Optional[Any]
        distance: float
        yaw_current: float
        yaw_at_lookahead: float

    def compute_lookahead(self, segment: Any, current_point_id: int) -> LookaheadInfo:
        """Compute lookahead point, actual distance and headings in one pass."""
        points = segment.points
        n = len(points)
        if n < 2:
            # Fallback: degenerate
            return self.LookaheadInfo(points[-1] if points else None, 0.0, 0.0, 0.0)

        i0 = max(0, min(current_point_id, n - 2))
        p0 = points[i0]
        p1 = points[i0 + 1]
        
        # Angle interpolation now uses shared math_utils.interpolate_angle

        # Use geometric segment direction for current heading to match traversal
        seg_yaw_geom = math.atan2(p1.y - p0.y, p1.x - p0.x)
        yaw_curr = seg_yaw_geom

        target_ld = self.lookahead_distance
        acc = 0.0
        i = i0
        while i < n - 1 and acc + self._segment_len(points[i], points[i + 1]) < target_ld:
            acc += self._segment_len(points[i], points[i + 1])
            i += 1

        # Interpolate inside segment i for remaining distance
        remaining = max(0.0, target_ld - acc)
        seg_len = self._segment_len(points[i], points[i + 1]) if i < n - 1 else 0.0
        if seg_len > 1e-9 and i < n - 1:
            ratio = min(1.0, remaining / seg_len)
            base = points[i]
            nxt = points[i + 1]
            look = type(base)()
            look.x, look.y = interpolate_xy(base.x, base.y, nxt.x, nxt.y, ratio)
            # Interpolate speed using shared utility for consistency
            look.speed = interpolate_speed(
                base.x, base.y, base.speed,
                nxt.x, nxt.y, nxt.speed,
                look.x, look.y,
            )
            # Orientation at lookahead: geometric direction within current segment
            yaw_look = math.atan2(nxt.y - base.y, nxt.x - base.x)
            actual_ld = acc + ratio * seg_len
        else:
            # Not enough path length: use last point
            look = points[-1]
            # Yaw at lookahead: use yaw of the last point
            yaw_look = points[-1].yaw
            # Actual distance is total accumulated length from i0 to end
            # Approximate as acc (already summed) since remaining segment too short
            actual_ld = acc

        return self.LookaheadInfo(look, actual_ld, yaw_curr, yaw_look)

    @staticmethod
    def _segment_len(a: Any, b: Any) -> float:
        return math.hypot(b.x - a.x, b.y - a.y)

    # Approach control removed: use ApproachController.calculate_control

    

    def reset_rms_tracking(self):
        """Reset RMS tracking for new action/trajectory."""
        if self.rms_calculator:
            self.rms_calculator.reset()


    def get_current_sensor_offset(self) -> float:
        """Get current estimated sensor offset in meters."""
        return self._offset_estimator.get_offset()

    def get_offset_quality_info(self) -> dict:
        """Expose estimator quality info (offset, confidence, stability, etc.)."""
        return self._offset_estimator.get_quality_info()

    def get_rms_summary_for_logging(self) -> str:
        """Get compact RMS summary for logging."""
        if not self.rms_calculator:
            return "rms=N/A"

        stats = self.rms_calculator.get_statistics_summary()
        return (f"rms_tot={stats['total_rms_lateral']*100:.1f}cm/"
               f"{math.degrees(stats['total_rms_yaw']):.0f}° "
               f"rms_rec={stats['recent_rms_lateral']*100:.1f}cm/"
               f"{math.degrees(stats['recent_rms_yaw']):.0f}°")

    # Approach PID/reset removed (handled by ApproachController)

    # Backward-compatibility helpers for tests that directly access private fields
    @property
    def _sensor_offset(self):  # type: ignore
        return self._offset_estimator.get_offset()

    @_sensor_offset.setter
    def _sensor_offset(self, value):  # type: ignore
        self._offset_estimator._offset = float(value)

    @property
    def _offset_update_count(self):  # type: ignore
        return getattr(self._offset_estimator, '_update_count', 0)

    @_offset_update_count.setter
    def _offset_update_count(self, value):  # type: ignore
        self._offset_estimator._update_count = int(value)


