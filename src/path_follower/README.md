# Path Follower - Контроллер траекторного управления

## Обзор

Path Follower - это ROS 2 нода для автономного управления движением бурового робота по заданной траектории. Система реализует гибридный алгоритм траекторного управления с lookahead point tracking, коррекцией боковой ошибки и feedforward управлением для точного следования по пути.

## Архитектура

Система построена на базе **BaseFSM** (конечный автомат) и включает три состояния:

- **`idle`** - ожидание задания на движение
- **`running`** - следование по траектории с использованием lookahead алгоритма
- **`approach`** - точное позиционирование бура над целевой скважиной

## Входы и выходы

### Входные топики

| Топик | Тип сообщения | Timeout | Описание |
|-------|---------------|---------|----------|
| `/drive_action` | `drill_msgs/DriveAction` | 300.0s | Задание на движение с сегментами пути |
| `/position` | `drill_msgs/Position` | 0.5s | Положение центра робота |
| `/drill_position` | `drill_msgs/Position` | 0.5s | Положение бура (используется в approach) |
| `/robomode` | `drill_msgs/BoolStamped` | 1.0s | Режим работы робота |
| `/permission` | `drill_msgs/Permission` | 1.0s | Разрешения на движение |
| `/main_state_machine_status` | `drill_msgs/StateMachineStatus` | 2.0s | Статус главного автомата |

### Выходные топики

| Топик | Тип сообщения | Описание |
|-------|---------------|----------|
| `/tracks_target_speed` | `drill_msgs/TracksCtrl` | Целевые скорости левой и правой гусеницы (м/с) |
| `/driver_status` | `drill_msgs/DriveStatus` | Статус выполнения задания (см. "Логика завершения заданий") |

### Сервисы

| Сервис | Тип | Описание |
|--------|-----|----------|
| `get_current_drive_action` | `drill_msgs/GetCurrentDriveAction` | Получение текущего задания |

## Структура DriveAction

```
DriveAction:
  int32 id                              # ID задания (-2 = отмена)
  drill_msgs/Path[] path_segments       # Массив сегментов пути
  drill_msgs/Point2d hole_position      # Позиция целевой скважины

Path:
  PathPoint[] points                    # Точки сегмента
  bool is_backward                      # Флаг движения задним ходом

PathPoint:
  float32 x, y                          # Координаты точки (м)
  float32 yaw                           # Курс в точке (рад)
  float32 speed                         # Скорость в точке (м/с)
```

## Алгоритм траекторного управления

### Гибридный подход

Контроллер использует **комбинацию методов**:

1. **Lookahead Point Tracking** - поиск точки на расстоянии `lookahead_distance` впереди
2. **Lateral Error Correction** - коррекция отклонения от линии пути
3. **Feedforward Control** - предвосхищение поворотов на основе кривизны

### Математические формулы

**1. Боковая ошибка:**
```
lateral_error = signed_distance_from_point_to_line(robot_pos, path_segment)
correction_angle = lateral_error * lateral_correction_gain
correction_angle = clamp(correction_angle, ±lateral_correction_limit)
```

**2. Целевой курс с коррекцией:**
```
corrected_yaw = normalize(segment_yaw + correction_angle)
if segment.is_backward:
    corrected_yaw += π
```

**3. Угловая ошибка и управление:**
```
yaw_error = normalize(corrected_yaw - robot_yaw)
proportional_turn_rate = yaw_error * heading_error_gain

# Feedforward компонент
path_curvature = (lookahead_yaw - current_segment_yaw) / lookahead_distance
feedforward_turn_rate = path_curvature_feedforward_gain * path_curvature * final_speed

total_turn_rate = proportional_turn_rate + feedforward_turn_rate
```

**4. Дифференциальная кинематика:**
```
left_speed = target_speed - total_turn_rate * vehicle_width / 2
right_speed = target_speed + total_turn_rate * vehicle_width / 2
```

### Ограничение вращения на месте

При больших ошибках курса контроллер выполняет «чистое вращение»: линейная скорость = 0, поворот за счёт дифференциала треков. Для безопасности ограничиваем линейную скорость каждого трака при таком вращении константой `MAX_PURE_ROTATION_WHEEL_SPEED` (м/с).

### Обработка движения задним ходом

Для сегментов с `is_backward = True`:
- Ориентация пути разворачивается на π (курс траектории обращён по направлению движения)
- Линейная скорость инвертируется: `final_speed = -abs(final_speed)`
- Знак feedforward-инкремента меняется за счёт использования отрицательной `final_speed`

Примечание: курс сегмента берётся из `PathPoint.yaw` и интерполируется по кратчайшей дуге между соседними точками.

## Режим точного наведения (Approach)

### Активация

Режим approach активируется при:
- Завершении всех сегментов пути (`current_segment_id >= len(path_segments)`)
- Наличии целевой позиции: `sqrt(hole_position.x² + hole_position.y²) > 0.01`

### Алгоритм позиционирования

**ВАЖНО:** Используется позиция **бура**, а не центра робота:
```python
drill_pos = robot_pos + tower2center * [cos(robot_yaw), sin(robot_yaw)]
```

**1. Расчет целевого направления:**
```
target_yaw = atan2(hole_y - drill_y, hole_x - drill_x)
yaw_error = normalize(drill_yaw - target_yaw)  # drill_yaw в радианах
```

**2. Управление скоростью:**
```
if |yaw_error| > max_approach_angular_error:
    speed_factor = max_approach_angular_error / |yaw_error|
    target_speed = max_approach_speed * speed_factor
else:
    target_speed = max_approach_speed
```

**3. Движение назад для больших углов:**
```
if |yaw_error| > π/2:
    yaw_error = (π - |yaw_error|) * sign(yaw_error)
    target_speed = -max_approach_speed * speed_factor
```

**4. PID управление поворотом:**
```
rotation_speed = approach_pid_p * yaw_error + 
                approach_pid_i * ∫(yaw_error * dt) + 
                approach_pid_d * d(yaw_error)/dt
rotation_speed = clamp(rotation_speed, ±approach_max_turn_rate)
```

### Критерии завершения

- **Успех:** `distance ≤ goal_achievement_radius` (по умолчанию 0.06м)
- **Отмена:** `distance > max_allowed_distance_to_target` (по умолчанию 0.15м)

## Параметры конфигурации

### Path Following (Основной алгоритм)

Актуальные значения параметров см. в `nodes.yaml` (секция `path_follower`). Ниже — описание полей:

| Параметр | Описание |
|----------|----------|
| `lookahead_distance` | Расстояние точки lookahead (м) |
| `lateral_correction_gain` | Коэффициент коррекции боковой ошибки |
| `lateral_correction_limit` | Максимальный угол коррекции (рад) |
| `heading_error_gain` | Пропорциональный коэффициент по курсу |
| `path_curvature_feedforward_gain` | Коэффициент feedforward по кривизне |

### Turn Rate Limiting (Ограничения поворота)

| Параметр | Описание |
|----------|----------|
| `turn_rate_base_limit` | Базовое ограничение угловой скорости (рад/с) |
| `turn_rate_speed_factor` | Скоростной фактор ограничения ((рад/с)/(м/с)) |
| `max_heading_error_for_full_speed` | Макс. ошибка курса для полной скорости (рад) |
| `max_heading_error_for_motion` | Макс. ошибка курса для движения (рад) |

### Approach Mode (Режим наведения)

| Параметр | Описание |
|----------|----------|
| `max_approach_speed` | Максимальная скорость в approach (м/с) |
| `max_approach_angular_error_deg` | Угловая ошибка для снижения скорости (°) |
| `goal_achievement_radius` | Радиус зоны достижения цели (м) |
| `approach_max_turn_rate` | Максимальная угловая скорость (рад/с) |
| `max_allowed_distance_to_target` | Максимальное расстояние до цели (м) |

### Approach PID

Актуальные значения PID см. в `nodes.yaml`. Описание полей:

| Параметр | Описание |
|----------|----------|
| `approach_pid.p` | Пропорциональный коэффициент |
| `approach_pid.i` | Интегральный коэффициент |
| `approach_pid.d` | Дифференциальный коэффициент |
| `approach_pid.i_saturation` | Ограничение интегральной составляющей |

### Vehicle Parameters (Параметры машины)

| Параметр | Описание |
|----------|----------|
| `vehicle.geometry.platform_width` | Ширина между гусеницами (м) |
| `vehicle.max_speed` | Максимальная скорость (м/с) |
| `vehicle.min_speed` | Минимальная скорость (м/с) |
| `vehicle.geometry.tower2center` | Смещение бура от центра робота (м) |

### Timing Parameters (Временные параметры)

| Параметр | Описание |
|----------|----------|
| `message_timeout` | Время жизни сообщений (с) |
| `output_smoothing_time_constant` | Константа сглаживания выхода (с) |
| `rate` | Частота работы ноды (Гц) |
| `rms_recent_time_constant` | Константа времени для RMS метрик (с) |

## Условия работы

### Обязательные условия для движения

1. **Режим робота**: `robomode.value == True`
2. **Разрешение**: `permission.permission == True`  
3. **Состояние системы**: `main_status.current_state` ∈ [`"MOVING"`, `"moving"`]
4. **Надежность позиции**: `position.is_reliable == True`
5. **Наличие задания**: `drive_action` не равен `None`

### Условия остановки

1. **Нарушение условий движения** (см. выше)
2. **Отмена задания**: получение `drive_action.id == -2`
3. **Устаревшие данные**: превышение `timeout` для критических топиков
4. **Ошибки безопасности**: через `safety_check()`
5. **Достижение цели** в режиме approach

## Диагностика и мониторинг

### Производительность (каждые 10 секунд)

```
STATUS: state=running t=15.2s a1[2/3] pt=5 pos_age=0.03s 
pos=(2.5,0.1,θ=45°) ctrl=(0.45,0.50) tgt=(3.0,0.0) dist=0.51m 
xtrk=0.10m spd=0.48/0.5 FWD rms_tot=12.3cm/5° rms_rec=8.1cm/3°
```

**Расшифровка:**
- `state` - текущее состояние FSM
- `t` - время в состоянии (с) 
- `a1[2/3]` - action ID 1, сегмент 2 из 3
- `pt` - текущая точка в сегменте
- `pos_age` - возраст данных позиции (с)
- `pos` - текущая позиция робота (x, y, θ)
- `ctrl` - команды управления (левая, правая)
- `tgt` - целевая точка
- `dist` - расстояние до цели (м)
- `xtrk` - cross-track error (м)
- `spd` - текущая/целевая скорость
- `FWD/REV` - направление движения
- `rms_tot/rec` - общий/недавний RMS ошибки

### Событие прохождения точек

```
PT_ADVANCE: 6/10 d=0.48m (5.0,0.0)→(6.0,0.0) FWD
SEG_ADVANCE: 0→1/2 (8pts, REV)
MISSION_COMPLETE: Action 123 finished
```

### Детальное управление (DEBUG уровень)

```
CTRL: lat_err=-0.25m yaw_err=-12° P=0.210 FF=0.045 
curv=5°/2.1m spd=0.65 out=(0.42,0.75) FWD
```

### Approach режим

```
APPROACH_POSITIONS: robot=(-5.3,-1.4,θ=146°) drill=(-7.2,1.1,θ=146°) 
hole=(-7.2,1.1) dist=0.055m tower2center=2.8m
APPROACH_COMPLETE: precision=5.5cm
```

## RMS Метрики точности

Система автоматически вычисляет метрики точности:

- **Total RMS** - точная RMS ошибка по всей траектории  
- **Recent RMS** - экспоненциально сглаженная RMS за последние ~7 секунд
- **Lateral Error** - боковое отклонение от пути (см)
- **Yaw Error** - ошибка курса (градусы)

## Безопасность

### Проверки безопасности

- **Надежность данных позиционирования** перед движением
- **Автоматическая остановка** при потере критических данных
- **Ограничение расстояния до пути** (`max_allowed_distance_to_target`)
- **Детекция застревания** (>30с без продвижения)
- **Плавное ограничение скоростей** с учетом кинематики

### Реакция на ошибки

- **Безопасная остановка** через `stop_control()`
- **Публикация событий** в топик `/events`
- **Переход в состояние failure** при критических ошибках
- **Логирование с периодическим ограничением** для предотвращения спама

## Алгоритм быстрой подгонки

При получении нового задания система **автоматически пропускает** пройденные участки:

1. **Глобальный поиск** ближайшего сегмента во всех `path_segments`
2. **Проекция позиции робота** на найденный сегмент  
3. **Консервативное продвижение** с учетом направления движения
4. **Обновление индексов** `current_segment_id` и `current_point_id`

```python
# Пример: робот на (5.2, 0.1), ближайший сегмент найден
# Проекция показывает 70% прохождения между точками 3→4
# Система устанавливает current_point_id = 4 (следующая цель)
```

## Сглаживание выходных команд

Применяется **линейное сглаживание** с постоянной времени:

```python
smoothing_factor = min(1.0, dt / output_smoothing_time_constant)
left_smoothed = left_prev + smoothing_factor * (left_target - left_prev)
right_smoothed = right_prev + smoothing_factor * (right_target - right_prev)
```

## Запуск и использование

### Сборка

```bash
# Активация ROS 2 окружения, например
source /Users/<USER>/ros2_jazzy/activate_ros

# Из корневой директории workspace
colcon build --packages-select path_follower
source install/setup.bash
```

### Запуск

```bash
# Запуск через launch файл
ros2 launch path_follower path_follower.xml

# Прямой запуск с параметрами
PARAM_SERVER_PORT=5005 ros2 run path_follower path_follower
```

### Отправка тестовых экшенов с использованием test_drive_action.py

```bash
# Запуск интерактивного тестера
python3 src/path_follower/test_drive_action.py

# Доступные команды:
# 1 - Простая прямая линия (7м)
# 2 - Комплексная траектория (прямая → поворот → прямая)  
# 3 - Движение задним ходом (5м)
# 4 - S-образная кривая
# 5 - Спираль с уменьшающимся радиусом
# 6 - Зигзаг с чередующимися поворотами
# 7 - Комплексный маневр с задним ходом
# 8 - Альтернативная траектория (прямая → разворот → назад)
# c - Отмена текущего задания
```

## Тестирование

### Автоматические тесты

```bash
# Математические функции (12 тестов)
python3 test/test_path_math.py

# Алгоритмы управления (8 тестов)
python3 test/test_path_algorithms.py

# Тесты точности траекторий
python3 test/test_accuracy.py

# Все основные тесты сразу
python3 test/test_path_math.py && python3 test/test_path_algorithms.py
```

### Визуализация траекторий (требует matplotlib)

```bash
# Установка зависимостей для визуализации
pip3 install matplotlib

# Запуск визуализации всех тестов
python3 test/test_visualizer.py

# Конкретная траектория  
python3 test/test_visualizer.py straight
python3 test/test_visualizer.py s_curve
python3 test/test_visualizer.py circle
```

### Результаты тестирования

**Path Following:** 100% успешность (RMS ошибки 0.0-3.7 см)
- Прямые участки: 0.0 см  
- Плавные кривые: 0.1 см
- Острые углы: 3.7 см

**Approach Control:** 17% успешность (требует настройки PID параметров)
- Успешные случаи: < 5 см точность
- Проблемные случаи: робот поворачивается на месте без продвижения

### Структура тестов

| Файл | Описание | Тесты |
|------|----------|-------|
| `test_path_math.py` | Математические функции (углы, расстояния, кинематика) | 12 ✅ |
| `test_path_algorithms.py` | Алгоритмы lookahead, path following, approach | 8 ✅ |
| `test_accuracy.py` | Точность траекторного управления и подхода | Интеграционные |
| `test_visualizer.py` | Визуализация траекторий с графиками ошибок (требует matplotlib) | Демонстрационные |
| `test_common.py` | Упрощенная тестовая инфраструктура | Библиотека |
| `test_mocks.py` | Mock объекты для тестирования | Утилиты |

## Мониторинг

```bash
# Статус выполнения
ros2 topic echo /driver_status

# Команды управления
ros2 topic echo /tracks_target_speed

# Диагностические события
ros2 topic echo /events

# Подробные логи (установить уровень DEBUG)
ros2 run path_follower path_follower --ros-args --log-level debug
```

### Unit тесты

```bash
# Математические функции
python3 -m pytest test/test_path_math.py -v

# Алгоритмы управления
python3 -m pytest test/test_path_algorithms.py -v

# Общие тесты
python3 -m pytest test/test_common.py -v

# Контроллер пути
python3 -m pytest test/test_path_controller.py -v

# Все тесты ROS 2
colcon test --packages-select path_follower
```

## Логика завершения заданий

### Унифицированная проверка завершения

Для всех контроллеров системы (path_follower, carousel_controller и т.д.) используется **единая логика проверки завершения заданий**:

```python
# Задание завершено, если:
action_completed = (cur_action_id == -1 and
                    last_action_id == expected_action_id)
```

**Поля DriveStatus:**
- `cur_action_id`: ID текущего выполняемого задания (-1 = нет активного задания)
- `last_action_id`: ID последнего завершённого задания

**Жизненный цикл задания:**
1. **Получение задания**: `cur_action_id = action_id`, `last_action_id` не меняется (пока 0 или старое значение)
2. **Выполнение**: `cur_action_id = action_id`, `last_action_id` не меняется
3. **Завершение**: `cur_action_id = -1`, `last_action_id = action_id` (устанавливается при переходе в idle)

**Преимущества:**
- Исключает ложные срабатывания при переходах в idle по другим причинам
- Надёжно работает для контроллеров без явного состояния idle
- Унифицирует логику во всей системе
- Упрощает диагностику и отладку

## Known Issues

### RTPS_READER_HISTORY Error

**Ошибка:** `[RTPS_READER_HISTORY Error] Change payload size of '24' bytes is larger than the history payload size of '11' bytes and cannot be resized. -> Function can_change_be_added_nts`

**Когда:** При вызове сервисов с большими данными (траектории), включая `get_current_drive_action`

**Решение:** Использовать cyclonedds: `export RMW_IMPLEMENTATION=rmw_cyclonedds_cpp`

**Причина:** По умолчанию FastDDS имеет ограниченный размер истории для сервисов, что, видимо, не позволяет передавать сообщения с большими траекториями.

## Интеграция с системой

### Координаты и углы

- **Система координат**: X - вперед, Y - влево
- **Углы**: радианы для внутренних расчетов, градусы в Position.msg
- **Скорости**: м/с для линейных, рад/с для угловых

### Взаимодействие с другими нодами

- **Планировщик** → `/drive_action` → **Path Follower**
- **Локализация** → `/position` → **Path Follower** 
- **State Tracker** → `/drill_position` → **Path Follower**
- **Path Follower** → `/tracks_target_speed` → **Tracks Regulator**
- **Main State Machine** ↔ **Path Follower** (статус и разрешения)

### Архитектурные принципы

- **Наследование от BaseFSM** для управления состояниями
- **Собственный сервер параметров** вместо ROS 2 параметров
- **Периодическое логирование** для предотвращения спама
- **Безопасная остановка** при любых ошибках
- **Модульная архитектура** с разделением PathController и PathFollowerNode

## Особенности реализации и соглашения

- В коде расчёта продвижения по точкам используются именованные константы вместо:
  - `MIN_FORWARD_PROGRESS_M` — минимальное продвижение вдоль текущего сегмента (по умолчанию 0.05 м)
  - `ADV_DISTANCE_BASE_CAP_M` — базовый потолок для продвижения (0.5 м)
  - `ADV_DISTANCE_LOOKAHEAD_FACTOR` — множитель от `lookahead_distance` (1.5)
- Для чистого вращения используется ограничение по линейной скорости гусениц, см. раздел «Ограничение вращения на месте».