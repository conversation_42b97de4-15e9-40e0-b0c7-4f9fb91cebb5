#!/usr/bin/env python3
"""
Физичное восстановление траектории центра и ориентации робота
по дискретной траектории сенсорной точки при фиксированном смещении сенсора
для гусеничной/дифф-базы без бокового проскальзывания.

Документация: SENSOR_CENTER_OPTIMIZER.md

Запуск (пример):
  python3 src/path_follower/sensor_center_optimizer.py --demo circle --rs 0.8,0.0 --viz

Вход:
  - Дискретная траектория сенсора S_i = (x_i, y_i), i=0..N-1 (CSV или демо).
  - Вектор смещения сенсора в корпусных осях r_s = (a_x, a_y).

Выход:
  - Дискретные траектории центра C_i, ориентации θ_i и время t_i,
    удовлетворяющие связи S_i = C_i + R(θ_i) r_s (в точках) и
    ограничениям дифф-базы (без боковой скорости у центра).
"""

from __future__ import annotations

import argparse
import math
from dataclasses import dataclass
from typing import Tuple, Optional, Dict

import numpy as np

try:
    import matplotlib.pyplot as plt  # type: ignore
    MATPLOTLIB_AVAILABLE = True
except Exception:
    MATPLOTLIB_AVAILABLE = False


# ------------------------------- Math helpers -------------------------------

def wrap_angle_continuous(theta: np.ndarray) -> np.ndarray:
    """Unwrap angles to keep continuity over the sequence."""
    return np.unwrap(theta)


def wrap_angle(x: float) -> float:
    """Wrap angle to [-pi, pi)."""
    y = (x + math.pi) % (2.0 * math.pi) - math.pi
    return y


def rotation(theta: float) -> np.ndarray:
    """2x2 rotation matrix R(theta)."""
    c = math.cos(theta)
    s = math.sin(theta)
    return np.array([[c, -s], [s, c]], dtype=float)


def rot90(v: np.ndarray) -> np.ndarray:
    """Rotate a 2D vector by +90 degrees (J*v)."""
    return np.array([-v[1], v[0]], dtype=float)


def path_length(points: np.ndarray) -> float:
    if len(points) < 2:
        return 0.0
    diffs = np.diff(points, axis=0)
    return float(np.sum(np.linalg.norm(diffs, axis=1)))


def densify_polyline(points: np.ndarray, max_step: float) -> np.ndarray:
    """Uniformly densify polyline so every segment length <= max_step."""
    if len(points) <= 1:
        return points.copy()
    result = [points[0].astype(float)]
    for i in range(len(points) - 1):
        p0 = points[i]
        p1 = points[i + 1]
        seg = p1 - p0
        L = float(np.linalg.norm(seg))
        if L <= 1e-12:
            continue
        steps = max(1, int(math.ceil(L / max(1e-6, max_step))))
        for k in range(1, steps + 1):
            alpha = k / steps
            result.append(p0 + alpha * seg)
    return np.stack(result, axis=0)


# ---------------------------- Data/model classes ----------------------------

@dataclass
class KinematicLimits:
    max_curvature: float = 0.4        # 1/m
    max_speed: float = 0.8            # m/s
    max_omega: float = 0.6            # rad/s
    min_dt: float = 0.02              # s


@dataclass
class OptimizationResult:
    sensor_xy: np.ndarray            # (N,2)
    center_xy: np.ndarray            # (N,2)
    theta: np.ndarray                # (N,)
    t: np.ndarray                    # (N,)
    stats: Dict[str, float]


# --------------------------- Demo/path IO utilities -------------------------

def demo_path(name: str, n_points: int = 180) -> np.ndarray:
    name = name.strip().lower()
    if name == "straight":
        xs = np.linspace(0.0, 10.0, n_points)
        ys = np.zeros_like(xs)
        return np.stack([xs, ys], axis=1)
    if name == "circle":
        r = 3.0
        thetas = np.linspace(0.0, 2.0 * math.pi, n_points)
        xs = r * np.cos(thetas) + r
        ys = r * np.sin(thetas)
        return np.stack([xs, ys], axis=1)
    if name == "s_curve":
        xs = np.linspace(0.0, 15.0, n_points)
        ys = 2.0 * np.sin(0.5 * xs)
        return np.stack([xs, ys], axis=1)
    if name == "l_turn":
        xs = []
        ys = []
        for i in range(n_points // 2):
            xs.append(10.0 * i / max(1, (n_points // 2) - 1))
            ys.append(0.0)
        for i in range(n_points - len(xs)):
            xs.append(10.0)
            ys.append(10.0 * (i + 1) / max(1, (n_points - len(xs))) )
        return np.stack([np.array(xs, float), np.array(ys, float)], axis=1)
    if name == "zigzag":
        xs = np.linspace(0.0, 12.0, n_points)
        ys = np.zeros_like(xs)
        for i in range(n_points):
            ys[i] = ((-1) ** (i // 6)) * (i % 6) * 0.3
        return np.stack([xs, ys], axis=1)
    raise ValueError(f"Unknown demo path: {name}")


def load_csv_xy(path: str) -> np.ndarray:
    data = np.loadtxt(path, delimiter=",", ndmin=2)
    if data.shape[1] < 2:
        raise ValueError("CSV must have at least two columns: x,y")
    return data[:, :2].astype(float)


# -------------------------- Core reconstruction logic ------------------------

def initial_theta_from_tangent(sensor_xy: np.ndarray) -> np.ndarray:
    if len(sensor_xy) < 2:
        return np.zeros((len(sensor_xy),), float)
    diffs = np.diff(sensor_xy, axis=0)
    tang = np.arctan2(diffs[:, 1], diffs[:, 0])
    theta = np.zeros((len(sensor_xy),), float)
    theta[:-1] = tang
    theta[-1] = tang[-1]
    return wrap_angle_continuous(theta)


def compute_center_from_theta(sensor_xy: np.ndarray, rs: np.ndarray, theta: np.ndarray) -> np.ndarray:
    center = np.zeros_like(sensor_xy)
    for i in range(len(theta)):
        center[i] = sensor_xy[i] - rotation(float(theta[i])) @ rs
    return center


def reconstruct_sequential(sensor_xy: np.ndarray,
                           rs: np.ndarray,
                           limits: KinematicLimits) -> Tuple[np.ndarray, np.ndarray]:
    """
    Последовательная реконструкция без бокового проскальзывания:
    для каждого сегмента решаем систему 2x2 на (Δs, δθ):
        dS = t_mid Δs + (R(θ_i+δθ)rs - R(θ_i)rs),
    где t_mid = [cos(θ_i + δθ/2), sin(...)]. Обеспечивает отсутствие боковой
    скорости у центра по определению, с жёсткой проекцией на ограничение кривизны.
    Возвращает (center_xy, theta).
    """
    n = len(sensor_xy)
    if n == 0:
        return sensor_xy.copy(), np.zeros((0,), float)
    # Инициализация θ_0 по касательной сенсорной траектории
    theta = initial_theta_from_tangent(sensor_xy)
    theta[0] = theta[0]
    # Центр из первого узла
    center_xy = np.zeros_like(sensor_xy)
    center_xy[0] = sensor_xy[0] - rotation(float(theta[0])) @ rs

    Jrs = rot90(rs)  # J*rs

    for i in range(n - 1):
        th_i = float(theta[i])
        dS = sensor_xy[i + 1] - sensor_xy[i]
        # Начальные приближения
        ds = float(dS[0] * math.cos(th_i) + dS[1] * math.sin(th_i))
        dth = 0.0

        # Corner-aware: если текущий продольный вектор плохо согласован с dS,
        # сначала пробуем чистую ротацию (ds=0), чтобы уменьшить несоответствие сенсора.
        t_i = np.array([math.cos(th_i), math.sin(th_i)])
        if np.linalg.norm(dS) > 1e-9:
            cos_align = float(np.clip((t_i @ (dS / np.linalg.norm(dS))), -1.0, 1.0))
        else:
            cos_align = 1.0
        if cos_align < 0.5:  # ~>60° несогласование
            for _ in range(20):
                # F_rot(δθ) = dS - (R(θ_i+δθ)rs - R(θ_i)rs)
                R_ip = rotation(th_i + dth)
                F_rot = dS - (R_ip @ rs - rotation(th_i) @ rs)
                J_rot = - (R_ip @ Jrs)  # ∂F/∂δθ
                denom = float(J_rot @ J_rot)
                if denom < 1e-12:
                    break
                step = float((J_rot @ F_rot) / denom)
                # Ограничение по вращению на месте
                max_step = limits.max_omega * limits.min_dt
                step = max(-max_step, min(max_step, step))
                dth += step
                if float(np.linalg.norm(F_rot)) < 1e-6 or abs(step) < 1e-6:
                    break

        # Ньютоновские итерации с бэктрекингом
        for _ in range(40):
            th_mid = th_i + 0.5 * dth
            t_mid = np.array([math.cos(th_mid), math.sin(th_mid)])
            R_ip = rotation(th_i + dth)
            # F = dS - t_mid*ds - (R(θ_i+δθ)rs - R(θ_i)rs)
            F = dS - t_mid * ds - (R_ip @ rs - rotation(th_i) @ rs)
            # Якобиан
            dtmid_ddth = 0.5 * np.array([-math.sin(th_mid), math.cos(th_mid)])
            dR_ddth_rs = R_ip @ Jrs
            J11 = -t_mid                  # ∂F/∂ds (vector)
            J12 = -dtmid_ddth * ds - dR_ddth_rs  # ∂F/∂δθ (vector)
            # Составим 2x2 систему по скалярным компонентам
            A = np.stack([np.array([J11[0], J12[0]]), np.array([J11[1], J12[1]])], axis=0)
            b = -F
            try:
                delta = np.linalg.lstsq(A, b, rcond=None)[0]
            except np.linalg.LinAlgError:
                break
            # Линейный поиск
            base_norm = float(np.linalg.norm(F))
            step_scale = 1.0
            for _bt in range(12):
                ds_try = ds + step_scale * float(delta[0])
                dth_try = dth + step_scale * float(delta[1])
                th_mid_try = th_i + 0.5 * dth_try
                t_mid_try = np.array([math.cos(th_mid_try), math.sin(th_mid_try)])
                R_try = rotation(th_i + dth_try)
                F_try = dS - t_mid_try * ds_try - (R_try @ rs - rotation(th_i) @ rs)
                if float(np.linalg.norm(F_try)) < base_norm * (1.0 - 1e-3):
                    ds = ds_try
                    dth = dth_try
                    F = F_try
                    break
                step_scale *= 0.5
            else:
                # если не улучшилось — минимальный шаг
                ds += 0.1 * float(delta[0])
                dth += 0.1 * float(delta[1])

            # Проверка сходимости по текущему F
            if float(np.linalg.norm(F)) < 1e-9:
                break

        # Проекция на ограничения: кривизна при движении и допускаемая чистая ротация
        if limits.max_curvature > 0.0:
            if abs(ds) > 1e-6:
                max_dth_move = limits.max_curvature * abs(ds)
                if abs(dth) > max_dth_move:
                    dth = math.copysign(max_dth_move, dth)
                    # Пересчёт ds при фиксированном dth, чтобы минимизировать невязку по t_mid
                    th_mid = th_i + 0.5 * dth
                    t_mid = np.array([math.cos(th_mid), math.sin(th_mid)])
                    R_ip = rotation(th_i + dth)
                    rhs = dS - (R_ip @ rs - rotation(th_i) @ rs)
                    ds = float(t_mid @ rhs)  # так как ||t_mid||=1
            else:
                # Разрешаем на месте вращение с ограничением по omega
                max_dth_rot = limits.max_omega * limits.min_dt
                if abs(dth) > max_dth_rot:
                    dth = math.copysign(max_dth_rot, dth)

        # Обновление состояния
        th_ip1 = th_i + dth
        th_mid = th_i + 0.5 * dth
        t_mid = np.array([math.cos(th_mid), math.sin(th_mid)])
        center_xy[i + 1] = center_xy[i] + t_mid * ds
        theta[i + 1] = th_ip1

    return center_xy, wrap_angle_continuous(theta)

def assign_time(center_xy: np.ndarray,
                theta: np.ndarray,
                limits: KinematicLimits) -> np.ndarray:
    n = len(center_xy)
    t = np.zeros((n,), float)
    for i in range(n - 1):
        ds = float(np.linalg.norm(center_xy[i + 1] - center_xy[i]))
        dth = float(theta[i + 1] - theta[i])
        dth = wrap_angle(dth)
        dt_v = ds / max(1e-6, limits.max_speed)
        dt_w = abs(dth) / max(1e-6, limits.max_omega)
        dt = max(limits.min_dt, dt_v, dt_w)
        t[i + 1] = t[i] + dt
    return t


def solve_center_trajectory(sensor_xy: np.ndarray,
                            rs: Tuple[float, float],
                            limits: Optional[KinematicLimits] = None) -> OptimizationResult:
    rs_vec = np.array(rs, float)
    limits = limits or KinematicLimits()
    # Densify sensor path to improve stability near corners
    sensor_xy = densify_polyline(sensor_xy, max_step=0.1)
    center_xy, theta = reconstruct_sequential(sensor_xy, rs_vec, limits)
    t = assign_time(center_xy, theta, limits)

    # Reconstructed sensor for diagnostics
    s_recon = np.zeros_like(center_xy)
    for i in range(len(theta)):
        s_recon[i] = center_xy[i] + rotation(float(theta[i])) @ rs_vec

    # Stats
    diffs_c = np.diff(center_xy, axis=0)
    ds = np.linalg.norm(diffs_c, axis=1) + 1e-9
    dtheta = np.diff(theta)
    dtheta = np.array([wrap_angle(float(x)) for x in dtheta])
    kappa = np.abs(dtheta) / ds
    max_kappa = float(np.max(kappa)) if len(kappa) else 0.0
    mean_kappa = float(np.mean(kappa)) if len(kappa) else 0.0
    total_len = path_length(center_xy)
    # Point-wise sensor error
    if len(s_recon) == len(sensor_xy):
        err = np.linalg.norm(sensor_xy - s_recon, axis=1)
        rms_err = float(np.sqrt(np.mean(err * err)))
        max_err = float(np.max(err))
    else:
        rms_err = float('nan')
        max_err = float('nan')

    stats = {
        "total_length_m": total_len,
        "max_curvature": max_kappa,
        "mean_curvature": mean_kappa,
        "N": float(len(sensor_xy)),
        "sensor_rms_error_m": rms_err,
        "sensor_max_error_m": max_err,
    }

    return OptimizationResult(
        sensor_xy=sensor_xy,
        center_xy=center_xy,
        theta=theta,
        t=t,
        stats=stats,
    )


# -------------------------------- Visualization -----------------------------

def visualize_result(res: OptimizationResult, rs: np.ndarray, title: str = "") -> None:
    if not MATPLOTLIB_AVAILABLE:
        print("Matplotlib is not available; skip visualization")
        return
    sensor_xy = res.sensor_xy
    center_xy = res.center_xy
    theta = res.theta

    plt.figure(figsize=(11, 9))
    plt.plot(sensor_xy[:, 0], sensor_xy[:, 1], "c--", lw=2.5, label="Sensor target S")
    plt.plot(center_xy[:, 0], center_xy[:, 1], "b-", lw=2.2, label="Center path C")
    # Reconstructed sensor path
    s_rec = np.zeros_like(center_xy)
    for i in range(len(theta)):
        s_rec[i] = center_xy[i] + rotation(float(theta[i])) @ rs
    plt.plot(s_rec[:, 0], s_rec[:, 1], "m:", lw=0.9, alpha=0.6, label="Sensor recon", zorder=1)
    plt.scatter(sensor_xy[0, 0], sensor_xy[0, 1], c="g", s=80, marker="o", label="Start S")
    plt.scatter(center_xy[0, 0], center_xy[0, 1], c="g", s=40, marker="x", label="Start C")
    plt.scatter(sensor_xy[-1, 0], sensor_xy[-1, 1], c="r", s=80, marker="o", label="End S")
    plt.scatter(center_xy[-1, 0], center_xy[-1, 1], c="r", s=40, marker="x", label="End C")

    # Draw small arrows showing body direction and sensor offset
    step = max(1, len(theta) // 20)
    for i in range(0, len(theta), step):
        c = center_xy[i]
        th = float(theta[i])
        s = c + rotation(th) @ rs
        # Body direction arrow
        d = 0.6
        dx = d * math.cos(th)
        dy = d * math.sin(th)
        plt.arrow(c[0], c[1], dx, dy, head_width=0.12, head_length=0.18, fc="orange", ec="orange", alpha=0.8)
        # Sensor offset arrow
        plt.annotate("", xy=(s[0], s[1]), xytext=(c[0], c[1]),
                     arrowprops=dict(arrowstyle="->", color="red", alpha=0.6, lw=1.5))

    plt.axis("equal")
    plt.grid(True, alpha=0.3)
    plt.legend()
    ttl = title or "Sensor-to-center optimization"
    ttl += f"\nLength={res.stats['total_length_m']:.2f}m, max κ={res.stats['max_curvature']:.3f} 1/m"
    if not (math.isnan(res.stats.get('sensor_rms_error_m', float('nan')))):
        ttl += f", S-RMS={res.stats['sensor_rms_error_m']*100:.1f}cm, S-Max={res.stats['sensor_max_error_m']*100:.1f}cm"
    plt.title(ttl)
    plt.xlabel("X (m)")
    plt.ylabel("Y (m)")
    plt.tight_layout()
    plt.show()


# ------------------------------ Animation utils -----------------------------

def save_animation(res: OptimizationResult,
                   rs: np.ndarray,
                   fps: int = 30,
                   robot_length: float = 3.0,
                   robot_width: float = 2.0,
                   name_hint: str = "trajectory") -> None:
    if not MATPLOTLIB_AVAILABLE:
        print("Matplotlib is not available; skip animation")
        return
    import os
    from matplotlib.animation import FFMpegWriter, PillowWriter, FuncAnimation

    sensor_xy = res.sensor_xy
    center_xy = res.center_xy
    theta = res.theta

    # Reconstruct sensor from solution
    s_rec = np.zeros_like(center_xy)
    for i in range(len(theta)):
        s_rec[i] = center_xy[i] + rotation(float(theta[i])) @ rs

    fig, ax = plt.subplots(1, 1, figsize=(10, 10))
    ax.plot(sensor_xy[:, 0], sensor_xy[:, 1], 'g--', lw=2, label='Target Sensor')
    ax.set_aspect('equal', adjustable='box')
    ax.grid(True, alpha=0.3)
    ax.set_xlabel('X (m)')
    ax.set_ylabel('Y (m)')
    ax.set_title('Reconstructed motion')

    sensor_path_line, = ax.plot([], [], 'm-', lw=1.2, alpha=0.8, label='Sensor recon')
    center_path_line, = ax.plot([], [], 'b-', lw=2, alpha=0.8, label='Center path')
    sensor_point, = ax.plot([], [], 'mo', ms=5)
    center_point, = ax.plot([], [], 'bo', ms=5)
    orientation_arrow = ax.annotate('', xy=(0, 0), xytext=(0, 0),
                                    arrowprops=dict(arrowstyle='->', color='red', lw=1.5))
    ax.legend(loc='best')

    # Limits with padding
    all_x = list(sensor_xy[:, 0]) + list(center_xy[:, 0])
    all_y = list(sensor_xy[:, 1]) + list(center_xy[:, 1])
    pad = max(robot_length, robot_width)
    ax.set_xlim(min(all_x) - pad, max(all_x) + pad)
    ax.set_ylim(min(all_y) - pad, max(all_y) + pad)

    # Robot body/track artists
    body_line, = ax.plot([], [], color='k', lw=1.5)
    left_track_line, = ax.plot([], [], color='0.3', lw=2)
    right_track_line, = ax.plot([], [], color='0.3', lw=2)

    def _body_polygon(cx_: float, cy_: float, yaw_: float):
        half_l = robot_length / 2.0
        half_w = robot_width / 2.0
        local = [
            (-half_l, +half_w),
            (-half_l, -half_w),
            (+half_l, -half_w),
            (0.0 + max(half_l, abs(rs[0])) * (1 if rs[0] >= 0 else -1), 0.0),
            (+half_l, +half_w),
            (-half_l, +half_w),
        ]
        cyaw = math.cos(yaw_)
        syaw = math.sin(yaw_)
        xs = [cx_ + px * cyaw - py * syaw for px, py in local]
        ys = [cy_ + px * syaw + py * cyaw for px, py in local]
        return xs, ys

    def _track_corners(cx_: float, cy_: float, yaw_: float, side: int):
        half_l = robot_length / 2.0
        half_w = robot_width / 2.0
        track_width = max(0.05, min(0.5, robot_width * 0.25))
        offset_y = half_w - track_width / 2.0
        y_center = side * offset_y
        local = [
            (+half_l, y_center + track_width / 2.0),
            (+half_l, y_center - track_width / 2.0),
            (-half_l, y_center - track_width / 2.0),
            (-half_l, y_center + track_width / 2.0),
            (+half_l, y_center + track_width / 2.0),
        ]
        cyaw = math.cos(yaw_)
        syaw = math.sin(yaw_)
        xs = [cx_ + px * cyaw - py * syaw for px, py in local]
        ys = [cy_ + px * syaw + py * cyaw for px, py in local]
        return xs, ys

    def init():
        sensor_path_line.set_data([], [])
        center_path_line.set_data([], [])
        sensor_point.set_data([], [])
        center_point.set_data([], [])
        body_line.set_data([], [])
        left_track_line.set_data([], [])
        right_track_line.set_data([], [])
        orientation_arrow.set_position((0, 0))
        orientation_arrow.xy = (0, 0)
        return (sensor_path_line, center_path_line, sensor_point, center_point,
                body_line, left_track_line, right_track_line, orientation_arrow)

    def update(frame: int):
        sensor_path_line.set_data(s_rec[:frame+1, 0], s_rec[:frame+1, 1])
        center_path_line.set_data(center_xy[:frame+1, 0], center_xy[:frame+1, 1])
        sensor_point.set_data([s_rec[frame, 0]], [s_rec[frame, 1]])
        center_point.set_data([center_xy[frame, 0]], [center_xy[frame, 1]])
        xs, ys = _body_polygon(center_xy[frame, 0], center_xy[frame, 1], theta[frame])
        body_line.set_data(xs, ys)
        ltx, lty = _track_corners(center_xy[frame, 0], center_xy[frame, 1], theta[frame], side=+1)
        rtx, rty = _track_corners(center_xy[frame, 0], center_xy[frame, 1], theta[frame], side=-1)
        left_track_line.set_data(ltx, lty)
        right_track_line.set_data(rtx, rty)
        orientation_arrow.set_position((center_xy[frame, 0], center_xy[frame, 1]))
        orientation_arrow.xy = (s_rec[frame, 0], s_rec[frame, 1])
        return (sensor_path_line, center_path_line, sensor_point, center_point,
                body_line, left_track_line, right_track_line, orientation_arrow)

    anim = FuncAnimation(fig, update, init_func=init, frames=len(center_xy), interval=1000/max(1, fps), blit=False)

    output_dir = os.path.join(os.path.dirname(__file__), 'trajectory_plots')
    os.makedirs(output_dir, exist_ok=True)
    base_name = f"recon_{name_hint}"
    mp4_path = os.path.join(output_dir, base_name + '.mp4')
    gif_path = os.path.join(output_dir, base_name + '.gif')

    saved = False
    try:
        writer = FFMpegWriter(fps=fps, bitrate=1800)
        anim.save(mp4_path, writer=writer, dpi=200)
        print(f"    🎬 Saved animation (mp4): {mp4_path}")
        saved = True
    except Exception as e:
        print(f"Warning: MP4 save failed ({e}). Falling back to GIF…")
    if not saved:
        try:
            anim.save(gif_path, writer=PillowWriter(fps=min(fps, 20)), dpi=200)
            print(f"    🎞️ Saved animation (gif): {gif_path}")
            saved = True
        except Exception as e:
            print(f"Error: Could not save animation as GIF either: {e}")
    plt.close()

# ----------------------------------- CLI -----------------------------------

def parse_args() -> argparse.Namespace:
    p = argparse.ArgumentParser(
        description=(
            "Восстановление траектории центра C и ориентации θ по дискретной траектории сенсора S "
            "с учётом жёсткого смещения r_s и ограничений кинематики."
        )
    )
    src = p.add_mutually_exclusive_group(required=True)
    src.add_argument("--demo", choices=["straight", "circle", "s_curve", "l_turn", "zigzag"], help="Демо-траектория сенсора")
    src.add_argument("--input", type=str, help="CSV-файл с колонками x,y")
    p.add_argument("--rs", type=str, default="0.8,0.0", help="Смещение сенсора в корпусных осях: ax,ay (м)")
    p.add_argument("--kappa-max", type=float, default=0.4, help="Ограничение кривизны центра (1/м)")
    p.add_argument("--v-max", type=float, default=0.8, help="Макс. линейная скорость (м/с)")
    p.add_argument("--omega-max", type=float, default=0.6, help="Макс. угловая скорость (рад/с)")
    p.add_argument("--viz", action="store_true", help="Показать визуализацию (matplotlib)")
    p.add_argument("--animate", action="store_true", help="Сохранить анимацию движения (mp4/gif)")
    p.add_argument("--fps", type=int, default=30, help="FPS анимации")
    p.add_argument("--robot-length", type=float, default=3.0, help="Длина корпуса (м)")
    p.add_argument("--robot-width", type=float, default=2.0, help="Ширина корпуса (м)")
    return p.parse_args()


def main() -> None:
    args = parse_args()
    if args.demo:
        sensor_xy = demo_path(args.demo)
    else:
        sensor_xy = load_csv_xy(args.input)

    rs_parts = [float(x) for x in args.rs.split(",")]
    if len(rs_parts) != 2:
        raise ValueError("--rs must be 'ax,ay'")
    rs = (rs_parts[0], rs_parts[1])

    limits = KinematicLimits(
        max_curvature=float(args.kappa_max),
        max_speed=float(args.v_max),
        max_omega=float(args.omega_max),
        min_dt=0.02,
    )
    res = solve_center_trajectory(sensor_xy, rs, limits)

    # Print brief stats
    print("Result stats:")
    for k, v in res.stats.items():
        print(f"  {k}: {v:.6f}")
    print(f"  total_time_s: {res.t[-1]:.3f}")

    if args.viz:
        visualize_result(res, np.array(rs, float), title=(args.demo or args.input or ""))
    if args.animate:
        name_hint = (args.demo or (args.input or "input").split("/")[-1].split(".")[0])
        try:
            save_animation(
                res,
                rs=np.array(rs, float),
                fps=int(args.fps),
                robot_length=float(args.robot_length),
                robot_width=float(args.robot_width),
                name_hint=name_hint,
            )
        except Exception as e:
            print(f"Animation error: {e}")


if __name__ == "__main__":
    main()

# ------------------- Sequential kinematic reconstruction --------------------

def reconstruct_sequential(sensor_xy: np.ndarray,
                           rs: np.ndarray,
                           limits: KinematicLimits) -> Tuple[np.ndarray, np.ndarray]:
    """
    Последовательная реконструкция без бокового проскальзывания:
    для каждого сегмента решаем систему 2x2 на (Δs, δθ):
        dS = t_mid Δs + (R(θ_i+δθ)rs - R(θ_i)rs),
    где t_mid = [cos(θ_i + δθ/2), sin(...)]. Обеспечивает отсутствие боковой
    скорости у центра по определению, с жёсткой проекцией на ограничение кривизны.
    Возвращает (center_xy, theta).
    """
    n = len(sensor_xy)
    if n == 0:
        return sensor_xy.copy(), np.zeros((0,), float)
    # Инициализация θ_0 по касательной сенсорной траектории
    theta = initial_theta_from_tangent(sensor_xy)
    theta[0] = theta[0]
    # Центр из первого узла
    center_xy = np.zeros_like(sensor_xy)
    center_xy[0] = sensor_xy[0] - rotation(float(theta[0])) @ rs

    Jrs = rot90(rs)  # J*rs

    for i in range(n - 1):
        th_i = float(theta[i])
        dS = sensor_xy[i + 1] - sensor_xy[i]
        # Начальные приближения
        ds = float(dS[0] * math.cos(th_i) + dS[1] * math.sin(th_i))
        dth = 0.0

        # Если направление сильно не совпадает с dS, повернёмся на месте перед движением
        t_i = np.array([math.cos(th_i), math.sin(th_i)])
        norm_dS = float(np.linalg.norm(dS))
        cos_align = float((t_i @ (dS / norm_dS))) if norm_dS > 1e-9 else 1.0
        if cos_align < 0.5:  # ~ более 60° рассогласование
            for _ in range(20):
                R_ip = rotation(th_i + dth)
                F_rot = dS - (R_ip @ rs - rotation(th_i) @ rs)
                J_rot = - (R_ip @ Jrs)
                denom = float(J_rot @ J_rot)
                if denom < 1e-12:
                    break
                step = float((J_rot @ F_rot) / denom)
                max_step = limits.max_omega * limits.min_dt
                step = max(-max_step, min(max_step, step))
                dth += step
                if float(np.linalg.norm(F_rot)) < 1e-6 or abs(step) < 1e-6:
                    break

        # Ньютоновские итерации с линейным поиском
        for _ in range(40):
            th_mid = th_i + 0.5 * dth
            t_mid = np.array([math.cos(th_mid), math.sin(th_mid)])
            R_ip = rotation(th_i + dth)
            # F = dS - t_mid*ds - (R(θ_i+δθ)rs - R(θ_i)rs)
            F = dS - t_mid * ds - (R_ip @ rs - rotation(th_i) @ rs)
            # Якобиан
            dtmid_ddth = 0.5 * np.array([-math.sin(th_mid), math.cos(th_mid)])
            dR_ddth_rs = R_ip @ Jrs
            J11 = -t_mid
            J12 = -dtmid_ddth * ds - dR_ddth_rs
            A = np.stack([np.array([J11[0], J12[0]]), np.array([J11[1], J12[1]])], axis=0)
            b = -F
            try:
                delta = np.linalg.lstsq(A, b, rcond=None)[0]
            except np.linalg.LinAlgError:
                break
            base_norm = float(np.linalg.norm(F))
            step_scale = 1.0
            improved = False
            for _bt in range(12):
                ds_try = ds + step_scale * float(delta[0])
                dth_try = dth + step_scale * float(delta[1])
                th_mid_try = th_i + 0.5 * dth_try
                t_mid_try = np.array([math.cos(th_mid_try), math.sin(th_mid_try)])
                R_try = rotation(th_i + dth_try)
                F_try = dS - t_mid_try * ds_try - (R_try @ rs - rotation(th_i) @ rs)
                if float(np.linalg.norm(F_try)) < base_norm * (1.0 - 1e-3):
                    ds = ds_try
                    dth = dth_try
                    improved = True
                    break
                step_scale *= 0.5
            if not improved:
                ds += 0.1 * float(delta[0])
                dth += 0.1 * float(delta[1])

            # Сходимость
            th_mid = th_i + 0.5 * dth
            t_mid = np.array([math.cos(th_mid), math.sin(th_mid)])
            R_ip = rotation(th_i + dth)
            F = dS - t_mid * ds - (R_ip @ rs - rotation(th_i) @ rs)
            if float(np.linalg.norm(F)) < 1e-9:
                break

        # Проекция на ограничения
        if limits.max_curvature > 0.0:
            if abs(ds) > 1e-6:
                max_dth_move = limits.max_curvature * abs(ds)
                if abs(dth) > max_dth_move:
                    dth = math.copysign(max_dth_move, dth)
            else:
                max_dth_rot = limits.max_omega * limits.min_dt
                if abs(dth) > max_dth_rot:
                    dth = math.copysign(max_dth_rot, dth)

        # Пересчёт ds при фиксированном dth для минимизации невязки на t_mid
        th_mid = th_i + 0.5 * dth
        t_mid = np.array([math.cos(th_mid), math.sin(th_mid)])
        R_ip = rotation(th_i + dth)
        rhs = dS - (R_ip @ rs - rotation(th_i) @ rs)
        ds = float(t_mid @ rhs)  # ||t_mid||=1
        # Безопасное ограничение величины шага
        ds = float(max(-norm_dS - 2.0 * np.linalg.norm(rs), min(norm_dS + 2.0 * np.linalg.norm(rs), ds)))

        # Обновление состояния
        th_ip1 = th_i + dth
        center_xy[i + 1] = center_xy[i] + t_mid * ds
        theta[i + 1] = th_ip1

    return center_xy, wrap_angle_continuous(theta)


