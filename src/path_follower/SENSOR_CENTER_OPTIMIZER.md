# Восстановление траектории центра и ориентации по траектории сенсора

Краткая документация к скрипту `src/path_follower/sensor_center_optimizer.py`.
Цель — простое и понятное описание без «заумных» формул.

## Постановка
- Дано: дискретная траектория сенсора $S_i = (x_i, y_i)$, фиксированное смещение сенсора в корпусных осях $r_s = (a_x, a_y)$.
- Ищем: траекторию кинематического центра шасси $C_i$, ориентации корпуса $\theta_i$ и время $t_i$, чтобы выполнялась геометрия

$$
S_i = C_i + R(\theta_i)\, r_s
$$

где `R(θ)` — матрица поворота в 2D, переводящая вектор смещения `r_s` из корпусных осей в глобальные. Кинематика — дифф/гусеничная база без бокового проскальзывания (кинематический центр движется вдоль продольной оси).

## Обозначения
- $dS_i = S_{i+1} - S_i$ — приращение сенсора между узлами.
- $\Delta c_i$ — продольное перемещение кинематического центра шасси (вдоль продольной оси корпуса) за шаг $i$ — не сенсора.
- $\delta\theta_i$ — приращение курса за шаг $i$.
- $t_{\mathrm{mid}}(\theta) = [\cos\theta,\ \sin\theta]^\top$ — единичный продольный вектор.

## Геометрия одного шага (без бокового скольжения)
За малый шаг:
- центр: $C_{i+1} = C_i + t_{\mathrm{mid}}\, \Delta c_i$, где $t_{\mathrm{mid}} = t_{\mathrm{mid}}(\theta_i + \tfrac{\delta\theta_i}{2})$;
- смещение сенсора: $R(\theta_i + \delta\theta_i)\, r_s − R(\theta_i)\, r_s$.

Итого аппроксимация приращения сенсора:

$$
dS_i \approx t_{\mathrm{mid}}\, \Delta c_i + \big( R(\theta_i + \delta\theta_i)\, r_s − R(\theta_i)\, r_s \big)
$$

Это позволяет для каждого $i$ решать небольшую нелинейную систему на $\Delta c_i$ и $\delta\theta_i$ (Ньютон + линейный поиск), затем обновлять $C_{i+1}$, $\theta_{i+1}$.

### Как решается шаг (Ньютон + линейный поиск)
- Неизвестные на шаге: $x = [\Delta c_i,\ \delta\theta_i]^\top$.
- Невязка сенсора:

$$
F(x) 
= dS_i - t_{\mathrm{mid}}(\theta_i + \tfrac{\delta\theta_i}{2})\, \Delta c_i 
- \Big( R(\theta_i + \delta\theta_i)\, r_s - R(\theta_i)\, r_s \Big)
$$

- Линеаризуем $F$ по $x$ и решаем 2×2 систему Ньютона: $A\,\Delta x = -F$.
  - Якобианы по компонентам (в проективной, «плоской» форме):
    - $\dfrac{\partial F}{\partial (\Delta c)} = -\, t_{\mathrm{mid}}$;
    - $\dfrac{\partial F}{\partial (\delta\theta)} \approx -\, \dfrac{\partial t_{\mathrm{mid}}}{\partial \theta}\, \dfrac{\Delta c}{2}\; -\; R(\theta_i + \delta\theta)\, J\, r_s$, где $J = \begin{bmatrix}0 & -1\\ 1 & 0\end{bmatrix}$.
  - Собираем $A = [\partial F/\partial (\Delta c)\;\; \partial F/\partial (\delta\theta)]$ построчно, решаем через МНК.
- Делаем линейный поиск по шагу $\alpha \in (0,1]$: принимаем $x \leftarrow x + \alpha\,\Delta x$, если $\lVert F(x)\rVert$ убывает (иначе уменьшаем $\alpha$).
- Проецируем результат на ограничения: при движении $|\delta\theta_i| \le \kappa_{\max}\,|\Delta c_i|$, при вращении на месте $|\delta\theta_i| \le \omega_{\max}\,\Delta t_{\min}$.
- Финально обновляем состояние:

$$
\theta_{i+1} = \theta_i + \delta\theta_i,\qquad
C_{i+1} = C_i + t_{\mathrm{mid}}(\theta_i + \tfrac{\delta\theta_i}{2})\, \Delta c_i.
$$

## Ограничения (физичность)
- Кривизна центра: $|\delta\theta_i| \le \kappa_{\max} \, |\Delta c_i|$.
- Если $\Delta c_i \approx 0$, допускаем «чистую ротацию» на месте: $|\delta\theta_i| \le \omega_{\max} \, \Delta t_{\min}$.
- Благодаря $C_{i+1} = C_i + t_{\mathrm{mid}} \, \Delta c_i$ поперечная скорость кинематического центра равна нулю по построению.

## Стабилизация
- Уплотнение траектории сенсора (шаг ≈ 0.1 м) — устойчивость на изломах.
- Если направление центра сильно расходится с направлением `dS_i`, сначала поворачиваем на месте небольшим `δθ_i`, потом движемся вперёд.
- Внутри шага используем Ньютон с линейным поиском по шагу.

## Время и скорости
$$
\Delta t_i = \max\!\Big( \tfrac{\Delta c_i}{v_{\max}},\ \tfrac{|\delta\theta_i|}{\omega_{\max}},\ \Delta t_{\min} \Big),\quad
t_{i+1} = t_i + \Delta t_i
$$

## Метрики
- `sensor_rms_error_m`, `sensor_max_error_m` — невязка между целевой $S_i$ и восстановленной $\hat{S}_i = C_i + R(\theta_i) r_s$ (по узлам).
- `max_curvature`, `mean_curvature` — кривизна центра.
- `total_length_m` — длина пути центра.

## Использование
- Демо: `--demo {straight|circle|s_curve|l_turn|zigzag}`
- CSV: `--input path.csv` (колонки `x,y`)
- Смещение: `--rs ax,ay` (по умолчанию `0.8,0.0`)
- Ограничения: `--kappa-max`, `--v-max`, `--omega-max`
- Визуализация: `--viz`
- Анимация: `--animate --fps 30 --robot-length 3.0 --robot-width 2.0`

Примеры:
- Круг: `python3 src/path_follower/sensor_center_optimizer.py --demo circle --rs 0.8,0.0 --viz --animate`
- Угол: `python3 src/path_follower/sensor_center_optimizer.py --demo l_turn --rs 0.8,0.0 --kappa-max 0.6 --omega-max 1.2 --viz`

## Замечания
- Идеальные изломы (`L-turn`) требуют либо коротких вращений на месте, либо дуг высокой кривизны; при жёстких лимитах небольшая ошибка — норма.
- Для плавности углов можно сгладить цель сплайном или уменьшить шаг уплотнения.
- Для кругов ошибка по узлам должна быть близка к нулю (геометрия согласована).
