#!/usr/bin/env python3
import os
import time
import capnp
import paho.mqtt.client as mqtt

from base_node.base_node import BaseNode
from drill_msgs.msg import (
    BoolStamped,
    Permission,
    Position,
    Level,
    StateMachineStatus,
    EngineState,
    DrillState,
    DepthState,
    MainAction,
    Point2d
)

current_dir = os.path.dirname(os.path.abspath(__file__))
workspace_root = None
path_parts = current_dir.split(os.sep)
for i in range(len(path_parts) - 1, -1, -1):
    potential_root = os.sep.join(path_parts[:i + 1])
    src_path = os.path.join(potential_root, 'src')
    install_path = os.path.join(potential_root, 'install')
    if os.path.exists(src_path) and os.path.exists(install_path):
        workspace_root = potential_root
        break

if workspace_root is None:
    raise FileNotFoundError(f"Could not find ROS workspace root from {current_dir}")

schema_path = os.path.join(workspace_root, 'src', 'drill_onboard', 'hal_connector', 'hal_connector',
                           'machine_protocol.capnp')
if not os.path.exists(schema_path):
    raise FileNotFoundError(f"Schema file not found: {schema_path}")

protocol = capnp.load(schema_path)


class HalConnectorNode(BaseNode):

    def __init__(self):
        self.mqtt_client = None
        super().__init__('hal_connector')

    def initialize(self):
        """Initialize node resources"""
        # Load parameters from BaseNode
        self.rate = float(self.node_params.get('rate', 10.0))
        self.mqtt_host = self.node_params.get('mqtt_host', 'localhost')
        self.mqtt_port = int(self.node_params.get('mqtt_port', 1883))
        self.message_timeout = float(self.node_params.get('message_timeout', 0.5))
        self.log_period = float(self.node_params.get('log_period', 5.0))
        self.hal_heartbeat_timeout = float(self.node_params.get('hal_heartbeat_timeout', 10.0))
        self.veh_id = self.vehicle_params.get('VEHID', 999)

        # Data storage for telemetry
        self.telemetry_data = {
            'robomode': None,
            'permission': None,
            'position': None,
            'level': None,
            'driller_status': None, # TODO: Currently missing, will be hardcoded to zeros
            'main_state_machine_status': None,
            'engine_state': None,
            'drill_state': None,
            'depth_state': None,  # TODO: Currently missing, will be hardcoded to zeros
        }

        # Timestamps for timeout checking
        self.last_message_times = {}

        # Flags for tracking if messages ever arrived
        self.messages_received = {
            'permission': True,  # Permission is optional, default True
            'position': False,
            'level': False,
            'main_state_machine_status': False,
            'engine_state': False,
            # TODO: Temporarily disable checks for missing topics
            # 'depth_state': False,
            # 'drill_state': False,
        }

        # HAL communication
        self.hal_last_heartbeat = 0

        # Setup ROS subscriptions and publishers
        self._setup_subscribers()
        self._setup_publishers()
        self._setup_mqtt()
        self.loop_rate = self.rate

        self.log("HAL Connector initialized", level=self.INFO, event_code=self.events.SW_ERROR)

    def _setup_subscribers(self):
        """Setup all ROS topic subscriptions"""
        self.robomode_sub = self.create_subscription(
            BoolStamped, '/robomode', self._robomode_callback, 10)

        self.permission_sub = self.create_subscription(
            Permission, '/permission', self._permission_callback, 10)

        self.position_sub = self.create_subscription(
            Position, '/position', self._position_callback, 10)

        self.level_sub = self.create_subscription(
            Level, '/level', self._level_callback, 10)

        self.driller_status_sub = self.create_subscription(
            StateMachineStatus, '/driller_status', self._driller_status_callback, 10)

        self.main_state_machine_status_sub = self.create_subscription(
            StateMachineStatus, '/main_state_machine_status', self._main_state_machine_status_callback, 10)

        self.engine_state_sub = self.create_subscription(
            EngineState, '/engine_state', self._engine_state_callback, 10)

        self.drill_state_sub = self.create_subscription(
            DrillState, '/drill_state', self._drill_state_callback, 10)

        # TODO: Temporarily commented out - these topics don't exist yet
        # Will need to uncomment when they become available
        # self.depth_state_sub = self.create_subscription(
        #     DepthState, '/depth_state', self._depth_state_callback, 10)

    def _setup_publishers(self):
        """Setup ROS topic publishers for outgoing commands"""
        self.permission_pub = self.create_publisher(Permission, '/permission', 10)
        self.main_action_pub = self.create_publisher(MainAction, '/main_action', 10)

    def _setup_mqtt(self):
        """Setup MQTT client for HAL communication"""
        self.mqtt_client = mqtt.Client()
        self.mqtt_client.on_connect = self._on_mqtt_connect
        self.mqtt_client.on_message = self._on_mqtt_message
        self.mqtt_client.on_disconnect = self._on_mqtt_disconnect

        try:
            self.mqtt_client.connect(self.mqtt_host, self.mqtt_port, 60)
            self.mqtt_client.loop_start()
            self.log(f"MQTT connected to {self.mqtt_host}:{self.mqtt_port}", level=self.INFO)
        except Exception as e:
            self.log(f"MQTT connection failed: {e}",
                     level=self.ERROR, event_code=self.events.SW_ERROR)

    def _on_mqtt_connect(self, client, userdata, flags, rc):
        """MQTT connection callback"""
        if rc == 0:
            topics = [
                f"hal/machine/{self.veh_id}/action",
                f"hal/machine/{self.veh_id}/heartbeat",
                f"hal/machine/{self.veh_id}/permission"
            ]
            for topic in topics:
                client.subscribe(topic)
            self.log(f"MQTT subscribed to HAL topics for vehicle {self.veh_id}", level=self.INFO)
        else:
            self.log(f"MQTT connection failed with code {rc}",
                     level=self.ERROR, event_code=self.events.SW_ERROR)

    def _on_mqtt_disconnect(self, client, userdata, rc):
        """MQTT disconnect callback"""
        self.log("MQTT disconnected from HAL",
                 level=self.WARN, event_code=self.events.SW_ERROR, period=self.log_period)

    def _on_mqtt_message(self, client, userdata, msg):
        """Handle incoming MQTT messages from HAL"""
        try:
            topic_parts = msg.topic.split('/')
            if len(topic_parts) >= 4:
                command_type = topic_parts[-1]

                if command_type == 'heartbeat':
                    self._handle_hal_heartbeat(msg.payload)
                elif command_type == 'permission':
                    self._handle_hal_permission(msg.payload)
                elif command_type == 'action':
                    self._handle_hal_action(msg.payload)

        except Exception as e:
            self.log(f"Error processing HAL message: {e}",
                     level=self.ERROR, event_code=self.events.SW_ERROR)

    def _handle_hal_heartbeat(self, payload):
        """Process heartbeat from HAL"""
        try:
            with protocol.Heartbeat.from_bytes(payload) as heartbeat_data:
                self.hal_last_heartbeat = time.time()
        except Exception as e:
            self.log(f"Error parsing HAL heartbeat: {e}",
                     level=self.ERROR, event_code=self.events.SW_ERROR, period=self.log_period)

    def _handle_hal_permission(self, payload):
        """Process permission command from HAL"""
        try:
            with protocol.Permission.from_bytes(payload) as permission_data:
                permission_msg = Permission()
                permission_msg.header.stamp = self.get_rostime()
                permission_msg.permission = False
                permission_msg.source = "hal"
                self.permission_pub.publish(permission_msg)
                self.log("Permission command received from HAL", level=self.INFO)

        except Exception as e:
            self.log(f"Error processing HAL permission: {e}",
                     level=self.ERROR, event_code=self.events.SW_ERROR, period=self.log_period)

    def _handle_hal_action(self, payload):
        """Process action command from HAL"""
        try:
            with protocol.Task.from_bytes(payload) as task:
                action_msg = MainAction()
                action_msg.header.stamp = self.get_rostime()
                action_msg.id = task.id
                action_msg.x = task.x
                action_msg.y = task.y
                action_msg.z = task.z
                action_msg.azimuth = task.azimuth
                action_msg.inclination = task.inclination
                action_msg.depth = task.depth
                action_msg.is_edge = task.isEdge
                action_msg.no_auto_move = task.noAutoMove

                action_msg.borders = []
                for point in task.borders:
                    border_point = Point2d()
                    border_point.x = point.x
                    border_point.y = point.y
                    action_msg.borders.append(border_point)

                action_msg.drilled_holes = []
                for point in task.drilledHoles:
                    hole_point = Point2d()
                    hole_point.x = point.x
                    hole_point.y = point.y
                    action_msg.drilled_holes.append(hole_point)

                self.main_action_pub.publish(action_msg)

                self.log(f"Action command received from HAL: ID={task.id}, X={task.x:.2f}, Y={task.y:.2f}",
                         level=self.INFO, event_code=self.events.ACTION_NOT_ACCEPTED)

        except Exception as e:
            self.log(f"Error processing HAL action: {e}",
                     level=self.ERROR, event_code=self.events.SW_ERROR, period=self.log_period)

    def _robomode_callback(self, msg):
        self.telemetry_data['robomode'] = msg
        self.last_message_times['robomode'] = time.time()
        self.messages_received['robomode'] = True

    def _permission_callback(self, msg):
        self.telemetry_data['permission'] = msg
        self.last_message_times['permission'] = time.time()
        self.messages_received['permission'] = True

    def _position_callback(self, msg):
        self.telemetry_data['position'] = msg
        self.last_message_times['position'] = time.time()
        self.messages_received['position'] = True

    def _level_callback(self, msg):
        self.telemetry_data['level'] = msg
        self.last_message_times['level'] = time.time()
        self.messages_received['level'] = True

    def _driller_status_callback(self, msg):
        self.telemetry_data['driller_status'] = msg
        self.last_message_times['driller_status'] = time.time()
        self.messages_received['driller_status'] = True

    def _main_state_machine_status_callback(self, msg):
        self.telemetry_data['main_state_machine_status'] = msg
        self.last_message_times['main_state_machine_status'] = time.time()
        self.messages_received['main_state_machine_status'] = True

    def _engine_state_callback(self, msg):
        self.telemetry_data['engine_state'] = msg
        self.last_message_times['engine_state'] = time.time()
        self.messages_received['engine_state'] = True

    def _drill_state_callback(self, msg):
        self.telemetry_data['drill_state'] = msg
        self.last_message_times['drill_state'] = time.time()
        self.messages_received['drill_state'] = True

    # TODO: Uncomment when these topics become available
    # def _depth_state_callback(self, msg):
    #     self.telemetry_data['depth_state'] = msg
    #     self.last_message_times['depth_state'] = time.time()
    #     self.messages_received['depth_state'] = True

    def _check_all_messages_received(self) -> bool:
        """Check if all required messages have been received at least once"""
        for key, received in self.messages_received.items():
            if not received:
                self.log(f"Waiting for first message from topic: {key}",
                         level=self.WARN, event_code=self.events.SENSOR_FAILURE, period=self.log_period)
                return False
        return True

    def _check_message_timeouts(self) -> bool:
        """Check if any messages have timed out"""
        current_time = time.time()
        timed_out_topics = []

        for topic, last_time in self.last_message_times.items():
            if topic in self.messages_received and self.messages_received[topic]:
                if current_time - last_time > self.message_timeout:
                    timed_out_topics.append(topic)

        if timed_out_topics:
            self.log(f"Message timeout detected for topics: {', '.join(timed_out_topics)}",
                     level=self.ERROR, event_code=self.events.SENSOR_FAILURE, period=self.log_period)
            return False
        return True

    def _check_hal_heartbeat(self):
        """Check HAL heartbeat status"""
        if self.hal_last_heartbeat == 0:
            return

        current_time = time.time()
        if current_time - self.hal_last_heartbeat > self.hal_heartbeat_timeout:
            self.log("HAL heartbeat timeout - no communication with HAL server",
                     level=self.WARN, event_code=self.events.SW_ERROR, period=self.log_period)

    def _build_telemetry_message(self) -> bytes:
        """Build Cap'n Proto telemetry message from current data"""
        telemetry = protocol.Telemetry.new_message()

        telemetry.timestamp = int(time.time() * 1000)

        main_status = self.telemetry_data.get('main_state_machine_status')
        if main_status:
            telemetry.lastActionId = int(main_status.last_action_id)
            telemetry.curActionId = int(main_status.cur_action_id)
            telemetry.mainState = str(main_status.current_state)
        else:
            telemetry.lastActionId = -1
            telemetry.curActionId = -1
            telemetry.mainState = "idle"  # Default state

        driller_status = self.telemetry_data.get('driller_status')
        if driller_status:
            telemetry.drillerState = str(driller_status.current_state)
        else:
            # TODO: Default driller state while driller node is not implemented
            telemetry.drillerState = "idle"  # Default state for driller

        position = self.telemetry_data.get('position')
        if position:
            telemetry.x = float(position.x)
            telemetry.y = float(position.y)
            # TODO: Z coordinate currently equals center of machine, not drill bit
            telemetry.z = float(position.z)
            telemetry.yaw = float(position.yaw)
            telemetry.posIsValid = bool(position.is_reliable)
        else:
            telemetry.x = 0.0
            telemetry.y = 0.0
            telemetry.z = 0.0
            telemetry.yaw = 0.0
            telemetry.posIsValid = False

        level = self.telemetry_data.get('level')
        if level:
            telemetry.roll = float(level.roll)
            telemetry.pitch = float(level.pitch)
        else:
            telemetry.roll = 0.0
            telemetry.pitch = 0.0

        permission = self.telemetry_data.get('permission')
        if permission:
            telemetry.movePermission = bool(permission.permission)
        else:
            telemetry.movePermission = True

        robomode = self.telemetry_data.get('robomode')
        if robomode:
            telemetry.robomode = bool(robomode.value)
        else:
            telemetry.robomode = False

        engine_state = self.telemetry_data.get('engine_state')
        if engine_state:
            telemetry.engineRpm = int(float(engine_state.engine_speed))
        else:
            telemetry.engineRpm = 0

        drill_state = self.telemetry_data.get('drill_state')
        if drill_state:
            telemetry.headPos = float(drill_state.head_pos)
            telemetry.feedSpeed = float(drill_state.head_speed)
            telemetry.feedPressure = float(drill_state.feed_pressure)
            telemetry.rotPressure = float(drill_state.rot_pressure)
            telemetry.airPressure = float(drill_state.air_pressure)
            telemetry.drillRpm = int(float(drill_state.drill_rpm))
        else:
            telemetry.headPos = 0.0
            telemetry.feedSpeed = 0.0
            telemetry.feedPressure = 0.0
            telemetry.rotPressure = 0.0
            telemetry.airPressure = 0.0
            telemetry.drillRpm = 0

        # TODO: Depth data - currently hardcoded as these topics don't exist yet
        depth_state = self.telemetry_data.get('depth_state')
        if depth_state:
            # telemetry.currentDepth = float(depth_state.depth)
            # telemetry.bitZ = float(depth_state.drill_bit_z)
            pass
        else:
            telemetry.currentDepth = 0.0
            telemetry.bitZ = 0.0

        # TODO: Bit X and Y coordinates - need to find this parameters
        # For now, use the same as machine position
        telemetry.bitX = telemetry.x
        telemetry.bitY = telemetry.y

        return telemetry.to_bytes()

    def _send_telemetry_to_hal(self):
        """Send telemetry data to HAL via MQTT"""
        try:
            telemetry_bytes = self._build_telemetry_message()
            topic = f"hal/machine/{self.veh_id}/telemetry"

            if self.mqtt_client:
                result = self.mqtt_client.publish(topic, telemetry_bytes)
                if result.rc != 0:
                    self.log(f"Failed to publish telemetry to HAL: MQTT error {result.rc}",
                             level=self.ERROR, event_code=self.events.SW_ERROR, period=self.log_period)
            else:
                self.log("MQTT client not available for telemetry transmission",
                         level=self.ERROR, event_code=self.events.SW_ERROR, period=self.log_period)

        except Exception as e:
            self.log(f"Error building/sending telemetry: {e}",
                     level=self.ERROR, event_code=self.events.SW_ERROR, period=self.log_period)

    def do_work(self):
        """Main work function - called by BaseNode timer"""
        self._check_hal_heartbeat()

        if not self._check_all_messages_received():
            return

        if not self._check_message_timeouts():
            return

        self._send_telemetry_to_hal()

    def stop(self):
        """Clean shutdown"""
        if self.mqtt_client:
            self.mqtt_client.loop_stop()
            self.mqtt_client.disconnect()
        super().stop()


def main(args=None):
    import rclpy
    rclpy.init(args=args)

    try:
        node = HalConnectorNode()
        node.run()
    except KeyboardInterrupt:
        pass
    except SystemExit:
        pass
    finally:
        if 'node' in locals() and hasattr(node, 'mqtt_client') and node.mqtt_client:
            node.mqtt_client.loop_stop()
            node.mqtt_client.disconnect()
        if rclpy.ok():
            rclpy.shutdown()


if __name__ == '__main__':
    main()