@0xdbb9ad1f14bf0b36;

struct Telemetry {
  timestamp @0 :UInt64;
  lastActionId @1 :Int32;
  curActionId @2 :Int32;
  drillerState @3 :Text;
  mainState @4 :Text;
  x @5 :Float32;
  y @6 :Float32;
  z @7 :Float32;
  posIsValid @8 :Bool;
  roll @9 :Float32;
  pitch @10 :Float32;
  yaw @11 :Float32;
  movePermission @12 :Bool;
  robomode @13 :Bool;
  engineRpm @14 :UInt16;
  headPos @15 :Float32;
  feedSpeed @16 :Float32;
  feedPressure @17 :Float32;
  rotPressure @18 :Float32;
  airPressure @19 :Float32;
  drillRpm @20 :Int16;
  currentDepth @21 :Float32;
  bitZ @22 :Float32;
  bitX @23 :Float32;
  bitY @24 :Float32;
}

struct Task {
  timestamp @0 :UInt64;
  id @1 :UInt32;
  x @2 :Float32;
  y @3 :Float32;
  z @4 :Float32;
  azimuth @5 :Float32;
  inclination @6 :UInt8;
  depth @7 :Float32;
  isEdge @8 :Bool;
  noAutoMove @9 :Bool;
  borders @10 :List(Point);
  drilledHoles @11 :List(Point);
}

struct Point {
  x @0 :Float32;
  y @1 :Float32;
}

struct Heartbeat {
  timestamp @0 :UInt64;
}

struct Permission {
  timestamp @0 :UInt64;
}
