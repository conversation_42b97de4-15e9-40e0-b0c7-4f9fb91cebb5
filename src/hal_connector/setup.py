"""
ROS2 package for MQTT communication with HAL backend
"""

from setuptools import setup
import os
from glob import glob

package_name = 'hal_connector'

setup(
    name=package_name,
    version='0.1.0',
    packages=[package_name],
    data_files=[
        ('share/ament_index/resource_index/packages',
            ['resource/' + package_name]),
        ('share/' + package_name, ['package.xml']),
    ],
    install_requires=[
        'setuptools',
        'paho-mqtt>=1.6.0',
        'pycapnp>=1.3.0'
    ],
    zip_safe=True,
    maintainer='Daniil Vinogradov',
    maintainer_email='<EMAIL>',
    description='ROS2 node for MQTT communication with HAL backend using CapnProto',
    license='Proprietary',
    tests_require=['pytest'],
    entry_points={
        'console_scripts': [
            'hal_connector = hal_connector.hal_connector:main',
        ],
    },
)