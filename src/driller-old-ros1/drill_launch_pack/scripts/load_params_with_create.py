#!/usr/bin/env python
import rospy
import os
import yaml


def set_nested_params(params, prefix=''):
    for key, value in params.items():
        if isinstance(value, dict):
            set_nested_params(value, prefix + key + '/')
        else:
            rospy.set_param(prefix + key, value)
            rospy.loginfo("Setting parameter: %s = %s", prefix + key, value)


def main():
    rospy.init_node('load_params_with_create')

    params_dump_file = rospy.get_param('~params_dump_file', '')

    if not os.path.exists(params_dump_file):
        with open(params_dump_file, 'w') as f:
            yaml.dump({}, f)
        rospy.loginfo("Created empty params dump file: %s", params_dump_file)

    # Load params_dump.yaml
    with open(params_dump_file, 'r') as f:
        params_dump = yaml.safe_load(f)
    if params_dump:
        set_nested_params(params_dump)
    else:
        rospy.loginfo("Params dump file %s is empty or could not be read or parsed", params_dump_file)

if __name__ == '__main__':
    main()
