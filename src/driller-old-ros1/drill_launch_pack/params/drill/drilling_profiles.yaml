# DrillerNode Configuration

# OVERVIEW:
# This configuration is split into 'default' parameters and 'profiles'.
# The 'default' section serves as a dictionary of system parameters that can be adjusted during profile application.
# 'profiles' are used to adjust these defaults for specific drilling conditions like wet hole or hard rock.

# DEFAULT:
# - 'common': These are universal settings applicable regardless the driller state.
# - STATES: Specific settings for each driller state (e.g., 'idle', 'touchdown', 'overburden_pass', 'drilling').
#           This helps better readability of configuration.
# - ALIASES: For values used across multiple states or settings, YAML aliases (&) can be utilized.
#           This avoids repetition and makes value changes more efficient.

# SPECIAL DEPTH-DEPENDENT PARAMETERS:
# Some parameters in drilling state depend on the current drill depth:
# - 'mandatory_pullups', 'feed_pressure_limit', 'rotation_speed_limit', and 'water' have special depth-based settings.
# - 'depth' can be defined relative to three references:
#   - 'from_ground': Depth from the surface.
#     Note: Parameters defined with from_ground are applicable only for the first shaft (typically ~16m length).
#     For other shafts, the from_added_shaft_start parameter should be used.
#   - 'from_added_shaft_start': Similar to from_ground, but applies to added shafts when the first_shaft_flag (from driller action input) is false.
#   - 'from_shaft_finish: Depth from current drilling finish, applied when drilling before rod adding (for every shaft but not last).
#   - 'from_hole_finish': Depth from the planned target depth. Note: It's applied to last shaft.
#   - 'drilled': Drilled depth at current state run (used for cracked).
# - For 'mandatory_pullups', you can specify the action as 'long' or 'short' for the drill's behavior at a specific depth.

# PROFILES:
# Profiles allow for the dynamic adjustment of default parameters:
# - They can contain a subset of the default dictionary's parameters.
# - A profile's purpose is to adjust the drill's behavior under specific conditions.
#   E.g., a 'wet' profile may adjust water handling parameters, or a 'hard-rock' profile might tweak rotation speeds.
# - Multiple profiles can be applied in sequence, with each subsequent profile potentially overriding the previous ones.

DrillerNode:

  ####################################
  # Default Parameters
  ####################################
  default:
    common:
      slow_feed_speed: &slow_feed_speed 0.2   # common parameter for slow feed speed, used in touchdown and after_pullup
      ground_detect_feed_speed_thr: 0.08      # Feed speed threshold for ground detection used in touchdown and after_pullup. If feed_speed < ground_detect_feed_speed_thr, we consider ground detections is started.
      ground_detect_duration: 1.5             # Duration threshold to confirm ground detection, used in touchdown and after_pullup

      touchdown_raw_feed_pressure: &touchdown_raw_feed_pressure 0.25  # raw small feed pressure control, used in touchdown and unstuck_down, unstuck_up
      short_hole_max_depth: 9.5             # threshold to determine hole is short or regular

      water_low: &water_low 0.18            # low water control level, used in drilling
      water_high: &water_high 0.58          # high water control level, used in drilling

      # vibrations check
      check_vibrations: True              # Turn on (True) or off (False) vibration check
      vibration_limits:                   # A list defining vibration amplitude limits (m/s) for various frequencies (Hz)
        - frequency: 3
          amplitude: 90
        - frequency: 5
          amplitude: 90
      high_vibration_duration_threshold: 2.0        # Timeout period for high vibrations detections (after excess vibration_limits) and reduced rotations speed apply. Used in set_safe_rotation in many states.

      # rotation speed control
      max_rotation_speed: &max_rotation_speed 115   # Maximum rotation speed, used as rotation speed control in many states
      reduced_rotation_speed: 65                    # Reduced rotation speed, used in case of high vibrations

      # Check Air Pressure params (and check_if_pullup_needed)
      max_air_pressure: 5.5                # The maximum allowable air pressure used in check_air_is_nominal() and AfterPullUpState for nominal air pressure set condition
      max_air_pressure_excess: 0.6         # The allowable excess over the nominal air pressure (pressure_state.air_pressure > nominal_air_pres + max_air_pressure_excess)
      air_not_nominal_max_time: 2.0        # The maximum duration during which air pressure can exceed max_air_pressure before it's considered non-nominal
      air_transient_response_time: 7.0     # Time to detect a compressor malfunction in check_air(), called in do_work_after

      pass_soft_press: &pass_soft_press 65     # Minimum value for feed speed control, used in many state
      default_air_power: &default_air_power 1  # default air power control, used in many state

      # Parameters used in main mostly
      # Stuck detection parameters
      too_high_rp_threshold: &too_high_rp_threshold 310   # used in stuck detection to set high rotation pressure flag
      too_low_rotation_speed: &too_low_rotation_speed 3   # used in stuck detection to set rotation stuck (by speed criteria) flag
      high_rp_duration: 1.5                     # Used to detect we have high rotation pressure for long time (hard rotation criteria in stuck check)
      no_rotation_duration: 0.5                           # Used to detect we no rotation (rotation speed < too_low_rotation_speed) for long time (another hard rotation criteria in stuck check)
      stuck_speed_threshold: 0.05                         # Feed speed threshold used to set stuck_criteria_move in stuck check
      stuck_move_duration: 1.247                          # Duration threshold to detect stuck by feed movement criteria
      min_state_duration_to_stuck: 0.391                  # Time before stuck detection is activated after entering a new state
      ignore_stuck_duration: 3                            # Duration after an unstuck event during which further stuck detections are temporarily ignored
      enable_unstuck: True                                # used to enable unstuck_down and unstuck_up states work (if True) or emit error (if False)
      unstuck_recency_limit: 10                           # Duration to check we just unstucked, drill might still be in a sensitive state, thus we apply different settings (e.g. rotation_speed_after_unstuck_ctrl)

      # params for depth valid checks
      invalid_depth_duration: 0.6                         # Maximum allowable duration without valid depth data during non-drilling states before considering it as a critical error
      invalid_depth_duration_drill: 8.0                   # Maximum allowable duration without valid depth data during drilling-related states (e.g., IDLE, DRILLING, AFTER_PULLUP, PASS_SOFT) before considering it as a critical error

      # arm management
      close_arm: true           # Flag to determine if the arm (lunette) should be used during vertical drilling
      arm_open_depth: 7.0       # Spindle depth below which the arm will be opened
      arm_close_depth: 5.0      # Spindle depth above which the arm will be closed
      arm_close_min_depth: 4.5  # Highest spindle depth above which movement is prohibited when the arm is open
      arm_open_max_depth: 7.5   # Lowest spindle depth below which movement is prohibited when the arm is closed

    touchdown:
      feed_speed_ctrl: *slow_feed_speed     # feed_speed control in the state
      rotation_speed_ctrl: 0                # rotation_speed control in the state
      air_power_ctrl: 0                     # air_power control in the state
      dust_ctrl: True                       # dust_curtain_down control in the state
      # no water_ctrl change in the state
      feed_pressure_ctrl_raw: *touchdown_raw_feed_pressure          # feed_pressure control (raw!) in the state
      depth_delta: 0.25                     # Minimum change in spindle depth required, from the depth of entering state, to confirm ground level detection

    overburden_pass:
      feed_speed_ctrl: 1                    # feed_speed control in the state
      rotation_speed_ctrl: 57               # start_rotation_speed or start_rotation_speed_short (70), was rotation_speed
      water_ctrl: *water_high               # water control in the state
      air_power_ctrl: *default_air_power    # air power control in the state
      dust_ctrl: True                       # dust_curtain_down control in the state
      feed_pressure_ctrl: 55                # feed pressure control in the state, bar
      spinup_time: 3                        # duration over which the rotation speed linearly increases to its target value (rotation_speed)
      max_duration: 60                      # max state duration (switch to DRILLING if state duration exceeds this parameter)
      overburden_layer_thickness: 0.5       # expected maximum thickness of the overburden layer, another condition to switch to DRILLING

    drilling:
      # pullups
      mandatory_pullups: # Depth at which a pull-up operation should occur
        - depth: { from_ground: 3.5 } # was mandatory_pullup_depth
          action: long
        - depth: { from_shaft_finish: 1.5 } # was pullup_before_finish
          action: short
#        - depth: { from_added_shaft_start: 3.0 } # for test
#          action: short
        - depth: { from_hole_finish: 2.5 }
          action: long
#        - depth: { from_ground: 20.5 }  # for test of too big depth
#          action: long

      water_ctrl: # Dict of water control depending on depth
        - depth: { from_ground: 0.0 }
          value: *water_high
        - depth: { from_ground: 3.0 }         # was water_decrease_depth
          value: *water_low
        - depth: { from_hole_finish: 1.5 }         # was water_stop_before_finish
          value: 0

      # Feed Pressure Control
      feed_pressure_reduced: 149              # Reduced feed pressure control for high rotation pressure condition
      feed_pressure_min: *pass_soft_press     # Minimum feed pressure control

      low_drill_speed_thr: 0.015            # The threshold speed below which the drill speed (drill_speed_smoothed) is considered low and timer is initiated
      target_drill_speed: 0.033             # target drilling speed used in i-control, m/s
      drilling_speed_i_max: 20              # Upper limit for drill speed error integral
      drilling_speed_i_gain: 800            # Gain for the drill speed error integral
      drilling_speed_i_min: -140            # Lower limit for drill speed error integral
      high_rp_threshold: 276                # Threshold of rotation pressure to start high_rp_last_time timer
      high_rp_duration_threshold: 15        # Duration threshold after which the feed pressure is reduced due to long high rotation pressure

      # Feed pressure limits based on depth
      feed_pressure_limit:
        - depth: { from_ground: -3 }
          value: 55 # start_feed_pressure
        - depth: { from_ground: 0 }
          value: 138 # from max_pulldown_dict
        - depth: { from_ground: 4.7 }
          value: 162 # from max_pulldown_dict
        - depth: { from_ground: 6.6 }
          value: 250 # from max_pulldown_dict

      # slow pressure increase
      slow_press_increase_rate: 11                    # Rate of feed pressure increase

      feed_speed_ctrl: 1                               # feed speed control in the state

      # rotation speed control based on depth
      rotation_speed_limit:
        - depth: { from_ground: 0 }
          value: 57 # from max_rot_dict
        - depth: { from_ground: 0.7 }
          value: 80 # from max_rot_dict
        - depth: { from_ground: 3.0 }
          value: 125 # from max_rot_dict

      # air power control
      air_power_ctrl: *default_air_power              # Control setting for air power during drilling
      dust_ctrl: True                                 # Dust control during drilling

    hard_rot:
      feed_speed_ctrl: 0.05       # Speed for upward motion during recovery, '-' for moving up is applied in code
      rotation_speed_after_unstuck_ctrl: *max_rotation_speed # Rotation speed if within unstuck_recency_limit seconds since last unstuck
      rotation_speed_normal_ctrl: 84        # Rotation speed if more than unstuck_recency_limit seconds since last unstuck
      dust_ctrl: True                       # Dust flaps control
      air_power_ctrl: *default_air_power    # Air power control setting
      feed_pressure_ctrl: *pass_soft_press  # Feed pressure control setting
      depth_delta: 0.5                      # Depth change threshold to revert to the previous state
      min_rotation_speed: *too_low_rotation_speed            # If rotation speed exceeds this threshold, return to the previous state
      max_rotation_pressure: 186          # 60% of too_high_rp_threshold. If rotation pressure drops below this value, return to the previous state

    pullup:
      depth_threshold: 4             # Threshold to decide which short_pullup_height_low or short_pullup_height_high value to use based on spindle depth relative to ground or relative to new shaft drill starting spindle depth (in case of not first shaft)
      short_pullup_height_low: 0.8   # Pull-up distance if depth < depth_threshold
      short_pullup_height_high: 2.0  # Pull-up distance if depth >= depth_threshold
      max_short_pullup_cnt: 3        # Maximum number of short pullups before doing long pullup
      free_rotation_pressure: 60     # The rotation pressure threshold which indicates that the drill rotating without much resistance, used in is_clear() check
      air_pressure_max: 7.0          # The air pressure threshold below which the drilling is considered clear, used in is_clear() check
      pullup_to_ground_limit: 0.2    # The maximum allowable distance to the ground during a pull-up, used in reached_long_pullup_limit() check
      rotation_speed_after_unstuck_ctrl: *max_rotation_speed # applied when the last unstuck event occurred less than unstuck_recency_limit seconds ago
      rotation_speed_normal_ctrl: 79 # rotation speed if the last unstuck event occurred more than unstuck_recency_limit seconds ago
      slow_pullup_depth: 3.0         # The proximity of depth to ground when we slow down feed speed
      feed_speed_normal_ctrl: 0.61   # The regular feed speed during a pull-up, '-' for moving up is applied in code
      feed_speed_reduced_ctrl: 0.3   # The reduced feed speed during a pull-up when it's close to the ground level, '-' for moving up is applied in code
      dust_ctrl: True                # Control for dust curtain
      air_power_normal_ctrl: *default_air_power         # The standard air power control
      pullup_compressor_on_to_ground_limit: 2.0         # The distance from the ground within which the air is turned off
      feed_pressure_ctrl: 0         # Feed pressure during pull-up

    after_pullup:
      feed_speed_ctrl: *slow_feed_speed     # Feed speed during the state
      feed_pressure_ctrl_raw: 0.36          # Feed pressure during the state
      rotation_speed_ctrl: 45               # Rotation speed during the state
      ground_detect_rot_pres: 49            # Rotation pressure threshold for ground detection, used with ground_detect_feed_speed_thr and ground_detect_duration from common
      air_power_ctrl: *default_air_power    # Air power control during the state
      dust_ctrl: True                       # Dust control during the state

    wait_after_drill:
      feed_speed_ctrl: 0.01                 # Feed speed during the state, '-' applied in code
      feed_pressure_ctrl: *pass_soft_press  # Feed pressure during the state
      rotation_speed_ctrl: 63               # Rotation speed during the state
      dust_ctrl: True                       # Dust control during the state
      air_power_ctrl: *default_air_power    # Air power control during the state
      max_duration: 6.5                     # max state duration (switch to RAISE if state duration exceeds this parameter)

    pass_soft:
      feed_pressure_ctrl: *pass_soft_press        # Feed pressure in pass_soft state
      rotation_speed_ctrl: *max_rotation_speed    # Rotation speed during the state
      air_power_ctrl: *default_air_power          # Air power control during the state
      dust_ctrl: True                             # Dust control during the state
      max_duration: 2                             # max state duration (switch to DRILLING if state duration exceeds this parameter)

    unstuck:
      unstuck_time_up: 1.80         # Duration (in seconds) the drill attempts to move upwards during an unstuck cycle.
      unstuck_time_down: 0.7        # Duration (in seconds) the drill attempts to move downwards during an unstuck cycle.
      unstuck_dist: 0.55            # Minimum distance (in meters) the drill needs to move to be considered as successfully unstuck.
      unstuck_up_feed_speed_ctrl: 1.0      # Feed speed control with which the drill moves upwards during the unstuck operation, '-' for upward direction is applied in code
      unstuck_down_feed_speed_ctrl: 0.2     # Feed speed control with which the drill moves downwards during the unstuck operation.
      unstuck_try_cnt: 15          # Maximum number of unstuck cycles allowed before the operation is considered as failed.

      feed_pressure_ctrl: *touchdown_raw_feed_pressure    # Feed pressure during the state
      rotation_speed_ctrl: 115                            # Rotation speed during the state
      air_power_ctrl: *default_air_power   # Air power control during the state

    raise:
      rotation_speed_after_unstuck_ctrl: *max_rotation_speed   # before 10 seconds from unstuck_last_t
      rotation_speed_normal_ctrl: 63    # Drill rotation speed after last_unstuck_time seconds from unstuck_last_t before stop_rotation_depth
      stop_rotation_depth: 7.0          # Depth to stop the drill rotation during raising
      stop_air_depth: 0.5               # Depth to stop the air flow
      dust_ctrl: True                   # Dust control during the state
      feed_pressure_ctrl: 0             # Feed pressure control during the state
      reduced_speed_zone: 1.5           # Threshold for reducing the feed speed during raising near target_raise_spindle_depth
      max_raise_feed_speed: 0.7         # Maximum feed speed during raising, '-' for moving up is applied in code
      min_raise_feed_speed: 0.06        # Minimum (reduced) feed speed during raising near target_raise_spindle_depth (with range reduced_speed_zone), '-' for moving up is applied in code


  ####################################
  # Profiles
  ####################################
  profiles:
    default-short:
      overburden_pass:
        water_ctrl: 0.4             # water_high_short
        rotation_speed_ctrl: 70     # start_rotation_speed_short
        feed_pressure_ctrl: 55      # start_feed_pressure_short
      drilling:
        feed_pressure_limit:
          - depth: { from_ground: -3 }
            value: 55 # start_feed_pressure_short
          - depth: { from_ground: 0 }
            value: 138 # from max_pulldown_dict
          - depth: { from_ground: 4.7 }
            value: 162 # from max_pulldown_dict
          - depth: { from_ground: 6.6 }
            value: 250 # from max_pulldown_dict
        rotation_speed_limit:
          - depth: { from_ground: 0 }
            value: 70 # from max_rot_dict_short
          - depth: { from_ground: 1.8 }
            value: 90 # from max_rot_dict_short
          - depth: { from_ground: 3.0 }
            value: 125 # from max_rot_dict_short
        water_ctrl:
          - depth: { from_ground: 0.0 }
            value: *water_low
          - depth: { from_ground: 2.0 } # from water_cutoff_depth_short
            value: 0 # turn off water after water_cutoff_depth_short
        mandatory_pullups:
          - depth: { from_ground: 0.7 }
            action: long # from short_hole_mandatory_pullup_depth
      raise:
        stop_air_depth: 10.0 # turn off an air for short hole

    hard:
      overburden_pass:
        feed_pressure_ctrl: 100 # start_feed_pressure_hard
      drilling:
        feed_pressure_limit:
          - depth: { from_ground: -3 }
            value: 100 # start_feed_pressure_hard
          - depth: { from_ground: 0 }
            value: 150 # value from max_pulldown_dict_hard
          - depth: { from_ground: 4.7 }
            value: 162 # value from max_pulldown_dict_hard
          - depth: { from_ground: 6.6 }
            value: 250 # value from max_pulldown_dict_hard
        rotation_speed_limit:
          - depth: { from_ground: 0 }
            value: 57 # value from max_rot_dict_hard
          - depth: { from_ground: 0.7 }
            value: 80 # value from max_rot_dict_hard
          - depth: { from_ground: 3.0 }
            value: 100 # value from max_rot_dict_hard

    wet:
      drilling:
        water_ctrl:
          - depth: { from_ground: 0.0 }
            value: *water_low
          - depth: { from_ground: 4.5 } # from wet_pullup_depths[-1]
            value: 0 # Turn off water after wet_pullup_depths[-1]
        mandatory_pullups:
          - depth: { from_ground: 1.5 } # from wet_pullup_depths
            action: long
          - depth: { from_ground: 2.5 } # from wet_pullup_depths
            action: long
          - depth: { from_ground: 3.5 } # from wet_pullup_depths
            action: long
          - depth: { from_ground: 4.5 } # from wet_pullup_depths
            action: short # short from wet_pullup_short_after: 4.0
          - depth: { from_shaft_finish: 1.5 } # from pullup_before_finish (only for long)
            action: short

    wet-short:
      drilling:
        mandatory_pullups:
          - depth: { from_ground: 1.5 } # from wet_pullup_depths
            action: long
          - depth: { from_ground: 2.5 } # from wet_pullup_depths
            action: long
          - depth: { from_ground: 3.5 } # from wet_pullup_depths
            action: long
          - depth: { from_ground: 4.5 } # from wet_pullup_depths
            action: short # short from wet_pullup_short_after: 4.0

    cracked:
      drilling:
        mandatory_pullups:
          - depth: { drilled: 1.5 }
            action: cracked
      pullup:
        air_power_normal_ctrl: 0
        short_pullup_height_low: 0.75   # Pull-up distance if depth < depth_threshold
        short_pullup_height_high: 0.75  # Pull-up distance if depth >= depth_threshold
        max_short_pullup_cnt: 7

    cracked-short:
      drilling:
        mandatory_pullups:
          - depth: { drilled: 1.5 }
            action: cracked
