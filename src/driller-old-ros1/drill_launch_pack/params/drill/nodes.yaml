CatDriverNode:
  debug: true
  rate: 20
  log_period: 2
  yaw_p: 0.15

  yawrate_pid:
    i_saturation: 0.2
    p: 0.3
    i: 0.00
    d: 0.01

  correction_pid:
    i_saturation: 0.1
    p: 0.5
    i: 0.00
    d: 0.02

  approach_yawrate_pid:
    i_saturation: 0.01
    p: 0.035
    i: 0.00
    d: 0.00

  # Граница насыщения интегральной составляющий ПИД-регулятора

  slowdown_k: 5.5
  max_turn_rate: 0.09
  approach_max_turn_rate: 0.015

  future_yaw_len: 0.5 # where to take target yaw

  # Максимальное допустимое расстояние между машиной и началом маршрута метры
  max_uncut_len: 0.5

  max_approach_speed: 0.078
  max_approach_ang_err: 9 #deg
  GOAL_ACHIVED_ZONE: 0.06
  max_allowed_target_dist: 0.15

  # Параметры для ErrorHandler
  yaw_thr_k: 0
  dist_thr_k: 0

PlannerNode:
  debug: true
  rate: 10
  # Длина максимального допустимого отклонения от траектории метры
  MAX_SHIFT: 2
  # Длина траектории в драйвер метры
  OUTPUT_ROUTE_LEN: 15
   # радиус зоны достижения цели в конечной точке маршрута метры
  GOAL_ACHIEVED_ZONE_INTERMEDIATE: 0.3
  GOAL_ACHIEVED_ZONE_FINAL: 0.35
  # Расстояние до конца маршрута, на котором скорость искуственно занижается для эффективной остановки метры
  before_end_low_speed_len: 1.0
  # Длина маршрута для анализа кривизны метры
  curve_analysis_length: 5
  # Максимальный радиус кривизны
  #max_curvature_radius: 60.0
  # целевая скорость движения
  target_speed: 0.45

  # Параметры класса построителя маневров:
  maneuver_builder_params:
    grid_step: 1.3
    grid_half_width: 6.7
    grid_extra_len: 6
    grid_yaw_step: 0.3925 #3.14 / 8,
    max_search_radius: 4
    min_search_radius: 2.47
    min_turn_radius: 3.6
    straight_part_len: 2.0
    straight_part_len_special: 5.0
    point_step: 0.1
    check_step: 0.8
    max_steps: 5
    timeout: 120
    #do_plots: true

  smooth_start_k: 0.08

  speed_limit_when_inclined: 0.2
  max_roll_to_rush: 2
  max_pitch_to_rush: 3
  inclined_slowdown_len: 1.5

LevelerNode:
  rate: 10
  debug: false
  log_period: 2

  max_roll_above_start: 1.5
  max_pitch_above_start: 2.0

  angles_time_thr: 0.6
#  crt_angle_thr: 1

# control input for touchdown
  touchdown_jacks_control_front: 1.0
  touchdown_jacks_control_back: 0.7

  pulling_down_speed_on_ground: 0.7
  pulling_ang_err_to_restore: 2

  allowed_modes:
    "pulling":
     - "idle"
     - "grounding"

    "restore_pulled":
     - "moving"
     - "idle"
     - "wait_before_level"
     - "leveling"
     - "restore_string"

    "pulled":
     - "grounding"
     - "moving"
     - "leveling"
     - "idle"
     - "restore_string"
     - "wait_before_level"

    "leveling":
     - "leveling"
     - "grounding"
     - "restore_string"

    "holding":
     - "calib"
     - "post_calib"
     - "drilling"
     - "tower_tilt"
     - "grounding"
     - "leveling"
     - "shaft_buildup"
     - "shaft_stow"
     - "wait_after_level"
     - "rod_lock/unlock"
     - "idle"
     - "restore_string"

    "final_leveling":
     - "leveling"
     - "restore_string"

  pulling_speed: 0.7
  pulling_speed_front: 0.8
  pulling_speed_rear: 1.0

  jacks_sync_p: 5

  max_jacks_speed: 1.0
#  touchdown_max_err: 0.01

#  level_extra_height: 0.03
#  pull_supress_k: 3

  pull_jacks_speed_p: 5
  pull_jacks_speed_i: 0.04
  pull_jacks_speed_d: 2
  pull_jacks_i_limit: 20

  max_level_speed: 0.7

  # Roll and pitch PID-regulator gains for leveling state

  leveling_roll_p: 9
  leveling_pitch_p: 8
  leveling_roll_i: 0.0
  leveling_pitch_i: 0.0
  leveling_roll_i_lim: 2.0
  leveling_pitch_i_lim: 2.0
  leveling_roll_d: 0
  leveling_pitch_d: 0

  # after the smallest element of the integral of
  # the jack controls exceeds this value, we stop
  # applying only positive controls
  leveling_ctrl_jacks_integral_min: 0.025
  common_lift_control: 0.2
  min_lift_height: 0.09

  final_level_ctrl: 0.25

  holding_dead_zone: 0.5 #deg
  holding_dead_zone_hist_low: 0.1 #deg
  holding_dead_zone_hist_high: 0.21 #deg
  required_stab_time: 3.0

TowerControllerNode:
  debug: true
  rate: 20

  # Режимы допускающие работу
  allowed_modes:
    - "tower_tilt"

  # Допустимые углы бурения
  allowed_angles:
    '0': 0 # 0°
    '5': 4.5
    '10': 9.5
    '15': 14.2

  # Допустимая ошибка по углу наклона
  allowed_tilt_error: 0.3

  # Минимальная величина на которую должен отличаться угол из задания от текущего угла
  min_angle_delta: 1

  # T1
  pins_move_time: 6

  # T2
  pins_no_reaction_time: 2

  # T3
  tilt_fixed_time: 3

  # Коэффициент для преобразования отклонения текущего угла наклона от целевого в скорость изменения угла
  tilt_error2speed: 0.3

  # Время стабилизации мачты секунды
  tilt_regulation_time: 40

  allow_inclined_with_no_feedback: False

MainStateMachineNode:
  debug: true
  rate: 10
  log_period: 2

  engine_idle_timeout: 30 # time in seconds to wait before low down engine rpm in Idle state
  engine_nopermission_timeout: 30 # time in seconds to wait before low down engine rpm when no permission
  low_rpm: 1200
  high_rpm: 1800

  use_z_depth_correction: True
  correction_limit: 3.0

  skip_moving: False
  skip_leveling: False
  skip_tower_tilt: False
  skip_drilling: False

  top_to_initial: 1.15 #m Hudbay 02

  allowed_string_drawdown: 0.3 #m
  allowed_string_exceeding: 0.45 #m
  allowed_string_drawdown_for_tower_tilt: 0.5

  wal_delay: 1.0
  wbl_delay: 4.0

  string_restore_speed: 0.08

  # Дополнительная глубина забуривания при бурении с наращиванием метры
  extra_depth: 0.2

  # Максимальное время ожидания подготовки машины (торможение и т.д) к переходу в ДУ секунду
  max_remote_prepare_waite_time: 3

  # Допустимая максимальная скорость движения для перехода в ДУ метры в секунду
  remote_prepare_max_speed: 0.05

  no_move_allowed_err: 0.2

  ignore_buildup_diff: 1.0

DrillerSimNode:
  rate: 100

  layer_thickness_mu: 2
  layer_thickness_sigma: 2

  layer_thickness_lower_bound: 1
  layer_thickness_upper_bound: 6

  # Мин. скорость бурения метры/секунду
  min_drilling_speed: 0.00028

  # Макс. давление подачи тонны/квадратный метр
  max_feed_pressure: 25

  # Макс. скорость вращения обороты/секунду
  max_rotation_speed: 2

  # Вероятность слоя замедления
  slowdown_layer_probability: 0.0

  # Вероятность изменения коэффициента замендления
  slowdown_change_prob: 0.5

  # Мин. период времени между изменениями коэффициента замедления на слое замедления секунды
  min_time_delta_slowdown_change: 1

  # Мин. период времени между возможными обвалами
  min_time_delta_landfall: 20

  # Вероятность обвала
  landfall_probability: 0.0

  # Коэффициент определяющий зависимость слоя осыпанию слоя обвала от перемещения инструмента
  upper2lower_k: 1

  # Коэффициент зависимости давления подачи от управляющей величины:
  feed_press_k: 25

  # Коэффициент зависимости скорости подачи от управляющей величины:
  feed_speed_k: 1

  # Коэффициент зависимости скорости вращения от управляющей величины:
  rot_speed_k: 1

  # Коэффициент зависимости давления вращения от скорости вращения:
  rot_speed2press_k: 100

  # Коэффициент зависимости давления вращения от замедления вращения:
  rot_dec2press_k: 500

  # Коэффициент зависимости давления вращения от слоя обвала:
  lf2rot_press_k: 6000

  # Коэффициент сглаживания в ФНЧ для расчета скорости вращения:
  low_pass_k: 0.05

  # Коэффициент пропорционально изменяющий мягкость всех слоев
  overall_softness_k: 0.01

DrillerNode:
  debug: true
  rate: 20
  log_period: 2

  user_feed_pressure: 186    # initial psp_regulated, controlled by RMO
  user_rotation_speed: 95

DrillRegulatorNode:
  rate: 20
  pdf_free_moving: 0.327 # pulldown force to move drill when not actually drilling
  default_rot_torque: 1.0 # rot torque limit to send when incoming torque ctrl is zero (most likely free moving)
  feed_reg:
    i_saturation: 1.0 # absolute max for I component
    p: 0.49 # P gain
    i: 0.60 # I gain
    d: 0.52 # D gain
    ff: 0.53 # FF gain. out = ctrl*FF + PID(ctrl, fb)
    min: -0.75 # max ctrl
    max: 0.56 # min ctrl
    out_min: -1 # max output
    out_max: 1 # min output
    d_term_tc: 0.6 # time constant to smootd D component
    out_tc: 0.1 # time constant to smooth output

  press_reg:
    i_saturation: 0.8
    p: 0.0002
    i: 0.00025
    d: 0.00
    ff: 0.0012
    min: 0
    max: 276
    out_min: 0
    out_max: 1.0
    d_term_tc: 0.2
    out_tc: 1.3
    one_side_i: True

#  # c НЕ работабщим счетчиком
#  rot_reg:
#    i_saturation: 0.3
#    p: 0
#    i: 0
#    d: 0
#    ff: 0.0068
#    min: -100
#    max: 100
#    out_min: -1
#    out_max: 1
#    d_term_tc: 0.2
#    out_tc: 0.1

  # с работающим счетчиком
  rot_reg:
    i_saturation: 0.8
    p: 0.0079 # 0.00135
    i: 0.0025 # 0.0022
    d: 0.0000 # 0.0001
    ff: 0.0027
    min: -130
    max: 130
    out_min: -1
    out_max: 1
    d_term_tc: 0.2
    out_tc: 0.55

JackRegulatorNode:
  rate: 20
  left:
    i_saturation: 1.0
    p: 0.5
    i: 0.75
    d: 0.35
    ff: 0.60
    min: -0.75
    max: 0.56
    out_min: -1
    out_max: 1
    d_term_tc: 0.6
    out_tc: 0.1
    force_zero: false

  right:
      i_saturation: 1.0
      p: 0.5
      i: 0.75
      d: 0.35
      ff: 0.60
      min: -0.75
      max: 0.56
      out_min: -1
      out_max: 1
      d_term_tc: 0.6
      out_tc: 0.1
      force_zero: false

  rear:
      i_saturation: 1.0
      p: 0.5
      i: 0.75
      d: 0.35
      ff: 0.60
      min: -0.75
      max: 0.56
      out_min: -1
      out_max: 1
      d_term_tc: 0.6
      out_tc: 0.1
      force_zero: false

  max_ls_timeout: 0.5

KobusAdapterNode:
  rate: 30
  kobus_address: "************"
  kobus_port: 9753
  robot_address: "0.0.0.0"
  robot_port: 9755
  kobus_company: "vist"
  kobus_password: "HiKobus"
  stream_interval: 100
  sensor_pack_len: 16
  max_package_size: 1400

  # Стартовая высота положения вращателя метры
  depth_start: 3.637
  top_to_calib: 3.748 #m


  allowed_reset_depth_error: 40 # milimeters!!!
  reset_depth_min_delta: 50
  reset_depth_max_delta: 300

  # Максимальное допустимое изменение обновления координат метры
  max_pos_change: 0.2

  # Максимальное допустимое измение обновления координат градусы
  max_yaw_change: 5

  sensors:
    #абсолютное давление масла в патрубке манометра давления привода вращателя (атм)
    rot_press: 501
    # Абсолютное давление масла в патрубке манометра давления привода подачи (атм)
    feed_press: 500
    # Абсолютное давление водно-воздушной смеси в патрубке манометра давления продувки (атм)
    air_press: 502
    # Качество получения координат приемника спутниковых навигационных
    # сигналов (0 = нет координат, 4 = FIX, 1 = standalone, остальное float/dgps и прочее)
    position_mode: 307
    # Высокоточные текущие локальные координаты бурового става в плане (метры).
    # Расположение осей: x - восток, y – север.
    position_x: 300
    position_y: 301
    position_z: 302
    # Разворот бурового станка на местности (азимут, град)
    # (направление на север, положительное по часовой стрелке)
    # (север=0, восток=90, юг=180, запад=-90 или 270)
    position_yaw: 303
    # Относительное значение датчика глубины с момента получения сигнала
    # обнуления (сброс по началу новой скважины), точность ~10 см.
    relative_depth: 525
    realtime: 10010
    SidPlatformAngleCross: 104 #угол поперечного наклона платформы (град.)
    SidPlatformAngleLong: 103 #угол продольного наклона платформы (град.) с частотой опроса датчика 10 Гц
    SidTowerAngleCross: 102 #угол поперечного наклона мачты (град.)
    SidTowerAngleLong: 101 #угол поперечного наклона мачты (град.)
    SidTemperatureOut: 512 #температура окружающего воздуха под станком (град. Цельсия)
    # Скорость вращения бурового става, об/мин
    rotation_speed: 508
  # Макс. время ожидания изменения глубины става
  depth_change_ttl: 5

  force_invalid_tower: False

ModbusNode:
  rate: 10
  method: 'rtu'
  port: "/dev/ttyXR1"
  baudrate: 115200

  IOs:
    robomode_read:
      unit: 0x0A
      address: 0
      count: 32
      type: "di"

#    HR_Ad1:
#      unit: 0x0A
#      address: 0
#      count: 3
#      type: "hr"

    robomode_set:
      unit: 0x0A
      address: 0
      count: 32
      type: "co"

    lamp_control:
      unit: 0x12
      address: 0
      count: 8
      type: "co"

    emergency_control:
      unit: 0x30
      address: 0
      count: 2
      type: "co"

    emergency_status:
      unit: 0x30
      address: 0
      count: 1
      type: "di"

  output_topics: # выходы из ноды, приём от устройства, паблишеры
#    setting_1: # имя топика
#      msg_type: "std_msgs/Int32"
#      fields:
#        data:
#          io: "HR_Ad1"
#          word: 0 # номер канала

    selector_robomode_fb: # имя топика
      msg_type: "drill_msgs/BoolStamped"
      fields:
        value:
          io: "robomode_read"
          word: 0 # номер канала

    emergency_status: # имя топика
      msg_type: "drill_msgs/BoolStamped"
      fields:
        value:
          io: "emergency_status"
          word: 0 # номер канала


  input_topics: # входы в ноду, выход в устройство, сабскрайберы
#    setup_1: # имя топика
#      msg_type: "std_msgs/Int32"
#      timeout: 10000000
#      fields:
#        data:
#          io: "HR_Ad1"
#          word: 0 # номер канала
#          latch: False
#          default: 0x0A

    selector_robomode_sp: # имя топика
      msg_type: "drill_msgs/BoolStamped"
      timeout: 0 # disable reset to default
      fields:
        value:
          io: "robomode_set"
          word: 'ALL' # номер канала
          latch: False
          default: False

    emergency_control: # имя топика
      msg_type: "drill_msgs/EmergencyCtrl"
      timeout: 0 # disable reset to default
      fields:
        set:
          io: "emergency_control"
          word: 0 # номер канала
          latch: False
          default: False
        reset:
          io: "emergency_control"
          word: 1 # номер канала
          latch: False
          default: False

    lamp_control: # имя топика
      msg_type: "drill_msgs/LampCtrl"
      timeout: 0 # disable reset to default
      fields:
        front_red:
          io: "lamp_control"
          word: 1 # номер канала
          latch: False
          default: False
        front_blue:
          io: "lamp_control"
          word: 2 # номер канала
          latch: False
          default: False
        front_yellow:
          io: "lamp_control"
          word: 0 # номер канала
          latch: False
          default: False

        rear_red:
          io: "lamp_control"
          word: 4 # номер канала
          latch: False
          default: False
        rear_blue:
            io: "lamp_control"
            word: 3 # номер канала
            latch: False
            default: False
        rear_yellow:
            io: "lamp_control"
            word: 5 # номер канала
            latch: False
            default: False

CanNode:
  rate: 40

  IOs:
    angle:
      can_channel: '/can_6/tx'
      value_field: 'angle'
      protocol: 'inc1'
      source_id: 0xcf

    angle_x:
      can_channel: '/can_6/tx'
      value_field: 'angle_x'
      protocol: 'inc2'
      source_id: 0xce

    angle_y:
      can_channel: '/can_6/tx'
      value_field: 'angle_y'
      protocol: 'inc2'
      source_id: 0xce

    front_imu_pitch:
      can_channel: '/can_1/tx'
      value_field: 'pitch'
      protocol: 'ssi2'
      source_id: 131 # поменять/проверить!

    front_imu_roll:
      can_channel: '/can_1/tx'
      value_field: 'roll'
      protocol: 'ssi2'
      source_id: 131 # поменять/проверить!

    front_imu_acceleration_x:
      can_channel: '/can_1/tx'
      value_field: 'acceleration_x'
      protocol: 'adp'
      source_id: 131 # поменять/проверить!

    front_imu_acceleration_y:
      can_channel: '/can_1/tx'
      value_field: 'acceleration_y'
      protocol: 'adp'
      source_id: 131 # поменять/проверить!

    front_imu_acceleration_z:
      can_channel: '/can_1/tx'
      value_field: 'acceleration_z'
      protocol: 'adp'
      source_id: 131 # поменять/проверить!

    front_imu_magnetic_field_x:
      can_channel: '/can_1/tx'
      value_field: 'magnetic_field_x'
      protocol: 'mdp'
      source_id: 131 # поменять/проверить!

    front_imu_magnetic_field_y:
      can_channel: '/can_1/tx'
      value_field: 'magnetic_field_y'
      protocol: 'mdp'
      source_id: 131 # поменять/проверить!

    front_imu_magnetic_field_z:
      can_channel: '/can_1/tx'
      value_field: 'magnetic_field_z'
      protocol: 'mdp'
      source_id: 131 # поменять/проверить!

    front_imu_angular_rate_x:
      can_channel: '/can_1/tx'
      value_field: 'angular_rate_x'
      protocol: 'ardp'
      source_id: 131 # поменять/проверить!

    front_imu_angular_rate_y:
      can_channel: '/can_1/tx'
      value_field: 'angular_rate_y'
      protocol: 'ardp'
      source_id: 131 # поменять/проверить!

    front_imu_angular_rate_z:
      can_channel: '/can_1/tx'
      value_field: 'angular_rate_z'
      protocol: 'ardp'
      source_id: 131 # поменять/проверить!

    tower_imu_pitch:
      can_channel: '/can_6/tx'
      value_field: 'pitch'
      protocol: 'ssi2'
      source_id: 130 # поменять/проверить!

    tower_imu_roll:
      can_channel: '/can_6/tx'
      value_field: 'roll'
      protocol: 'ssi2'
      source_id: 130 # поменять/проверить!

    tower_imu_acceleration_x:
      can_channel: '/can_6/tx'
      value_field: 'acceleration_x'
      protocol: 'adp'
      source_id: 130 # поменять/проверить!

    tower_imu_acceleration_y:
      can_channel: '/can_6/tx'
      value_field: 'acceleration_y'
      protocol: 'adp'
      source_id: 130 # поменять/проверить!

    tower_imu_acceleration_z:
      can_channel: '/can_6/tx'
      value_field: 'acceleration_z'
      protocol: 'adp'
      source_id: 130 # поменять/проверить!

    tower_imu_magnetic_field_x:
      can_channel: '/can_6/tx'
      value_field: 'magnetic_field_x'
      protocol: 'mdp'
      source_id: 130 # поменять/проверить!

    tower_imu_magnetic_field_y:
      can_channel: '/can_6/tx'
      value_field: 'magnetic_field_y'
      protocol: 'mdp'
      source_id: 130 # поменять/проверить!

    tower_imu_magnetic_field_z:
      can_channel: '/can_6/tx'
      value_field: 'magnetic_field_z'
      protocol: 'mdp'
      source_id: 130 # поменять/проверить!

    tower_imu_angular_rate_x:
      can_channel: '/can_6/tx'
      value_field: 'angular_rate_x'
      protocol: 'ardp'
      source_id: 130 # поменять/проверить!

    tower_imu_angular_rate_y:
      can_channel: '/can_6/tx'
      value_field: 'angular_rate_y'
      protocol: 'ardp'
      source_id: 130 # поменять/проверить!

    tower_imu_angular_rate_z:
      can_channel: '/can_6/tx'
      value_field: 'angular_rate_z'
      protocol: 'ardp'
      source_id: 130 # поменять/проверить!

    back_imu_pitch:
      can_channel: '/can_1/tx'
      value_field: 'pitch'
      protocol: 'ssi2'
      source_id: 129 # поменять/проверить!

    back_imu_roll:
      can_channel: '/can_1/tx'
      value_field: 'roll'
      protocol: 'ssi2'
      source_id: 129 # поменять/проверить!

    back_imu_acceleration_x:
      can_channel: '/can_1/tx'
      value_field: 'acceleration_x'
      protocol: 'adp'
      source_id: 129 # поменять/проверить!

    back_imu_acceleration_y:
      can_channel: '/can_1/tx'
      value_field: 'acceleration_y'
      protocol: 'adp'
      source_id: 129 # поменять/проверить!

    back_imu_acceleration_z:
      can_channel: '/can_1/tx'
      value_field: 'acceleration_z'
      protocol: 'adp'
      source_id: 129 # поменять/проверить!

    back_imu_magnetic_field_x:
      can_channel: '/can_1/tx'
      value_field: 'magnetic_field_x'
      protocol: 'mdp'
      source_id: 129 # поменять/проверить!

    back_imu_magnetic_field_y:
      can_channel: '/can_1/tx'
      value_field: 'magnetic_field_y'
      protocol: 'mdp'
      source_id: 129 # поменять/проверить!

    back_imu_magnetic_field_z:
      can_channel: '/can_1/tx'
      value_field: 'magnetic_field_z'
      protocol: 'mdp'
      source_id: 129 # поменять/проверить!

    back_imu_angular_rate_x:
      can_channel: '/can_1/tx'
      value_field: 'angular_rate_x'
      protocol: 'ardp'
      source_id: 129 # поменять/проверить!

    back_imu_angular_rate_y:
      can_channel: '/can_1/tx'
      value_field: 'angular_rate_y'
      protocol: 'ardp'
      source_id: 129 # поменять/проверить!

    back_imu_angular_rate_z:
      can_channel: '/can_1/tx'
      value_field: 'angular_rate_z'
      protocol: 'ardp'
      source_id: 129 # поменять/проверить!

    accel_pedal:
      can_channel: '/can_2/tx'
      value_field: 'accel_pedal'
      protocol: 'eec2'
      source_id: 0x00

    engine_load:
      can_channel: '/can_2/tx'
      value_field: 'engine_load'
      protocol: 'eec2'
      source_id: 0x00

    demand_torque:
      can_channel: '/can_2/tx'
      value_field: 'demand_torque'
      protocol: 'eec1'
      source_id: 0x00

    actual_torque:
      can_channel: '/can_2/tx'
      value_field: 'actual_torque'
      protocol: 'eec1'
      source_id: 0x00

    engine_speed:
      can_channel: '/can_2/tx'
      value_field: 'engine_speed'
      protocol: 'eec1'
      source_id: 0x00

    nominal_friction:
      can_channel: '/can_2/tx'
      value_field: 'nominal_friction'
      protocol: 'eec3'
      source_id: 0x00

    desired_engine_speed:
      can_channel: '/can_2/tx'
      value_field: 'desired_engine_speed'
      protocol: 'eec3'
      source_id: 0x00

    engine_hours:
      can_channel: '/can_2/tx'
      value_field: 'engine_hours'
      protocol: 'hours'
      source_id: 0x00

    engine_total_fuel_used:
      can_channel: '/can_2/tx'
      value_field: 'engine_total_fuel_used'
      protocol: 'lfc'
      source_id: 0x00

    engine_fuel_rate:
      can_channel: '/can_2/tx'
      value_field: 'engine_fuel_rate'
      protocol: 'lfe1'
      source_id: 0x00

    battery_potential:
      can_channel: '/can_2/tx'
      value_field: 'battery_potential'
      protocol: 'vep1'
      source_id: 0x00

    keyswitch_potential:
      can_channel: '/can_2/tx'
      value_field: 'keyswitch_potential'
      protocol: 'vep1'
      source_id: 0x00

    coolant_temperature:
      can_channel: '/can_2/tx'
      value_field: 'engine_coolant_temperature'
      protocol: 'et1'
      source_id: 0x00

    engine_fuel_temperature:
      can_channel: '/can_2/tx'
      value_field: 'engine_fuel_temperature'
      protocol: 'et1'
      source_id: 0x00

    engine_fuel_delivery_pressure:
      can_channel: '/can_2/tx'
      value_field: 'engine_fuel_delivery_pressure'
      protocol: 'eflp1'
      source_id: 0x00

    engine_oil_pressure:
      can_channel: '/can_2/tx'
      value_field: 'engine_oil_pressure'
      protocol: 'eflp1'
      source_id: 0x00

    engine_coolant_level:
      can_channel: '/can_2/tx'
      value_field: 'engine_coolant_level'
      protocol: 'eflp1'
      source_id: 0x00

    arm_open:
      can_channel: '/can_5/tx'
      value_field: 'arm_o'
      protocol: 'x90ArmData'

    arm_closed:
      can_channel: '/can_5/tx'
      value_field: 'arm_c'
      protocol: 'x90ArmData'

    arm_grip:
      can_channel: '/can_5/tx'
      value_field: 'arm_g'
      protocol: 'x90ArmData'

    arm_stage_1:
      can_channel: '/can_5/tx'
      value_field: 'stage_1'
      protocol: 'x90ArmData'

    arm_stage_2:
      can_channel: '/can_5/tx'
      value_field: 'stage_2'
      protocol: 'x90ArmData'

    carousel_open:
      can_channel: '/can_5/tx'
      value_field: 'carousel_o'
      protocol: 'x90CarouselData'

    carousel_closed:
      can_channel: '/can_5/tx'
      value_field: 'carousel_c'
      protocol: 'x90CarouselData'

    carousel_index_1:
      can_channel: '/can_5/tx'
      value_field: 'carousel_1'
      protocol: 'x90CarouselData'

    carousel_index_2:
      can_channel: '/can_5/tx'
      value_field: 'carousel_2'
      protocol: 'x90CarouselData'

    carousel_main_len:
      can_channel: '/can_5/tx'
      value_field: 'carousel_main_axis_len'
      protocol: 'x90CarouselData'

    carousel_index_len:
      can_channel: '/can_5/tx'
      value_field: 'carousel_index_axis_len'
      protocol: 'x90CarouselData'

    carousel_cup_1:
      can_channel: '/can_5/tx'
      value_field: 'bar_dt_1'
      protocol: 'x90BarData'

    carousel_cup_2:
      can_channel: '/can_5/tx'
      value_field: 'bar_dt_2'
      protocol: 'x90BarData'

    bracket_left:
      can_channel: '/can_5/tx'
      value_field: 'bracket_left'
      protocol: 'x90BracketData'

    bracket_right:
      can_channel: '/can_5/tx'
      value_field: 'bracket_right'
      protocol: 'x90BracketData'

    bracket_dt_2:
      can_channel: '/can_5/tx'
      value_field: 'bracket_dt_2'
      protocol: 'x90BracketData'

    jack_rear_left_len:
      can_channel: '/can_5/tx'
      value_field: 'uls_1'
      protocol: 'x90UlsJacks'

    jack_rear_right_len:
      can_channel: '/can_5/tx'
      value_field: 'uls_2'
      protocol: 'x90UlsJacks'

    jack_left_len:
      can_channel: '/can_5/tx'
      value_field: 'uls_3'
      protocol: 'x90UlsJacks'

    jack_right_len:
      can_channel: '/can_5/tx'
      value_field: 'uls_4'
      protocol: 'x90UlsJacks'

    jack_b_1:
      can_channel: '/can_5/tx'
      value_field: 'jack_b_1'
      protocol: 'x90JacksData'

    jack_b_2:
      can_channel: '/can_5/tx'
      value_field: 'jack_b_2'
      protocol: 'x90JacksData'

    jack_f_1:
      can_channel: '/can_5/tx'
      value_field: 'jack_f_1'
      protocol: 'x90JacksData'

    jack_f_2:
      can_channel: '/can_5/tx'
      value_field: 'jack_f_2'
      protocol: 'x90JacksData'

    jack_b_1_ground:
      can_channel: '/can_5/tx'
      value_field: 'jack_b_1_ground'
      protocol: 'x90JacksData'
      inverse: False

    jack_b_2_ground:
      can_channel: '/can_5/tx'
      value_field: 'jack_b_2_ground'
      protocol: 'x90JacksData'
      inverse: False

    jack_f_1_ground:
      can_channel: '/can_5/tx'
      value_field: 'jack_f_1_ground'
      protocol: 'x90JacksData'

    jack_f_2_ground:
      can_channel: '/can_5/tx'
      value_field: 'jack_f_2_ground'
      protocol: 'x90JacksData'

    rotation_pressure:
      can_channel: '/can_5/tx'
      value_field: 'pressure_rotate'
      protocol: 'x90Prssr'

    feed_pressure:
      can_channel: '/can_5/tx'
      value_field: 'feed_pressure'
      protocol: 'x90Prssr'

    air_pressure:
      can_channel: '/can_5/tx'
      value_field: 'pressure_air'
      protocol: 'x90Prssr'

    vert_pin_cs_c:
      can_channel: '/can_5/tx'
      value_field: 'vert_pin_cs_c'
      protocol: 'x90PinsData'

    vert_pin_cs_o:
      can_channel: '/can_5/tx'
      value_field: 'vert_pin_cs_o'
      protocol: 'x90PinsData'

    vert_pin_ncs_c:
      can_channel: '/can_5/tx'
      value_field: 'vert_pin_ncs_c'
      protocol: 'x90PinsData'

    vert_pin_ncs_o:
      can_channel: '/can_5/tx'
      value_field: 'vert_pin_ncs_o'
      protocol: 'x90PinsData'

    tilt_pins_c:
      can_channel: '/can_5/tx'
      value_field: 'tilt_pins_c'
      protocol: 'x90PinsData'

    tilt_pins_o:
      can_channel: '/can_5/tx'
      value_field: 'tilt_pins_o'
      protocol: 'x90PinsData'

    laser:
      can_channel: '/can_5/tx'
      value_field: 'laser'
      protocol: 'x90Encoders'

    dust_flaps_c:
      can_channel: '/can_5/tx'
      value_field: 'dust_flaps_dt_2'
      protocol: 'x90DustFlaps'

    dust_flaps_o:
      can_channel: '/can_5/tx'
      value_field: 'dust_flaps_dt_1'
      protocol: 'x90DustFlaps'

    wrench_dt_1:
      can_channel: '/can_5/tx'
      value_field: 'wrench_dt_1'
      protocol: 'x90WrenchData'

    wrench_dt_2:
      can_channel: '/can_5/tx'
      value_field: 'wrench_dt_2'
      protocol: 'x90WrenchData'

    wrench_dt_3:
      can_channel: '/can_5/tx'
      value_field: 'wrench_dt_3'
      protocol: 'x90WrenchData'

    arm_ctrl_data:
        can_channel: '/can_5/rx'
        value_field: 'arm'
        protocol: 'x90ArmCtrl'
        min_value: -250
        max_value: 300
        deadband_value_pos: 235
        deadband_value_neg: -200
        deadband_threshold: 0.001

    fork_ctrl_data:
        can_channel: '/can_5/rx'
        value_field: 'bracket'
        protocol: 'x90BracketCtrl'
        min_value: -1000
        max_value: 1000
        deadband_value_pos: 100
        deadband_value_neg: -100
        deadband_threshold: 0.001

    rear_jack_left:
        can_channel: '/can_5/rx'
        value_field: 'jack_left'
        protocol: 'x90JacksBackCtrl'
        min_value: -450
        max_value: 550
        deadband_value_pos: 305
        deadband_value_neg: -305
        deadband_threshold: 0.001

    front_jack_left:
        can_channel: '/can_5/rx'
        value_field: 'jack_left'
        protocol: 'x90JacksFrontCtrl'
        min_value: -500
        max_value: 560
        deadband_value_pos: 318
        deadband_value_neg: -318
        deadband_threshold: 0.001

    front_jack_right:
        can_channel: '/can_5/rx'
        value_field: 'jack_right'
        protocol: 'x90JacksFrontCtrl'
        min_value: -470
        max_value: 500
        deadband_value_pos: 313
        deadband_value_neg: -313
        deadband_threshold: 0.001

    cat_left:
      can_channel: '/can_5/rx'
      value_field: 'cat_left'
      protocol: 'x90CatCtrl'
      min_value: -678
      max_value: 738
      polynomial_pos: [0.0, 1.76651, -3.34973, 2.58322]
      polynomial_neg: [0.0, 1.34466, -1.76995, 1.42529]
      deadband_value_pos: 326
      deadband_value_neg: -344
      deadband_threshold: 0.001

    cat_right:
      can_channel: '/can_5/rx'
      value_field: 'cat_right'
      protocol: 'x90CatCtrl'
      min_value: -735
      max_value: 770
      polynomial_pos: [0.0, 1.32475, -1.82612, 1.50137]
      polynomial_neg: [0.0, 1.01008, -0.619589, 0.60951]
      deadband_value_pos: 463
      deadband_value_neg: -404
      deadband_threshold: 0.001

    carousel_move:
      can_channel: '/can_5/rx'
      value_field: 'carousel'
      protocol: 'x90CarouselCtrl'
      min_value: -1000
      max_value: 1000
      deadband_value_pos: 1
      deadband_value_neg: -1
      deadband_threshold: 0.001

    carousel_rotate:
      can_channel: '/can_5/rx'
      value_field: 'carousel_rot'
      protocol: 'x90CarouselCtrl'
      min_value: -1000
      max_value: 1000
      deadband_value_pos: 100
      deadband_value_neg: -100
      deadband_threshold: 0.001

    vert_pin:
      can_channel: '/can_5/rx'
      value_field: 'vert_pin'
      protocol: 'x90PinsCtrl'
      min_value: -32768
      max_value: 32767

    tilt_pin:
      can_channel: '/can_5/rx'
      value_field: 'tilt_pin'
      protocol: 'x90PinsCtrl'
      min_value: -32768
      max_value: 32767

    epiroc_roll:
      can_channel: '/can_5/tx'
      value_field: 'roll'
      protocol: 'x90MastLvl'

    epiroc_pitch:
      can_channel: '/can_5/tx'
      value_field: 'pitch'
      protocol: 'x90MastLvl'

    rotate_velocity:
      can_channel: '/can_5/tx'
      value_field: 'rotate_velocity'
      protocol: 'x90MastLvl'
#      min_value: -1000
#      max_value: 1000
#      deadband_value_pos: 1
#      deadband_value_neg: -1
#      deadband_threshold: 0.001

    mast:
      can_channel: '/can_5/rx'
      value_field: 'mast'
      protocol: 'x90MastCtrl'
      min_value: -1000
      max_value: 1000
      deadband_value_pos: 1
      deadband_value_neg: -1
      deadband_threshold: 0.001

    wrench_grip:
      can_channel: '/can_5/rx'
      value_field: 'wrench'
      protocol: 'x90WrenchCtrl'
      min_value: -32768
      max_value: 32767

    wrench_move:
      can_channel: '/can_5/rx'
      value_field: 'wrench2'
      protocol: 'x90WrenchCtrl'
      min_value: -350
      max_value: 350
      deadband_value_pos: 277
      deadband_value_neg: -275
      deadband_threshold: 0.001

    water_level:
      can_channel: '/can_5/tx'
      value_field: 'water_level'
      protocol: 'x90WaterData'

    fuel_level:
      can_channel: '/can_5/tx'
      value_field: 'level_fuel'
      protocol: 'x90WaterData'

    water_injection:
      can_channel: '/can_5/rx'
      value_field: 'water_injection'
      protocol: 'x90HooverRelayCtrl'
      min_value: 0
      max_value: 500
      deadband_value_pos: 240
      deadband_threshold: 0.001

    driller_relay:
      can_channel: '/can_5/rx'
      value_field: 'driller_relay'
      protocol: 'x90HooverRelayCtrl'

    compressor_relay:
      can_channel: '/can_2/rx'
      value_field: 'compressor_relay'
      protocol: 'compressor'

    compressor_power:
      can_channel: '/can_2/rx'
      value_field: 'compressor_power'
      protocol: 'compressor'
      min_value: 0
      max_value: 100

    rot:
      can_channel: '/can_5/rx'
      value_field: 'rot'
      protocol: 'x90RotSupCtrl'
      min_value: -668
      max_value: 649
      deadband_value_pos: 403
      deadband_value_neg: -407
      deadband_threshold: 0.001

    feed:
      can_channel: '/can_5/rx'
      value_field: 'feed'
      protocol: 'x90RotSupCtrl'
      min_value: -586
      max_value: 629
#      polynomial_pos: [0.0, 2.01, -1.4, 0.39]
#      polynomial_neg: [0.0, 2.01, -1.4, 0.39]
      deadband_value_pos: 331
      deadband_value_neg: -343
      deadband_threshold: 0.001

    dust_flaps:
      can_channel: '/can_5/rx'
      value_field: 'dust_flaps'
      protocol: 'x90DustFlapsCtrl'
      min_value: -32768
      max_value: 32767
      deadband_value_pos: 100
      deadband_value_neg: -100
      deadband_threshold: 0.001

    feed_press:
      can_channel: '/can_5/rx'
      value_field: 'feed'
      protocol: 'x90FanSuppCtrl'
      min_value: 10
#      max_value: 225 --> 250 --> 275
      max_value: 312
      deadband_value_pos: 1
      deadband_threshold: 0.001

    fan_speed:
      can_channel: '/can_5/rx'
      value_field: 'fan'
      protocol: 'x90FanSuppCtrl'
      min_value: 0
      max_value: 1000
      deadband_value_pos: 100
      deadband_threshold: 0.001

    rot_torque:
      can_channel: '/can_5/rx'
      value_field: 'rotation'
      protocol: 'x90FanSuppCtrl'
      min_value: 1
      max_value: 880
      deadband_value_pos: 300
      deadband_threshold: 0.001

    rot_angle:
      can_channel: '/can_5/tx'
      value_field: 'encoder_1'
      protocol: 'x90Encoders'

  output_topics: # выходы из ноды, приём от устройства, паблишеры
    tower_inc_angle: # имя топика
      msg_type: "drill_msgs/IncAngles"
      period: 5
      rate: 10
      fields:
        angle_x:
          io: "angle"

    board_inc_angles:
      msg_type: "drill_msgs/IncAngles"
      period: 5
      rate: 10
      fields:
        angle_x:
          io: "angle_x"
        angle_y:
          io: "angle_y"

    front_imu_data:
      msg_type: "drill_msgs/ImuData"
      period: 5
      rate: 10
      fields:
        roll:
          io: "front_imu_roll"
        pitch:
          io: "front_imu_pitch"
        acceleration.x:
          io: "front_imu_acceleration_x"
        acceleration.y:
          io: "front_imu_acceleration_y"
        acceleration.z:
          io: "front_imu_acceleration_z"
        magnetic_field.x:
          io: "front_imu_magnetic_field_x"
        magnetic_field.y:
          io: "front_imu_magnetic_field_y"
        magnetic_field.z:
          io: "front_imu_magnetic_field_z"
        angular_rate.x:
          io: "front_imu_angular_rate_x"
        angular_rate.y:
          io: "front_imu_angular_rate_y"
        angular_rate.z:
          io: "front_imu_angular_rate_z"

    tower_imu_data:
      msg_type: "drill_msgs/ImuData"
      period: 5
      rate: 10
      fields:
        roll:
          io: "tower_imu_roll"
        pitch:
          io: "tower_imu_pitch"
        acceleration.x:
          io: "tower_imu_acceleration_x"
        acceleration.y:
          io: "tower_imu_acceleration_y"
        acceleration.z:
          io: "tower_imu_acceleration_z"
        magnetic_field.x:
          io: "tower_imu_magnetic_field_x"
        magnetic_field.y:
          io: "tower_imu_magnetic_field_y"
        magnetic_field.z:
          io: "tower_imu_magnetic_field_z"
        angular_rate.x:
          io: "tower_imu_angular_rate_x"
        angular_rate.y:
          io: "tower_imu_angular_rate_y"
        angular_rate.z:
          io: "tower_imu_angular_rate_z"

    back_imu_data:
      msg_type: "drill_msgs/ImuData"
      period: 5
      rate: 10
      fields:
        roll:
          io: "back_imu_roll"
        pitch:
          io: "back_imu_pitch"
        acceleration.x:
          io: "back_imu_acceleration_x"
        acceleration.y:
          io: "back_imu_acceleration_y"
        acceleration.z:
          io: "back_imu_acceleration_z"
        magnetic_field.x:
          io: "back_imu_magnetic_field_x"
        magnetic_field.y:
          io: "back_imu_magnetic_field_y"
        magnetic_field.z:
          io: "back_imu_magnetic_field_z"
        angular_rate.x:
          io: "back_imu_angular_rate_x"
        angular_rate.y:
          io: "back_imu_angular_rate_y"
        angular_rate.z:
          io: "back_imu_angular_rate_z"

    epiroc_inclination_raw:
      msg_type: "drill_msgs/ImuData"
      period: 5
      rate: 10
      fields:
        roll:
          io: "epiroc_roll"
        pitch:
          io: "epiroc_pitch"

    head_rangefinder:
      msg_type: "drill_msgs/FloatStamped"
      timeout: 10
      period: 5
      fields:
        value:
          io: 'laser'

    water_level_raw:
      msg_type: "drill_msgs/FloatStamped"
      timeout: 10
      period: 5
      fields:
        value:
          io: 'water_level'

    fuel_level_raw:
      msg_type: "drill_msgs/FloatStamped"
      timeout: 10
      period: 5
      fields:
        value:
          io: 'fuel_level'

    rotation_speed:
      msg_type: "drill_msgs/FloatStamped"
      timeout: 10
      period: 5
      fields:
        value:
          io: 'rotate_velocity'

    rotation_angle:
      msg_type: "drill_msgs/FloatStamped"
      timeout: 10
      period: 5
      fields:
        value:
          io: 'rot_angle'

    dust_flaps_switch_state:
      msg_type: "drill_msgs/DustFlapsSwitchState"
      timeout: 10
      period: 5
      fields:
        open:
          io: 'dust_flaps_o'
        closed:
          io: 'dust_flaps_c'

    wrench_switch_state_raw:
      msg_type: "drill_msgs/WrenchSwitchState"
      timeout: 10
      period: 5
      fields:
        stage_len_1:
          io: 'wrench_dt_1'
        stage_len_2:
          io: 'wrench_dt_2'
        stage_len_3:
          io: 'wrench_dt_3'

    tower_switch_state_raw:
      msg_type: "drill_msgs/TowerSwitchState"
      timeout: 10
      period: 5
      fields:
        ncs_vertical_switch_release:
          io: 'vert_pin_ncs_o'
        ncs_vertical_switch_lock:
          io: 'vert_pin_ncs_c'
        cs_vertical_switch_release:
          io: 'vert_pin_cs_o'
        cs_vertical_switch_lock:
          io: 'vert_pin_cs_c'

    arm_switch_state_raw:
      msg_type: "drill_msgs/ArmState"
      timeout: 10
      period: 5
      fields:
        open:
          io: 'arm_open'
        closed:
          io: 'arm_closed'
        grip:
          io: 'arm_grip'
        stage_1:
          io: 'arm_stage_1'
        stage_2:
          io: 'arm_stage_2'

    carousel_switch_state_raw:
      msg_type: "drill_msgs/CarouselSwitchState"
      timeout: 10
      period: 5
      fields:
        open:
          io: 'carousel_open'
        closed:
          io: 'carousel_closed'
        index_1:
          io: 'carousel_index_1'
        index_2:
          io: 'carousel_index_2'
        cup_1:
          io: 'carousel_cup_1'
        cup_2:
          io: 'carousel_cup_2'
        main_len:
          io: 'carousel_main_len'
        index_len:
          io: 'carousel_index_len'

    fork_switch_state_raw:
      msg_type: "drill_msgs/ForkSwitchState"
      timeout: 10
      period: 5
      fields:
        ccw_switch_on:
          io: 'bracket_left'
        cw_switch_on:
          io: 'bracket_right'
        length:
          io: 'bracket_dt_2'

    jacks_state_raw:
      msg_type: "drill_msgs/JacksState"
      timeout: 10
      period: 5
      fields:
        rear_left_len:
          io: 'jack_rear_left_len'
        rear_right_len:
          io: 'jack_rear_right_len'
        left_len:
          io: 'jack_left_len'
        right_len:
          io: 'jack_right_len'
        rear_left:
          io: 'jack_b_1'
        rear_right:
          io: 'jack_b_2'
        left:
          io: 'jack_f_1'
        right:
          io: 'jack_f_2'
        rear_left_on_ground:
          io: 'jack_b_2_ground'
        rear_right_on_ground:
          io: 'jack_b_1_ground'
        left_on_ground:
          io: 'jack_f_1_ground'
        right_on_ground:
          io: 'jack_f_2_ground'

    pressure_state_raw:
      msg_type: "drill_msgs/PressureState"
      timeout: 10
      period: 5
      fields:
        rotation_pressure:
          io: 'rotation_pressure'
        feed_pressure:
          io: 'feed_pressure'
        air_pressure:
          io: 'air_pressure'

    engine_state:
      msg_type: "drill_msgs/EngineState"
      timeout: 10
      period: 5
      fields:
        accel_pedal:
          io: "accel_pedal"
        engine_load:
          io: "engine_load"
        demand_torque:
          io: "demand_torque"
        actual_torque:
          io: "actual_torque"
        engine_speed:
          io: "engine_speed"
        nominal_friction:
          io: "nominal_friction"
        desired_engine_speed:
          io: "desired_engine_speed"
        engine_hours:
          io: "engine_hours"
        engine_total_fuel_used:
          io: "engine_total_fuel_used"
        engine_fuel_rate:
          io: "engine_fuel_rate"
        battery_potential:
          io: "battery_potential"
        keyswitch_potential:
          io: "keyswitch_potential"
        coolant_temperature:
          io: "coolant_temperature"
        engine_fuel_temperature:
          io: "engine_fuel_temperature"
        engine_fuel_delivery_pressure:
          io: "engine_fuel_delivery_pressure"
        engine_oil_pressure:
          io: "engine_oil_pressure"
          min_value: 0
          max_value: 1000
        engine_coolant_level:
          io: "engine_coolant_level"
          min_value: 0
          max_value: 1000

  input_topics: # входы в ноду, выход в устройство, сабскрайберы

    jacks_control:
      msg_type: "drill_msgs/JacksCtrlStamped"
      timeout: 0.5
      fields:
        rear:
          io: 'rear_jack_left'
          default: 0
          min_value: -1
          max_value: 1
        left:
          io: 'front_jack_left'
          default: 0
          min_value: -1
          max_value: 1
        right:
          io: 'front_jack_right'
          default: 0
          min_value: -1
          max_value: 1

    fan_control:
      msg_type: "drill_msgs/FloatStamped"
      timeout: 0.5
      ignore_move_permission: False
      fields:
        value:
          io: 'fan_speed'
          default: 0
          min_value: -1
          max_value: 1

    arm_control:
      msg_type: "drill_msgs/FloatStamped"
      timeout: 0.5
      fields:
        value:
          io: 'arm_ctrl_data'
          default: 0
          min_value: -1
          max_value: 1

    fork_control:
      msg_type: "drill_msgs/FloatStamped"
      timeout: 0.5
      fields:
        value:
          io: 'fork_ctrl_data'
          default: 0
          min_value: -1
          max_value: 1

    cats_control:
      msg_type: "drill_msgs/CatsStamped"
      timeout: 0.5
      fields:
        cat_left:
          io: 'cat_left'
          default: 0
          min_value: -1
          max_value: 1
        cat_right:
          io: 'cat_right'
          default: 0
          min_value: -1
          max_value: 1

    carousel_control:
      msg_type: "drill_msgs/CarouselCtrlStamped"
      timeout: 0.5
      fields:
        move:
          io: 'carousel_move'
          default: 0
          min_value: -1
          max_value: 1
        rotate:
          io: 'carousel_rotate'
          default: 0
          min_value: -1
          max_value: 1

    tower_control:
      msg_type: "drill_msgs/TowerCtrlStamped"
      timeout: 0.5
      fields:
        vertical_pin_movement:
          io: 'vert_pin'
          default: 0
          min_value: -1
          max_value: 1
        inclined_pin_movement:
          io: 'tilt_pin'
          default: 0
          min_value: -1
          max_value: 1
        tilt_speed:
          io: 'mast'
          default: 0
          min_value: -1
          max_value: 1

    wrench_control:
      msg_type: "drill_msgs/WrenchCtrlStamped"
      timeout: 0.5
      fields:
        move:
          io: 'wrench_move'
          default: 0
          min_value: -1
          max_value: 1
        grip:
          io: 'wrench_grip'
          default: 0
          min_value: -1
          max_value: 1

    water_injection_control:
      same_in_remote: True
      msg_type: "drill_msgs/FloatStamped"
      timeout: 0.5
#      ignore_move_permission: True
      fields:
        value:
          io: 'water_injection'
          default: 0
          min_value: 0
          max_value: 1

    dust_flaps_control:
      msg_type: "drill_msgs/FloatStamped"
      timeout: 0.5
      fields:
        value:
          io: 'dust_flaps'
          default: 0
          min_value: -1
          max_value: 1

    drill_propel_mode:
      msg_type: "drill_msgs/DrillPropel"
      timeout: 0.5
      fields:
        mode:
          io: 'driller_relay'
          default: False

    compressor_control:
      msg_type: "drill_msgs/CompressorCtrl"
      timeout: 0.5
      fields:
        turned_on:
          io: 'compressor_relay'
          default: False
        power:
          io: 'compressor_power'
          default: 0
          min_value: 0
          max_value: 1

    drill_actuator:
      msg_type: "drill_msgs/DrillCtrl"
      timeout: 0.5
      fields:
        feed_speed:
          io: 'feed'
          default: 0
          min_value: -1
          max_value: 1
        rotation_speed:
          io: 'rot'
          default: 0
          min_value: -1
          max_value: 1
        feed_pressure:
          io: 'feed_press'
          default: 0
          min_value: 0
          max_value: 1
        rotation_torque:
          io: 'rot_torque'
          default: 0
          min_value: 0
          max_value: 1

CollisionPreventerNode:
  rate: 20
  log_period: 2
  safety_dist: 10
  safety_dist_tailing: 1.5
  max_ignoring_period: 20
  tailing_include_dist: 2
  min_dangerous_points: 10
  min_dangerous_events: 5
  danger_timeout: 10
  obstacle_topic_timeout: 2
  cameras_stop_classes:
    worker: 0.9
    car: 0.9
    truck: 0.9
#    dozer: 0.9
  cameras_stop_areas:
    left:
      - [614, 0]
      - [1278, 40]
      - [1278, 156]
      - [1015, 476]
      - [566, 381]
    right:
      - [257, 0]
      - [919, 0]
      - [826, 565]
      - [284, 623]
  ignoring_period: 8

ArmControllerNode:
  rate: 10
  # Допустимые режимы для каждого из состояний
  allowed_modes:
    - "drilling"
    - "tower_tilt"
    - "shaft_buildup"
    - "shaft_stow"
    - "idle"
    - 'moving'

  # Допустимое время выполнения каждого состояния
  opening_time: 30
  closing_time: 30
  open_push_time: 0.5
  close_push_time: 0.5
  grip_push_time: 1.5
  no_reaction_time: 5.5
  max_depth_to_close: 13.0

RemoteConnectorNode:
  rate: 20

  PROTOCOL: 'tcp'
  IP: '0.0.0.0'
  PORT: 6000

  enable_hal_allow_movement: True
  param_files:
    - 'global.yaml'
    - 'vehicle.yaml'
    - 'nodes.yaml'
    - 'drilling_profiles.yaml'
  dump_file: 'params_dump.yaml'
  params_filter:
    - '/ros'
    - '/StateTracker2Node/enters'
    - '/StateTracker2Node/links'
    - '/StateTracker2Node/nodes'
    - '/Global/topics'
    - '/Global/Reports'
    - '/Global/SYSTEM_STATUS'
    - '/Global/events'
    - '/CanNode/input_topics'
    - '/CanNode/output_topics'
    - '/KobusAdapterNode'
    - '/DrillerSimNode'
    - '/SimulatorNode'
    - '/StatsLoggerNode'
    - '/RCSAdapterNode'
    - '/BagsRecorderNode'
    - '/JackRegulatorNode'
    - '/run_id'
    - '/load_params_with_create'
#    - '/CanNode'
#    - '/CanNode/IOs'

  cameras:
    '1':
      IP: '**********'
      login: 'admin'
      password: 'icanseethedrill271'

    '2':
      IP: '**********'
      login: 'admin'
      password: 'icanseethedrill271'

    '3':
      IP: '**********'
      login: 'admin'
      password: 'icanseethedrill271'

    '4':
      IP: '**********'
      login: 'admin'
      password: 'icanseethedrill271'

    '5':
      IP: '**********'
      login: 'admin'
      password: 'icanseethedrill271'

    '6':
      IP: '**********'
      login: 'admin'
      password: 'icanseethedrill271'

    '7':
      IP: '**********'
      login: 'admin'
      password: 'icanseethedrill271'

  # Последовательность id РМО по приоритетности (в порядке возрастания)
  iplace_ids:
    - 124
    - 123

  max_msg_delay: 1

  max_speed_err: 0.1

  # Карта для перевода данных телеметрии из ROS-сообщений в формат в пакете для отправки на РМО
  telemetry_map:
    # Название топика
    "state":
      # Поля сообщения
      "position":
        "x": "position_x"
        "y": "position_y"
        "z": "position_z"
      "speed": "speed"
      "yaw": "yaw"
      "quality": "gps_quality"

    "drill_state":
      "spindle_depth": "spindle_depth"
      "rotation_speed": "drill_rotation_speed"
      "feed_speed": "drill_feed_speed"
      "angular_pose": "drill_angular_pose"

    "pressure_state":
      "rotation_pressure": "drill_rotation_pressure"
      "feed_pressure": "drill_feed_pressure"
      "air_pressure": "air_pressure"

    "engine_state":
      "engine_speed": "engine_speed"
      "battery_potential": "battery_potential"
      "coolant_temperature": "coolant_temperature"

    "water_level":
      "value": "water_level"

    "fuel_level":
      "value": "fuel_level"

    "vibrations":
      "vibrations": "vibration"

    "jacks_state":
      "rear": "rear_jack_pulled"
      "left": "left_jack_pulled"
      "right": "right_jack_pulled"
      "rear_on_ground": "rear_jack_on_ground"
      "left_on_ground": "left_jack_on_ground"
      "right_on_ground": "right_jack_on_ground"

    "platform_level":
       "roll": "platform_roll"
       "pitch": "platform_pitch"

    "tower_state":
       "value": "tower_angle"

    "pwm_enabled":
       "value": "pwm_enabled"

    "planned_route":
      "points":
        "local_pose":
          "x": "point_x"
          "y": "point_y"
          "z": "point_z"

    "planned_routes":
      "routes":
        "points":
          "local_pose":
            "x": "point_x"
            "y": "point_y"
            "z": "point_z"

    "main_mode":
      "mode": "MainStateMachineNode"

    "last_main_mode":
      "mode": "LastMainMode"

    "LevelerNodeStatus":
      "status": "LevelerNode"

    "ArmControllerNodeStatus":
      "status": "ArmControllerNode"

    "DrillerNodeStatus":
      "status": "DrillerNode"

    "PlannerNodeStatus":
      "status": "PlannerNode"

    "TowerControllerNodeStatus":
      "status": "TowerControllerNode"

    "CarouselControllerNodeStatus":
      "status": "CarouselControllerNode"

    "DustFlapsControllerNodeStatus":
      "status": "DustFlapsControllerNode"

    "RodChangerNodeStatus":
      "status": "RodChangerNode"

    "ForkControlNodeStatus":
      "status": "ForkControlNode"

    "WrenchControllerNodeStatus":
      "status": "WrenchControllerNode"

    "tailing_map_polygons":
      "RAW": "tailing_map"

    "segmented_obstacles":
      "RAW": "obstacles_pc"

    "camera_detections_left":
      "RAW": "camera_detections_4"

    "camera_detections_right":
      "RAW": "camera_detections_3"

    "arm_switch_state":
      "open": "arm_open"
      "closed": "arm_grip"

    "dust_flaps_switch_state":
      "open": "dust_flaps_open"
      "closed": "dust_flaps_closed"

    "carousel_switch_state":
      "closed": "carousel_closed"
      "open": "carousel_open"
      "index_1": "carousel_index_1"
      "index_2": "carousel_index_2"
      "cup_1": "carousel_cup_1"
      "cup_2": "carousel_cup_2"

    "wrench_switch_state":
      "stage_1_closed": "wrench_engaged"
      "stage_1_open": "wrench_stowed"
      "stage_2_closed": "wrench_grip"
      "stage_2_open": "wrench_open"

    "fork_switch_state":
      "front_switch_on": "fork_extended"
      "rear_switch_on": "fork_retracted"

    "tower_switch_state_raw":
      "ncs_vertical_switch_lock": "vert_pin_lock"
      "ncs_vertical_switch_release": "vert_pin_release"
#      "inclined_switch_on": "inclined_pin"

    "tower_angle_ready":
      "value": "tower_ok_to_lock"

    "last_finished_holeid":
      "data": "last_holeid"

    "maneuver_processing":
      "value": "maneuver_processing"

    "MainStateMachineNodeStatus":
      "state_duration_sec": "state_duration_sec"

    "shaft_counter":
      "value": "shaft_counter"

    "need_shaft_reset":
      "stop": "is_need_shaft_reset"

  # Карта для перевода данных smart-управления из ZMQ-пакета в формат ROS-сообщений
  smart_control_map:
    "cat":
      "args":
        "left_track": {"NAME": "cat_left", "MIN": -10, "MAX": 10}
        "right_track": {"NAME": "cat_right", "MIN": -10, "MAX": 10}

      "TOPIC": "/cat_control_smart"
      "MSG": "drill_msgs/CatsStamped"

    "drill":
      "args":
        "rotation_speed": {"NAME": "rotation_speed", "MIN": -10, "MAX": 10}
        "feed_pressure": {"NAME": "feed_pressure", "MIN": -10, "MAX": 10}
        "feed_speed": {"NAME": "feed_speed", "MIN": -10, "MAX": 10}

      "TOPIC": "/remote_drilling_smart"
      "MSG": "drill_msgs/DrillCtrl"

  # Карта для перевода данных direct-управления из TCP-пакета в формат ROS-сообщений
  direct_control_map:
    "drivedrill":
      "args":
        "mode": {"NAME": "mode", "MIN": -1, "MAX": 1}
      "TOPIC": "/remote_drill_propel_mode"
      "MSG": "drill_msgs/DrillPropel"

      "rc_needed": true

    "cat":
      "args":
        "left_track": {"NAME": "cat_left", "MIN": -1, "MAX": 1}
        "right_track": {"NAME": "cat_right", "MIN": -1, "MAX": 1}

      "TOPIC": "/remote_cats_control"
      "MSG": "drill_msgs/CatsStamped"

      "rc_needed": true

    "drill":
      "args":
        "rotation_speed": {"NAME": "rotation_speed", "MIN": -1, "MAX": 1}
        "feed_pressure": {"NAME": "feed_pressure", "MIN": -1, "MAX": 1}
        "feed_speed": {"NAME": "feed_speed", "MIN": -1, "MAX": 1}
        "rotation_torque": {"NAME": "rotation_torque", "MIN": -1, "MAX": 1}

      "TOPIC": "/remote_drill_actuator"
      "MSG": "drill_msgs/DrillCtrl"

      "rc_needed": true

    "tower":
      "args":
        "tower_tilt": {"NAME": "tilt_speed", "MIN": -1, "MAX": 1}
        "vertical_pin": {"NAME": "vertical_pin_movement", "MIN": -1, "MAX": 1, "NO_INTERPOLATE": true}
        "inclined_pin": {"NAME": "inclined_pin_movement", "MIN": -1, "MAX": 1, "NO_INTERPOLATE": true}

      "TOPIC": "/remote_tower_control"
      "MSG": "drill_msgs/TowerCtrlStamped"

      "rc_needed": true

    "leveler":
      "args":
        "rear_jack": {"NAME": "rear", "MIN": -1, "MAX": 1}
        "right_jack": {"NAME": "right", "MIN": -1, "MAX": 1}
        "left_jack": {"NAME": "left", "MIN": -1, "MAX": 1}

      "TOPIC": "/remote_jacks_control"
      "MSG": "drill_msgs/JacksCtrlStamped"

    "arm":
      "args":
        "move_arm": {"NAME": "value", "MIN": -1, "MAX": 1}

      "TOPIC": "/remote_arm_control"
      "MSG": "drill_msgs/FloatStamped"

      "rc_needed": true

    "dust_flaps":
      "args":
        "move_df": { "NAME": "value", "MIN": -1, "MAX": 1 }

      "TOPIC": "/remote_dust_flaps_control"
      "MSG": "drill_msgs/FloatStamped"

      "rc_needed": true

    "fork":
      "args":
        "move_fork": {"NAME": "value", "MIN": -1, "MAX": 1}

      "TOPIC": "/remote_fork_control"
      "MSG": "drill_msgs/FloatStamped"

      "rc_needed": true

    "wrench":
      "args":
        "move_wrench": {"NAME": "move", "MIN": -1, "MAX": 1}
        "grip_wrench": {"NAME": "grip", "MIN": -1, "MAX": 1, "NO_INTERPOLATE": true}

      "TOPIC": "/remote_wrench_control"
      "MSG": "drill_msgs/WrenchCtrlStamped"

      "rc_needed": true

    "carousel":
      "args":
        "move_carousel": {"NAME": "move", "MIN": -1, "MAX": 1}
        "rotate_carousel": {"NAME": "rotate", "MIN": -1, "MAX": 1}

      "TOPIC": "/remote_carousel_control"
      "MSG": "drill_msgs/CarouselCtrlStamped"

      "rc_needed": true

    "compressor":
      "args":
        "compressor_on": {"NAME": "turned_on", "MIN": 0, "MAX": 1, "NO_INTERPOLATE": true}
        "compressor_power": {"NAME": "power", "MIN": 0, "MAX": 1}

      "TOPIC": "/remote_compressor_control"
      "MSG": "drill_msgs/CompressorCtrl"

      "rc_needed": true

    "dust_collector":
      "args":
        "value": {"NAME": "value", "MIN": -1, "MAX": 1}

      "TOPIC": "/water_injection_control"
      "MSG": "drill_msgs/FloatStamped"

      "rc_needed": false

    "enable_lights":
      "args":
        "value": { "NAME": "value", "MIN": 0, "MAX": 1, "NO_INTERPOLATE": true}

      "TOPIC": "/lights_control_direct"
      "MSG": "drill_msgs/BoolStamped"

      "rc_needed": false

    "rock_type":
      "args":
        "data": { "NAME": "data", "MIN": 0, "MAX": 1 }
      "TOPIC": "/rmo_rock_type"
      "MSG": "std_msgs/String"
      "rc_needed": false

    "hole_water":
      "args":
        "data": { "NAME": "data", "MIN": 0, "MAX": 1 }
      "TOPIC": "/rmo_hole_water"
      "MSG": "std_msgs/String"
      "rc_needed": false

  m_top_ang_easy_tilt: 60 # верхний предел угла наклона мачты для лёгкого ограничения управления мачтой
  m_low_ang_easy_tilt: 30  # нижний предел угла наклона мачты для лёгкого ограничения управления мачтой
  m_top_ang_hard_tilt: 75 # верхний предел угла наклона мачты для усиленного ограничения управления мачтой
  m_low_ang_hard_tilt: 15 # нижний предел угла наклона мачты для усиленного ограничения управления мачтой
  easy_tilt_coef: 0.5 # коэффициент лёгкого ограничения управления мачтой
  hard_tilt_coef: 0.75 # коэффициент усиленного ограничения управления мачтой

DrillSupervisorNode:
  rate: 30
  log_period: 2

  # Отслеживаемые ноды. Обязательно на основе BaseNode
  nodes: []
  # Отслеживаемые топики
  topic_paramkeys_to_monitor: []
  topics_to_monitor: []
  topics_to_restart_nodes:
    "segmented_obstacles": "tailing_detector_driller"

  restart_lock_time: 10

  # Максимальная задержка между отчетами нод для определения их зависания
  max_report_time_delay: 1

  special_timeouts:
    RemoteConnectorNode: 4
    HalClientNode: 10
    BagsRecorderNode: 1200

  # Карта выбора действия при получении ошибки от ноды.
  error_map:
    # Имя ноды: Тип ошибки: Тип, обработчик которого вызывать
    PlannerNode:
      '5': 4
    DriverNode:
      '5': 4

  # Временной отрезок для оценки частоты сообщений об ошибках секунды
  error_rate_check_period: 1
  # Максимальное допустимое число ошибок полученных за заданный временной отрезок
  max_errors_rate: 15

CatRegulatorNode:
  rate: 20
  disable_mode_check: False
  disable_msg_check: False
  left_reg:
    i_saturation: 0.35
    p: 1.2
    i: 0.8
    d: 0.5
    ff: 1.125
    min: -0.5
    max: 0.5
    out_min: -1
    out_max: 1
    d_term_tc: 0.3
    out_tc: 0.3
    force_zero: True

  right_reg:
    i_saturation: 0.35
    p: 1.2
    i: 0.8
    d: 0.5
    ff: 1.125
    # Макс скорость вперед - 0.4 м/с
    # Макс скорость назад - 0.4 м/c
    min: -0.5
    max: 0.5
    out_min: -1
    out_max: 1
    d_term_tc: 0.3
    out_tc: 0.3
    force_zero: True

SimulatorNode:
  rate: 50

  x_start: 0
  y_start: 0
  yaw_start: 0
  spindle_start: 1

  max_jack_len: 4
  min_jack_len: 1.4
  left_jack_len_start: 1.4
  right_jack_len_start: 1.4
  rear_jack_len_start: 1.4

  max_vert_pin_len: 0.3
  max_incl_pin_len: 0.3
  # Коэффициент зависимости скорости движения штифта от управляющей величины:
  pin_c2f: 0.004
  # Коэффициент зависимости скорости движения мачты от управляющей величины:
  tilt_c2f: 0.2

  max_tilt: 90

  max_arm_len: 0.3
  min_arm_len: 0
  arm_c2f: 0.2

  max_fork_len: 0.3
  min_fork_len: 0
  fork_c2f: 0.2

  wrench_move_c2f: 0.2
  wrench_grip_c2f: 0.2
  max_wrench_move_len: 0.3
  max_wrench_grip_len: 0.2

  carousel_move_c2f: 1
  carousel_rot_c2f: 0.2
  max_carousel_move_len: 0.3
  max_carousel_rot_len: 0.3

  max_air_press: 4
  min_air_press: 0
  air_c2f: 3

  cat_c2f: 3 # was 2.4
  jack_c2f: 0.2
  # Коэффициент зависимости скорости подачи от управляющей величины:
  head_c2f: 0.6
  # Коэффициент зависимости скорости вращения от управляющей величины:
  rot_c2f: 100
  # Коэффициент зависимости давления подачи от управляющей величины:
  press_c2f: 0.01

  # Уровень земли под домкратами
  grnd_left: 0.3
  grnd_right: 0.2
  grnd_rear: 0

  # Мин. скорость бурения метры/секунду
  min_drilling_speed: 0.00028
  # Макс. давление подачи тонны/квадратный метр
  max_feed_pressure: 25
  # Макс. скорость вращения обороты/секунду
  max_rotation_speed: 2

  layers:
    # Кол-во слоев земли
    total_num: 50

    thickness:
      mu: 2
      sigma: 2
      lower_bound: 1
      upper_bound: 6

StateTracker2Node:
  rate: 100

  # Параметры нод графа, которые могут быть заданы вручную через ГУИ:
  # Имя ноды в параметрах графа
  vibration_estimator:
    window_size: 30

  state_pub:
    limit_period: 0.15

  drill_state_pub:
    limit_period: 0.1

  inc_roll_pitch_align:
    pitch_offset: 0.497499998658896
    roll_offset: -0.363750003278255

  tower_imu_roll_pitch_align:
    pitch_offset: 0.0
    roll_offset: 0.0

  tower_inc_roll_pitch_align:
    pitch_offset: -90.0
    roll_offset: -90.0

  front_imu_roll_pitch_align:
    pitch_offset: 15.49 # 15.0908966064453
    roll_offset: 178.43 # 178.31477355957

  back_imu_roll_pitch_align:
    pitch_offset: 3.65 #3.97684097290039
    roll_offset: 179.90124130249

  yaw_comp:
    coefficient: 0.25

  smooth_yaw:
    time_window: 0.3

  smooth_roll:
    time_window: 0.01
    input_rate: 100

  smooth_pitch:
    time_window: 0.01
    input_rate: 100

  yaw_update:
    max_period: 0.2

  x_update:
    max_period: 0.2

  y_update:
    max_period: 0.2

  z_update:
    max_period: 0.2


  wrench_st1_extend_len_check:
    max_val: 25
    min_val: 21.55

  wrench_st1_retract_len_check:
    max_val: 3.35
    min_val: 0

  wrench_st2_extend_len_check:
    max_val: 0.8
    min_val: 0.0

  wrench_st2_retract_len_check:
    max_val: 25
    min_val: 22.55

  wrench_st3_extend_len_check:
    max_val: 25
    min_val: 22.0

  wrench_st3_retract_len_check:
    max_val: 7.20
    min_val: 0

  arm_open_check:
    max_val: 4.325
    min_val: 0

  arm_close_check:
    max_val: 24
    min_val: 21.3

  arm_grip_check:
    max_val: 24
    min_val: 16.6

  carousel_open_check:
    max_val: 24
    min_val: 21.6

  carousel_closed_check:
    max_val: 6.26
    min_val: 0

  fork_len_pred:
    measure_2: 24
    measure_1: 0
    result_2: 0.43
    result_1: 0

  fork_len_rear_check:
    max_val: 4.1
    min_val: 0

  fork_len_front_check:
    max_val: 15
    min_val: 8.5

  jack_rl_len_pred:
    measure_1: 0.268
    measure_2: 0.926
    result_1: 0.15
    result_2: 6.1

  jack_rr_len_pred:
    measure_1: 0.268
    measure_2: 0.926
    result_1: 0.22
    result_2: 6.4


  jack_fl_len_pred:
    measure_1: 0.268
    measure_2: 0.926
    result_1: 0.19
    result_2: 5.31

  jack_fr_len_pred:
    measure_1: 0.268
    measure_2: 0.926
    result_1: 0.15
    result_2: 6.20

  jack_rr_and_rl_pulled:
    quantity: 2

  jack_fl_update:
    max_period: 0.2

  jack_fl_smooth:
    time_window: 0.1

  jack_fl_dt_smooth:
    time_window: 0.4

  jack_rr_update:
    max_period: 0.2

  jack_rr_smooth:
    time_window: 0.1

  jack_rl_update:
    max_period: 0.2

  jack_rl_smooth:
    time_window: 0.1

  jack_rear_max_len_update:
    max_period: 0.2

  jack_rear_max_len_smooth:
    time_window: 0.1

  jack_rear_len_update:
    max_period: 0.2

  jack_rear_len_smooth:
    time_window: 0.1

  jack_rear_len_dt_smooth:
    time_window: 0.4

  jack_fr_update:
    max_period: 0.2

  jack_fr_smooth:
    time_window: 0.1

  jack_fr_dt_smooth:
    time_window: 0.4

  smooth_x:
    time_window: 0.4

  smooth_y:
    time_window: 0.4

  smooth_z:
    time_window: 0.4

  smooth_w:
    time_window: 0.35

  smooth_vx:
    time_window: 0.35

  smooth_vy:
    time_window: 0.35

  smooth_v:
    time_window: 0.45

  rel_depth_update:
    max_period: 0.1

  smooth_feed_speed:
    time_window: 0.45

  head_rangefinder_pred:
    measure_1: 61
    result_1: 0
    measure_2: 27437
    result_2: 19.963060379

  head_rangefinder_len_check:
    max_val: 19.97
    min_val: 0.0

  rel_repth_proc:
    reset_depth_min_delta: 0.05
    high_jump_thr: 0.3
    allowed_reset_depth_error: 0.04

  smooth_depth:
    time_window: 0.45

  head_angle_proc:
    encoder_cpr: 60
    reverse: false

  smooth_tower_inc_angle:
    'time_window': 0.8

  fl_ground_check:
    max_val: 999
    min_val: 5.25  # tune this if not works

  fr_ground_check:
    max_val: 999
    min_val: 5.25 # tune this if not works

  rl_ground_check:
    max_val: 5 # tune this if not works
    min_val: 0

  rr_ground_check:
    max_val: 5 # tune this if not works
    min_val: 0

#  rot_speed_pred:
#    measure_2: 4.8808
#    measure_1: 3.3426
#    result_2: 110.0
#    result_1: 40.0

  rot_speed_pred:
    measure_2: 4.64
    measure_1: 2.17
    result_2: 110.0
    result_1: 1.0

  smooth_rot_speed:
    time_window: 2

  rot_press_pred:
    measure_2: 3.845
    measure_1: 2.717
    result_2: 311.72
    result_1: 209.187

  smooth_rot_press:
    time_window: 0.5

  feed_press_pred:
    measure_2: 3.1659
    measure_1: 2.4497
    result_2: 248.3492
    result_1: 185.1242

  smooth_feed_press:
    time_window: 0.5

  air_press_pred:
    measure_2: 0.71474
    measure_1: 0.19379
    result_2: 3.864537
    result_1: 0.358527

  smooth_air_press:
    time_window: 1.5

  water_level_pred:
#    measure_2: 13719.0
    measure_2: 4.186833015
#    measure_1: 9469.0
    measure_1: 2.889797662
    result_2: 0.8
    result_1: 0.5625

  fuel_level_pred:
    measure_2: 4.26380205154
    measure_1: 1.60258793831
    result_2: 0.969
    result_1: 0.287

RodChangerNode:
  rate: 20

  #ALIGN_FORK
  align_fork_timeout: 45

  align_fork_angle:
    buildup:
      rod_1: 3.14
      rod_2: 3.14
    stow:
      rod_1: 3.14
      rod_2: 3.14

  align_fork_depth:
    buildup:
      rod_1: 19.44
      rod_2: 19.44
    stow:
      rod_1: 11.83
      rod_2: 11.83

  #ALIGN_CUP
  align_cup_angle:
    rod_1: -3.036
    rod_2: -1.88
  align_cup_depth:
    rod_1: 10.80
    rod_2: 10.80
  align_cup_timeout: 20

  #ALIGN_COMMON
  align_feed_speed: 0.2
  align_feed_press: 0.1
  align_rotation_speed: 10

  #LOCK\UNLOCK
  lock_timeout: 20
  unlock_timeout: 20
  max_lock_attempts: 8

  #TURN_CW
  cw_rot_speed: 40
  turn_cw_timeout: 20.0

  #TURN_CCW
  ccw_rot_speed: 10
  turn_ccw_timeout: 20.0

  #TURN_CW_CUP
  turn_cw_cup_time: 1
  turn_cw_cup_rotation_speed: 0.1

  #BRAKEOUT
  brakeout:
    rotation_speed_ctrl: 120
  brakeout_timeout: 5
  max_brakeout_attempts: 5
  brakeout_rev: 0.208

  #CLOSE_WRENCH
  close_wrench_timeout: 20.0

  #APPLY_WRENCH
  apply_wrench_timeout: 55
  wrench_angle_threshold: 0.104
  apply_wrench_attempts: 5

  #OPEN_WRENCH
  open_wrench_timeout: 20

  #DETACH
  rot_detach_speed: 60
  detach_rev: 7
  detach_timeout: 20

  #DETACH_CUP
  detach_cup_timeout: 20

  #LIFT_TO_CAROUSEL
  lift_to_carousel_depth: 10.71
  lift_to_carousel_speed: 0.6
  lift_to_carousel_timeout: 45.0

  #OPEN_CAROUSEL
  open_carousel_timeout: 30

  #CLOSE_CAROUSEL
  close_carousel_timeout: 30

  #APPROACH_CAROUSEL
  approach_carousel_timeout: 30
  approach_depth: 10.86

  #INSERT
  insert_max_pressure: 5.7
  insert_max_attempts: 8
  insert_timeout: 20
  insert_depth:
    rod_1: 11.03
    rod_2: 11.03

  #SCREWING
  feed_press_screwing: 0.05
  feed_speed_screwing: 0.02
  rot_screwing_speed: 50
  screwing_pressure_threshold: 270
  max_feed_press: 15
  screwing_timeout: 40.0

  #NEW_ROD_SCREWING
  new_rod_screwing_timeout: 40.0

  #ARM
  arm_timeout: 30

  #CLOSE_ARM
  close_arm_timeout: 30

  #OPEN_ARM
  open_arm_timeout: 30

  #OPEN_FORKARM
  open_forkarm_timeout: 20

  #PULL_OUT
  pull_out_depth: 10.78
  pull_out_timeout: 10

  #APPROACH_FORK
  approach_fork_depth:
    buildup:
      rod_1: 11.56
      rod_2: 11.56
    stow:
      rod_1: 19.25
      rod_2: 19.25
  approach_fork_timeout: 45

  #FINAL_POSITION
  final_position_timeout: 45
  
  #COMMON
  depth_error: 0.03
  angle_error: 0.052
  feed_speed_normal: 0.5
  feed_press_normal: 0.1
  rotation_speed_normal: 40
  fix_time: 0.1
  
  allowed_modes:
    - "shaft_buildup"
    - "shaft_stow"

WrenchControllerNode:
  rate: 20
  open_speed: 1.0
  close_speed: 1.0
  opening_time: 15.0
  closing_time: 15.0
  turn_time: 15.0
  release_time: 15.0
  allowed_modes:
    - "shaft_buildup"
    - "shaft_stow"
    - "idle"
    - "drilling"


CarouselControllerNode:
  rate: 5
  open_speed: 1.0
  close_speed: 1.0
  rotate_speed: 1.0
  max_oc_time: 10
  max_turn_time: 10

  allowed_modes:
    - "shaft_buildup"
    - "shaft_stow"
    - "idle"

StatsLoggerNode:
  rate: 1
  seconds_per_file: 3600 # 1 час
  work_time: 259200 # 3 суток

  # Название бэга и список топиков, которые будут в него записываться
  ns_topics:
    state:
      - state
      - drill_state
      - pressure_state
      - platform_level
      - jacks_state
#      - tower_state
      - vibration_state

HalClientNode:
  rate: 20

  PROTOCOL: "tcp"
  #IP: "***********" # рмо
  #IP: "*********" # рмо через впн
  #IP: "************" # reineke
  #IP: "************" # впс
#  IP: '127.0.0.1' # localhost
  IP: '*********' # RMO Hudbay
  PORT: 5555

RCSAdapterNode:
  rate: 30

  rcs_ip: "**************"
  rcs_port: 3737

  robot_ip: "0.0.0.0"
  robot_port: 3738

  max_package_size: 1024

VehicleSupervisorNode:
  rate: 20

  restrictions_off_mode_duration: 60
  control_check_time_window: 3
  max_repetitions: 20

  topics_to_monitor:
    platform_level:
      msg_type: 'drill_msgs/PlatformState'
      validation_parameters:
        roll:
          min_val: '/Vehicle/restrictions/min_allowed_roll'
          max_val: '/Vehicle/restrictions/max_allowed_roll'
          thr_level_0: '/Vehicle/restrictions/crt_angle_thr_1'
          thr_level_1: '/Vehicle/restrictions/crt_angle_thr_2'
          allow_level_2: true
          changeability_check: true
          event_code: 'roll_critical'
        pitch:
          min_val: '/Vehicle/restrictions/min_allowed_pitch'
          max_val: '/Vehicle/restrictions/max_allowed_pitch'
          thr_level_0: '/Vehicle/restrictions/crt_angle_thr_1'
          thr_level_1: '/Vehicle/restrictions/crt_angle_thr_2'
          allow_level_2: true
          changeability_check: true
          event_code: 'pitch_critical'
        timestamp:
          timeout: 1.5

    wrench_switch_state_raw:
      msg_type: 'drill_msgs/WrenchSwitchState'
      validation_parameters:
        stage_len_1:
          changeability_check: true
          min_val: 15.6
          max_val: 21.6
          thr_level_0: 0.05
          thr_level_1: 0.5
          allow_level_2: true
          event_code: 'wrench_sensor1_failure'
        stage_len_2:
          changeability_check: true
          min_val: 12.6
          max_val: 18.7
          thr_level_0: 0.05
          thr_level_1: 0.5
          allow_level_2: true
          event_code: 'wrench_sensor2_failure'
        stage_len_3:
          changeability_check: true
          min_val: 12.5
          max_val: 15.7
          thr_level_0: 0.05
          thr_level_1: 0.5
          allow_level_2: true
          event_code: 'wrench_sensor3_failure'
        timestamp:
          timeout: 1.5

    rotation_speed:
      msg_type: 'drill_msgs/FloatStamped'
      validation_parameters:
        value:
          changeability_check: true
          min_val: 2
          max_val: 4.85
          thr_level_0: 0.05
          thr_level_1: 0.5
          allow_level_2: true
          event_code: 'rpm_sensor_failure'
        timestamp:
          timeout: 1.5

    carousel_switch_state_raw:
      msg_type: 'drill_msgs/CarouselSwitchState'
      validation_parameters:
        main_len:
          changeability_check: true
          min_val: 6.1
          max_val: 21.85
          thr_level_0: 0.05
          thr_level_1: 0.5
          allow_level_2: true
          event_code: 'carousel_linear_sensor_failure'
        timestamp:
          timeout: 1.5

    fork_switch_state_raw:
      msg_type: 'drill_msgs/ForkSwitchState'
      validation_parameters:
        length:
          changeability_check: true
          min_val: 6.4
          max_val: 10.6
          thr_level_0: 0.05
          thr_level_1: 0.1
          allow_level_2: true
          event_code: 'fork_linear_sensor_failure'
        timestamp:
          timeout: 1.5


    arm_switch_state_raw:
      msg_type: 'drill_msgs/ArmState'
      validation_parameters:
        stage_1:
          changeability_check: true
          min_val: 4.1
          max_val: 21.65
          thr_level_0: 0.05
          thr_level_1: 0.1
          allow_level_2: true
          event_code: 'arm_linear_sensor1_failure'
        stage_2:
          changeability_check: true
          min_val: 1.7
          max_val: 17.55
          thr_level_0: 0.05
          thr_level_1: 0.1
          allow_level_2: true
          event_code: 'arm_linear_sensor2_failure'
        timestamp:
          timeout: 1.5

    jacks_state_raw:
      msg_type: 'drill_msgs/JacksState'
      validation_parameters:
        left_len:
          changeability_check: true
          min_val: 1.3
          max_val: 21.9
          thr_level_0: 0.05
          thr_level_1: 0.1
          allow_level_2: true
          event_code: 'left_jack_linear_sensor_failure'
        right_len:
          changeability_check: true
          min_val: 1.3
          max_val: 21.9
          thr_level_0: 0.05
          thr_level_1: 0.1
          allow_level_2: true
          event_code: 'right_jack_linear_sensor_failure'
        rear_left_len:
          changeability_check: true
          min_val: 1.3
          max_val: 17.8
          thr_level_0: 0.05
          thr_level_1: 0.1
          allow_level_2: true
          event_code: 'rear_left_jack_linear_sensor_failure'
        rear_right_len:
          changeability_check: true
          min_val: 1.3
          max_val: 17.8
          thr_level_0: 0.05
          thr_level_1: 0.1
          allow_level_2: true
          event_code: 'rear_right_jack_linear_sensor_failure'
        timestamp:
          timeout: 1.5

    pressure_state_raw:
      msg_type: 'drill_msgs/PressureState'
      validation_parameters:
        rotation_pressure:
          changeability_check: true
          min_val: 0.3
          max_val: 4.3
          thr_level_0: 0.05
          thr_level_1: 0.1
          allow_level_2: true
          event_code: 'rotation_pressure_sensor_failure'
        feed_pressure:
          changeability_check: true
          min_val: 0.25
          max_val: 4.0
          thr_level_0: 0.05
          thr_level_1: 0.1
          allow_level_2: true
          event_code: 'feed_pressure_sensor_failure'
        air_pressure:
          changeability_check: true
          min_val: 0.05
          max_val: 2.15
          thr_level_0: 0.02
          thr_level_1: 0.1
          allow_level_2: true
          event_code: 'air_pressure_sensor_failure'
        timestamp:
          timeout: 1.5

    head_rangefinder:
      msg_type: 'drill_msgs/FloatStamped'
      validation_parameters:
        value:
          changeability_check: true
        timestamp:
          timeout: 1.5

#    engine_state:
#      msg_type: "drill_msgs/EngineState"
#      validation_parameters:
#        engine_speed:
#          max_val: 1950
#        coolant_temperature:
#          min_val: -40
#          max_val: 97
#        engine_oil_pressure:
#          min_val: 120
#          max_val: 600

QarlTrimble:
  rate: 20
  KNOT: 0.5144444

RoboModeControllerNode:
  rate: 10
  can_module_topic: "/can_2/rx"

DustFlapsControllerNode:
  rate: 10
  allowed_modes:
    - "drilling"
    - "grounding"
    - "idle"
    - "moving"
    - "wait_after_level"

  # Допустимое время выполнения каждого состояния
  opening_time: 30
  closing_time: 30
  open_push_time: 1.5
  close_push_time: 1.5
  no_reaction_time: 4.0


BagsRecorderNode:
  rate: 5
  drill_bags_recorder_lidar: "drill_bags_recorder_lidar"
  drill_bags_recorder_nolidar: "drill_bags_recorder_nolidar"
#  record_script_nolidar: "rosbag record --split --duration=1m -a -o drill_nolidar"
#  record_script_lidar: "rosbag record --split --duration=1m -a -o drill_withlidar"
  bags_prefix_nolidar: "drill_nolidar"
  bags_prefix_withlidar: "drill_withlidar"
  split_duration_min: 1
  max_save_minutes: 30
  min_save_minutes: 3
  do_recording: true        # starts rosbag record with given params if true or expect it is run externally (if false),
                            # in such a case be sure to sync settings (path, prefix, split duration)
  extra_duration_min: 2     # extra duration to add to minutes_to_save (from last main action)
  wait_before_copy_min: 1   # sleep given minutes before copying bags to wait current active bag would be finished
                            # (should correspond to --split --duration param of rosbag record)
  lidars_minutes_to_save: 3  # always save lidar bags of given lifetime
  topics_nolidar:
    - /ArmControllerNodeStatus
    - /DEBUG_correction_ang
    - /DEBUG_path_deviation
    - /DEBUG_rotation_speed
    - /DEBUG_target_yaw_pose
    - /DEBUG_yaw
    - /DEBUG_yaw_sp
    - /DrillerNodeStatus
    - /DustFlapsControllerNodeStatus
    - /LevelerNodeStatus
    - /MainStateMachineNodeStatus
    - /SC/orientation
    - /SC/position
    - /SC/quality
    - /SC/speed
    - /SC/trimble_nmea_gga
    - /area
    - /arm_action
    - /arm_control
    - /arm_switch_state
    - /back_imu_data
    - /bags_save_cmd
    - /board_inc_angles
    - /can_1/tx
    - /can_2/rx
    - /can_2/tx
    - /can_5/rx
    - /can_5/tx
    - /can_6/tx
    - /can_node_log
    - /carousel_switch_state
    - /cat_control_smart
    - /cats_control
    - /compressor_control
    - /current_robomode
    - /drill_actuator
    - /drill_depth_info
    - /rmo_rock_type
    - /rmo_hole_water
    - /drill_propel_mode
    - /drill_state
    - /driller_action
    - /driller_out
    - /driver_out
    - /dust_collector_control
    - /dust_flaps_action
    - /dust_flaps_control
    - /dust_flaps_switch_state
    - /emergency_control
    - /emergency_status
    - /engine_ctrl
    - /engine_state
    - /epiroc_inclination_calibrated
    - /epiroc_inclination_raw
    - /event
    - /fan_control
    - /fork_switch_state
    - /fork_switch_state_raw
    - /front_imu_data
    - /fuel_level
    - /fuel_level_raw
    - /hal_permission
    - /head_rangefinder
    - /internal_error
    - /internal_report
    - /jacks_control
    - /jacks_state
    - /jacks_state_raw
    - /lamp_control
    - /last_finished_holeid
    - /lights_control_direct
    - /main_action
    - /main_mode
    - /main_sm_drill_out
    - /maneuver_processing
    - /manual_state
    - /moving_error
    - /permission
    - /planned_route
    - /planner_action
    - /planner_status
    - /platform_level
    - /platform_level_epiroc
    - /pressure_state
    - /pressure_state_raw
    - /progress_topic
    - /recalculate_maneuver
    - /remote_arm_control
    - /remote_carousel_control
    - /remote_cats_control
    - /remote_compressor_control
    - /remote_drill_actuator
    - /remote_drill_propel_mode
    - /remote_driller_actuator
    - /remote_drilling_smart
    - /remote_dust_flaps_control
    - /remote_fan_control
    - /remote_fork_control
    - /remote_jacks_control
    - /remote_moving
    - /remote_tower_control
    - /remote_type
    - /remote_wrench_control
    - /rmo_robomode_sp
    - /rosout
    - /rosout_agg
    - /rotation_speed
    - /selector_robomode_fb
    - /selector_robomode_sp
    - /speed_ctrl_stopped
    - /st/cat_fb
    - /st/slip
    - /st/w
    - /state
    - /system_status
    - /target
    - /tf
    - /tf_static
    - /tower_action
    - /tower_angle_ready
    - /tower_control
    - /tower_imu_data
    - /tower_inc_angle
    - /tower_state
    - /tower_switch_state_raw
    - /vibration_state
    - /water_injection_auto
    - /water_injection_control
    - /water_level
    - /water_level_raw
    - /wrench_control
    - /wrench_switch_state
    - /wrench_switch_state_raw
    - /camera_detections_left
    - /camera_detections_right
    - /obst_shortest_dist

  topics_lidar: # in addition to topics_nolidar
    - /segmented_obstacles
    - /segmented_road
    - /segmented_tailing
    - /tailing_edge_flt
    - /tailing_edge_raw
    - /tailing_map
    - /tailing_map_polygons
    - /tailing_vis_array
    - /velodyne_points
    - /os_cloud_node/points

  record_folder: "/media/vist/ADATA777/autobags/nolidar"
  save_path: "/media/vist/ADATA777/autobags/bags_saved"
  free_space_treshold_warn_gb: 3
  free_space_treshold_stop_gb: 2
  do_tar: false

EngineControllerNode:
  rate: 100
  engine_topic: "/can_2/rx"
  default_rpm: 1200

AiConnectorNode:
  rate: 10
  port: 7777
  cameras:
    - 'left'
    - 'right'

ForkControlNode:
  rate: 10
    
  allowed_modes:
    - "shaft_buildup"
    - "shaft_stow"
    
  opening_time: 5
  closing_time: 5
  start_timeout: 2
