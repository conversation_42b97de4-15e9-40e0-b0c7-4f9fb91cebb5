<launch>
  <group>
    <include file="$(find drill_launch_pack)/launch/load_params.launch"/>

    <include file="$(find xsens_driver)/launch/xsens.launch">
            <arg name="device" value="ttyUSB0"/>
    </include>
    <include file="$(find xsens_driver)/launch/xsens.launch">
            <arg name="device" value="ttyUSB1"/>
    </include>

    <include file="$(find kobus_adapter)/launch/run.launch"/>
    <include file="$(find drill_adam_adapter)/launch/run.launch"/>
    <include file="$(find state_tracker2)/launch/run.launch"/>
    <include file="$(find remote_connector)/launch/run.launch"/>
    <include file="$(find main_state_machine)/launch/run.launch"/>
    <include file="$(find tower_controller)/launch/run.launch"/>
    <include file="$(find arm_controller)/launch/run.launch"/>
    <include file="$(find cat_regulator)/launch/run.launch"/>
    <include file="$(find cat_driver)/launch/run.launch"/>
    <include file="$(find planner)/launch/run.launch"/>
    <include file="$(find leveler)/launch/run.launch"/>
    <include file="$(find carousel_controller)/launch/run.launch"/>
    <include file="$(find driller)/launch/run.launch"/>
    <include file="$(find drill_regulator)/launch/run.launch"/>
    <include file="$(find drill_supervisor)/launch/run.launch"/>
    <include file="$(find hal_client)/launch/run.launch"/>


  </group>
</launch>


