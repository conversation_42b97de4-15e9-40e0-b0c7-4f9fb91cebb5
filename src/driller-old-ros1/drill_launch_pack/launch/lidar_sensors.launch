<launch>
  <group>
  <include file="$(find velodyne_pointcloud)/launch/VLP16_points.launch">
            <arg name="frame_id" value="velodyne_rear"/>
            <arg name="port" value="2369"/>
            <arg name="device_ip" value="**********"/>
  </include>
  <include file="$(find velodyne_pointcloud)/launch/VLP16_points.launch">
            <arg name="frame_id" value="velodyne_front"/>
            <arg name="port" value="2368"/>
            <arg name="device_ip" value="**********"/>
  </include>
<!--  <include file="$(find ouster_ros)/ouster.launch">-->
<!--            <arg name="sensor_hostname" value="**********"/>-->
<!--            <arg name="metadata" value="/srv/vist/src/ouster/meta.json"/>-->
<!--  </include>-->

  </group>
</launch>
