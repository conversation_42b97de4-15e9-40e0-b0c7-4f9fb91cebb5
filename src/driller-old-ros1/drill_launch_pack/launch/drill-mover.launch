<launch>
  <group>
    <include file="$(find bags_recorder)/launch/run.launch"/>
    <include file="$(find main_state_machine)/launch/run.launch"/>
    <!--include file="$(find tower_controller)/launch/run.launch"/-->
    <include file="$(find arm_controller)/launch/run.launch"/>
    <include file="$(find dust_flaps_controller)/launch/run.launch"/>
    <!--include file="$(find carousel_controller)/launch/run.launch"/-->
    <include file="$(find cat_regulator)/launch/run.launch"/>
    <include file="$(find cat_driver)/launch/run.launch"/>
    <include file="$(find planner)/launch/run.launch"/>
    <include file="$(find leveler)/launch/run.launch"/>
    <include file="$(find driller)/launch/run.launch"/>
    <include file="$(find drill_regulator)/launch/run.launch"/>
    <include file="$(find drill_supervisor)/launch/run.launch"/>
    <include file="$(find hal_client)/launch/run.launch"/>
    <!--include file="$(find lidar_launch_pack)/launch/driller_run.launch"/-->
    <!--include file="$(find tailing_detector_driller)/launch/run_tailing_detector_driller.launch"/-->
    <include file="$(find collision_preventer)/launch/run.launch"/>
    <include file="$(find collision_preventer)/launch/ai_connector_run.launch"/>
  </group>
</launch>
