# Driller Node

The DrillerNode manages drilling operations by processing incoming actions and interacting with other nodes to control the drilling process.

## Contents:

[TOC]

![<img src="./docs/driller-rosgraph.png" width="600"/>](./images/driller-rosgraph.png)

DrillerNode subscribes to the `/driller_action` topic, accepting high-level command messages of type `common_msgs/Action`. 
A typical message includes action data encapsulated as a JSON string in the `data` field:

    - **first_shaft_flag**: Boolean, indicating if it’s the first shaft being drilled.
    - **last_shaft_flag**: Boolean, indicating if it’s the last shaft being drilled for this hole.
    - **drill_spindle_depth**: Numeric, the targeted spindle depth (rotary head vertical position, 0 at top, positive down) of the current drilling operation.
    - **raise_spindle_depth**: Numeric, specifying the pullback spindle depth during raising after drilling operation.
    - **hole_depth**: Numeric, specifying the full hole depth (from main action) from planned z height.
    - **tower_angle**: Numeric, providing the drill's tower orientation angle.
    - **id**: Identifier of the hole.

!!! note spindle_depth - The depth of the rotator's descent relative to its top position, 
    facing down (i.e., increases during drilling).  

#### Example Driller Action Data:

```json
{
  "first_shaft_flag": true,
  "last_shaft_flag": true,
  "drill_spindle_depth": 12.348463670137011,
  "raise_spindle_depth": 1.27,
  "hole_depth": 11.0,
  "tower_angle": 0.0,
  "id": 30
}
```

DrillerNode transmits the computed control signals to `/DrillRegulatorNode`, where  PID regulator controls the rotational and translational movements of the drill and feed pressure.
  
For controlling water and air compressor DrillerNode sends directives straight to CAN node (no regulator needed).

## Subscribed Topics

* `/ArmControllerNodeStatus` [drill_msgs/StateMachineStatus] - Status messages from the arm controller node.
* `/current_robomode` [drill_msgs/BoolStamped] - Current mode of the robot.
* `/drill_actuator` [drill_msgs/DrillCtrl] - Current control for the drill actuator.
* `/rmo_rock_type` [std_msgs/String] - Type of rock (hard, cracked, normal) to set profile 
* `/rmo_hole_water` [std_msgs/String] - Water in hole setting (dry, wet) to set profile 
* `/drill_state` [drill_msgs/DrillState] - Current state of the drill.
* `/driller_action` [common_msgs/Action] - Actions for the driller.
* `/main_mode` [drill_msgs/VehicleBehaviorMode] - Main mode of the robot.
* `/manual_state` [drill_msgs/ManualState] - Manual state switch if the Driller Node state machine.
* `/permission` [common_msgs/MovePermission] - Permission messages received from other nodes.
* `/pressure_state` [drill_msgs/PressureState] - Current state of drilling pressures.
* `/system_status` [common_msgs/SystemStatus] - General system status.
* `/vibration_state` [drill_msgs/VibrationArray] - Array of vibration amplitudes for several frequencies.
* `/water_injection_control` [drill_msgs/FloatStamped] - Manual control messages for water injection (from RMO).


## Published Topics

* `/DrillerNodeStatus` [drill_msgs/StateMachineStatus] - Represents the state of the Driller Node state machine.
* `/arm_action` [common_msgs/Action] - Actions for the drilling arm.
* `/compressor_control` [drill_msgs/CompressorCtrl] - Compressor control messages.
* `/driller_out` [drill_msgs/DrillCtrl] - Drilling control messages.
* `/dust_collector_control` [drill_msgs/BoolStamped] - Dust collector activation or deactivation.
* `/event` [common_msgs/Event] - General event messages.
* `/internal_report` [common_msgs/InternalReport] - Internal reports from the Driller Node.
* `/permission` [common_msgs/MovePermission] - Permission messages (not published?).
* `/rosout` [rosgraph_msgs/Log] - Standard ROS debug msgs.
* `/water_injection_auto` [drill_msgs/FloatStamped] - Control messages for water injection.


## Services

* `/DrillerNodeReloadParams` - Reload parameters for the Driller Node.
* `/recalibrate_air` - Recalibrate the air system.


## Node parameters controlled by RMO

Node has 2 parameters, controlled by user via RMO slider:

- `user_feed_pressure`: the feed pressure applied as the initial setting for the regulated feed pressure in the `drilling` state;
- `user_rotation_speed`: the user setting for the rotation speed in the `drilling` state. The minimum of `rotation_speed_limit` (from profile) and `user_rotation_speed` is applied.


## Concept of Profiles in Drilling

### Default Configurations
The **default** configuration holds baseline settings to be applied in a generic drilling scenario and is divided into:

- **'common'**: universal settings, applicable in all drilling states.
- **STATES**: detailed settings per driller state (e.g., 'idle', 'drilling') enhancing configuration readability.
- **ALIASES**: using YAML aliases (&) avoids repetitive declarations and facilitates efficient value modifications across settings.

### Profiles

**Profiles** dynamically tweak these defaults under specific drilling circumstances:
- They contain selective parameters from the default dictionary.
- Aligned for particular conditions, a 'wet' profile could modify water management parameters, whereas a 'hard' profile might adjust drilling speeds.
- Multiple profiles can be layered, with later ones potentially modifying the predecessors' values.

### RMO Button Groups to Set Profiles in GUI

Two distinct button groups enable user interaction for on-the-fly modifications to drilling conditions:

#### 1. Rock Type

Configurable through the **rock_type** section, enabling operators to select the geological state, reflected on the `/rmo_rock_type` topic.

- **Normal**: No additional profile applied.
- **Cracked**: Applies a "cracked" profile.
- **Hard**: Applies a "hard" profile.

#### 2. Hole Water

Allowing drilling operator to specify the wetness condition, interfacing with the `/rmo_hole_water` topic.

- **Dry**: No additional profile applied.
- **Flooded**: Triggers the "wet" profile.

### Onboard Profile Management

Upon changes through the GUI, the chosen selections are mapped to respective topics, `/rmo_rock_type` and `/rmo_hole_water`, which are subsequently processed onboard. 
The system compares new profile values with the last applied ones, ensuring unnecessary re-applications.


## States Behaviour and Parameters

List of states: 

- 1. [IdleState](#idle-state-description)
The `idle` represents the passive state of the `Driller` node when it's not active.
This state serves as a resting phase and is ready to initiate a new drilling sequence.
- 2. [TouchDownState](#touchdown-state-description)
In `touchdown` state `Driller` attempts to detect when the drill makes contact with the ground.
- 3. [OverburdenPassState](#overburden_pass-state-description)
The `overburden_pass` state manages the drill as it progresses through the overburden layer, adjusting its parameters based on the ground type (hard, or default currently).
- 4. [DrillingState](#drilling-state-description)
`drilling` state is main regular drilling state. 
It has pullup logic, water control logic, target depth monitoring, check air pressure is nominal,  
feed pressure control, feed speed control, rotation speed control, air power control, dust collector control, 
hard rotation check.    
- 5. [HardRotState](#hard_rot-state-description)
`hard_rot` state is used to release drilling bit when rotation stucks.
- 6. [PullUpState](#pullup-state-description)
The `pullup` state is responsible for pulling up the drill. It adjusts the rotation speed, feed speed, 
and other controls based on current conditions. 
Depending on various conditions, such as the drill reaching a specific height or getting stuck, 
switch to another state, like `AFTER_PULLUP` or `UNSTUCK`. The state is used to clean the hole and can be triggered by high air pressure or at specified depth.
- 7. [AfterPullUpState](#after_pullup-state-description)
The `after_pullup` state defines the behavior of the system after a pull-up. Used to lower the drill back into hole.
- 8. [RaiseState](#raise-state-description)
The `raise` state defines the system's behavior during the drill raising action after the hole have been completed.
- 9. [WaitAfterDrillState](#wait_after_drill-state-description)
The `wait_after_drill` state prepares the drill for raising by rotating the drill with no pulldown pressure for a specified duration after drilling.
- 10. [PassSoftState](#pass_soft-state-description)
The `pass_soft` state represents the drill's action when passing through softer terrains. Applies very low pulldown pressure to avoid bit plugging. Used for first few seconds of drilling after pullup.
- 11. [UnstuckState](#unstuck_down-state-description)
The `unstuck` state represents the drill's strategy when it gets stuck.
It alternates between moving the drill upwards and downwards for a specified number of cycles to free itself.
If it fails after a certain number of cycles, it reports an error and transitions back to the previous state.

### State Diagram

```mermaid
stateDiagram-v2
    [*] --> IDLE


    IDLE --> TOUCHDOWN: new_action_flag (and first_shaft_flag)
    IDLE --> DRILLING: new_action_flag and not first_shaft_flag

    TOUCHDOWN --> OVERBURDEN_PASS: Detected ground\n (feed_speed < ground_detect_feed_speed_thr \nfor ground_detect_time_thr\n and depth change > touchdown_depth_delta \n and drill_ctrl.feed_speed > 0.3)

    OVERBURDEN_PASS --> HARD_ROT: is_hard_rot flag set \n (set in check_data if no_rotation_duration \n or high_rp_duration)  
    OVERBURDEN_PASS --> DRILLING: if max_ob_pass_time passed \n or depth change > overburden_layer_thickness

    DRILLING --> PULLUP: long, \n if at mandatory pullup \n depth
    DRILLING --> PULLUP: short, \n if at pullup before finish \n depth
    DRILLING --> WAIT_AFTER_DRILL: depth > target_drill_depth
    DRILLING --> PULLUP: cracked, \n if rmo_rock_type is cracked \n and mandatory_pullup_cnt > 0 \n and depth according to \n cracked_pullup_step
    DRILLING --> PULLUP: if check_if_pullup_needed() \n (too high air pressure)
    DRILLING --> HARD_ROT: if is_hard_rot

    HARD_ROT --> [PREVIOUS_STATE]: if depth change > height_delta_hard_rot \n or rotation_speed > too_low_rotation_speed \n or rotation_pressure < too_high_rp_threshold * 0.6 \n or spindle_depth < ground_spindle_depth 

    PULLUP --> AFTER_PULLUP: if not cracked and (\n (not long and short_pullup_cnt < 3) or short \n) and depth change > short_height \n and is_clear (air and rot pressure is OK) \n or is_at_limit (target_pull_depth or pullup_to_ground_limit)
    PULLUP --> AFTER_PULLUP: if cracked and depth change > cracked_pullup_height
    PULLUP --> UNSTUCK: if is_stuck 

    AFTER_PULLUP --> PASS_SOFT: when ground detected \n(by feed_speed or rotation_pressure)

    RAISE --> UNSTUCK: if is_stuck while depth > target_pull_depth
    RAISE --> IDLE: if depth < target_pull_depth

    WAIT_AFTER_DRILL --> RAISE: wait_after_drill time elapsed

    PASS_SOFT --> PULLUP: if check_if_pullup_needed()
    PASS_SOFT --> WAIT_AFTER_DRILL: if depth >= target_drill_depth
    PASS_SOFT --> DRILLING: pass_soft_time elapsed \n from state change

    UNSTUCK --> [PREVIOUS_STATE]: depth change > unstuck_dist \n (moved enough)
    UNSTUCK --> [PREVIOUS_STATE]: cycle_cnt >= unstuck_try_cnt \n(unstucking failed)

```


### `idle` State Description

The `idle` represents the passive state of the `Driller` node when it's not active.
This state serves as a resting phase and is ready to initiate a new drilling sequence.

#### **1. On Transition**:
   - Reset action sequence and flag for new actions.
   - Stop water flow.

#### **2. Work**:
   - Zero all controls.
   - If there's a new action:
     - Start `TOUCHDOWN` for the first shaft.
     - Otherwise, begin `DRILLING`.

### `touchdown` State Description

In `touchdown` state `Driller` attempts to detect when the drill makes contact with the ground.

#### **1. On Transition**:
   - If the last state was `IDLE`, capture the current spindle depth.
   - Init ground detect timer.

#### **2. Work**:
   - Fetch `touchdown` and `common` parameters (set with current profile).
   - Set controls based on the `touchdown` params:
     - Set feed speed (should be slow), rotation speed (usually 0), air power (usually 0), dust collector control (usually on), and raw feed pressure.
   - Monitor feed speed. If above threshold, reset the detection timer.
   - Check for ground contact based on the following conditions 1) elapsed time since starting ground detection exceeds `ground_detect_duration`, and 2) change in spindle depth from the entry depth exceeds depth_delta, and 3) feed_speed control is above 0.3 (to ensure the control is actually applied). If met:
     - Update the ground level and log it.
     - Transition to `OVERBURDEN_PASS`.

#### **3. Parameters**:

Check `touchdown` sections of profiles in [drilling_profiles.yaml](https://gitlab.com/vistmt/drill-mover/-/blob/master/drill_launch_pack/params/drilling_profiles.yaml).

### `overburden_pass` State Description

The `overburden_pass` state manages the drill as it progresses through the overburden layer, adjusting its parameters based on the ground type (hard, or default currently).

#### **1. On Transition**:
   - If the last state was `TOUCHDOWN`:
     - capture the current spindle depth, 
     - reset the nominal air pressure to `None`, 
     - and set water control as per the `water_ctrl` defined by the current profile.

#### **2. Work**:
   - Update the nominal air pressure to the maximum observed value during the overburden pass.
   - Controls settings:
     - Set feed speed control.
     - Smoothly increase the rotation speed, linearly ramping up based on elapsed time until it reaches the specified rotation speed control for overburden pass.
     - Set feed pressure control.
     - Activate air power and switch dust collector based on the profile settings.
   - Check and transition to the `HARD_ROT` state if hard rotation conditions are detected (`is_hard_rot`).
   - State transition to `DRILLING` if:
     - The duration in the current state exceeds `max_duration`.
     - The drilled depth surpasses the defined `overburden_layer_thickness`.
     - Upon this transition, the nominal air pressure value is logged.

#### **3. Parameters**:

Check `overburden_pass` sections of profiles in [drilling_profiles.yaml](https://gitlab.com/vistmt/drill-mover/-/blob/master/drill_launch_pack/params/drilling_profiles.yaml).

### `drilling` State Description

#### Work

##### 1. Target Depth Monitoring
- If the current spindle depth (rotary head vertical position) meets or exceeds the target depth, 
  transition the drilling state to `WAIT_AFTER_DRILL`.

##### 2. Pullups: transition to the `PULLUP` state
- Determine if a mandatory pullup is needed based on depth.
- Determines a pullup is needed due to high air pressure (by `check_if_pullup_needed()`).

##### 3. Water Control
- Adjust water control based on the current depth.

##### 4. Drilling Speed Monitoring
- Record the timestamp if the smoothed drilling speed exceeds a defined threshold (was used for clay mode).

##### 5. Air Pressure Checks
- Save the timestamp if the air pressure is not nominal.

##### 6. Feed Pressure Regulation
- Calculate the error between the target and actual drill speeds.
- Adjust the error integral (`drilling_speed_error_i`) based on the computed error.
- Clamp this integral to be within defined maximum and minimum values.
- Calculate `psp_regulated` by adding the error integral to the normal feed pressure.
- Adjust `psp_regulated` if rotation pressure exceeds a threshold within a certain time window.
- Limit `psp_regulated` based on the current depth.
- Apply a slow pressure increase mechanism if activated.

##### 7. Feed Speed Control
- Set the feed speed control using profile parameter.

##### 8. Rotation Speed Control
- Adjust rotation speed based on the current depth and a predefined maximum.

##### 9. Air Power Control
- Set the air power control using profile parameter.

##### 10. Dust Collector Control
- Switch dust collector using profile parameter.

##### 11. Hard Rotation Check
- If hard rotation is detected, transition to the `HARD_ROT` state.

#### **Parameters**:

Check `drilling` sections of profiles in [drilling_profiles.yaml](https://gitlab.com/vistmt/drill-mover/-/blob/master/drill_launch_pack/params/drilling_profiles.yaml).

## `hard_rot` State Description

Free the drill from rotation stuck

#### **1. On Transition**:
   - Records the current depth of the drilling spindle.
   - Logs the message indicating too hard rotation and starts recovery.

#### **2. Working Logic (`do_work`)**:

   - Sets the feed speed control for reverse (up) motion based on `feed_speed_ctrl` parameter.
   - Adjusts the rotation speed based on the time elapsed since the last unstuck operation:
     - If more than `unstuck_recency_limit` seconds have passed since the last unstuck event, it uses the value of `rotation_speed_normal_ctrl`.
     - Otherwise, it uses the value of `rotation_speed_after_unstuck_ctrl`.
   - Enables dust collector using `dust_ctrl` parameter.
   - Sets the air power control to `air_power_ctrl`.
   - Sets the feed pressure control to `feed_pressure_ctrl`.

   - **Conditions to return to a previous state**:
       - If the difference in depth since the state started exceeds the threshold defined in `depth_delta`.
       - If current rotation speed is higher than the `min_rotation_speed`.
       - If current rotation pressure is less than `max_rotation_pressure`.
       - If spindle depth is above the ground level.

#### **3. Parameters**:

Check `hard_rot` sections of profiles in [drilling_profiles.yaml](https://gitlab.com/vistmt/drill-mover/-/blob/master/drill_launch_pack/params/drilling_profiles.yaml).

### `pullup` State Description

This state is responsible for pulling up the drill. 
It adjusts the rotation speed, feed speed, and other controls based on current conditions. 
Depending on various conditions, such as the drill reaching a specific height or getting stuck, 
switch to another state, like AFTER_PULLUP or UNSTUCK.

#### **1. On Transition**:

- Records the current entry spindle depth.
- Saves the water control (if `saved_water_restored` flag set) and resets it to zero.

#### **2. Working Logic (`do_work`)**:

- Sets the `short_pullup_distance` based on the entry depth from the ground level for first shaft: 
  - `short_pullup_height_low` if less `depth_threshold` 
  - or `short_pullup_height_high` if > `depth_threshold`.
- for not first shafts sets `short_pullup_distance` to `short_pullup_height_high`
- Sets the rotation speed based on the time since the last unstuck operation
  - to `max_rotation_speed` if < `unstuck_recency_limit` seconds or 
  - to `pullup_rotation_speed` if > `unstuck_recency_limit` seconds
- Adjusts the feed speed based on proximity to the ground.
  - `-feed_speed_normal_ctrl` if depth from the ground > `slow_pullup_depth` or `first_shaft_flag` is `False`;
  - `-feed_speed_reduced_ctrl` else.
- Sets dust collector control according to `dust_ctrl`. 
- Air power control is set to default, but it's turned off if the drill is close to the ground, within a `pullup_compressor_on_to_ground_limit` limit.
- The feed pressure is set to `feed_pressure_ctrl` (usually zero).
- State Transitions:
    - **Transition to `AFTER_PULLUP` When Near Ground (Long Pull-Up)**: if drill is not in the hole or close to the ground 
      within `pullup_to_ground_limit`, the short pull-up counter is reset, and the state transitions to `AFTER_PULLUP`.
    - **Transition to `AFTER_PULLUP` After Short Pull-Up**:
        - If a short pull-up was **explicitly** requested 
          or if a long pull-up was not explicitly requested and the short pull-up count is below `max_short_pullup_cnt`.
        - Executes transition if drill is considered clear (rotation pressure and air pressure are below specified thresholds) and 
          the depth change since entering the state exceeds `short_pullup_distance`.
        - Increments the short pull-up counter and transitions to `AFTER_PULLUP`.
    - **Transition to `UNSTUCK`**: if the drill is stuck, the state transitions to `UNSTUCK`.

!!! note
    If short pullup is not explicitly set, the pullup height will be set to short `short` ( `short_pullup_distance`),
    if pullups counter is less than `max_short_pullup_cnt`, or to long (almost up to the ground) 
    if pullups counter is equal to `max_short_pullup_cnt`. <br> 
    The short pullup counter is reset when not in a hole and in `IDLE` state (to start a new hole with a zero counter). 
    Long pullup can also be requested explicitly (from the mandatory pullups list).

#### **3. Parameters**:

Check `pullup` sections of profiles in [drilling_profiles.yaml](https://gitlab.com/vistmt/drill-mover/-/blob/master/drill_launch_pack/params/drilling_profiles.yaml).

### `after_pullup` State Description

State defines the behavior of the system after a pull-up.

#### 1. **On Transition**:
- Resets the `ground_detect_time_started` timer
- Sets `water_ctrl` to 0

#### 2. **Working Logic (`do_work`)**:

- Sets the feed speed to `feed_speed_ctrl`.
- Sets the feed pressure to `feed_pressure_ctrl_raw`.
- Sets rotations speed to `rotation_speed_ctrl`.
- Turn on dust collector according to `dust_ctrl`.
- Sets the air power to `air_power_ctrl`.
- Restores water control to the previously saved value (`node.saved_water_ctrl` saved in `pullup` state `on_transition_to()`).

- Ground detection and updating `nominal_air_pres`:
  - If the feed speed surpasses the threshold (`ground_detect_feed_speed_thr`) or the rotation pressure drops below a threshold (`ground_detect_rot_pres`):
    - Resets the `ground_detect_time_started` timer.
    - If certain air pressure conditions are met (current `air_pressure` exceeds `nominal_air_pres` and is below `max_air_pressure - 0.5`), updates the `nominal_air_pres` with the current air pressure.
- If the time since `ground_detect_time_started` exceeds `ground_detect_duration`, it triggers a state transition:
  - Transitions to the `PASS_SOFT` state.
  - Logs the new nominal air pressure.
  - Restores the water control value and flags the water as restored.

#### **3. Parameters**:

Check `after_pullup` sections of profiles in [drilling_profiles.yaml](https://gitlab.com/vistmt/drill-mover/-/blob/master/drill_launch_pack/params/drilling_profiles.yaml).

### `raise` State Description

Defines the system's behavior during the drill raising action.

#### **1. On Transition**:

- Stops the water flow.

#### **2. Working Logic (`do_work`)**:

- If the drill's spindle is nearly at ground level (less than 0.1 m difference):
  - The air is turned off.
- Otherwise:
  - for first `unstuck_recency_limit` seconds since last unstuck event: rotation speed set to `rotation_speed_after_unstuck_ctrl`
  - if the time since last unstuck event > `unstuck_recency_limit`:
    - the rotation speed set to either `rotation_speed_normal_ctrl` if depth from ground > `stop_rotation_depth` or to 0 (if less)
  - If distance to bottom (target) is less than `distance_from_finish_to_hold_air`, air is on. 
  - Otherwise, air is turned off.
- Dust collector control is set to `dust_ctrl`, 
- Feed pressure is set to `feed_pressure_ctrl` (usually 0).
- If the drill data is not valid, logs warning and returns without further actions.
- Based on the current spindle depth relative to the `target_pull_depth` (`depth_err`):
  - feed speed switching between `-min_raise_feed_speed` inside `reduced_speed_zone` and `-max_raise_feed_speed`.
  - If the drill gets stuck (`node.is_stuck`), the state transitions to `UNSTUCK`.
  - If the spindle depth is less than (rotary head is above) the `target_pull_depth`, all controls are turned off and the state transitions to `IDLE`.

#### **3. Parameters**:

Check `raise` sections of profiles in [drilling_profiles.yaml](https://gitlab.com/vistmt/drill-mover/-/blob/master/drill_launch_pack/params/drilling_profiles.yaml).

### `wait_after_drill` State Description

In summary, the WaitAfterDrillState prepares the drill for raising after waiting for a specified duration after drilling.

#### **1. Working Logic (`do_work`)**:

  - Sets the feed speed to `feed_speed_ctrl`(usually slow negative speed).
  - Feed pressure is set to `feed_pressure_ctrl` (usually pass soft pressure).
  - The rotation speed of the drill is set to `rotation_speed_ctrl`.
  - Dust collector control is set to `dust_ctrl`.
  - Air power is adjusted to the `air_power_ctrl`.
  - After waiting for the duration specified in `max_duration`, the state transitions to `RAISE`.

#### **2. Parameters**:

Check `wait_after_drill` sections of profiles in [drilling_profiles.yaml](https://gitlab.com/vistmt/drill-mover/-/blob/master/drill_launch_pack/params/drilling_profiles.yaml).

### `pass_soft` State Description

This state represents the drill's action when passing through softer terrains.

#### **1. Working Logic (`do_work`)**:

- Apply control parameters from the `pass_soft` section of the current profile:
  - Sets feed pressure to `feed_pressure_ctrl`.
  - Sets the drill's rotation speed (safe) to `rotation_speed_ctrl`.
  - Enables or disables the dust collector based on `dust_ctrl`.
  - Modifies air power according to the `air_power_ctrl` parameter.
- State Transitions:
  - If the pull-up conditions are met, as determined by `check_if_pullup_needed()`, the state transitions to `PULLUP`.
  - If the spindle depth reaches or exceeds the target drill depth, the state transitions to `WAIT_AFTER_DRILL`.
  - If the duration in the current state exceeds the maximum duration defined by `max_duration`, the state transitions to `DRILLING`.

#### **2. Parameters**:

Check `pass_soft` sections of profiles in [drilling_profiles.yaml](https://gitlab.com/vistmt/drill-mover/-/blob/master/drill_launch_pack/params/drilling_profiles.yaml).

### `unstuck_down` State Description

This state represents the drill's strategy when it gets stuck, 
attempting to free itself by initially moving downwards.

#### **1. On Transition Logic (`on_transition_to`):**

- If unstuck actions are enabled (`enable_unstuck`):
  - Records the current spindle depth as the starting depth (`unstuck_h_start`) for the unstuck operation.
- If not enabled:
  - An internal error is reported with the message "Stuck!", and appropriate error handling takes place based on the configuration (`rc_unstuck` event).

#### **2. Working Logic (`do_work`):**

- Apply control parameters from the `unstuck_down` section of the current profile:
  - Sets the air power using `air_power_ctrl`.
  - Adjusts the drill's rotation speed with `rotation_speed_ctrl`.
  - Activates or deactivates the dust collector based on `dust_ctrl`.
  - Modifies the feed pressure (raw!) control using `feed_pressure_ctrl_raw`.
  - Alters the feed speed to `feed_speed_ctrl` (must be positive to move the drill down).
- State Transitions:
  - The state transitions to `UNSTUCK_SPIN` if:
    - The difference between the current spindle depth and the recorded entry spindle depth exceeds `depth_delta`.
    - The time in the current state surpasses the `max_duration` defined in `unstuck_down` parameters.

#### **3. Parameters**:

Check `unstuck_down` sections of profiles in [drilling_profiles.yaml](https://gitlab.com/vistmt/drill-mover/-/blob/master/drill_launch_pack/params/drilling_profiles.yaml).

### `unstuck_spin` State Description

This state represents the unstuck strategy attempting to free drill by spinning in place.

#### **1. Working Logic (`do_work`)**:

- Apply control parameters from the `unstuck_spin` section of the current profile:
  - Sets the air power using `air_power_ctrl`.
  - Adjusts the drill's rotation speed with `rotation_speed_ctrl`.
  - Activates or deactivates the dust collector based on `dust_ctrl`.
  - Halts feed movement by setting the feed speed and feed pressure to 0.
- State Transitions:
  - If the duration spent in the current state surpasses the `max_duration`, the following actions are taken:
    - The `last_unstuck_time` is updated to the current time.
    - The system tries to return to the previous state recorded before the unstuck attempt.
    - If the previous state's name is invalid or not defined, an internal error is raised.

#### **2. Parameters**:

Check `unstuck_spin` sections of profiles in [drilling_profiles.yaml](https://gitlab.com/vistmt/drill-mover/-/blob/master/drill_launch_pack/params/drilling_profiles.yaml).

### `unstuck_up` State Description

In summary, `UnstuckUpState` is a state in which the drill attempts to free itself moving upwards. 
It alternates between moving the drill upwards and downwards for a specified number of cycles to free itself.
If it fails after a certain number of cycles, it reports an error and transitions back to the previous state.

#### **1. On Transition Logic (`on_transition_to`):**

- If unstuck actions are enabled (`enable_unstuck`):
  - Records the current spindle depth as the starting depth (`unstuck_h_start`) for the unstuck operation.
- If not enabled:
  - An internal error is reported with the message "Stuck!", and appropriate error handling takes place based on the configuration (`rc_unstuck` event).

#### **2. Working Logic (`do_work`)**:

- Calculates how many complete cycles have been executed (cycle consists of `cracked_unstuck_uptime` and `cracked_unstuck_downtime`).
- Sets the feed pressure, rotation speed, and air power using their respective control parameters from the `unstuck_up` section of the current profile
- If the difference between the current spindle depth and the recorded depth (`entry_spindle_depth`) exceeds a threshold (`cracked_unstuck_dist`):
  - System considers that it has moved enough and transitions back to the previous state, logging a success message.
- If the time within a cycle (`cycle_period`) is less than or equal to the defined up movement time (`cracked_unstuck_uptime`):
  - Sets the feed speed for upward movement.
- Otherwise, the feed speed is set for downward movement.
- If the number of complete cycles surpasses the defined limit (`cracked_unstuck_try_cnt`):
  - Transitions back to the previous state and raises an internal error, indicating a failure in the unstuck attempt.

#### **3. Parameters**:

Check `unstuck_up` sections of profiles in [drilling_profiles.yaml](https://gitlab.com/vistmt/drill-mover/-/blob/master/drill_launch_pack/params/drilling_profiles.yaml).
