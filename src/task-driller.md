# Обзор

Мне нужно сделать заново Driller ноду управления бурения в замен имеющейся ROS-1 ноды для робота-бурового станка на ROS2 для этого проекта бортового софта.

## Входы и выходы

## На входе - надо подписываться с помощью create_subscription из BaseFSM (base_fsm.py)

1) **Action (команда управления, свой формат)**

`/driller_action`

```
drill_msgs/DrillerAction
Header header
int id                      # ID скважины
float32 drill_spindle_depth # Целевая глубина шпинделя для бурения (м, положительная вниз)
float32 raise_spindle_depth # Целевая глубина шпинделя для подъема (м, положительная вниз)
bool first_rod_flag         # Флаг первой штанги (требует детекции грунта)
bool last_rod_flag          # Флаг последней штанги (влияет на профили)
float32 hole_depth          # Общая глубина скважины от устья (м)
float32 inclination         # Угол наклона мачты (градусы, 0=вертикально)
```

**Пример данных Action:**
```json
{
  "id": 30,
  "drill_spindle_depth": 12.348463670137011,
  "raise_spindle_depth": 1.27,
  "first_rod_flag": true,
  "last_rod_flag": true,
  "hole_depth": 11.0,
  "inclination": 0.0
}
```

2) **Состояние бурения** 

`/drill_state`

```
drill_msgs/DrillState (src/drill_msgs/msg/DrillState.msg)
std_msgs/Header header
float32 head_pos            # Позиция головы шпинделя (м, 0 вверху, положительная вниз)
float32 head_speed          # Скорость движения головы (м/с, положительная вниз)
float32 head_angular_pos    # Угловая позиция (градусы)
float32 drill_rpm           # Скорость вращения бура (об/мин)
float32 feed_pressure       # Давление подачи (бар)
float32 rot_pressure        # Давление вращения (бар)
float32 air_pressure        # Давление воздуха (бар)
bool head_pos_is_reliable   # Флаг достоверности позиции головы
```

При необходимости, можно будет использовать src/drill_msgs/msg/DepthInfo.msg также (проанализировать).

3) **Тип породы и наличие воды** для выбора подрежима бурения (см. профили)

`/rock_type`
```
drill_msgs/RockType
uint8 type          # Тип породы: NORMAL=0, HARD=1, CRACKED=2
bool is_flooded     # Наличие воды в скважине
```

4) **Статус главной машины состояний**

`/main_state_machine_status`

а также (на входе, функциональность base_node/base_node/base_fsm.py -- подписывается автоматически, не надо вручную):

5) **Режим робота**
```
/robomode
drill_msgs/BoolStamped
```

6) **Разрешения на движение**
```
/permission
drill_msgs/Permission (src/drill_msgs/msg/Permission.msg)
```

## На выходе

1) **Сигнал (уставка) управления бурением** (по скоростям) -- это далее идет на низкоуровневый регулятор (Drill regulator)

`/driller_setpoints`
```
drill_msgs/DrillCtrl (src/drill_msgs/msg/DrillCtrl.msg)
Header header
float32 feed_speed          # Скорость подачи: м/с или [-1..1] если is_raw=false
float32 rotation_speed      # Скорость вращения: об/мин или [-1..1] если is_raw=false
float32 feed_pressure       # Давление подачи: бар или [-1..1] если is_raw=false
bool feed_speed_is_raw      # true = абсолютные единицы, false = нормализованные [-1..1]
bool rotation_speed_is_raw  # true = абсолютные единицы, false = нормализованные [-1..1]
bool feed_pressure_is_raw   # true = абсолютные единицы, false = нормализованные [-1..1]
```

2) **Уставка (команда) управления подачей воды**

`/water_ctrl_auto`
```
drill_msgs/FloatCtrl (src/drill_msgs/msg/FloatCtrl.msg)
Header header
float32 value               # Уставка подачи воды [0..1]
```

3) **Уставка (команда) управления воздухом/компрессором**

`/air_ctrl`
```
drill_msgs/AirCtrl (src/drill_msgs/msg/AirCtrl.msg)
Header header
float32 power               # Мощность компрессора [0..1]
bool enabled                # Включен/выключен компрессор
```

4) **Action (команда) управления люнетом** (arm, rod support)

`/arm_action`
```
drill_msgs/OpenCloseAction
Header header
int id                      # ID команды
int action                  # 1 = открыть, -1 = закрыть
```

а также (на выходе, функциональность base_node/base_node/base_fsm.py -- работает автоматически, не надо создавать):

5) **События системы**
`/events`

6) **Внутренние отчеты**
`/internal_report`

7) **Статус машины состояний**
```
/driller_status
drill_msgs/StateMachineStatus
```


# Функциональность ROS-1 ноды (анализ старой реализации)

Далее идет описание имеющейся ноды на основе анализа кода `src/driller-old-ros1/`.
Это описание для информации. При реализации ROS-2 можно улучшить архитектуру и код, но функциональность должна остаться не меньше.

## Архитектура старой ноды

Нода реализует FSM на базе `AbstractNodeStateMachine`, параметры получает из `rosparam`, профили применяет динамически, управление публикуется централизованно из одного места цикла.

**Основные компоненты:**
- **DrillerNode** - главный класс, наследует AbstractNodeStateMachine
- **Состояния** - отдельные классы для каждого состояния (IdleState, TouchDownState, etc.)
- **DrillControls** - структура для хранения управляющих сигналов
- **Dict2Obj** - утилита для работы с параметрами как объектами
- **Система профилей** - динамическое применение профилей бурения

## Конечный автомат состояний (11 состояний)

**Список состояний:**
1. `idle` - ожидание команды
2. `touchdown` - детекция касания грунта (только для первой штанги)
3. `overburden_pass` - прохождение верхнего слоя грунта
4. `drilling` - основное бурение
5. `hard_rot` - освобождение от заклинивания вращения
6. `pullup` - подтяжка бура (короткая/длинная)
7. `after_pullup` - возврат в скважину после подтяжки
8. `pass_soft` - прохождение мягких участков
9. `wait_after_drill` - ожидание после завершения бурения
10. `raise` - подъем бура после завершения скважины
11. `unstuck` - освобождение от заклинивания движения

## Система безопасности

- **Проверка robomode и permission** встроена в BaseFSM
- **Таймауты подписок** ведут к безопасной остановке
- **Контроль воздуха:** номинальное давление, время реакции компрессора
- **Детекция заклинивания:** по скорости движения, вращения, давлению
- **Валидация глубины:** проверка достоверности данных датчиков
- **Управление люнетом:** блокировка движения при неправильном положении

## Параметры конфигурации

- **Профили бурения** — в файле `drilling_profiles.yaml`, ключ `DrillerNode`
- **Параметры ноды:** `rate`, `user_feed_pressure`, `user_rotation_speed`
- **Система профилей:** `default` + динамические профили (`hard`, `wet`, `cracked`, `*-short`)



## Особенности реализации старой ноды

### Система профилей (ключевая особенность)

**Базовая структура:**
- `default` - базовые параметры для всех состояний
- `profiles` - модификации базовых параметров для специальных условий

**Динамическое применение профилей:**
```python
def process_profiles(self):
    profiles = []
    # Добавляем профили на основе RMO входов
    if self.subs.rmo_rock_type and self.subs.rmo_rock_type.data:
        profiles.append(self.subs.rmo_rock_type.data)  # 'hard', 'cracked'
    if self.subs.rmo_hole_water and self.subs.rmo_hole_water.data:
        profiles.append(self.subs.rmo_hole_water.data)  # 'wet'

    # Для коротких скважин
    if self.is_short_hole():
        profiles.insert(0, 'default-short')
        # Заменяем профили на их -short версии если есть
        profiles = [profile if profile + '-short' not in self.node_params["profiles"]
                   else profile + '-short' for profile in profiles]
```

**Профили в drilling_profiles.yaml:**
- `default-short` - для коротких скважин (< 9.5м)
- `hard` - для твердых пород (увеличенное давление)
- `wet` - для скважин с водой (дополнительные подтяжки)
- `cracked` - для трещиноватых пород (специальные подтяжки)
- `wet-short`, `cracked-short` - комбинированные профили

### Глубинно-зависимые параметры

**Система ссылок на глубину:**
```yaml
# Пример из drilling_profiles.yaml
mandatory_pullups:
  - depth: { from_ground: 3.5 }        # От поверхности земли
    action: long
  - depth: { from_shaft_finish: 1.5 }  # От конца текущей штанги
    action: short
  - depth: { from_hole_finish: 2.5 }   # От конца скважины
    action: long
  - depth: { drilled: 1.5 }            # От начала текущего состояния
    action: cracked
```

**Обработка в коде:**
```python
def get_value_for_current_depth(self, depth_dict, default=None):
    # Преобразование всех ссылок к единой системе координат (spindle_depth)
    # Сортировка по глубине
    # Выбор подходящего значения для текущей глубины
```

### Система управления

**DrillControls - централизованное управление:**
```python
class DrillControls:
    def __init__(self):
        self.feed_speed = 0          # Скорость подачи
        self.rotation_speed = 0      # Скорость вращения
        self.feed_pressure = 0       # Давление подачи
        self.air_power = 0          # Мощность компрессора
        self.dust_collector_on = False  # Пылесборник
```

**Отправка управления:**
```python
def send_control(self, deny_string_movement=True):
    # Отправка DrillCtrl
    # Отправка CompressorCtrl
    # Отправка пылесборника
```

### Обработка ошибок
Система использует централизованную обработку через `handle_internal_error()`:
- **Предупреждения**: логирование без остановки работы
- **Критические ошибки**: остановка управления + генерация событий
- **События**: отправка в систему мониторинга для операторов

# Требования к программной реализации 

- ROS2
- Базироваться на BaseFSM (base_node/base_node/base_fsm.py), см. base_node/BaseFSM.md, реализовать описанные лучшие практики по структуре программы, логированию, состояниям, ивентам и т.п. 
- не переусложнять, не думблировать код;
- Не использовать параметры ROS2, вместо этого использовать функциональность BaseNode для собственной реализации получения параметров от собственного сервера.
- При использовании параметров, не использовать get с значением по умолчанию - лучше при инициализации вылететь, если неправильное имя параметра.
- Не переусердствовать с getatr и тому подобным для ROS-сообщения, у которых структура фиксирована.
- Не делать fallback, compatability и т.п. без явной необходимости, приоритет за однозначным понятным кодом.
- Не держать константы в коде, а вводить и использовать параметры, см. params_server/params_server/base_config/vehicle.yaml params_server/params_server/base_config/nodes.yaml
- Не переусложнять код, делать его наиболее коротким и понятным
- KISS, SOLID
- Обязательно сделать README.md с полным описанием входов выходов и работы
- Желательно сделать launch file (см src/launchpack/launch/general.xml для примера, но нам для одной ноды и параметр, если нужно)
- Соблюдать лучшие практики ROS2 (когда это не противоречит требованиям из BaseFSM.md и BaseNode.md, например не использовать ROS2-параметры, использовать логирование из BaseNode.md а не напрямую, работать со временем как показано в BaseNode.md, и т.д.)
- не дублировать а использовать функционал BaseFSM.
- Документация в README.md на русском языке. 
- Комментарии в коде -- на английском.
- Всегда помни о задаче в целом и доделывай до конца, от общего к частному.
- Заботься о красивой архитектуре.


# Примеры 

## Старая ROS1-нода driller бурового станка /Users/<USER>/Work/ZyfraDrillv1/catkin_ws/src/drill-software/drill-mover/driller

Нужно использовать старый пример /Users/<USER>/Work/ZyfraDrillv1/catkin_ws/src/drill-software/drill-mover/driller
Что относительно этого примера в нашей реализации надо поменять: 
- Он был на ROS1, 
- он со старым окружением (другая base node, отличающиеся сообщения и логика работы немного). 

Документация по старой ноде /Users/<USER>/Work/drill-docs-dev/docs/onboard-system/drilling-and-mechanisms/driller.md

Профили бурения: конфиг и документация в виде комментов /Users/<USER>/Work/ZyfraDrillv1/catkin_ws/src/drill-software/drill-mover/drill_launch_pack/params/drill/drilling_profiles.yaml

# Детальный анализ состояний (на основе старого кода)

## 1. IdleState - Состояние ожидания

**Назначение:** Пассивное состояние ожидания новой команды бурения.

**При входе в состояние:**
```python
def on_transition_to(self):
    self.node._cur_action_seq = -1
    self.node.new_action_flag = False
    self.node.set_water(0, force_send=True)  # Отключить воду
    self.node.last_mandatory_pullup_spindle_depth = None  # Сброс подтяжек
```

**Рабочий цикл:**
```python
def do_work(self):
    self.node.zero_ctrl()  # Обнулить все управления
    self.node.short_pullup_cnt = 0  # Сброс счетчика коротких подтяжек

    # Переход при получении новой команды
    if self.node.new_action_flag:
        if not self.node.subs.drill_state.is_valid:
            # Ждем валидных данных глубины
            return
        else:
            self.node.action_start_spindle_depth = self.node.subs.drill_state.head_pos

        if self.node.first_rod_flag:
            self.node.set_state(TOUCHDOWN)  # Первая штанга - детекция грунта
        else:
            self.node.set_state(DRILLING)   # Не первая штанга - сразу бурение
```

## 2. TouchDownState - Детекция касания грунта

**Назначение:** Определение момента касания бура с поверхностью грунта (только для первой штанги).

**При входе в состояние:**
```python
def on_transition_to(self):
    if self.node.get_last_state().get_name() == IDLE:
        self.entry_spindle_depth = self.node.subs.drill_state.head_pos
    self.ground_detect_time_started = rospy.get_time()
```

**Рабочий цикл:**
```python
def do_work(self):
    # Параметры из профиля
    touchdown_params = self.node.current_params.touchdown
    common_params = self.node.current_params.common

    # Управления (медленная подача, без вращения и воздуха)
    self.node.controls.feed_speed = touchdown_params.feed_speed_ctrl  # ~0.2 м/с
    self.node.controls.rotation_speed = 0
    self.node.controls.air_power = 0
    self.node.controls.dust_collector_on = True
    self.node.controls.feed_pressure = touchdown_params.feed_pressure_ctrl_raw  # ~0.25 бар

    # Детекция грунта по скорости подачи
    if self.node.subs.drill_state.head_speed > common_params.ground_detect_feed_speed_thr:
        self.ground_detect_time_started = rospy.get_time()  # Сброс таймера если движемся быстро

    # Условия перехода в overburden_pass:
    # 1. Скорость < порога достаточно долго (ground_detect_duration = 1.5с)
    # 2. Прошли минимальную глубину (depth_delta = 0.25м)
    # 3. Команда подачи активна (feed_speed > 0.3)
    elif (rospy.get_time() - self.ground_detect_time_started > common_params.ground_detect_duration and
          self.node.subs.drill_state.head_pos - self.entry_spindle_depth >= touchdown_params.depth_delta and
          self.node.subs.drill_ctrl.feed_speed > 0.3):

        # Зафиксировать уровень грунта
        self.node.ground_spindle_depth = self.node.subs.drill_state.head_pos
        self.node.log("Set ground spindle depth to %.2f" % self.node.ground_spindle_depth)
        self.node.set_state(OVERBURDEN_PASS)
```

## 3. OverburdenPassState - Прохождение верхнего слоя

**Назначение:** Прохождение верхнего слоя грунта с плавным увеличением оборотов.

**При входе в состояние:**
```python
def on_transition_to(self):
    if self.node.get_last_state().get_name() == TOUCHDOWN:
        self.entry_spindle_depth = self.node.subs.drill_state.head_pos
        self.node.nominal_air_pres = None  # Сброс номинального давления
        self.node.set_water(self.node.current_params.overburden_pass.water_ctrl)  # Включить воду
```

**Рабочий цикл:**
```python
def do_work(self):
    overburden_params = self.node.current_params.overburden_pass

    # Обновление номинального давления воздуха (максимальное наблюдаемое)
    if (self.node.nominal_air_pres is None or
        self.node.nominal_air_pres < self.node.subs.pressure_state.air_pressure):
        self.node.nominal_air_pres = self.node.subs.pressure_state.air_pressure

    # Управления
    self.node.set_water(overburden_params.water_ctrl)  # ~0.58
    self.node.controls.feed_speed = overburden_params.feed_speed_ctrl  # 1.0

    # Плавное увеличение оборотов за spinup_time (3 сек)
    srs = overburden_params.rotation_speed_ctrl  # 57 об/мин
    elapsed_time_fraction = self.node.get_current_state_duration() / overburden_params.spinup_time
    self.node.set_safe_rotation(min(srs, elapsed_time_fraction * srs))

    self.node.controls.feed_pressure = overburden_params.feed_pressure_ctrl  # 55 бар
    self.node.controls.air_power = overburden_params.air_power_ctrl  # 1
    self.node.controls.dust_collector_on = True

    # Переходы
    if self.node.is_hard_rot:
        self.node.set_state(HARD_ROT)

    depth_delta = self.node.subs.drill_state.head_pos - self.entry_spindle_depth
    if (self.node.get_current_state_duration() > overburden_params.max_duration or  # 60 сек
        depth_delta >= overburden_params.overburden_layer_thickness):  # 0.5 м
        self.node.log("Nominal air pressure set to %.2f bar" % self.node.nominal_air_pres)
        self.node.set_state(DRILLING)
```

## Подробная спецификация реализации DrillerNode (ROS2)

### Архитектура

- Наследование: `DrillerNode` ← `BaseFSM` (`src/base_node/base_node/base_fsm.py`).
- Логирование/ивенты: через `BaseNode.log()` и коды из `Global.events`.
- Параметры: только через `params_server` (`BaseNode`), никаких ROS‑параметров. Обработка обновлений — `on_params_update()`.
- Частота цикла: параметр `rate` (Гц) из `nodes.yaml`.
- Состояния реализовать отдельными классами, регистрируются через `add_states()`.

### Подписки

Использовать интерфейсы, перечисленные в разделе «Входы и выходы». Подключение выполнять через `add_subscriber(topic, msg_type, name, timeout)` (там, где это не делает `BaseFSM` автоматически).

### Публикации

Использовать интерфейсы, перечисленные в разделе «Входы и выходы». Публикацию всех уставок и команд централизовать в одном месте цикла ноды (единая функция отправки, вызываемая из одного хука цикла FSM).

### Сервис

- `/{node_name}/recalibrate_air` (`std_srvs/Trigger`): зафиксировать `nominal_air_pres` по текущему `air_pressure` (разрешён вне `idle`).

## 4. DrillingState - Основное бурение (самое сложное состояние)

**Назначение:** Основной режим бурения с I-регулятором давления, контролем глубины, обязательными подтяжками.

**При входе в состояние:**
```python
def on_transition_to(self):
    self.last_fast_drill_ts = 0
    self.high_rp_last_time = 0
    self.drilling_speed_error_i = 0  # Интеграл ошибки скорости
    self.prev_drill_ts = rospy.get_time()
    self.last_air_not_nominal_time = 0
    self.increase_press_slowly = True  # Флаг плавного увеличения давления
    self.entry_spindle_depth = self.node.subs.drill_state.head_pos
```

**Рабочий цикл (очень сложный):**
```python
def do_work(self):
    drilling_params = self.node.current_params.drilling
    common_params = self.node.current_params.common
    dt = rospy.get_time() - self.prev_drill_ts
    self.prev_drill_ts = rospy.get_time()

    # 1. ПРОВЕРКА ДОСТИЖЕНИЯ ЦЕЛЕВОЙ ГЛУБИНЫ
    if self.node.subs.drill_state.head_pos >= self.node.target_drill_spindle_depth:
        self.node.set_state(WAIT_AFTER_DRILL)
        return

    # 2. ОБЯЗАТЕЛЬНЫЕ ПОДТЯЖКИ (по глубинным таблицам)
    pullup_action = self.get_pullup_action_for_depth(
        self.entry_spindle_depth,
        drilling_params.mandatory_pullups,
        self.node.last_mandatory_pullup_spindle_depth
    )
    if pullup_action:
        self.node.log("Starting %s pullup at depth: %f" % (pullup_action, self.node.subs.drill_state.head_pos))
        self.node.last_mandatory_pullup_spindle_depth = self.node.subs.drill_state.head_pos
        if pullup_action == 'long':
            self.node.set_state(PULLUP, long=True)
        elif pullup_action == 'short':
            self.node.set_state(PULLUP, short=True)
        elif pullup_action == 'cracked':
            self.node.set_state(PULLUP, cracked=True)
        return

    # 3. ПРОВЕРКА НЕОБХОДИМОСТИ ПОДТЯЖКИ ПО ДАВЛЕНИЮ ВОЗДУХА
    if self.node.check_if_pullup_needed():
        self.node.set_state(PULLUP)
        return

    # 4. УПРАВЛЕНИЕ ВОДОЙ (по глубинной таблице)
    water_ctrl = self.get_value_for_current_depth(drilling_params.water_ctrl)
    self.node.set_water(water_ctrl)

    # 5. КОНТРОЛЬ СКОРОСТИ БУРЕНИЯ
    if self.node.drill_speed_smoothed > drilling_params.low_drill_speed_thr:
        self.last_fast_drill_ts = rospy.get_time()

    # 6. КОНТРОЛЬ ДАВЛЕНИЯ ВОЗДУХА
    if not self.node.check_air_is_nominal():
        self.last_air_not_nominal_time = rospy.get_time()

    # 7. I-РЕГУЛЯТОР ДАВЛЕНИЯ ПОДАЧИ (ключевая логика!)
    # Интеграл ошибки скорости бурения
    drilling_speed_error = drilling_params.target_drill_speed - self.node.drill_speed_smoothed
    self.drilling_speed_error_i += drilling_speed_error * dt * drilling_params.drilling_speed_i_gain

    # Ограничение интеграла
    if self.drilling_speed_error_i > drilling_params.drilling_speed_i_max:
        self.drilling_speed_error_i = drilling_params.drilling_speed_i_max
    elif self.drilling_speed_error_i < drilling_params.drilling_speed_i_min:
        self.drilling_speed_error_i = drilling_params.drilling_speed_i_min

    # Расчет регулируемого давления
    psp_regulated = self.node.node_params['user_feed_pressure'] + self.drilling_speed_error_i
    if psp_regulated < drilling_params.feed_pressure_min:
        psp_regulated = drilling_params.feed_pressure_min

    # Снижение давления при высоком давлении вращения
    if self.node.subs.pressure_state.rot_pressure > drilling_params.high_rp_threshold:
        self.high_rp_last_time = rospy.get_time()

    if rospy.get_time() - self.high_rp_last_time < drilling_params.high_rp_duration_threshold:
        psp_regulated = min(drilling_params.feed_pressure_reduced, psp_regulated)

    # Ограничение по глубинной таблице
    max_pd = self.get_value_for_current_depth(drilling_params.feed_pressure_limit)
    psp_regulated = min(psp_regulated, max_pd)

    # Плавное увеличение давления
    if self.increase_press_slowly:
        if self.node.controls.feed_pressure < drilling_params.feed_pressure_min:
            self.node.controls.feed_pressure = drilling_params.feed_pressure_min

        psp_slow = self.node.controls.feed_pressure + drilling_params.slow_press_increase_rate * dt
        if psp_slow >= psp_regulated:
            self.increase_press_slowly = False
        self.node.controls.feed_pressure = min(psp_regulated, psp_slow)
    else:
        self.node.controls.feed_pressure = psp_regulated

    # 8. ОСТАЛЬНЫЕ УПРАВЛЕНИЯ
    self.node.controls.feed_speed = drilling_params.feed_speed_ctrl  # 1.0

    # Скорость вращения (ограничена глубинной таблицей и пользовательской уставкой)
    max_rot = self.get_value_for_current_depth(drilling_params.rotation_speed_limit)
    self.node.set_safe_rotation(min(max_rot, self.node.node_params['user_rotation_speed']))

    self.node.controls.air_power = drilling_params.air_power_ctrl
    self.node.controls.dust_collector_on = drilling_params.dust_ctrl

    # 9. ПРОВЕРКА ЗАКЛИНИВАНИЯ ВРАЩЕНИЯ
    if self.node.is_hard_rot:
        self.node.set_state(HARD_ROT)
```

**Ключевые функции для работы с глубинными таблицами:**
```python
def get_value_for_current_depth(self, depth_dict, default=None):
    """Получить значение параметра для текущей глубины из глубинной таблицы"""
    # Преобразование всех ссылок на глубину к единой системе координат
    # Поддержка: from_ground, from_added_shaft_start, from_shaft_finish, from_hole_finish

def get_pullup_action_for_depth(self, entry_spindle_depth, pullup_dict, last_mandatory_pullup_spindle_depth):
    """Определить необходимость подтяжки для текущей глубины"""
    # Поддержка дополнительно: drilled (от начала состояния)
```

### Параметры и профили

- Профили: `src/params_server/base_config/drilling_profiles.yaml`, ключ `Driller` (структура как в ROS1).
- Выбор профилей:
  - Всегда `default` как база.
  - По РМО (`drill_msgs/RockType`):
    - `type == HARD` → добавить профиль `hard`.
    - `type == CRACKED` → добавить профиль `cracked`.
    - `is_flooded == true` → добавить профиль `wet`.
  - Короткая скважина: если `hole_depth < default.common.short_hole_max_depth`, первыми применить `default-short`, а профиль `X` заменить на `X-short` при наличии.
  - Слияние — последовательное, последний слой имеет приоритет.
  - При изменении входов профиля (RMO, длина) — переcчитать `current_params` на лету.

- Параметры ноды (`nodes.yaml/Driller`):
  - `rate`
  - `user_feed_pressure` — старт для i‑регулятора подачи в `drilling`.
  - `user_rotation_speed` — верхний предел уставки вращения в `drilling`.

## 5. PullUpState - Подтяжка бура

**Назначение:** Подтяжка бура для очистки скважины (короткая/длинная/cracked).

**При входе в состояние:**
```python
def on_transition_to(self):
    self.entry_spindle_depth = self.node.subs.drill_state.head_pos

    # Сохранение и отключение воды
    if self.node.saved_water_restored:
        self.node.saved_water_ctrl = self.node.subs.water_ctrl.value
        self.node.saved_water_restored = False
    self.node.set_water(0, remember=False)
```

**Рабочий цикл:**
```python
def do_work(self):
    pullup_params = self.node.current_params.pullup

    # Определение дистанции короткой подтяжки
    if self.node.first_rod_flag:
        entry_depth_from_ground = self.entry_spindle_depth - self.node.ground_spindle_depth
        if entry_depth_from_ground < pullup_params.depth_threshold:  # 4м
            self.short_pullup_distance = pullup_params.short_pullup_height_low   # 0.8м
        else:
            self.short_pullup_distance = pullup_params.short_pullup_height_high  # 2.0м
    else:
        self.short_pullup_distance = pullup_params.short_pullup_height_high

    # Управления
    # Вращение зависит от времени с последнего unstuck
    if rospy.get_time() - self.node.last_unstuck_time > self.node.current_params.common.unstuck_recency_limit:
        self.node.set_safe_rotation(pullup_params.rotation_speed_normal_ctrl)  # 79
    else:
        self.node.set_safe_rotation(pullup_params.rotation_speed_after_unstuck_ctrl)  # 115

    # Скорость подачи (вверх) зависит от близости к грунту
    if self.node.first_rod_flag:
        current_depth_from_ground = self.node.subs.drill_state.head_pos - self.node.ground_spindle_depth
        if current_depth_from_ground > pullup_params.slow_pullup_depth:  # 3м
            self.node.controls.feed_speed = -pullup_params.feed_speed_normal_ctrl  # -0.61
        else:
            self.node.controls.feed_speed = -pullup_params.feed_speed_reduced_ctrl  # -0.3
    else:
        self.node.controls.feed_speed = -pullup_params.feed_speed_normal_ctrl

    self.node.controls.dust_collector_on = True
    self.node.controls.air_power = pullup_params.air_power_normal_ctrl  # 1

    # Отключение компрессора у поверхности
    if self.node.first_rod_flag:
        current_depth_from_ground = self.node.subs.drill_state.head_pos - self.node.ground_spindle_depth
        if current_depth_from_ground < pullup_params.pullup_compressor_on_to_ground_limit:  # 2м
            self.node.controls.air_power = 0

    self.node.controls.feed_pressure = 0

    # ПЕРЕХОДЫ
    # 1. Длинная подтяжка - до поверхности
    if not self.node.is_in_hole(max_distance=pullup_params.pullup_to_ground_limit):  # 0.2м
        self.node.short_pullup_cnt = 0
        self.node.set_state(AFTER_PULLUP)
        return

    depth_delta = self.entry_spindle_depth - self.node.subs.drill_state.head_pos

    # 2. Короткая подтяжка
    do_short_pullup = False
    if self.last_transition_kwargs.get('short'):  # Явно запрошена короткая
        do_short_pullup = True
    # Обычная подтяжка и счетчик не превышен
    if (not self.last_transition_kwargs.get('long') and
        self.node.short_pullup_cnt < pullup_params.max_short_pullup_cnt):  # 3
        do_short_pullup = True

    if (do_short_pullup and self.is_clear() and
        depth_delta > self.short_pullup_distance):
        self.node.short_pullup_cnt += 1
        self.node.set_state(AFTER_PULLUP)
        return

    # 3. Заклинивание
    if self.node.is_stuck:
        self.node.set_state(UNSTUCK)

def is_clear(self):
    """Проверка что скважина чистая (низкие давления)"""
    return (self.node.subs.pressure_state.rot_pressure < self.node.current_params.pullup.free_rotation_pressure and  # 60
            self.node.subs.pressure_state.air_pressure < self.node.current_params.pullup.air_pressure_max)  # 7.0
```

## 6. UnstuckState - Освобождение от заклинивания

**Назначение:** Циклическое движение вверх-вниз для освобождения заклинившего бура.

**При входе в состояние:**
```python
def on_transition_to(self):
    self.entry_spindle_depth = self.node.subs.drill_state.head_pos

    if not self.node.current_params.common.enable_unstuck:
        # Если unstuck отключен - ошибка
        err_msg = "Unstuck is disabled!"
        self.node.handle_internal_error(error_message=err_msg, ...)
```

**Рабочий цикл:**
```python
def do_work(self):
    unstuck_params = self.node.current_params.unstuck
    self.node.last_unstuck_time = rospy.get_time()

    time_trying = self.node.get_current_state_duration()
    full_cycle_duration = unstuck_params.unstuck_time_up + unstuck_params.unstuck_time_down  # 1.8 + 0.7 = 2.5с
    cycles_cnt = time_trying / full_cycle_duration
    cycle_period = time_trying % full_cycle_duration

    # Управления
    self.node.controls.feed_pressure = unstuck_params.feed_pressure_ctrl  # 0.25
    self.node.controls.rotation_speed = unstuck_params.rotation_speed_ctrl  # 115
    self.node.controls.air_power = unstuck_params.air_power_ctrl  # 1

    # Проверка успешности
    if (not self.node.is_in_hole() or
        abs(self.entry_spindle_depth - self.node.subs.drill_state.head_pos) >= unstuck_params.unstuck_dist):  # 0.55м
        prev_state_name = self.node.get_last_state().get_name()
        self.node.set_state(prev_state_name)
        return

    # Циклическое движение
    if cycle_period <= unstuck_params.unstuck_time_up:  # 1.8с - движение вверх
        self.node.controls.feed_speed = -unstuck_params.unstuck_up_feed_speed_ctrl  # -1.0
    else:  # 0.7с - движение вниз
        self.node.controls.feed_speed = unstuck_params.unstuck_down_feed_speed_ctrl  # 0.2

    # Проверка превышения лимита циклов
    if cycles_cnt >= unstuck_params.unstuck_try_cnt:  # 15 циклов
        prev_state_name = self.node.get_last_state().get_name()
        self.node.set_state(prev_state_name)
        err_msg = "Unstuck failed"
        self.node.handle_internal_error(error_message=err_msg, ...)
```

### Состояния и логика

Общие соглашения:
- Фактическая глубина шпинделя берётся из `DrillState.head_pos` (растёт вниз). «От земли» — от `ground_spindle_depth`.
- На первой штанге до детекции грунта `ground_spindle_depth = target_raise_spindle_depth` из action.
- Сглаживание скорости бурения: `drill_speed_smoothed += (head_speed - drill_speed_smoothed) * 0.1`.
- Валидация глубины: по `head_pos_is_reliable` и тайм‑аутам `invalid_depth_duration(_drill)`.

## Система безопасности и контроля (детальный анализ)

### Детекция заклинивания (is_stuck, is_hard_rot)

**Функция check_for_stuck() - ключевая для безопасности:**
```python
def check_for_stuck(self):
    common_params = self.node.current_params.common
    current_time = rospy.get_time()

    # Сброс критериев заклинивания (вне скважины или не в режиме бурения)
    if (not self.is_in_hole(0.1) or
        self.subs.main_mode.mode != DRILLING):
        self.is_stuck = False
        self.is_hard_rot = False
        self.last_normal_rotation_time = rospy.get_time()
        self.last_nostuck_move_time = rospy.get_time()
        return

    # 1. КРИТЕРИЙ ЗАКЛИНИВАНИЯ ВРАЩЕНИЯ
    stuck_criteria_rot = (self.use_rotation_speed_sensor and
                         abs(self.subs.drill_ctrl.rotation_speed) > 0.95 and  # Команда на вращение
                         abs(self.subs.drill_state.drill_rpm) < common_params.too_low_rotation_speed)  # 3 об/мин
    if not stuck_criteria_rot:
        self.last_normal_rotation_time = current_time

    # 2. КРИТЕРИЙ ЗАКЛИНИВАНИЯ ДВИЖЕНИЯ
    stuck_criteria_move = (self.subs.drill_ctrl.feed_speed < -0.95 and  # Команда на подъем
                          self.subs.drill_state.head_speed > -common_params.stuck_speed_threshold)  # -0.05 м/с
    if not stuck_criteria_move:
        self.last_nostuck_move_time = current_time

    # 3. КРИТЕРИЙ ВЫСОКОГО ДАВЛЕНИЯ ВРАЩЕНИЯ
    high_rp = self.subs.pressure_state.rot_pressure > common_params.too_high_rp_threshold  # 310 бар
    if not high_rp:
        self.last_normal_rp_time = current_time

    # 4. ОПРЕДЕЛЕНИЕ ЗАКЛИНИВАНИЯ ВРАЩЕНИЯ (is_hard_rot)
    has_long_no_rotation = current_time - self.last_normal_rotation_time >= common_params.no_rotation_duration  # 0.5с
    has_long_high_rotation_pressure = current_time - self.last_normal_rp_time > common_params.high_rp_duration  # 1.5с
    self.is_hard_rot = has_long_no_rotation or has_long_high_rotation_pressure

    # 5. ОПРЕДЕЛЕНИЕ ОБЩЕГО ЗАКЛИНИВАНИЯ (is_stuck)
    has_long_no_move = current_time - self.last_nostuck_move_time > common_params.stuck_move_duration  # 1.247с

    allow_stuck_detection = (self.get_current_state_duration() > common_params.min_state_duration_to_stuck and  # 0.391с
                           current_time - self.last_unstuck_time > common_params.ignore_stuck_duration)  # 3с

    if allow_stuck_detection and (has_long_no_move or self.is_hard_rot):
        self.is_stuck = True
    else:
        self.is_stuck = False
```

### Управление люнетом (arm management)

**Функция manage_arm() - критична для безопасности:**
```python
def manage_arm(self):
    common_params = self.node.current_params.common
    current_state_name = self.get_current_state().get_name()
    deny_movement = False  # Флаг запрета движения

    # Сброс флагов отправленных команд по статусу люнета
    if self.subs.arm_status is not None:
        if self.subs.arm_status.status == CLOSED:
            self.sent_arm_close = False
        elif self.subs.arm_status.status == OPEN:
            self.sent_arm_open = False

    # Таймауты команд люнета (5 секунд)
    time_since_last_arm_action = rospy.get_time() - self.sent_arm_time
    if self.sent_arm_open and time_since_last_arm_action > 5.0:
        self.sent_arm_open = False
    if self.sent_arm_close and time_since_last_arm_action > 5.0:
        self.sent_arm_close = False

    # Логика управления (не в IDLE и не в remote режиме)
    if current_state_name != IDLE and 'remote' not in self.subs.main_mode.mode:
        # Условия для закрытия люнета
        need_to_close_arm = (common_params.close_arm and current_state_name == RAISE) or self.hole_angle != 0

        # Команды открытия/закрытия по глубине
        if (self.subs.drill_state.head_pos < common_params.arm_close_depth and  # 5.0м
            not self.sent_arm_close):
            if (need_to_close_arm and self.arm_present and
                self.subs.arm_status.status != CLOSED):
                self.close_arm_func()

        elif (self.subs.drill_state.head_pos > common_params.arm_open_depth and  # 7.0м
              not self.sent_arm_open):
            if self.subs.arm_status.status != OPEN:
                self.open_arm()

        # КРИТИЧЕСКИЕ ЗОНЫ - ЗАПРЕТ ДВИЖЕНИЯ
        if ((self.subs.drill_state.head_pos < common_params.arm_close_min_depth and  # 4.5м
             self.subs.arm_status.status != CLOSED and need_to_close_arm) or
            (self.subs.drill_state.head_pos > common_params.arm_open_max_depth and  # 7.5м
             self.subs.arm_status.status != OPEN)):
            deny_movement = True
            self.logwarn("Feed locked while rod support in wrong state")

    return deny_movement

def open_arm(self):
    self.publish_arm_action(do_open=True)
    self.sent_arm_open = True

def close_arm_func(self):
    self.publish_arm_action(do_open=False)
    self.sent_arm_close = True

def publish_arm_action(self, do_open=False):
    message = Action()
    message.data = json.dumps({'open': do_open})
    message.seq = 1
    self.arm_action_pub.publish(message)
    self.sent_arm_time = rospy.get_time()
```

### Воздух, компрессор и безопасность

**Система контроля воздуха:**
```python
def check_air_is_nominal(self):
    """Проверка номинального давления воздуха"""
    return ((self.subs.pressure_state.air_pressure <= self.current_params.common.max_air_pressure) and  # 5.5 бар
            (self.nominal_air_pres is None or
             self.subs.pressure_state.air_pressure <= self.nominal_air_pres + self.current_params.common.max_air_pressure_excess))  # +0.6 бар

def check_if_pullup_needed(self):
    """Проверка необходимости подтяжки по давлению воздуха"""
    current_time = rospy.get_time()

    if self.check_air_is_nominal():
        self.last_air_is_nominal_time = current_time
    elif current_time - self.last_air_is_nominal_time > self.current_params.common.air_not_nominal_max_time:  # 2.0с
        self.log("Too high air pressure! Need to pullup", loglevel=2)
        return True

    return False

def check_air(self):
    """Диагностика компрессора - вызывается в do_work_after()"""
    current_time = rospy.get_time()

    compressor_enabled = (self.controls.air_power > 0) and self.get_move_permission()
    # Ожидаемое поведение: включен и давление > 0.7 ИЛИ выключен и давление < 0.3
    air_is_ok = ((compressor_enabled and self.subs.pressure_state.air_pressure > 0.7) or
                (not compressor_enabled and self.subs.pressure_state.air_pressure < 0.3))

    if air_is_ok:
        self.first_air_is_bad_time = 0
    else:
        if not self.first_air_is_bad_time:
            self.first_air_is_bad_time = current_time
        elif current_time - self.first_air_is_bad_time > self.current_params.common.air_transient_response_time:  # 7.0с
            err_msg = "Compressor malfunction!"
            self.handle_internal_error(error_message=err_msg,
                                     error_type=self.global_params['Reports']['Critical']['Hardware_malfunction'],
                                     event=self.global_params['events']['compressor_failure'])

    self.last_air_check_time = current_time
```

### Валидация данных и безопасность

**Функция check_and_update_depth_data() - критична для работы:**
```python
def check_and_update_depth_data(self):
    """Проверка достоверности данных глубины и обновление сглаженной скорости"""

    if self.subs.drill_state.head_pos_is_reliable:
        self.last_valid_depth_time = rospy.get_time()

        # Обновление сглаженной скорости бурения (фильтр 1-го порядка)
        if self.drill_speed_smoothed is None:
            self.drill_speed_smoothed = self.subs.drill_state.head_speed
        else:
            self.drill_speed_smoothed += (self.subs.drill_state.head_speed - self.drill_speed_smoothed) * 0.1
        return True

    else:
        elapsed_since_valid = rospy.get_time() - self.last_valid_depth_time
        cur_state_name = self.get_current_state().get_name()

        # Разные таймауты для разных состояний
        if cur_state_name in [IDLE, DRILLING, AFTER_PULLUP, PASS_SOFT]:
            max_invalid_time = self.current_params.common.invalid_depth_duration_drill  # 8.0с
        else:
            max_invalid_time = self.current_params.common.invalid_depth_duration  # 0.6с

        if elapsed_since_valid < max_invalid_time:
            self.log("INVALID DEPTH DATA", loglevel=2, event=self.global_params['events']['laser_failure'])
            return True
        else:
            self.drill_speed_smoothed = None
            self.log("INVALID DEPTH DATA", loglevel=3, event=self.global_params['events']['laser_failure'])
            return False
```

**Функция check_data() - главная проверка в цикле:**
```python
def check_data(self):
    current_state_name = self.get_current_state().get_name()

    # Обновление профилей (кроме IDLE)
    if current_state_name != IDLE:
        self.process_profiles()

    # Проверка данных глубины
    if not self.check_and_update_depth_data():
        return False

    # Проверка заклинивания
    self.check_for_stuck()

    if self.subs.main_mode is None:
        return False

    # Пропуск проверок для IDLE и remote режимов
    if (current_state_name == IDLE or
        self.subs.main_mode.mode in (REMOTE, REMOTE_WAIT, REMOTE_PREPARE)):
        return True

    # Управление люнетом
    self.manage_arm()

    return True
```

### Система профилей (подробная реализация)

**Структура профилей в drilling_profiles.yaml:**
```yaml
DrillerNode:
  default:
    common:
      short_hole_max_depth: 9.5  # Порог короткой скважины
      water_low: &water_low 0.18
      water_high: &water_high 0.58
      max_rotation_speed: &max_rotation_speed 115
      # ... остальные общие параметры

    touchdown:
      feed_speed_ctrl: 0.2
      rotation_speed_ctrl: 0
      # ... параметры состояния

    drilling:
      mandatory_pullups:
        - depth: { from_ground: 3.5 }
          action: long
        - depth: { from_shaft_finish: 1.5 }
          action: short
      water_ctrl:
        - depth: { from_ground: 0.0 }
          value: *water_high
        - depth: { from_ground: 3.0 }
          value: *water_low
      # ... остальные параметры

  profiles:
    default-short:  # Для коротких скважин
      drilling:
        water_ctrl:
          - depth: { from_ground: 0.0 }
            value: *water_low
          - depth: { from_ground: 2.0 }
            value: 0

    hard:  # Для твердых пород
      drilling:
        feed_pressure_limit:
          - depth: { from_ground: -3 }
            value: 100  # Увеличенное давление

    wet:  # Для скважин с водой
      drilling:
        mandatory_pullups:
          - depth: { from_ground: 1.5 }
            action: long
          - depth: { from_ground: 2.5 }
            action: long

    cracked:  # Для трещиноватых пород
      drilling:
        mandatory_pullups:
          - depth: { drilled: 1.5 }
            action: cracked
```

**Применение профилей в коде:**
```python
def process_profiles(self):
    """Применение профилей на основе RMO входов и типа скважины"""
    profiles = []

    # Профили от RMO
    if self.subs.rock_type and self.subs.rock_type.type == HARD:
        profiles.append('hard')
    if self.subs.rock_type and self.subs.rock_type.type == CRACKED:
        profiles.append('cracked')
    if self.subs.rock_type and self.subs.rock_type.is_flooded:
        profiles.append('wet')

    # Для коротких скважин
    if self.is_short_hole():
        profiles.insert(0, 'default-short')
        # Замена профилей на -short версии
        profiles = [profile if profile + '-short' not in self.node_params["profiles"]
                   else profile + '-short' for profile in profiles]

    # Применение если изменились
    if profiles != self.last_profiles:
        self.log("Applying changed profiles: '%s'" % str(profiles))
        if self.apply_profiles(profiles):
            self.last_profiles = profiles

def apply_profiles(self, profiles):
    """Слияние профилей с базовыми параметрами"""
    current_params = deepcopy(self.node_params['default'])

    for profile_name in profiles:
        if profile_name in self.node_params["profiles"]:
            profile_params = self.node_params["profiles"][profile_name]
            current_params = deep_update(current_params, profile_params)
        else:
            self.log("No profile '%s' in known profiles!" % profile_name, 3)
            return False

    self.current_params = Dict2Obj(current_params)
    return True

def is_short_hole(self):
    """Определение короткой скважины"""
    if (self.hole_target_depth is not None and
        self.current_params.common.short_hole_max_depth is not None):
        return self.hole_target_depth < self.current_params.common.short_hole_max_depth
    return False
```

### Инициализация/обновление параметров

- При старте: запросить `node_params`, `Vehicle`, `Global` (как делает `BaseNode`), сформировать `current_params=default`, применить активные профили.
- При `on_params_update()`: пере‑прочитать и применить профили без сброса FSM, если возможно; перепечатать активные параметры в лог при изменениях профиля.

### Тестирование

- Табличные функции глубины (выбор значений и pullup‑порогов) — юнит‑тесты.
- Сценарные тесты FSM по основным переходам.
- Проверка профилей (`hard`, `wet`, `cracked`, `*-short`).

# Полная архитектура ROS2 реализации

## Структура классов

```python
# Главный класс ноды
class DrillerNode(BaseFSM):
    def __init__(self):
        # Инициализация FSM, подписок, публикаций

    def drill_initialize(self):
        # Инициализация состояний, параметров, переменных

    def action_callback(self, message):
        # Обработка команд бурения

    def process_profiles(self):
        # Динамическое применение профилей

    def check_data(self):
        # Валидация данных, проверки безопасности

    def send_control(self):
        # Централизованная отправка управления

# Структура управления
class DrillControls:
    feed_speed: float = 0
    rotation_speed: float = 0
    feed_pressure: float = 0
    air_power: float = 0
    dust_collector_on: bool = False

# Базовый класс состояний
class DrillState(BaseState):
    def __init__(self, node, name):
        super().__init__(node, name)

    def on_transition_to(self):
        # Действия при входе в состояние

    def do_work(self):
        # Рабочий цикл состояния

    def get_value_for_current_depth(self, depth_dict):
        # Работа с глубинными таблицами

# Конкретные состояния
class IdleState(DrillState): ...
class TouchDownState(DrillState): ...
class OverburdenPassState(DrillState): ...
class DrillingState(DrillState): ...
class HardRotState(DrillState): ...
class PullUpState(DrillState): ...
class AfterPullUpState(DrillState): ...
class PassSoftState(DrillState): ...
class WaitAfterDrillState(DrillState): ...
class RaiseState(DrillState): ...
class UnstuckState(DrillState): ...
```

## Диаграмма состояний (Mermaid)

```mermaid
stateDiagram-v2
    [*] --> idle

    idle --> touchdown: new_action_flag AND first_rod_flag
    idle --> drilling: new_action_flag AND NOT first_rod_flag

    touchdown --> overburden_pass: ground_detected

    overburden_pass --> drilling: max_duration OR depth_delta
    overburden_pass --> hard_rot: is_hard_rot

    drilling --> wait_after_drill: target_depth_reached
    drilling --> pullup: mandatory_pullup OR high_air_pressure
    drilling --> hard_rot: is_hard_rot

    hard_rot --> drilling: conditions_cleared
    hard_rot --> overburden_pass: conditions_cleared

    pullup --> after_pullup: pullup_complete OR at_surface
    pullup --> unstuck: is_stuck

    after_pullup --> pass_soft: ground_detected

    pass_soft --> drilling: max_duration
    pass_soft --> pullup: high_air_pressure
    pass_soft --> wait_after_drill: target_depth_reached

    wait_after_drill --> raise: max_duration

    raise --> idle: target_raise_depth_reached
    raise --> unstuck: is_stuck

    unstuck --> drilling: unstuck_success
    unstuck --> pullup: unstuck_success
    unstuck --> raise: unstuck_success
    unstuck --> hard_rot: unstuck_failed
```

## Ключевые алгоритмы

### 1. Обработка глубинных таблиц
```python
def get_value_for_current_depth(self, depth_dict, default=None):
    """
    Универсальная функция для работы с глубинными параметрами
    Поддерживает ссылки: from_ground, from_added_shaft_start,
    from_shaft_finish, from_hole_finish, drilled
    """
    adjusted_depth_dict = []

    for entry in depth_dict:
        depth_entry = entry.depth

        # Преобразование к единой системе координат (spindle_depth)
        if hasattr(depth_entry, 'from_ground'):
            if self.node.first_rod_flag:
                threshold_spindle_depth = self.node.ground_spindle_depth + depth_entry.from_ground
            else:
                continue  # Пропустить для не первой штанги

        elif hasattr(depth_entry, 'from_added_shaft_start'):
            if not self.node.first_rod_flag:
                threshold_spindle_depth = self.node.action_start_spindle_depth + depth_entry.from_added_shaft_start
            else:
                continue

        elif hasattr(depth_entry, 'from_shaft_finish'):
            if not self.node.last_rod_flag:
                threshold_spindle_depth = self.node.target_drill_spindle_depth - depth_entry.from_shaft_finish
            else:
                continue

        elif hasattr(depth_entry, 'from_hole_finish'):
            if self.node.last_rod_flag:
                threshold_spindle_depth = self.node.target_drill_spindle_depth - depth_entry.from_hole_finish
            else:
                continue

        elif hasattr(depth_entry, 'drilled'):
            threshold_spindle_depth = self.entry_spindle_depth + depth_entry.drilled

        adjusted_depth_dict.append({
            'threshold_spindle_depth': threshold_spindle_depth,
            'value': entry.value
        })

    # Сортировка и выбор значения
    adjusted_depth_dict.sort(key=lambda x: x['threshold_spindle_depth'])

    last_value = default if default is not None else adjusted_depth_dict[0]['value']
    for entry in adjusted_depth_dict:
        if self.node.subs.drill_state.head_pos < entry['threshold_spindle_depth']:
            break
        last_value = entry['value']

    return last_value
```

### 2. I-регулятор давления подачи (в DrillingState)
```python
# Интегральное регулирование скорости бурения
drilling_speed_error = target_drill_speed - drill_speed_smoothed
drilling_speed_error_i += drilling_speed_error * dt * drilling_speed_i_gain

# Ограничение интеграла
drilling_speed_error_i = max(min(drilling_speed_error_i, drilling_speed_i_max), drilling_speed_i_min)

# Базовое давление + коррекция
psp_regulated = user_feed_pressure + drilling_speed_error_i

# Ограничения и коррекции
psp_regulated = max(psp_regulated, feed_pressure_min)
if high_rotation_pressure_too_long:
    psp_regulated = min(psp_regulated, feed_pressure_reduced)
psp_regulated = min(psp_regulated, get_value_for_current_depth(feed_pressure_limit))
```

### 3. Система безопасности
```python
def check_data(self):
    # 1. Валидация глубины
    if not self.check_and_update_depth_data():
        return False

    # 2. Детекция заклинивания
    self.check_for_stuck()

    # 3. Управление люнетом с блокировкой движения
    deny_movement = self.manage_arm()

    # 4. Контроль воздуха
    self.check_air()

    return True

def send_control(self, deny_string_movement=False):
    # Централизованная отправка с возможностью блокировки
    message = DrillCtrl()
    message.feed_speed = self.controls.feed_speed if not deny_string_movement else 0
    message.rotation_speed = self.controls.rotation_speed
    message.feed_pressure = self.controls.feed_pressure
    # ... отправка остальных управлений
```

## Файлы для реализации

### 1. Основные файлы
- `src/driller_node/driller_node/driller_node.py` - главный класс
- `src/driller_node/driller_node/states/` - папка с состояниями
  - `idle_state.py`
  - `touchdown_state.py`
  - `drilling_state.py`
  - `pullup_state.py`
  - `unstuck_state.py`
  - и т.д.
- `src/driller_node/driller_node/drill_controls.py` - структура управления

### 2. Конфигурация
- `src/params_server/base_config/drilling_profiles.yaml` - профили бурения
- `src/params_server/base_config/nodes.yaml` - параметры ноды

### 3. Сообщения
- `src/drill_msgs/msg/DrillerAction.msg` - команда бурения

### 4. Launch файлы
- `src/driller_node/launch/driller.launch.py`

### Реализовать/проверить

- Добавить `drill_msgs/DrillerAction.msg` (см. поля выше) и включить в сборку пакета `drill_msgs`.
- В `nodes.yaml` — секцию `DrillerNode` с `rate`, `user_feed_pressure`, `user_rotation_speed`.
- Реализовать классы состояний и основной класс ноды по спецификации.
- Создать полный набор профилей в `drilling_profiles.yaml`.
- Написать README.md с полным описанием работы ноды.
- Создать launch файл для запуска ноды.
- Написать юнит-тесты для ключевых алгоритмов.