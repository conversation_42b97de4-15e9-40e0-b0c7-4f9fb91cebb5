# Обзор

Мне нужно сделать заново Driller ноду управления бурения в замен имеющейся ROS-1 ноды для робота-бурового станка на ROS2 для этого проекта бортового софта. 

## Входы и выходы

## На входе

1) Action (команда управления, свой формат) 

/driller_action

drill_msgs/DrillerAction
Header header
int id
float32 drill_spindle_depth
float32 raise_spindle_depth
bool first_rod_flag
bool last_rod_flag
float32 hole_depth
float32 inclination


2) Состояние бурения - надо подписываться с помощью create_subscription из BaseFSM (base_fsm.py)c

/drill_state

drill_msgs/DrillState (src/drill_msgs/msg/DrillState.msg)
std_msgs/Header header
float32 head_pos # meters
float32 head_speed # meters
float32 head_angular_pos  # degrees
float32 drill_rpm
float32 feed_pressure # bar
float32 rot_pressure # bar
float32 air_pressure # bar
bool head_pos_is_reliable

При необходимости, можно будет использовать src/drill_msgs/msg/DepthInfo.msg также (проанализировать).

3) /rock_type -- тип породы и наличие воды для выбора подрежима бурения (см. профили)

/rock_type
drill_msgs/RockType
uint8 type
bool is_flooded

4) /main_state_machine_status

а также (на входе, функциональность base_node/base_node/base_fsm.py -- подписывается автоматически, не надо вручную)
4) /robomode
"topic": "robomode",
"msg_type": "drill_msgs/msg/BoolStamped"

5) /permission
"topic": "permission",
"msg_type": "drill_msgs/msg/Permission" (src/drill_msgs/msg/Permission.msg)

## На выходе 

1) Сигнал (уставка) управления бурением (по скоростям) -- это далее идет на низкоуровневый регулятор (Drill regulator)

/driller_setpoints
drill_msgs/DrillCtrl (src/drill_msgs/msg/DrillCtrl.msg)
Header header
float32 feed_speed  # ms / [-1..1]
float32 rotation_speed   # rpm / [-1..1]
float32 feed_pressure  # bar / [-1..1]
bool feed_speed_is_raw
bool rotation_speed_is_raw
bool feed_pressure_is_raw

2) Уставка (команда) управления подачей воды 
/water_ctrl_auto
drill_msgs/FloatCtrl (src/drill_msgs/msg/FloatCtrl.msg)

3) Уставка (команда) управления воздухом 
/air_ctrl
drill_msgs/AirCtrl (src/drill_msgs/msg/AirCtrl.msg)
float32 power
bool enabled

4) Action (команда) управления люнетом (arm. rod support)
/arm_action
drill_msgs/OpenCloseAction
Header header
int id
int action  # [1, -1]



а также (на выходе, функциональность base_node/base_node/base_fsm.py -- работает автоматически, не надо создавать)
5) /events

6) /internal_report

7) /driller_status
drill_msgs/StateMachineStatus


# Функциональность ROS-1 ноды

Далее идет описание имеющейся ноды.
В любом случае, как на первый источник истины и полной информации опирайся на код /Users/<USER>/Work/ZyfraDrillv1/catkin_ws/src/drill-software/drill-mover/driller

Это описание для информации. Ты при реализации ROS-2 как профессионал можешь улучшить архитектуру и код, но функциональность должна отстаться не меньше. 

## Архитектура

Нода реализует FSM на базе `BaseFSM`, параметры получает из `params_server`, профили применяет динамически, управление публикуется централизованно из одного места цикла.

## Конечный автомат состояний

См. раздел «Подробная спецификация реализации»: описаны все состояния (`idle`, `touchdown`, `overburden_pass`, `drilling`, `hard_rot`, `pullup`, `after_pullup`, `pass_soft`, `wait_after_drill`, `raise`, `unstuck`) с условиями входа/выхода и управлением.

## Система безопасности

- Проверка `robomode` и `permission` встроена в `BaseFSM`.
- Таймауты подписок ведут к безопасной остановке.
- Воздух контролируется по номиналу и времени реакции.

## Параметры конфигурации

- Профили бурения и параметры состояний — в отдельном файле `src/params_server/base_config/drilling_profiles.yaml`, ключ `Driller`. Подключение файла обеспечивается запуском `params_server` с аргументом `drilling_profiles` (см. README сервера параметров, раздел Launch). Сервер объединяет YAML‑файлы, так что профили живут отдельно от `nodes.yaml`.
- Параметры ноды `Driller` (в `nodes.yaml`): `rate`, `user_feed_pressure`, `user_rotation_speed`.



## Особенности реализации

### Обработка ошибок
Система использует централизованную обработку через `handle_internal_error()`:
- **Предупреждения**: логирование без остановки работы
- **Критические ошибки**: остановка управления + генерация событий
- **События**: отправка в систему мониторинга для операторов

# Требования к программной реализации 

- ROS2
- Базироваться на BaseFSM (base_node/base_node/base_fsm.py), см. base_node/BaseFSM.md, реализовать описанные лучшие практики по структуре программы, логированию, состояниям, ивентам и т.п. 
- не переусложнять, не думблировать код;
- Не использовать параметры ROS2, вместо этого использовать функциональность BaseNode для собственной реализации получения параметров от собственного сервера.
- При использовании параметров, не использовать get с значением по умолчанию - лучше при инициализации вылететь, если неправильное имя параметра.
- Не переусердствовать с getatr и тому подобным для ROS-сообщения, у которых структура фиксирована.
- Не делать fallback, compatability и т.п. без явной необходимости, приоритет за однозначным понятным кодом.
- Не держать константы в коде, а вводить и использовать параметры, см. params_server/params_server/base_config/vehicle.yaml params_server/params_server/base_config/nodes.yaml
- Не переусложнять код, делать его наиболее коротким и понятным
- KISS, SOLID
- Обязательно сделать README.md с полным описанием входов выходов и работы
- Желательно сделать launch file (см src/launchpack/launch/general.xml для примера, но нам для одной ноды и параметр, если нужно)
- Соблюдать лучшие практики ROS2 (когда это не противоречит требованиям из BaseFSM.md и BaseNode.md, например не использовать ROS2-параметры, использовать логирование из BaseNode.md а не напрямую, работать со временем как показано в BaseNode.md, и т.д.)
- не дублировать а использовать функционал BaseFSM.
- Документация в README.md на русском языке. 
- Комментарии в коде -- на английском.
- Всегда помни о задаче в целом и доделывай до конца, от общего к частному.
- Заботься о красивой архитектуре.


# Примеры 

## Старая ROS1-нода driller бурового станка /Users/<USER>/Work/ZyfraDrillv1/catkin_ws/src/drill-software/drill-mover/driller

Нужно использовать старый пример /Users/<USER>/Work/ZyfraDrillv1/catkin_ws/src/drill-software/drill-mover/driller
Что относительно этого примера в нашей реализации надо поменять: 
- Он был на ROS1, 
- он со старым окружением (другая base node, отличающиеся сообщения и логика работы немного). 

Документация по старой ноде /Users/<USER>/Work/drill-docs-dev/docs/onboard-system/drilling-and-mechanisms/driller.md

Профили бурения: конфиг и документация в виде комментов /Users/<USER>/Work/ZyfraDrillv1/catkin_ws/src/drill-software/drill-mover/drill_launch_pack/params/drill/drilling_profiles.yaml

## Подробная спецификация реализации DrillerNode (ROS2)

### Архитектура

- Наследование: `DrillerNode` ← `BaseFSM` (`src/base_node/base_node/base_fsm.py`).
- Логирование/ивенты: через `BaseNode.log()` и коды из `Global.events`.
- Параметры: только через `params_server` (`BaseNode`), никаких ROS‑параметров. Обработка обновлений — `on_params_update()`.
- Частота цикла: параметр `rate` (Гц) из `nodes.yaml`.
- Состояния реализовать отдельными классами, регистрируются через `add_states()`.

### Подписки

- `/driller_action`: команда бурения текущей штанги.
  - Тип: создать `drill_msgs/DrillerAction.msg`:
    - `std_msgs/Header header`
    - `int32 id`
    - `float32 drill_spindle_depth`
    - `float32 raise_spindle_depth`
    - `bool first_rod_flag`
    - `bool last_rod_flag`
    - `float32 hole_depth`
    - `float32 inclination`
  - Приём только в `idle`, одна активная команда за раз.

- `/drill_state`: `drill_msgs/DrillState` (используем поля: `head_pos`, `head_speed`, `drill_rpm`, `feed_pressure`, `rot_pressure`, `air_pressure`, `head_pos_is_reliable`).

- `/rmo_rock_type`: `std_msgs/String` (`normal|cracked|hard`).
- `/rmo_hole_water`: `std_msgs/String` (`dry|wet`).
- `/water_injection_control`: `drill_msgs/FloatCtrl` (`ctrl` ∈ [0..1]) — ручной след для сравнения/логов.

- Встроенные `BaseFSM`: `/robomode` (`drill_msgs/BoolStamped`), `/permission` (`drill_msgs/Permission`).

Реализовывать подписки через `add_subscriber(topic, msg_type, name, timeout)`.

### Публикации

- `/driller_out`: `drill_msgs/DrillCtrl` — уставки на регулятор.
  - `feed_speed` ∈ [-1..1], `rotation_speed` ∈ [-1..1], `feed_pressure` (см. флаг raw).
  - `feed_speed_is_raw=False`, `rotation_speed_is_raw=False`.
  - `feed_pressure_is_raw=True` в `touchdown`, `after_pullup`, `unstuck`; иначе `False`.

- `/water_injection_auto`: `drill_msgs/FloatCtrl` (`ctrl` ∈ [0..1]).
- `/compressor_control`: `drill_msgs/AirCtrl` (`enabled`, `power` ∈ [0..1]).
- `/dust_collector_control`: `drill_msgs/BoolStamped` (`value`).
- `/arm_action`: `drill_msgs/BoolStamped` (`value=True` — открыть, `False` — закрыть).
- Статус FSM публикует `BaseFSM` автоматически в `/{node_name}_status`.

Единая функция отправки уставок должна выставлять временные метки `get_rostime()` на все сообщения.

### Сервис

- `/{node_name}/recalibrate_air` (`std_srvs/Trigger`): зафиксировать `nominal_air_pres` по текущему `air_pressure` (разрешён вне `idle`).

### Параметры и профили

- Профили: `src/params_server/base_config/drilling_profiles.yaml`, ключ `Driller` (структура как в ROS1).
- Выбор профилей:
  - Всегда `default` как база.
  - По РМО: добавлять `hard`/`cracked` и/или `wet` при соответствующих значениях.
  - Короткая скважина: если `hole_depth < default.common.short_hole_max_depth`, первыми применить `default-short`, а профиль `X` заменить на `X-short` при наличии.
  - Слияние — последовательное, последний слой имеет приоритет.
  - При изменении входов профиля (RMO, длина) — переcчитать `current_params` на лету.

- Параметры ноды (`nodes.yaml/Driller`):
  - `rate`
  - `user_feed_pressure` — старт для i‑регулятора подачи в `drilling`.
  - `user_rotation_speed` — верхний предел уставки вращения в `drilling`.

### Состояния и логика

Общие соглашения:
- Глубина головки (`head_pos`) растёт вниз. «От земли» — от `ground_head_pos`.
- На первой штанге до детекции грунта `ground_head_pos = target_raise_head_pos` из action.
- Сглаживание скорости бурения: `drill_speed_smoothed += (head_speed - drill_speed_smoothed) * 0.1`.
- Валидация глубины: по `head_pos_is_reliable` и тайм‑аутам `invalid_depth_duration(_drill)`.

1) `idle`
- При входе: обнуление уставок, вода=0, сброс флагов/счётчиков, `last_mandatory_pullup_spindle_depth=None`.
- При action: сохранить поля, выставить целевые глубины/флаги; если `first_rod_flag` — `touchdown`, иначе — `drilling`.

2) `touchdown`
- Управления из `default.touchdown`.
- Детекция грунта: `feed_speed < ground_detect_feed_speed_thr` выдержано `ground_detect_duration`, `Δdepth >= touchdown.depth_delta`, и команда `feed_speed>0.3` — зафиксировать `ground_spindle_depth`, перейти в `overburden_pass`.

3) `overburden_pass`
- При входе: если после `touchdown` — сохранить входную глубину; `nominal_air_pres=None`; установить воду.
- Работа: линейный спин‑ап вращения до `rotation_speed_ctrl` за `spinup_time`; воздух/пыль/давление — по профилю; `nominal_air_pres = max(nominal_air_pres, air_pressure)`.
- Переходы: `drilling` по `max_duration` или `Δdepth >= overburden_layer_thickness`; `hard_rot` при `is_hard_rot`.

4) `drilling`
- Цель: при `head_pos >= target_drill_head_pos` — `wait_after_drill`.
- Обязательные подтяжки: по `drilling.mandatory_pullups` (источники глубины: `from_ground`, `from_added_shaft_start`, `from_shaft_finish`, `from_hole_finish`, `drilled`); при срабатывании — `pullup` с `short|long|cracked` и обновить `last_mandatory_pullup_spindle_depth`.
- Вода по `drilling.water_ctrl`.
- I‑регулятор давления:
  - `e = target_drill_speed - drill_speed_smoothed`, `I += e*dt*drilling_speed_i_gain`, ограничить `[i_min..i_max]`.
  - `psp = user_feed_pressure + I`, ограничить `>= feed_pressure_min`.
  - Если `rot_pressure > high_rp_threshold` достаточно долго — `psp = min(psp, feed_pressure_reduced)`.
  - Ограничить сверху по глубинной таблице `feed_pressure_limit`.
  - Плавный рост к `psp` с `slow_press_increase_rate` до отключения флага.
- Уставки: `feed_speed = drilling.feed_speed_ctrl`; вращение = min(`rotation_speed_limit(depth)`, `user_rotation_speed`). Воздух/пыль — по профилю.
- Переходы: `pullup` при `check_if_pullup_needed()`; `hard_rot` при `is_hard_rot`.

5) `hard_rot`
- Подача вверх: `-feed_speed_ctrl`; вращение — обычное/макс. по давности `unstuck_recency_limit`; воздух/пыль/давление — по профилю.
- Возврат в предыдущее состояние при: достигнут `depth_delta` ИЛИ `rotation_speed > min_rotation_speed` ИЛИ `rot_pressure < max_rotation_pressure` ИЛИ «не в скважине».

6) `pullup`
- При входе: сохранить глубину входа; запомнить и временно обнулить воду.
- Дистанция `short_pullup_distance`: первая штанга — по глубине от грунта и `depth_threshold`, иначе — `short_pullup_height_high`.
- Вращение — обычное/макс. по давности; подача вверх: нормальная/уменьшенная при близости к грунту; воздух — выкл. при близости `pullup_compressor_on_to_ground_limit`; давление — по профилю.
- Переходы: длинный — при выходе к грунту в пределах `pullup_to_ground_limit`; короткий — если явно `short` или не `long` и `short_pullup_cnt < max_short_pullup_cnt`, при «чистой» скважине и `Δdepth > short_pullup_distance`; `unstuck` при застревании.

7) `after_pullup`
- Управления по профилю, вода=0, вращение ограниченное.
- Детекция грунта по `ground_detect_*`; обновление `nominal_air_pres` при допустимом давлении. По таймеру — восстановить воду, перейти в `pass_soft`.

8) `pass_soft`
- Управления: `feed_pressure`, безопасное `rotation_speed`, `air_power`, `dust` — по профилю.
- Переходы: `pullup` по воздуху; `wait_after_drill` при достижении глубины; `drilling` по таймеру.

9) `wait_after_drill`
- Управления: медленная обратная подача, вращение/воздух/пыль — по профилю, давление — `pass_soft_press`.
- Переход: по таймеру `max_duration` — `raise`.

10) `raise`
- При входе: вода=0.
- Вращение — до `stop_rotation_depth` (и с учётом `unstuck_recency_limit`), воздух — до `stop_air_depth`, давление=0, пыль — по профилю.
- Подача вверх: `-max_raise_feed_speed` вне зоны, `-min_raise_feed_speed` в `reduced_speed_zone`.
- Переход: при `head_pos < target_raise_head_pos` — всё в 0 и `idle`; при застревании — `unstuck`.

11) `unstuck`
- Циклы вверх/вниз по `unstuck_time_up/down`, общий лимит циклов `unstuck_try_cnt`, критерий успешности `unstuck_dist` (или выход из скважины).
- Управления: `feed_pressure`, `rotation_speed`, `air_power` — по профилю; `feed_speed`: вверх/вниз по фазе.

### Управление люнетом

- Флаг `Global.system_flags.arm_present`.
- Условия открытия/закрытия: глубина и `inclination` (при наклоне закрывать). Пороговые глубины: `arm_open_depth`, `arm_close_depth`, запрет движения внутри зон `arm_close_min_depth`/`arm_open_max_depth` при неверном состоянии люнета (тогда отправлять `feed_speed=0`).
- Публикация команд на `/arm_action` `BoolStamped` с задержкой 5с между командами, вести флаги «команда отправлена».

### Воздух, компрессор и безопасность

- `check_air_is_nominal()`: `air_pressure <= max_air_pressure` И (нет `nominal_air_pres` ИЛИ `air_pressure <= nominal_air_pres + max_air_pressure_excess`).
- `check_if_pullup_needed()`: если выше условие нарушено дольше `air_not_nominal_max_time` — вернуть `True`.
- Диагностика компрессора: несоответствие `enabled`/давление дольше `air_transient_response_time` — критическая ошибка (`Global.events.compressor_failure`).

### Инициализация/обновление параметров

- При старте: запросить `node_params`, `Vehicle`, `Global` (как делает `BaseNode`), сформировать `current_params=default`, применить активные профили.
- При `on_params_update()`: пере‑прочитать и применить профили без сброса FSM, если возможно; перепечатать активные параметры в лог при изменениях профиля.

### Тестирование

- Табличные функции глубины (выбор значений и pullup‑порогов) — юнит‑тесты.
- Сценарные тесты FSM по основным переходам.
- Проверка профилей (`hard`, `wet`, `cracked`, `*-short`).

### Реализовать/проверить

- Добавить `drill_msgs/DrillerAction.msg` (см. поля выше) и включить в сборку пакета `drill_msgs`.
- В `nodes.yaml` — секцию `DrillerNode` с `rate`, `user_feed_pressure`, `user_rotation_speed`.
- Реализовать классы состояний и основной класс ноды по спецификации.