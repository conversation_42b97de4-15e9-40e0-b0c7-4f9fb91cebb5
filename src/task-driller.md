# Обзор

Мне нужно сделать заново Driller ноду управления бурения в замен имеющейся ROS-1 ноды для робота-бурового станка на ROS2 для этого проекта бортового софта.

## Входы и выходы

## На входе - надо подписываться с помощью create_subscription из BaseFSM (base_fsm.py)

1) **Action (команда управления, свой формат)**

`/driller_action`

```
drill_msgs/DrillerAction
Header header
int id                      # ID скважины
float32 drill_spindle_depth # Целевая глубина шпинделя для бурения (м, положительная вниз)
float32 raise_spindle_depth # Целевая глубина шпинделя для подъема (м, положительная вниз)
bool first_rod_flag         # Флаг первой штанги (требует детекции грунта)
bool last_rod_flag          # Флаг последней штанги (влияет на профили)
float32 hole_depth          # Общая глубина скважины от устья (м)
float32 inclination         # Угол наклона мачты (градусы, 0=вертикально)
```

**Пример данных Action:**
```json
{
  "id": 30,
  "drill_spindle_depth": 12.348463670137011,
  "raise_spindle_depth": 1.27,
  "first_rod_flag": true,
  "last_rod_flag": true,
  "hole_depth": 11.0,
  "inclination": 0.0
}
```

2) **Состояние бурения** 

`/drill_state`

```
drill_msgs/DrillState (src/drill_msgs/msg/DrillState.msg)
std_msgs/Header header
float32 head_pos            # Позиция головы шпинделя (м, 0 вверху, положительная вниз)
float32 head_speed          # Скорость движения головы (м/с, положительная вниз)
float32 head_angular_pos    # Угловая позиция (градусы)
float32 drill_rpm           # Скорость вращения бура (об/мин)
float32 feed_pressure       # Давление подачи (бар)
float32 rot_pressure        # Давление вращения (бар)
float32 air_pressure        # Давление воздуха (бар)
bool head_pos_is_reliable   # Флаг достоверности позиции головы
```

При необходимости, можно будет использовать src/drill_msgs/msg/DepthInfo.msg также (проанализировать).

3) **Тип породы и наличие воды** для выбора подрежима бурения (см. профили)

`/rock_type`
```
drill_msgs/RockType
uint8 type          # Тип породы: NORMAL=0, HARD=1, CRACKED=2
bool is_flooded     # Наличие воды в скважине
```

4) **Статус главной машины состояний**

`/main_state_machine_status`

а также (на входе, функциональность base_node/base_node/base_fsm.py -- подписывается автоматически, не надо вручную):

5) **Режим робота**
```
/robomode
drill_msgs/BoolStamped
```

6) **Разрешения на движение**
```
/permission
drill_msgs/Permission (src/drill_msgs/msg/Permission.msg)
```

## На выходе

1) **Сигнал (уставка) управления бурением** (по скоростям) -- это далее идет на низкоуровневый регулятор (Drill regulator)

`/driller_setpoints`
```
drill_msgs/DrillCtrl (src/drill_msgs/msg/DrillCtrl.msg)
Header header
float32 feed_speed          # Скорость подачи: м/с или [-1..1] если is_raw=false
float32 rotation_speed      # Скорость вращения: об/мин или [-1..1] если is_raw=false
float32 feed_pressure       # Давление подачи: бар или [-1..1] если is_raw=false
bool feed_speed_is_raw      # true = абсолютные единицы, false = нормализованные [-1..1]
bool rotation_speed_is_raw  # true = абсолютные единицы, false = нормализованные [-1..1]
bool feed_pressure_is_raw   # true = абсолютные единицы, false = нормализованные [-1..1]
```

2) **Уставка (команда) управления подачей воды**

`/water_ctrl_auto`
```
drill_msgs/FloatCtrl (src/drill_msgs/msg/FloatCtrl.msg)
Header header
float32 value               # Уставка подачи воды [0..1]
```

3) **Уставка (команда) управления воздухом/компрессором**

`/air_ctrl`
```
drill_msgs/AirCtrl (src/drill_msgs/msg/AirCtrl.msg)
Header header
float32 power               # Мощность компрессора [0..1]
bool enabled                # Включен/выключен компрессор
```

4) **Action (команда) управления люнетом** (arm, rod support)

`/arm_action`
```
drill_msgs/OpenCloseAction
Header header
int id                      # ID команды
int action                  # 1 = открыть, -1 = закрыть
```

а также (на выходе, функциональность base_node/base_node/base_fsm.py -- работает автоматически, не надо создавать):

5) **События системы**
`/events`

6) **Внутренние отчеты**
`/internal_report`

7) **Статус машины состояний**
```
/driller_status
drill_msgs/StateMachineStatus
```


# Функциональность ROS-1 ноды (анализ старой реализации)

Далее идет описание имеющейся ноды на основе анализа кода `src/driller-old-ros1/`.
Это описание для информации. При реализации ROS-2 можно улучшить архитектуру и код, но функциональность должна остаться не меньше.

## Архитектура старой ноды

Нода реализует FSM на базе `AbstractNodeStateMachine`, параметры получает из `rosparam`, профили применяет динамически, управление публикуется централизованно из одного места цикла.

**Основные компоненты:**
- **DrillerNode** - главный класс, наследует AbstractNodeStateMachine
- **Состояния** - отдельные классы для каждого состояния (IdleState, TouchDownState, etc.)
- **DrillControls** - структура для хранения управляющих сигналов
- **Dict2Obj** - утилита для работы с параметрами как объектами
- **Система профилей** - динамическое применение профилей бурения

## Конечный автомат состояний (11 состояний)

**Список состояний:**
1. `idle` - ожидание команды
2. `touchdown` - детекция касания грунта (только для первой штанги)
3. `overburden_pass` - прохождение верхнего слоя грунта
4. `drilling` - основное бурение
5. `hard_rot` - освобождение от заклинивания вращения
6. `pullup` - подтяжка бура (короткая/длинная)
7. `after_pullup` - возврат в скважину после подтяжки
8. `pass_soft` - прохождение мягких участков
9. `wait_after_drill` - ожидание после завершения бурения
10. `raise` - подъем бура после завершения скважины
11. `unstuck` - освобождение от заклинивания движения

## Система безопасности

- **Проверка robomode и permission** встроена в BaseFSM
- **Таймауты подписок** ведут к безопасной остановке
- **Контроль воздуха:** номинальное давление, время реакции компрессора
- **Детекция заклинивания:** по скорости движения, вращения, давлению
- **Валидация глубины:** проверка достоверности данных датчиков
- **Управление люнетом:** блокировка движения при неправильном положении

## Параметры конфигурации

- **Профили бурения** — в файле `drilling_profiles.yaml`, ключ `DrillerNode`
- **Параметры ноды:** `rate`, `user_feed_pressure`, `user_rotation_speed`
- **Система профилей:** `default` + динамические профили (`hard`, `wet`, `cracked`, `*-short`)



## Особенности реализации старой ноды

### Система профилей (ключевая особенность)

**Базовая структура:**
- `default` - базовые параметры для всех состояний
- `profiles` - модификации базовых параметров для специальных условий

**Динамическое применение профилей:**
```python
def process_profiles(self):
    profiles = []
    # Добавляем профили на основе RMO входов
    if self.subs.rmo_rock_type and self.subs.rmo_rock_type.data:
        profiles.append(self.subs.rmo_rock_type.data)  # 'hard', 'cracked'
    if self.subs.rmo_hole_water and self.subs.rmo_hole_water.data:
        profiles.append(self.subs.rmo_hole_water.data)  # 'wet'

    # Для коротких скважин
    if self.is_short_hole():
        profiles.insert(0, 'default-short')
        # Заменяем профили на их -short версии если есть
        profiles = [profile if profile + '-short' not in self.node_params["profiles"]
                   else profile + '-short' for profile in profiles]
```

**Профили в drilling_profiles.yaml:**
- `default-short` - для коротких скважин (< 9.5м)
- `hard` - для твердых пород (увеличенное давление)
- `wet` - для скважин с водой (дополнительные подтяжки)
- `cracked` - для трещиноватых пород (специальные подтяжки)
- `wet-short`, `cracked-short` - комбинированные профили

### Глубинно-зависимые параметры

**Система ссылок на глубину:**
```yaml
# Пример из drilling_profiles.yaml
mandatory_pullups:
  - depth: { from_ground: 3.5 }        # От поверхности земли
    action: long
  - depth: { from_shaft_finish: 1.5 }  # От конца текущей штанги
    action: short
  - depth: { from_hole_finish: 2.5 }   # От конца скважины
    action: long
  - depth: { drilled: 1.5 }            # От начала текущего состояния
    action: cracked
```

**Обработка в коде:**
```python
def get_value_for_current_depth(self, depth_dict, default=None):
    # Преобразование всех ссылок к единой системе координат (spindle_depth)
    # Сортировка по глубине
    # Выбор подходящего значения для текущей глубины
```

### Система управления

**DrillControls - централизованное управление:**
```python
class DrillControls:
    def __init__(self):
        self.feed_speed = 0          # Скорость подачи
        self.rotation_speed = 0      # Скорость вращения
        self.feed_pressure = 0       # Давление подачи
        self.air_power = 0          # Мощность компрессора
        self.dust_collector_on = False  # Пылесборник
```

**Отправка управления:**
```python
def send_control(self, deny_string_movement=True):
    # Отправка DrillCtrl
    # Отправка CompressorCtrl
    # Отправка пылесборника
```

### Обработка ошибок
Система использует централизованную обработку через `handle_internal_error()`:
- **Предупреждения**: логирование без остановки работы
- **Критические ошибки**: остановка управления + генерация событий
- **События**: отправка в систему мониторинга для операторов

# Требования к программной реализации 

- ROS2
- Базироваться на BaseFSM (base_node/base_node/base_fsm.py), см. base_node/BaseFSM.md, реализовать описанные лучшие практики по структуре программы, логированию, состояниям, ивентам и т.п. 
- не переусложнять, не думблировать код;
- Не использовать параметры ROS2, вместо этого использовать функциональность BaseNode для собственной реализации получения параметров от собственного сервера.
- При использовании параметров, не использовать get с значением по умолчанию - лучше при инициализации вылететь, если неправильное имя параметра.
- Не переусердствовать с getatr и тому подобным для ROS-сообщения, у которых структура фиксирована.
- Не делать fallback, compatability и т.п. без явной необходимости, приоритет за однозначным понятным кодом.
- Не держать константы в коде, а вводить и использовать параметры, см. params_server/params_server/base_config/vehicle.yaml params_server/params_server/base_config/nodes.yaml
- Не переусложнять код, делать его наиболее коротким и понятным
- KISS, SOLID
- Обязательно сделать README.md с полным описанием входов выходов и работы
- Желательно сделать launch file (см src/launchpack/launch/general.xml для примера, но нам для одной ноды и параметр, если нужно)
- Соблюдать лучшие практики ROS2 (когда это не противоречит требованиям из BaseFSM.md и BaseNode.md, например не использовать ROS2-параметры, использовать логирование из BaseNode.md а не напрямую, работать со временем как показано в BaseNode.md, и т.д.)
- не дублировать а использовать функционал BaseFSM.
- Документация в README.md на русском языке. 
- Комментарии в коде -- на английском.
- Всегда помни о задаче в целом и доделывай до конца, от общего к частному.
- Заботься о красивой архитектуре.


# Примеры 

## Старая ROS1-нода driller бурового станка /Users/<USER>/Work/ZyfraDrillv1/catkin_ws/src/drill-software/drill-mover/driller

Нужно использовать старый пример /Users/<USER>/Work/ZyfraDrillv1/catkin_ws/src/drill-software/drill-mover/driller
Что относительно этого примера в нашей реализации надо поменять: 
- Он был на ROS1, 
- он со старым окружением (другая base node, отличающиеся сообщения и логика работы немного). 

Документация по старой ноде /Users/<USER>/Work/drill-docs-dev/docs/onboard-system/drilling-and-mechanisms/driller.md

Профили бурения: конфиг и документация в виде комментов /Users/<USER>/Work/ZyfraDrillv1/catkin_ws/src/drill-software/drill-mover/drill_launch_pack/params/drill/drilling_profiles.yaml

# Спецификация состояний для ROS2 реализации

## Краткий обзор состояний FSM

| Состояние | Назначение | Ключевые особенности |
|-----------|------------|---------------------|
| **idle** | Ожидание команды | Обнуление управлений, сброс счетчиков |
| **touchdown** | Детекция грунта | Медленная подача, детекция по скорости |
| **overburden_pass** | Верхний слой | Плавный спин-ап, калибровка воздуха |
| **drilling** | Основное бурение | I-регулятор, подтяжки, контроль глубины |
| **hard_rot** | Заклинивание вращения | Подача вверх, возврат в предыдущее |
| **pullup** | Подтяжка бура | Короткие/длинные, управление водой |
| **after_pullup** | После подтяжки | Детекция грунта, восстановление воды |
| **pass_soft** | Мягкие участки | Низкое давление, переходы |
| **wait_after_drill** | После бурения | Подготовка к подъему |
| **raise** | Подъем бура | Переменная скорость, остановки |
| **unstuck** | Заклинивание движения | Циклы вверх-вниз |

## Требования к реализации состояний

### 1. **Базовый класс состояний**
```python
from abc import ABC, abstractmethod
from typing import Optional, Dict, Any
from base_node.base_fsm import BaseState

class DrillState(BaseState, ABC):
    """Базовый класс для всех состояний бурения"""

    def __init__(self, node: 'DrillerNode', name: str):
        super().__init__(node, name)
        self.entry_time: float = 0.0
        self.entry_spindle_depth: Optional[float] = None

    def on_transition_to(self) -> None:
        """Действия при входе в состояние - ОБЯЗАТЕЛЬНО переопределить"""
        self.entry_time = self.node.get_time()
        if self.node.subs.drill_state:
            self.entry_spindle_depth = self.node.subs.drill_state.head_pos

    @abstractmethod
    def do_work(self) -> None:
        """Рабочий цикл состояния - ОБЯЗАТЕЛЬНО переопределить"""
        pass

    def get_current_params(self) -> Dict[str, Any]:
        """Получение параметров текущего состояния из профиля"""
        state_name = self.get_name().lower()
        return getattr(self.node.profile_manager.current_params, state_name, {})

    def get_value_for_current_depth(self, depth_dict: List[Dict], default: Any = None) -> Any:
        """Получение значения из глубинной таблицы"""
        return self.node.depth_calculator.get_value_for_current_depth(depth_dict, default)
```

### 2. **Ключевые состояния (упрощенная спецификация)**

#### **IdleState**
```python
class IdleState(DrillState):
    def on_transition_to(self) -> None:
        super().on_transition_to()
        self.node.controls.reset()
        self.node.set_water(0.0)
        self.node.short_pullup_cnt = 0
        self.node.last_mandatory_pullup_spindle_depth = None

    def do_work(self) -> None:
        if self.node.current_action:
            if not self.node.safety_manager.validate_depth_data():
                return

            if self.node.current_action.first_rod_flag:
                self.node.set_state('touchdown')
            else:
                self.node.set_state('drilling')
```

#### **DrillingState (самое сложное)**
```python
class DrillingState(DrillState):
    def __init__(self, node):
        super().__init__(node, 'drilling')
        self.drilling_speed_error_i: float = 0.0
        self.increase_press_slowly: bool = True

    def do_work(self) -> None:
        params = self.get_current_params()

        # 1. Проверка достижения цели
        if self._target_depth_reached():
            self.node.set_state('wait_after_drill')
            return

        # 2. Обязательные подтяжки
        pullup_action = self._check_mandatory_pullups()
        if pullup_action:
            self.node.set_state('pullup', action=pullup_action)
            return

        # 3. Подтяжка по давлению воздуха
        if self.node.safety_manager.check_if_pullup_needed():
            self.node.set_state('pullup')
            return

        # 4. I-регулятор давления
        self._update_pressure_control(params)

        # 5. Остальные управления
        self._set_other_controls(params)

        # 6. Проверка заклинивания
        if self.node.safety_manager.is_hard_rot:
            self.node.set_state('hard_rot')
```

## Подробная спецификация реализации DrillerNode (ROS2)

### Архитектура

- Наследование: `DrillerNode` ← `BaseFSM` (`src/base_node/base_node/base_fsm.py`).
- Логирование/ивенты: через `BaseNode.log()` и коды из `Global.events`.
- Параметры: только через `params_server` (`BaseNode`), никаких ROS‑параметров. Обработка обновлений — `on_params_update()`.
- Частота цикла: параметр `rate` (Гц) из `nodes.yaml`.
- Состояния реализовать отдельными классами, регистрируются через `add_states()`.

### Подписки

Использовать интерфейсы, перечисленные в разделе «Входы и выходы». Подключение выполнять через `add_subscriber(topic, msg_type, name, timeout)` (там, где это не делает `BaseFSM` автоматически).

### Публикации

Использовать интерфейсы, перечисленные в разделе «Входы и выходы». Публикацию всех уставок и команд централизовать в одном месте цикла ноды (единая функция отправки, вызываемая из одного хука цикла FSM).

### Сервис

- `/{node_name}/recalibrate_air` (`std_srvs/Trigger`): зафиксировать `nominal_air_pres` по текущему `air_pressure` (разрешён вне `idle`).

### 3. **Ключевые алгоритмы для реализации**

#### **I-регулятор давления подачи (DrillingState)**
```python
def _update_pressure_control(self, params: Dict[str, Any]) -> None:
    """Обновление I-регулятора давления подачи"""
    dt = self.node.get_time() - self.prev_update_time
    self.prev_update_time = self.node.get_time()

    # Ошибка скорости бурения
    target_speed = params['target_drill_speed']
    current_speed = self.node.safety_manager.drill_speed_smoothed
    speed_error = target_speed - current_speed

    # Интегральная составляющая
    self.drilling_speed_error_i += speed_error * dt * params['drilling_speed_i_gain']
    self.drilling_speed_error_i = max(min(self.drilling_speed_error_i,
                                         params['drilling_speed_i_max']),
                                     params['drilling_speed_i_min'])

    # Базовое давление + коррекция
    base_pressure = self.node.node_params['user_feed_pressure']
    regulated_pressure = base_pressure + self.drilling_speed_error_i
    regulated_pressure = max(regulated_pressure, params['feed_pressure_min'])

    # Коррекция по высокому давлению вращения
    if self._high_rotation_pressure_detected(params):
        regulated_pressure = min(regulated_pressure, params['feed_pressure_reduced'])

    # Ограничение по глубинной таблице
    max_pressure = self.get_value_for_current_depth(params['feed_pressure_limit'])
    regulated_pressure = min(regulated_pressure, max_pressure)

    # Плавное увеличение давления
    if self.increase_press_slowly:
        slow_pressure = (self.node.controls.feed_pressure +
                        params['slow_press_increase_rate'] * dt)
        if slow_pressure >= regulated_pressure:
            self.increase_press_slowly = False
        self.node.controls.feed_pressure = min(regulated_pressure, slow_pressure)
    else:
        self.node.controls.feed_pressure = regulated_pressure
```

#### **Работа с глубинными таблицами**
```python
# В DepthCalculator классе
def get_value_for_current_depth(self, depth_dict: List[Dict], default: Any = None) -> Any:
    """
    Универсальная функция для работы с глубинными параметрами
    Поддерживает: from_ground, from_added_shaft_start, from_shaft_finish,
                 from_hole_finish, drilled
    """
    adjusted_entries = []
    current_depth = self.node.subs.drill_state.head_pos

    for entry in depth_dict:
        depth_ref = entry['depth']
        threshold_depth = self._convert_depth_reference(depth_ref)

        if threshold_depth is not None:
            adjusted_entries.append({
                'threshold': threshold_depth,
                'value': entry['value']
            })

    # Сортировка и выбор значения
    adjusted_entries.sort(key=lambda x: x['threshold'])

    result_value = default
    for entry in adjusted_entries:
        if current_depth >= entry['threshold']:
            result_value = entry['value']
        else:
            break

    return result_value

def _convert_depth_reference(self, depth_ref: Dict[str, float]) -> Optional[float]:
    """Преобразование ссылки на глубину к абсолютной координате шпинделя"""
    if 'from_ground' in depth_ref and self.node.current_action.first_rod_flag:
        return self.node.ground_spindle_depth + depth_ref['from_ground']

    elif 'from_added_shaft_start' in depth_ref and not self.node.current_action.first_rod_flag:
        return self.node.action_start_spindle_depth + depth_ref['from_added_shaft_start']

    elif 'from_shaft_finish' in depth_ref and not self.node.current_action.last_rod_flag:
        return self.node.current_action.drill_spindle_depth - depth_ref['from_shaft_finish']

    elif 'from_hole_finish' in depth_ref and self.node.current_action.last_rod_flag:
        return self.node.current_action.drill_spindle_depth - depth_ref['from_hole_finish']

    elif 'drilled' in depth_ref:
        return self.entry_spindle_depth + depth_ref['drilled']

    return None
```

#### **Система безопасности**
```python
# В SafetyManager классе
def update_stuck_detection(self) -> None:
    """Обновление детекции заклинивания"""
    current_time = self.node.get_time()

    # Сброс при выходе из скважины
    if not self._is_in_hole():
        self.is_stuck = False
        self.is_hard_rot = False
        return

    # Критерии заклинивания
    rotation_stuck = self._check_rotation_stuck()
    movement_stuck = self._check_movement_stuck()
    high_rotation_pressure = self._check_high_rotation_pressure()

    # Обновление флагов
    self.is_hard_rot = rotation_stuck or high_rotation_pressure

    # Общее заклинивание
    if (self._stuck_detection_allowed() and
        (movement_stuck or self.is_hard_rot)):
        self.is_stuck = True
    else:
        self.is_stuck = False

def check_if_pullup_needed(self) -> bool:
    """Проверка необходимости подтяжки по давлению воздуха"""
    if self._check_air_is_nominal():
        self.last_air_is_nominal_time = self.node.get_time()
        return False

    timeout = self.node.profile_manager.current_params['common']['air_not_nominal_max_time']
    if self.node.get_time() - self.last_air_is_nominal_time > timeout:
        self.node.log("High air pressure detected, pullup needed", level='warning')
        return True

    return False
```

### Параметры и профили

- Профили: `src/params_server/base_config/drilling_profiles.yaml`, ключ `Driller` (структура как в ROS1).
- Выбор профилей:
  - Всегда `default` как база.
  - По РМО (`drill_msgs/RockType`):
    - `type == HARD` → добавить профиль `hard`.
    - `type == CRACKED` → добавить профиль `cracked`.
    - `is_flooded == true` → добавить профиль `wet`.
  - Короткая скважина: если `hole_depth < default.common.short_hole_max_depth`, первыми применить `default-short`, а профиль `X` заменить на `X-short` при наличии.
  - Слияние — последовательное, последний слой имеет приоритет.
  - При изменении входов профиля (RMO, длина) — переcчитать `current_params` на лету.

- Параметры ноды (`nodes.yaml/Driller`):
  - `rate`
  - `user_feed_pressure` — старт для i‑регулятора подачи в `drilling`.
  - `user_rotation_speed` — верхний предел уставки вращения в `drilling`.

# Резюме: Переход от ROS1 к ROS2

## Ключевые улучшения архитектуры

### 1. **От монолитного к модульному**
- **ROS1**: Один большой файл `main_node_2.py` (737 строк)
- **ROS2**: Модульная архитектура с разделением ответственности
  - `DrillerNode` - главная логика FSM
  - `ProfileManager` - управление профилями
  - `SafetyManager` - система безопасности
  - `DepthCalculator` - работа с глубинными таблицами
  - `ControlSender` - централизованная отправка

### 2. **От слабой к строгой типизации**
- **ROS1**: `Dict2Obj`, динамические атрибуты
- **ROS2**: `@dataclass`, `typing`, статическая проверка типов

### 3. **От разрозненной к централизованной обработке ошибок**
- **ROS1**: `handle_internal_error()` в разных местах
- **ROS2**: Единая система через `BaseNode` с иерархией исключений

### 4. **От ручного к автоматическому управлению ресурсами**
- **ROS1**: Ручное управление подписками, таймерами
- **ROS2**: `add_subscriber()` с автоматическими таймаутами

## Гарантии сохранения функциональности

| Компонент | Статус | Улучшения |
|-----------|--------|-----------|
| ✅ **11 состояний FSM** | Полностью сохранены | + типизация, + модульность |
| ✅ **Система профилей** | Полностью сохранена | + валидация, + типизация |
| ✅ **I-регулятор давления** | Полностью сохранен | + улучшенная обработка ошибок |
| ✅ **Детекция заклинивания** | Полностью сохранена | + централизованная логика |
| ✅ **Управление люнетом** | Полностью сохранено | + улучшенная безопасность |
| ✅ **Глубинные таблицы** | Полностью сохранены | + типизация ссылок |
| ✅ **Контроль воздуха** | Полностью сохранен | + улучшенная диагностика |
| ✅ **Обязательные подтяжки** | Полностью сохранены | + типизированные действия |

## Финальные требования к реализации

### ✅ **Обязательные компоненты (100% покрытие функциональности)**
1. **Главный класс**: `DrillerNode(BaseFSM)` с модульной архитектурой
2. **Состояния**: 11 классов состояний с типизацией
3. **Менеджеры**: `ProfileManager`, `SafetyManager`, `DepthCalculator`
4. **Структуры данных**: Типизированные `@dataclass` для всех данных
5. **Конфигурация**: Полный набор профилей в `drilling_profiles.yaml`
6. **Сообщения**: `DrillerAction.msg` с полной спецификацией
7. **Документация**: README.md с полным описанием
8. **Тесты**: Юнит-тесты для ключевых алгоритмов

### ✅ **Соответствие стандартам ROS2**
- Использование `BaseFSM` вместо прямого наследования от `Node`
- Параметры через `params_server`, НЕ через ROS2 параметры
- Логирование через `BaseNode.log()`, НЕ через `get_logger()`
- Время через `BaseNode.get_time()`, НЕ через `get_clock()`
- Подписки через `add_subscriber()` с обязательными таймаутами
- Централизованная обработка ошибок через `handle_internal_error()`

### ✅ **Качество кода**
- Строгая типизация всех компонентов
- Модульная архитектура с разделением ответственности
- Централизованная система безопасности
- Улучшенная обработка ошибок с иерархией исключений
- Полное покрытие тестами критических алгоритмов

**Результат**: ROS2 версия будет иметь ВСЮ функциональность ROS1 версии, но с современной архитектурой, лучшей безопасностью, типизацией и тестируемостью.

## Система безопасности и контроля (детальный анализ)

### Детекция заклинивания (is_stuck, is_hard_rot)

**Функция check_for_stuck() - ключевая для безопасности:**
```python
def check_for_stuck(self):
    common_params = self.node.current_params.common
    current_time = rospy.get_time()

    # Сброс критериев заклинивания (вне скважины или не в режиме бурения)
    if (not self.is_in_hole(0.1) or
        self.subs.main_mode.mode != DRILLING):
        self.is_stuck = False
        self.is_hard_rot = False
        self.last_normal_rotation_time = rospy.get_time()
        self.last_nostuck_move_time = rospy.get_time()
        return

    # 1. КРИТЕРИЙ ЗАКЛИНИВАНИЯ ВРАЩЕНИЯ
    stuck_criteria_rot = (self.use_rotation_speed_sensor and
                         abs(self.subs.drill_ctrl.rotation_speed) > 0.95 and  # Команда на вращение
                         abs(self.subs.drill_state.drill_rpm) < common_params.too_low_rotation_speed)  # 3 об/мин
    if not stuck_criteria_rot:
        self.last_normal_rotation_time = current_time

    # 2. КРИТЕРИЙ ЗАКЛИНИВАНИЯ ДВИЖЕНИЯ
    stuck_criteria_move = (self.subs.drill_ctrl.feed_speed < -0.95 and  # Команда на подъем
                          self.subs.drill_state.head_speed > -common_params.stuck_speed_threshold)  # -0.05 м/с
    if not stuck_criteria_move:
        self.last_nostuck_move_time = current_time

    # 3. КРИТЕРИЙ ВЫСОКОГО ДАВЛЕНИЯ ВРАЩЕНИЯ
    high_rp = self.subs.pressure_state.rot_pressure > common_params.too_high_rp_threshold  # 310 бар
    if not high_rp:
        self.last_normal_rp_time = current_time

    # 4. ОПРЕДЕЛЕНИЕ ЗАКЛИНИВАНИЯ ВРАЩЕНИЯ (is_hard_rot)
    has_long_no_rotation = current_time - self.last_normal_rotation_time >= common_params.no_rotation_duration  # 0.5с
    has_long_high_rotation_pressure = current_time - self.last_normal_rp_time > common_params.high_rp_duration  # 1.5с
    self.is_hard_rot = has_long_no_rotation or has_long_high_rotation_pressure

    # 5. ОПРЕДЕЛЕНИЕ ОБЩЕГО ЗАКЛИНИВАНИЯ (is_stuck)
    has_long_no_move = current_time - self.last_nostuck_move_time > common_params.stuck_move_duration  # 1.247с

    allow_stuck_detection = (self.get_current_state_duration() > common_params.min_state_duration_to_stuck and  # 0.391с
                           current_time - self.last_unstuck_time > common_params.ignore_stuck_duration)  # 3с

    if allow_stuck_detection and (has_long_no_move or self.is_hard_rot):
        self.is_stuck = True
    else:
        self.is_stuck = False
```

### Управление люнетом (arm management)

**Функция manage_arm() - критична для безопасности:**
```python
def manage_arm(self):
    common_params = self.node.current_params.common
    current_state_name = self.get_current_state().get_name()
    deny_movement = False  # Флаг запрета движения

    # Сброс флагов отправленных команд по статусу люнета
    if self.subs.arm_status is not None:
        if self.subs.arm_status.status == CLOSED:
            self.sent_arm_close = False
        elif self.subs.arm_status.status == OPEN:
            self.sent_arm_open = False

    # Таймауты команд люнета (5 секунд)
    time_since_last_arm_action = rospy.get_time() - self.sent_arm_time
    if self.sent_arm_open and time_since_last_arm_action > 5.0:
        self.sent_arm_open = False
    if self.sent_arm_close and time_since_last_arm_action > 5.0:
        self.sent_arm_close = False

    # Логика управления (не в IDLE и не в remote режиме)
    if current_state_name != IDLE and 'remote' not in self.subs.main_mode.mode:
        # Условия для закрытия люнета
        need_to_close_arm = (common_params.close_arm and current_state_name == RAISE) or self.hole_angle != 0

        # Команды открытия/закрытия по глубине
        if (self.subs.drill_state.head_pos < common_params.arm_close_depth and  # 5.0м
            not self.sent_arm_close):
            if (need_to_close_arm and self.arm_present and
                self.subs.arm_status.status != CLOSED):
                self.close_arm_func()

        elif (self.subs.drill_state.head_pos > common_params.arm_open_depth and  # 7.0м
              not self.sent_arm_open):
            if self.subs.arm_status.status != OPEN:
                self.open_arm()

        # КРИТИЧЕСКИЕ ЗОНЫ - ЗАПРЕТ ДВИЖЕНИЯ
        if ((self.subs.drill_state.head_pos < common_params.arm_close_min_depth and  # 4.5м
             self.subs.arm_status.status != CLOSED and need_to_close_arm) or
            (self.subs.drill_state.head_pos > common_params.arm_open_max_depth and  # 7.5м
             self.subs.arm_status.status != OPEN)):
            deny_movement = True
            self.logwarn("Feed locked while rod support in wrong state")

    return deny_movement

def open_arm(self):
    self.publish_arm_action(do_open=True)
    self.sent_arm_open = True

def close_arm_func(self):
    self.publish_arm_action(do_open=False)
    self.sent_arm_close = True

def publish_arm_action(self, do_open=False):
    message = Action()
    message.data = json.dumps({'open': do_open})
    message.seq = 1
    self.arm_action_pub.publish(message)
    self.sent_arm_time = rospy.get_time()
```

### Воздух, компрессор и безопасность

**Система контроля воздуха:**
```python
def check_air_is_nominal(self):
    """Проверка номинального давления воздуха"""
    return ((self.subs.pressure_state.air_pressure <= self.current_params.common.max_air_pressure) and  # 5.5 бар
            (self.nominal_air_pres is None or
             self.subs.pressure_state.air_pressure <= self.nominal_air_pres + self.current_params.common.max_air_pressure_excess))  # +0.6 бар

def check_if_pullup_needed(self):
    """Проверка необходимости подтяжки по давлению воздуха"""
    current_time = rospy.get_time()

    if self.check_air_is_nominal():
        self.last_air_is_nominal_time = current_time
    elif current_time - self.last_air_is_nominal_time > self.current_params.common.air_not_nominal_max_time:  # 2.0с
        self.log("Too high air pressure! Need to pullup", loglevel=2)
        return True

    return False

def check_air(self):
    """Диагностика компрессора - вызывается в do_work_after()"""
    current_time = rospy.get_time()

    compressor_enabled = (self.controls.air_power > 0) and self.get_move_permission()
    # Ожидаемое поведение: включен и давление > 0.7 ИЛИ выключен и давление < 0.3
    air_is_ok = ((compressor_enabled and self.subs.pressure_state.air_pressure > 0.7) or
                (not compressor_enabled and self.subs.pressure_state.air_pressure < 0.3))

    if air_is_ok:
        self.first_air_is_bad_time = 0
    else:
        if not self.first_air_is_bad_time:
            self.first_air_is_bad_time = current_time
        elif current_time - self.first_air_is_bad_time > self.current_params.common.air_transient_response_time:  # 7.0с
            err_msg = "Compressor malfunction!"
            self.handle_internal_error(error_message=err_msg,
                                     error_type=self.global_params['Reports']['Critical']['Hardware_malfunction'],
                                     event=self.global_params['events']['compressor_failure'])

    self.last_air_check_time = current_time
```

### Валидация данных и безопасность

**Функция check_and_update_depth_data() - критична для работы:**
```python
def check_and_update_depth_data(self):
    """Проверка достоверности данных глубины и обновление сглаженной скорости"""

    if self.subs.drill_state.head_pos_is_reliable:
        self.last_valid_depth_time = rospy.get_time()

        # Обновление сглаженной скорости бурения (фильтр 1-го порядка)
        if self.drill_speed_smoothed is None:
            self.drill_speed_smoothed = self.subs.drill_state.head_speed
        else:
            self.drill_speed_smoothed += (self.subs.drill_state.head_speed - self.drill_speed_smoothed) * 0.1
        return True

    else:
        elapsed_since_valid = rospy.get_time() - self.last_valid_depth_time
        cur_state_name = self.get_current_state().get_name()

        # Разные таймауты для разных состояний
        if cur_state_name in [IDLE, DRILLING, AFTER_PULLUP, PASS_SOFT]:
            max_invalid_time = self.current_params.common.invalid_depth_duration_drill  # 8.0с
        else:
            max_invalid_time = self.current_params.common.invalid_depth_duration  # 0.6с

        if elapsed_since_valid < max_invalid_time:
            self.log("INVALID DEPTH DATA", loglevel=2, event=self.global_params['events']['laser_failure'])
            return True
        else:
            self.drill_speed_smoothed = None
            self.log("INVALID DEPTH DATA", loglevel=3, event=self.global_params['events']['laser_failure'])
            return False
```

**Функция check_data() - главная проверка в цикле:**
```python
def check_data(self):
    current_state_name = self.get_current_state().get_name()

    # Обновление профилей (кроме IDLE)
    if current_state_name != IDLE:
        self.process_profiles()

    # Проверка данных глубины
    if not self.check_and_update_depth_data():
        return False

    # Проверка заклинивания
    self.check_for_stuck()

    if self.subs.main_mode is None:
        return False

    # Пропуск проверок для IDLE и remote режимов
    if (current_state_name == IDLE or
        self.subs.main_mode.mode in (REMOTE, REMOTE_WAIT, REMOTE_PREPARE)):
        return True

    # Управление люнетом
    self.manage_arm()

    return True
```

### Система профилей (подробная реализация)

**Структура профилей в drilling_profiles.yaml:**
```yaml
DrillerNode:
  default:
    common:
      short_hole_max_depth: 9.5  # Порог короткой скважины
      water_low: &water_low 0.18
      water_high: &water_high 0.58
      max_rotation_speed: &max_rotation_speed 115
      # ... остальные общие параметры

    touchdown:
      feed_speed_ctrl: 0.2
      rotation_speed_ctrl: 0
      # ... параметры состояния

    drilling:
      mandatory_pullups:
        - depth: { from_ground: 3.5 }
          action: long
        - depth: { from_shaft_finish: 1.5 }
          action: short
      water_ctrl:
        - depth: { from_ground: 0.0 }
          value: *water_high
        - depth: { from_ground: 3.0 }
          value: *water_low
      # ... остальные параметры

  profiles:
    default-short:  # Для коротких скважин
      drilling:
        water_ctrl:
          - depth: { from_ground: 0.0 }
            value: *water_low
          - depth: { from_ground: 2.0 }
            value: 0

    hard:  # Для твердых пород
      drilling:
        feed_pressure_limit:
          - depth: { from_ground: -3 }
            value: 100  # Увеличенное давление

    wet:  # Для скважин с водой
      drilling:
        mandatory_pullups:
          - depth: { from_ground: 1.5 }
            action: long
          - depth: { from_ground: 2.5 }
            action: long

    cracked:  # Для трещиноватых пород
      drilling:
        mandatory_pullups:
          - depth: { drilled: 1.5 }
            action: cracked
```

**Применение профилей в коде:**
```python
def process_profiles(self):
    """Применение профилей на основе RMO входов и типа скважины"""
    profiles = []

    # Профили от RMO
    if self.subs.rock_type and self.subs.rock_type.type == HARD:
        profiles.append('hard')
    if self.subs.rock_type and self.subs.rock_type.type == CRACKED:
        profiles.append('cracked')
    if self.subs.rock_type and self.subs.rock_type.is_flooded:
        profiles.append('wet')

    # Для коротких скважин
    if self.is_short_hole():
        profiles.insert(0, 'default-short')
        # Замена профилей на -short версии
        profiles = [profile if profile + '-short' not in self.node_params["profiles"]
                   else profile + '-short' for profile in profiles]

    # Применение если изменились
    if profiles != self.last_profiles:
        self.log("Applying changed profiles: '%s'" % str(profiles))
        if self.apply_profiles(profiles):
            self.last_profiles = profiles

def apply_profiles(self, profiles):
    """Слияние профилей с базовыми параметрами"""
    current_params = deepcopy(self.node_params['default'])

    for profile_name in profiles:
        if profile_name in self.node_params["profiles"]:
            profile_params = self.node_params["profiles"][profile_name]
            current_params = deep_update(current_params, profile_params)
        else:
            self.log("No profile '%s' in known profiles!" % profile_name, 3)
            return False

    self.current_params = Dict2Obj(current_params)
    return True

def is_short_hole(self):
    """Определение короткой скважины"""
    if (self.hole_target_depth is not None and
        self.current_params.common.short_hole_max_depth is not None):
        return self.hole_target_depth < self.current_params.common.short_hole_max_depth
    return False
```

### Инициализация/обновление параметров

- При старте: запросить `node_params`, `Vehicle`, `Global` (как делает `BaseNode`), сформировать `current_params=default`, применить активные профили.
- При `on_params_update()`: пере‑прочитать и применить профили без сброса FSM, если возможно; перепечатать активные параметры в лог при изменениях профиля.

### Тестирование

- Табличные функции глубины (выбор значений и pullup‑порогов) — юнит‑тесты.
- Сценарные тесты FSM по основным переходам.
- Проверка профилей (`hard`, `wet`, `cracked`, `*-short`).

# Полная архитектура ROS2 реализации

## Ключевые отличия от ROS1 версии

| Аспект | ROS1 версия | ROS2 версия | Улучшения |
|--------|-------------|-------------|-----------|
| **Базовый класс** | `AbstractNodeStateMachine` | `BaseFSM` | Современная архитектура FSM, лучшая интеграция |
| **Параметры** | `rosparam` | `params_server` | Централизованное управление, валидация |
| **Типизация** | Слабая типизация | `dataclasses`, `typing` | Статическая проверка типов, меньше ошибок |
| **Обработка ошибок** | Разрозненная | Централизованная через `BaseNode` | Единообразная обработка, лучшее логирование |
| **Структура кода** | Монолитный файл | Модульная архитектура | Лучшая читаемость, тестируемость |
| **Конфигурация** | Встроенные константы | Параметризованная | Гибкость настройки без изменения кода |
| **Логирование** | `rospy.log*` | `BaseNode.log()` | Структурированное логирование с событиями |
| **Время** | `rospy.get_time()` | `BaseNode.get_time()` | Единообразная работа со временем |
| **Подписки** | Ручное управление | `add_subscriber()` с таймаутами | Автоматическое управление жизненным циклом |

## Архитектурные улучшения ROS2 версии

### 1. **Модульная структура**
```
src/driller_node/
├── driller_node/
│   ├── __init__.py
│   ├── driller_node.py          # Главный класс
│   ├── drill_controls.py        # Структуры данных
│   ├── profile_manager.py       # Управление профилями
│   ├── safety_manager.py        # Система безопасности
│   ├── depth_calculator.py      # Работа с глубинными таблицами
│   └── states/                  # Модуль состояний
│       ├── __init__.py
│       ├── base_state.py        # Базовый класс состояний
│       ├── idle_state.py
│       ├── touchdown_state.py
│       ├── drilling_state.py
│       └── ...
├── launch/
│   └── driller.launch.py
├── config/
│   └── driller_params.yaml
└── README.md
```

### 2. **Типизированные структуры данных**
```python
from dataclasses import dataclass
from typing import Optional, Dict, List, Union
from enum import Enum

class RockType(Enum):
    NORMAL = 0
    HARD = 1
    CRACKED = 2

class PullupAction(Enum):
    SHORT = "short"
    LONG = "long"
    CRACKED = "cracked"

@dataclass
class DrillControls:
    """Типизированная структура управляющих сигналов"""
    feed_speed: float = 0.0
    rotation_speed: float = 0.0
    feed_pressure: float = 0.0
    air_power: float = 0.0
    dust_collector_on: bool = False

    def reset(self) -> None:
        """Обнуление всех управлений"""
        self.feed_speed = 0.0
        self.rotation_speed = 0.0
        self.feed_pressure = 0.0
        self.air_power = 0.0
        self.dust_collector_on = False

@dataclass
class DrillAction:
    """Типизированная команда бурения"""
    id: int
    drill_spindle_depth: float
    raise_spindle_depth: float
    first_rod_flag: bool
    last_rod_flag: bool
    hole_depth: float
    inclination: float

@dataclass
class DepthReference:
    """Ссылка на глубину в профилях"""
    from_ground: Optional[float] = None
    from_added_shaft_start: Optional[float] = None
    from_shaft_finish: Optional[float] = None
    from_hole_finish: Optional[float] = None
    drilled: Optional[float] = None
```

### 3. **Главный класс DrillerNode**
```python
from typing import Dict, List, Optional, Any
from base_node.base_fsm import BaseFSM
from .drill_controls import DrillControls, DrillAction
from .profile_manager import ProfileManager
from .safety_manager import SafetyManager
from .states import *

class DrillerNode(BaseFSM):
    """
    ROS2 нода управления бурением с улучшенной архитектурой
    """

    def __init__(self):
        super().__init__('driller_node')

        # Типизированные компоненты
        self.controls: DrillControls = DrillControls()
        self.current_action: Optional[DrillAction] = None
        self.profile_manager: ProfileManager = ProfileManager(self)
        self.safety_manager: SafetyManager = SafetyManager(self)

        # Состояние системы
        self.ground_spindle_depth: Optional[float] = None
        self.nominal_air_pres: Optional[float] = None
        self.drill_speed_smoothed: Optional[float] = None

        # Инициализация
        self._initialize_states()
        self._initialize_subscribers()
        self._initialize_publishers()
        self._initialize_services()

    def _initialize_states(self) -> None:
        """Инициализация состояний FSM"""
        self.set_current_state(IdleState(self))
        self.add_states([
            TouchDownState(self),
            OverburdenPassState(self),
            DrillingState(self),
            HardRotState(self),
            PullUpState(self),
            AfterPullUpState(self),
            PassSoftState(self),
            WaitAfterDrillState(self),
            RaiseState(self),
            UnstuckState(self)
        ])

    def _initialize_subscribers(self) -> None:
        """Инициализация подписок с таймаутами"""
        self.add_subscriber(
            topic='driller_action',
            msg_type='drill_msgs/DrillerAction',
            name='driller_action',
            timeout=1.0,
            callback=self._action_callback
        )

        self.add_subscriber(
            topic='drill_state',
            msg_type='drill_msgs/DrillState',
            name='drill_state',
            timeout=0.5
        )

        self.add_subscriber(
            topic='rock_type',
            msg_type='drill_msgs/RockType',
            name='rock_type',
            timeout=5.0
        )

    def _initialize_publishers(self) -> None:
        """Инициализация публикаций"""
        self.create_publisher('drill_msgs/DrillCtrl', 'driller_setpoints', 10)
        self.create_publisher('drill_msgs/FloatCtrl', 'water_ctrl_auto', 10)
        self.create_publisher('drill_msgs/AirCtrl', 'air_ctrl', 10)
        self.create_publisher('drill_msgs/OpenCloseAction', 'arm_action', 10)

    def _initialize_services(self) -> None:
        """Инициализация сервисов"""
        self.create_service(
            'std_srvs/Trigger',
            'recalibrate_air',
            self._recalibrate_air_callback
        )
```

### 4. **Менеджер профилей (ProfileManager)**
```python
from typing import Dict, List, Any
from copy import deepcopy

class ProfileManager:
    """Управление профилями бурения с валидацией"""

    def __init__(self, node: 'DrillerNode'):
        self.node = node
        self.current_profiles: List[str] = []
        self.current_params: Dict[str, Any] = {}

    def update_profiles(self) -> bool:
        """Обновление профилей на основе входных данных"""
        new_profiles = self._calculate_required_profiles()

        if new_profiles != self.current_profiles:
            self.node.log(f"Applying profiles: {new_profiles}")
            if self._apply_profiles(new_profiles):
                self.current_profiles = new_profiles
                return True
            else:
                self.node.log("Failed to apply profiles", level='error')
                return False
        return True

    def _calculate_required_profiles(self) -> List[str]:
        """Расчет необходимых профилей"""
        profiles = []

        # Базовый профиль для коротких скважин
        if self._is_short_hole():
            profiles.append('default-short')

        # Профили от RMO
        if self.node.subs.rock_type:
            rock_type = self.node.subs.rock_type.type
            if rock_type == RockType.HARD.value:
                profiles.append('hard')
            elif rock_type == RockType.CRACKED.value:
                profiles.append('cracked')

            if self.node.subs.rock_type.is_flooded:
                profiles.append('wet')

        # Замена на -short версии для коротких скважин
        if self._is_short_hole():
            profiles = [
                f"{profile}-short" if f"{profile}-short" in self.node.node_params.get("profiles", {})
                else profile for profile in profiles
            ]

        return profiles

    def _apply_profiles(self, profiles: List[str]) -> bool:
        """Применение списка профилей"""
        try:
            base_params = deepcopy(self.node.node_params['default'])

            for profile_name in profiles:
                if profile_name in self.node.node_params.get("profiles", {}):
                    profile_params = self.node.node_params["profiles"][profile_name]
                    base_params = self._deep_update(base_params, profile_params)
                else:
                    self.node.log(f"Profile '{profile_name}' not found", level='warning')
                    return False

            self.current_params = base_params
            return True

        except Exception as e:
            self.node.log(f"Error applying profiles: {e}", level='error')
            return False

class SafetyManager:
    """Централизованная система безопасности"""

    def __init__(self, node: 'DrillerNode'):
        self.node = node
        self.last_valid_depth_time: float = 0.0
        self.last_unstuck_time: float = 0.0
        self.is_stuck: bool = False
        self.is_hard_rot: bool = False

    def check_safety(self) -> bool:
        """Главная проверка безопасности"""
        if not self._validate_depth_data():
            return False

        self._update_stuck_detection()
        self._check_air_safety()

        return True

    def _validate_depth_data(self) -> bool:
        """Валидация данных глубины с таймаутами"""
        if self.node.subs.drill_state.head_pos_is_reliable:
            self.last_valid_depth_time = self.node.get_time()
            self._update_drill_speed_smoothed()
            return True

        timeout = self._get_depth_timeout()
        if self.node.get_time() - self.last_valid_depth_time > timeout:
            self.node.log("Depth data timeout exceeded", level='error')
            return False

        return True

    def manage_arm_safety(self) -> bool:
        """Управление люнетом с блокировкой движения"""
        # Реализация логики управления люнетом
        # Возвращает True если движение разрешено
        pass
```

## Диаграмма состояний ROS2 версии

```mermaid
stateDiagram-v2
    [*] --> idle

    idle --> touchdown: DrillAction received AND first_rod_flag
    idle --> drilling: DrillAction received AND NOT first_rod_flag

    touchdown --> overburden_pass: ground_detected

    overburden_pass --> drilling: max_duration OR depth_delta
    overburden_pass --> hard_rot: safety_manager.is_hard_rot

    drilling --> wait_after_drill: target_depth_reached
    drilling --> pullup: mandatory_pullup OR safety_manager.high_air_pressure
    drilling --> hard_rot: safety_manager.is_hard_rot

    hard_rot --> drilling: safety_conditions_cleared
    hard_rot --> overburden_pass: safety_conditions_cleared

    pullup --> after_pullup: pullup_complete OR at_surface
    pullup --> unstuck: safety_manager.is_stuck

    after_pullup --> pass_soft: ground_detected

    pass_soft --> drilling: max_duration
    pass_soft --> pullup: safety_manager.high_air_pressure
    pass_soft --> wait_after_drill: target_depth_reached

    wait_after_drill --> raise: max_duration

    raise --> idle: target_raise_depth_reached
    raise --> unstuck: safety_manager.is_stuck

    unstuck --> drilling: unstuck_success
    unstuck --> pullup: unstuck_success
    unstuck --> raise: unstuck_success
    unstuck --> hard_rot: unstuck_failed

    note right of safety_manager: Централизованная система безопасности
    note right of profile_manager: Динамическое управление профилями
```

## ROS2-специфичные требования к реализации

### 1. **Использование BaseFSM**
```python
# ОБЯЗАТЕЛЬНО: Наследование от BaseFSM
class DrillerNode(BaseFSM):
    def __init__(self):
        super().__init__('driller_node')
        # НЕ использовать rclpy.Node напрямую!

# ОБЯЗАТЕЛЬНО: Использование методов BaseFSM для подписок
def _initialize_subscribers(self):
    # Правильно - с таймаутами и автоматическим управлением
    self.add_subscriber(
        topic='drill_state',
        msg_type='drill_msgs/DrillState',
        name='drill_state',
        timeout=0.5  # ОБЯЗАТЕЛЬНЫЙ таймаут
    )

    # НЕПРАВИЛЬНО - не использовать create_subscription напрямую!
    # self.create_subscription(...)
```

### 2. **Параметры через params_server**
```python
# ПРАВИЛЬНО: Получение параметров через BaseNode
def initialize_params(self):
    # Автоматически загружается из params_server
    self.node_params = self.get_node_params()  # Из nodes.yaml
    self.vehicle_params = self.get_vehicle_params()  # Из vehicle.yaml
    self.global_params = self.get_global_params()  # Из global.yaml

    # Профили бурения из drilling_profiles.yaml
    drilling_config = self.node_params.get('drilling_profiles', {})

# НЕПРАВИЛЬНО: НЕ использовать ROS2 параметры!
# self.declare_parameter('rate', 50.0)  # НЕ ДЕЛАТЬ ТАК!
```

### 3. **Логирование через BaseNode**
```python
# ПРАВИЛЬНО: Использование BaseNode.log()
self.log("Starting drilling operation", level='info')
self.log("High air pressure detected", level='warning',
         event=self.global_params['events']['high_air_pressure'])
self.log("Compressor failure", level='error',
         event=self.global_params['events']['compressor_failure'])

# НЕПРАВИЛЬНО: НЕ использовать rclpy логирование!
# self.get_logger().info("message")  # НЕ ДЕЛАТЬ ТАК!
```

### 4. **Работа со временем**
```python
# ПРАВИЛЬНО: Использование BaseNode.get_time()
current_time = self.get_time()
if current_time - self.last_check_time > timeout:
    # действие

# НЕПРАВИЛЬНО: НЕ использовать rclpy время!
# current_time = self.get_clock().now()  # НЕ ДЕЛАТЬ ТАК!
```

### 5. **Обработка ошибок через BaseNode**
```python
# ПРАВИЛЬНО: Централизованная обработка ошибок
def handle_critical_error(self, error_msg: str):
    self.handle_internal_error(
        error_message=error_msg,
        error_type=self.global_params['Reports']['Critical']['Hardware_malfunction'],
        event=self.global_params['events']['compressor_failure']
    )

# Автоматическая остановка управления и генерация событий
```

## Чек-лист соответствия функциональности ROS1 → ROS2

| Функция | ROS1 реализация | ROS2 требование | Статус |
|---------|-----------------|-----------------|--------|
| **Состояния FSM** | 11 состояний | ✅ Все 11 состояний | Обязательно |
| **Профили бурения** | 6 профилей | ✅ Все профили + улучшения | Обязательно |
| **Глубинные таблицы** | 5 типов ссылок | ✅ Все типы + типизация | Обязательно |
| **I-регулятор** | Простая реализация | ✅ + улучшенная валидация | Обязательно |
| **Детекция заклинивания** | 3 критерия | ✅ Все критерии + SafetyManager | Обязательно |
| **Управление люнетом** | Базовая логика | ✅ + улучшенная безопасность | Обязательно |
| **Контроль воздуха** | 2 проверки | ✅ Все проверки + диагностика | Обязательно |
| **Валидация данных** | Простые таймауты | ✅ + типизированная валидация | Обязательно |
| **Обязательные подтяжки** | 4 типа | ✅ Все типы + cracked режим | Обязательно |
| **Управление водой** | Глубинная таблица | ✅ + автоматическое сохранение | Обязательно |
| **Сервис калибровки** | `/recalibrate_air` | ✅ Тот же интерфейс | Обязательно |

## Улучшения архитектуры в ROS2 версии

### 1. **Типизация и валидация**
```python
# Строгая типизация всех структур данных
@dataclass
class DrillState:
    head_pos: float
    head_speed: float
    head_pos_is_reliable: bool
    # ... остальные поля с типами

# Валидация входных данных
def validate_drill_action(self, action: DrillAction) -> bool:
    if action.drill_spindle_depth <= 0:
        self.log("Invalid drill depth", level='error')
        return False
    # ... остальные проверки
```

### 2. **Модульная архитектура**
```python
# Разделение ответственности на модули
class DrillerNode(BaseFSM):
    def __init__(self):
        self.profile_manager = ProfileManager(self)  # Управление профилями
        self.safety_manager = SafetyManager(self)    # Система безопасности
        self.depth_calculator = DepthCalculator(self) # Работа с глубиной
        self.control_sender = ControlSender(self)    # Отправка управления
```

### 3. **Улучшенная обработка ошибок**
```python
# Иерархия исключений
class DrillerError(Exception):
    pass

class SafetyError(DrillerError):
    pass

class ConfigurationError(DrillerError):
    pass

# Централизованная обработка
def handle_error(self, error: Exception):
    if isinstance(error, SafetyError):
        self.emergency_stop()
    self.log(f"Error: {error}", level='error')
```

## Структура файлов ROS2 реализации

### 1. **Основной пакет driller_node**
```
src/driller_node/
├── package.xml                    # ROS2 пакет
├── setup.py                       # Python setup
├── setup.cfg                      # Конфигурация setup
├── README.md                      # Документация ноды
├── driller_node/
│   ├── __init__.py
│   ├── driller_node.py           # Главный класс DrillerNode
│   ├── drill_controls.py         # Типизированные структуры данных
│   ├── profile_manager.py        # Управление профилями бурения
│   ├── safety_manager.py         # Централизованная система безопасности
│   ├── depth_calculator.py       # Работа с глубинными таблицами
│   ├── control_sender.py         # Централизованная отправка управления
│   └── states/                   # Модуль состояний FSM
│       ├── __init__.py
│       ├── base_state.py         # Базовый класс состояний
│       ├── idle_state.py         # Состояние ожидания
│       ├── touchdown_state.py    # Детекция касания грунта
│       ├── overburden_pass_state.py  # Прохождение верхнего слоя
│       ├── drilling_state.py     # Основное бурение
│       ├── hard_rot_state.py     # Освобождение от заклинивания вращения
│       ├── pullup_state.py       # Подтяжка бура
│       ├── after_pullup_state.py # После подтяжки
│       ├── pass_soft_state.py    # Прохождение мягких участков
│       ├── wait_after_drill_state.py  # Ожидание после бурения
│       ├── raise_state.py        # Подъем бура
│       └── unstuck_state.py      # Освобождение от заклинивания
├── launch/
│   └── driller.launch.py         # Launch файл для запуска
├── config/
│   └── driller_params.yaml       # Локальные параметры (если нужны)
└── test/
    ├── test_depth_calculator.py  # Тесты глубинных таблиц
    ├── test_profile_manager.py   # Тесты профилей
    ├── test_safety_manager.py    # Тесты безопасности
    └── test_driller_fsm.py       # Интеграционные тесты FSM
```

### 2. **Конфигурация в params_server**
```
src/params_server/base_config/
├── drilling_profiles.yaml        # Профили бурения (ключ: DrillerNode)
└── nodes.yaml                    # Параметры ноды (секция: DrillerNode)
```

### 3. **Сообщения в drill_msgs**
```
src/drill_msgs/msg/
└── DrillerAction.msg             # Новое сообщение команды бурения
```

## Обязательные требования к реализации

### 1. **Файл package.xml**
```xml
<?xml version="1.0"?>
<package format="3">
  <name>driller_node</name>
  <version>1.0.0</version>
  <description>ROS2 drilling control node</description>
  <maintainer email="<EMAIL>">Developer</maintainer>
  <license>Proprietary</license>

  <depend>rclpy</depend>
  <depend>base_node</depend>
  <depend>drill_msgs</depend>
  <depend>std_srvs</depend>

  <test_depend>pytest</test_depend>

  <export>
    <build_type>ament_python</build_type>
  </export>
</package>
```

### 2. **Файл setup.py**
```python
from setuptools import setup, find_packages

package_name = 'driller_node'

setup(
    name=package_name,
    version='1.0.0',
    packages=find_packages(),
    data_files=[
        ('share/ament_index/resource_index/packages',
            ['resource/' + package_name]),
        ('share/' + package_name, ['package.xml']),
        ('share/' + package_name + '/launch', ['launch/driller.launch.py']),
    ],
    install_requires=['setuptools'],
    zip_safe=True,
    maintainer='Developer',
    maintainer_email='<EMAIL>',
    description='ROS2 drilling control node',
    license='Proprietary',
    tests_require=['pytest'],
    entry_points={
        'console_scripts': [
            'driller_node = driller_node.driller_node:main',
        ],
    },
)
```

### 3. **Launch файл**
```python
from launch import LaunchDescription
from launch_ros.actions import Node

def generate_launch_description():
    return LaunchDescription([
        Node(
            package='driller_node',
            executable='driller_node',
            name='driller_node',
            output='screen',
            parameters=[
                # Параметры загружаются через params_server
            ]
        )
    ])
```

### 4. **Конфигурация в nodes.yaml**
```yaml
DrillerNode:
  rate: 50.0                    # Частота цикла FSM (Гц)
  user_feed_pressure: 100.0     # Базовое давление подачи (бар)
  user_rotation_speed: 100.0    # Максимальная скорость вращения (об/мин)

  # Таймауты подписок
  timeouts:
    drill_state: 0.5            # Критичные данные
    rock_type: 5.0              # Не критичные данные
    driller_action: 1.0         # Команды управления
```

### 5. **Сообщение DrillerAction.msg**
```
# Команда управления бурением
std_msgs/Header header

# Идентификатор скважины
int32 id

# Целевые глубины шпинделя (м, положительные вниз)
float32 drill_spindle_depth     # Глубина для бурения
float32 raise_spindle_depth     # Глубина для подъема

# Флаги штанги
bool first_rod_flag             # Первая штанга (требует детекции грунта)
bool last_rod_flag              # Последняя штанга

# Параметры скважины
float32 hole_depth              # Общая глубина скважины (м)
float32 inclination             # Угол наклона башни (градусы)
```

## Финальный чек-лист реализации

### ✅ **Обязательные компоненты**
- [ ] Главный класс `DrillerNode(BaseFSM)`
- [ ] 11 классов состояний в модуле `states/`
- [ ] `ProfileManager` для управления профилями
- [ ] `SafetyManager` для системы безопасности
- [ ] `DepthCalculator` для глубинных таблиц
- [ ] `ControlSender` для централизованной отправки
- [ ] Типизированные структуры данных
- [ ] Сообщение `DrillerAction.msg`
- [ ] Конфигурация в `drilling_profiles.yaml`
- [ ] Параметры в `nodes.yaml`
- [ ] Launch файл
- [ ] README.md с документацией
- [ ] Юнит-тесты

### ✅ **Соответствие требованиям**
- [ ] Использование `BaseFSM` вместо прямого наследования от `Node`
- [ ] Параметры через `params_server`, НЕ через ROS2 параметры
- [ ] Логирование через `BaseNode.log()`, НЕ через `get_logger()`
- [ ] Время через `BaseNode.get_time()`, НЕ через `get_clock()`
- [ ] Подписки через `add_subscriber()` с таймаутами
- [ ] Централизованная обработка ошибок через `handle_internal_error()`
- [ ] Типизация всех структур данных
- [ ] Модульная архитектура
- [ ] Сохранение всей функциональности ROS1 версии