#!/usr/bin/env python3

import rclpy
from base_node import BaseNode
from drill_msgs.msg import (BoolStamped, Permission, StateMachineStatus,
                           TracksCtrl, TracksState)

# Import state constants
try:
    from main_state_machine.constants import MOVING
except ImportError:
    # Fallback if constants not available
    MOVING = 'moving'

# Import PID from base_node
from base_node.pid import PID


class TracksRegulatorNode(BaseNode):
    """ROS2 node for differential drive tracks speed regulation with dual PID control."""

    def __init__(self):
        """Initialize tracks regulator node."""
        super().__init__('tracks_regulator')

        # State variables
        self.is_moving_mode = False
        self.move_permission = False
        self.robomode = False

        # Data timestamps
        self.last_target_time = 0.0
        self.last_state_time = 0.0

        # Target and feedback values
        self.target_left = 0.0
        self.target_right = 0.0
        self.feedback_left = 0.0
        self.feedback_right = 0.0
        self.state_reliable = False

        # Control parameters (will be loaded from parameters)
        self.message_timeout = 1.0

        # PID controllers (will be initialized in initialize())
        self.left_pid = None
        self.right_pid = None

    def initialize(self):
        """Initialize node resources."""
        # Set logger level to DEBUG programmatically
        import rclpy.logging
        rclpy.logging.set_logger_level(self.get_name(), rclpy.logging.LoggingSeverity.DEBUG)
        
        self.log('Initializing TracksRegulator node', level=self.INFO)

        # Load control parameters
        self.message_timeout = self.node_params['timeout']
        # Override with global parameters if available
        if self.global_params and 'MSG_TTL' in self.global_params:
            self.message_timeout = self.global_params['MSG_TTL']

        # Initialize PID controllers with parameters
        self._initialize_pid_controllers()

        # Create publishers
        self.tracks_ctrl_pub = self.create_publisher(TracksCtrl, 'tracks_ctrl', 10)

        # Create subscribers for data updates
        self.create_subscription(TracksCtrl, 'tracks_target_speed',
                                 self._target_callback, 10)
        self.create_subscription(TracksState, 'tracks_state',
                                 self._state_callback, 10)
        self.create_subscription(StateMachineStatus, 'main_state_machine_status',
                                 self._state_machine_callback, 10)
        self.create_subscription(BoolStamped, 'robomode',
                                 self._robomode_callback, 10)
        self.create_subscription(Permission, 'permission',
                                 self._permission_callback, 10)

        self.log(f"TracksRegulator initialized with timeout: {self.message_timeout}", 
                level=self.DEBUG)
        self.log('TracksRegulator initialized successfully', level=self.INFO)

    def _initialize_pid_controllers(self):
        """Initialize PID controllers with parameters."""
        left_params = self.node_params['pid']['left']
        right_params = self.node_params['pid']['right']
        
        self.log(f"Left PID params: p={left_params['p']:.3f} i={left_params['i']:.3f} d={left_params['d']:.3f}", 
                level=self.DEBUG)
        self.log(f"Right PID params: p={right_params['p']:.3f} i={right_params['i']:.3f} d={right_params['d']:.3f}", 
                level=self.DEBUG)

        self.left_pid = PID(
            get_time=self.get_time,
            node=self,
            p=left_params['p'],
            i=left_params['i'],
            d=left_params['d'],
            ff=left_params['ff'],
            i_saturation=left_params['i_saturation'],
            out_min=left_params['out_min'],
            out_max=left_params['out_max'],
            d_term_tc=left_params['d_term_tc'],
            out_tc=left_params['out_tc'],
            force_zero=left_params['force_zero'],
            name="LEFT"
        )

        self.right_pid = PID(
            get_time=self.get_time,
            node=self,
            p=right_params['p'],
            i=right_params['i'],
            d=right_params['d'],
            ff=right_params['ff'],
            i_saturation=right_params['i_saturation'],
            out_min=right_params['out_min'],
            out_max=right_params['out_max'],
            d_term_tc=right_params['d_term_tc'],
            out_tc=right_params['out_tc'],
            force_zero=right_params['force_zero'],
            name="RIGHT"
        )

    def do_work(self):
        """Execute main work loop periodically."""
        current_time = self.get_time()

        # Status debug output every 5 seconds
        self.log(f"Status: move={self.is_moving_mode} perm={self.move_permission} robo={self.robomode} reliable={self.state_reliable}", 
                level=self.DEBUG, period=5.0)
        self.log(f"Targets: L={self.target_left:.2f} R={self.target_right:.2f} | Feedback: L={self.feedback_left:.2f} R={self.feedback_right:.2f}", 
                level=self.DEBUG, period=5.0)

        # Check working conditions
        if not self._check_working_conditions(current_time):
            self._publish_control(0.0, 0.0)
            return

        # Calculate control signals
        left_out = self.left_pid.update(self.target_left, self.feedback_left)
        right_out = self.right_pid.update(self.target_right, self.feedback_right)

        self._publish_control(left_out, right_out)

    def _check_working_conditions(self, current_time):
        """Check if all conditions for operation are met."""
        # Check if in MOVING mode
        if not self.is_moving_mode:
            self.log(f'Not in {MOVING.upper()} mode', level=self.WARN, period=5.0)
            return False

        # Check move permission
        if not self.move_permission:
            self.log('No permission for moving', level=self.WARN, period=5.0)
            return False

        # Check robomode
        if not self.robomode:
            self.log('Robomode disabled', level=self.WARN, period=5.0)
            return False

        # Check state data freshness
        if current_time - self.last_state_time > self.message_timeout:
            self.log('Outdated tracks state', level=self.ERROR,
                     event_code=self.events.SW_ERROR, period=1.0)
            return False

        # Check target data freshness
        if current_time - self.last_target_time > self.message_timeout:
            self.log('Outdated target speed', level=self.ERROR,
                     event_code=self.events.SW_ERROR, period=1.0)
            return False

        # Check state reliability
        if not self.state_reliable:
            self.log('Tracks state unreliable', level=self.WARN, period=1.0)
            return False

        return True

    def _publish_control(self, left, right):
        """Publish control signals."""
        msg = TracksCtrl()
        msg.header.stamp = self.get_rostime()
        msg.left = float(left)
        msg.right = float(right)
        self.tracks_ctrl_pub.publish(msg)

    def _target_callback(self, msg):
        """Handle target speed messages."""
        self.last_target_time = self.get_time()
        prev_left = self.target_left
        prev_right = self.target_right
        self.target_left = msg.left
        self.target_right = msg.right

        # Diagnostic: detect direction flip or sudden sign changes
        try:
            flip_left = (prev_left * self.target_left) < 0.0
            flip_right = (prev_right * self.target_right) < 0.0
            if flip_left or flip_right:
                self.log(
                    f"TARGET_DIR_FLIP: prev=({prev_left:.3f},{prev_right:.3f}) new=({self.target_left:.3f},{self.target_right:.3f})",
                    level=self.WARN,
                    period=0.5,
                )
        except Exception:
            pass

    def _state_callback(self, msg):
        """Handle tracks state messages."""
        self.last_state_time = self.get_time()
        self.feedback_left = msg.left
        self.feedback_right = msg.right
        old_reliable = self.state_reliable
        self.state_reliable = msg.is_reliable
        
        if not old_reliable and self.state_reliable:
            self.log(f"State became reliable: L={self.feedback_left:.2f} R={self.feedback_right:.2f}", 
                    level=self.INFO)
        elif old_reliable and not self.state_reliable:
            self.log("State became unreliable", level=self.WARN)

    def _state_machine_callback(self, msg):
        """Handle state machine status messages."""
        old_mode = self.is_moving_mode
        self.is_moving_mode = (msg.current_state == MOVING)
        if old_mode != self.is_moving_mode:
            self.log(f"State machine: {msg.current_state} (moving: {self.is_moving_mode})", 
                    level=self.INFO)

    def _robomode_callback(self, msg):
        """Handle robomode messages."""
        old_robomode = self.robomode
        self.robomode = msg.value
        if old_robomode != self.robomode:
            self.log(f"Robomode changed: {self.robomode}", level=self.INFO)

    def _permission_callback(self, msg):
        """Handle permission messages."""
        old_permission = self.move_permission
        self.move_permission = msg.permission
        if old_permission != self.move_permission:
            self.log(f"Permission changed: {self.move_permission}", level=self.INFO)

    def on_params_update(self, updated_keys):
        """Handle parameter updates."""
        self.log('Parameters updated, reinitializing PID controllers', level=self.INFO)

        # Update timeout parameter
        self.message_timeout = self.node_params['timeout']
        if self.global_params and 'MSG_TTL' in self.global_params:
            self.message_timeout = self.global_params['MSG_TTL']

        # Update PID controllers
        if self.left_pid is not None:
            self._update_pid_controller(self.left_pid, 'left')

        if self.right_pid is not None:
            self._update_pid_controller(self.right_pid, 'right')

    def _update_pid_controller(self, pid_controller, side):
        """Update PID controller parameters."""
        params = self.node_params['pid'][side]
        
        pid_controller.p = params['p']
        pid_controller.i = params['i']
        pid_controller.d = params['d']
        pid_controller.ff = params['ff']
        pid_controller.i_saturation = params['i_saturation']
        pid_controller.out_min = params['out_min']
        pid_controller.out_max = params['out_max']
        pid_controller.d_term_tc = params['d_term_tc']
        pid_controller.out_tc = params['out_tc']
        pid_controller.force_zero = params['force_zero']
        
        self.log(f"Updated {side.upper()} PID: p={pid_controller.p:.3f} i={pid_controller.i:.3f} d={pid_controller.d:.3f}", 
                level=self.INFO)


def main(args=None):
    """Entry point for the tracks regulator node."""
    rclpy.init(args=args)
    node = TracksRegulatorNode()
    try:
        node.run()
    except KeyboardInterrupt:
        pass


if __name__ == '__main__':
    main() 