from .node import Node
import math
from .data_types import *
import rclpy
import tf2_geometry_msgs


class Mix(Node):
    """
    mixtable:

    [((inputnum, valnum), (outnum, valnum)), ... ]
    """

    def __init__(self, ros_node, name, inputs, outputs, mixtable):
        super(Mix, self).__init__(ros_node, name, inputs=inputs, outputs=outputs)
        self.mixtable = mixtable

    def process(self):
        for mix in self.mixtable:
            i = self.get_input(mix[0][0], mix[0][1])
            self.set_output(mix[1][0], i, mix[1][1])
        self.is_updated = True


class CheckDeviation(Node):
    def __init__(self, ros_node, name, allowed_deviation, ang_unit=None):
        inputs = [
            ('Float', ['data']),
            ('Float', ['data'])
        ]
        outputs = [
            ('Bool', ['data']),
        ]
        super(CheckDeviation, self).__init__(ros_node, name, inputs=inputs, outputs=outputs)
        self.allowed_deviation = allowed_deviation
        if ang_unit == "rad":
            self.ang_lim = math.pi
        elif ang_unit == "deg":
            self.ang_lim = 180
        elif ang_unit is None:
            self.ang_lim = None
        else:
            raise ValueError("ang_unit must be either 'rad' or 'deg'")

    def update_params(self, new_params):
        self.allowed_deviation = new_params.get("allowed_deviation", self.allowed_deviation)

    def normalize_angle(self, angle):
        if self.ang_lim is None:
            return angle

        while angle > self.ang_lim:
            angle -= 2 * self.ang_lim
        while angle < -self.ang_lim:
            angle += 2 * self.ang_lim
        return angle

    def process(self):
        first_value = self.get_input(0, 0)
        second_value = self.get_input(1, 0)
        diff = first_value - second_value
        if self.ang_lim is not None:
            diff = self.normalize_angle(diff)
        if abs(diff) <= self.allowed_deviation:
            res = True
        else:
            res = False
        self.set_output(0, res, 0)
        self.is_updated = True


class LPF(Node):
    def __init__(self, ros_node, name, cutoff_freq, ang_unit=None):
        inputs = [
            ('Float', ['data']),
            ('builtin_interfaces/Time', [])
        ]
        outputs = [
            ('Float', ['data']),
        ]
        super(LPF, self).__init__(ros_node, name, inputs=inputs, outputs=outputs)
        self.cutoff_freq = cutoff_freq
        self.last_value = None
        self.last_time = 0
        self.ang_unit = ang_unit

    def update_params(self, new_params):
        self.cutoff_freq = new_params.get("cutoff_freq", self.cutoff_freq)

    def process(self):
        in_value = self.get_input(0, 0)
        ros_ts = self.get_input(1)
        timestamp = ros_ts.sec + ros_ts.nanosec * 1e-9
        if timestamp == 0:
            timestamp = self.ros_node.get_time()

        dt = timestamp - self.last_time
        # Инициализация состояния фильтра при первом запуске или после паузы
        if self.last_value is None or dt > 0.5 or dt <= 0:
            self.last_value = in_value
            self.last_time = timestamp
            out_value = in_value
        else:
            RC = 1 / (2 * math.pi * self.cutoff_freq)
            alpha = dt / (RC + dt)
            delta = in_value - self.last_value
            if self.ang_unit == 'rad':
                delta = math.remainder(delta, 2 * math.pi)
            elif self.ang_unit == 'deg':
                delta = math.remainder(delta, 360.0)
            out_value = self.last_value + alpha * delta
            self.last_value = out_value
            self.last_time = timestamp

        self.set_output(0, out_value, 0)
        self.is_updated = True


class LogicalAnd(Node):
    def __init__(self, ros_node, name, inputs=None):
        node_inputs = [
            ('Bool', ['data']),
            ('Bool', ['data']),
        ]
        if inputs is not None:
            node_inputs += inputs
        outputs = [
            ('Bool', ['data'])
        ]
        super(LogicalAnd, self).__init__(ros_node, name, inputs=node_inputs, outputs=outputs)

    def process(self):
        res = True
        for i in range(len(self.inputs)):
            value = self.get_input(i, 0)
            res = res and value
        self.set_output(0, res, 0)
        self.is_updated = True


class LogicalOr(Node):
    def __init__(self, ros_node, name, inputs=None):
        node_inputs = [
            ('Bool', ['data']),
            ('Bool', ['data']),
        ]
        if inputs is not None:
            node_inputs += inputs
        outputs = [
            ('Bool', ['data'])
        ]
        super(LogicalOr, self).__init__(ros_node, name, inputs=node_inputs, outputs=outputs)

    def process(self):
        res = False
        for i in range(len(self.inputs)):
            value = self.get_input(i, 0)
            res = res or value
        self.set_output(0, res, 0)
        self.is_updated = True


class EqualTo(Node):
    def __init__(self, ros_node, name, value):
        inputs = [
            ('Float', ['data'])
        ]

        outputs = [
            ('Bool', ['data'])
        ]
        super().__init__(ros_node, name, inputs=inputs, outputs=outputs)
        self.value = value

    def process(self):
        i = self.get_input(0, 0)
        self.set_output(0, i == self.value, 0)
        self.is_updated = True


class EulerToQuaternion(Node):
    def __init__(self, ros_node, name, input_is_radian=False):
        inputs = [("Vector3d", ["x", "y", "z"])]
        outputs = [("Quaternion", ["x", "y", "z", "w"])]
        super().__init__(ros_node, name, inputs=inputs, outputs=outputs)
        self.input_is_radian = input_is_radian

    def rpy_to_quaternion(self, rpy: Vector3d) -> Quaternion:
        roll = rpy.x
        pitch = rpy.y
        yaw = rpy.z

        if not self.input_is_radian:
            roll = math.radians(roll)
            pitch = math.radians(pitch)
            yaw = math.radians(yaw)

        half_roll = roll * 0.5
        half_pitch = pitch * 0.5
        half_yaw = yaw * 0.5

        cr = math.cos(half_roll)
        sr = math.sin(half_roll)
        cp = math.cos(half_pitch)
        sp = math.sin(half_pitch)
        cy = math.cos(half_yaw)
        sy = math.sin(half_yaw)

        w = cr * cp * cy + sr * sp * sy
        x = sr * cp * cy - cr * sp * sy
        y = cr * sp * cy + sr * cp * sy
        z = cr * cp * sy - sr * sp * cy

        return Quaternion(x, y, z, w)

    def process(self):
        v = self.get_input(0)
        q = self.rpy_to_quaternion(v)
        self.set_output(0, q)
        self.is_updated = True


class TransformPose(Node):
    def __init__(self, ros_node, name, from_frame, to_frame):
        inputs = [("geometry_msgs/Pose", [
            "position.x",
            "position.y",
            "position.z",
            "orientation.x",
            "orientation.y",
            "orientation.z",
            "orientation.w"
        ]), ('builtin_interfaces/Time', [])]
        outputs = [("geometry_msgs/Pose", [
            "position.x",
            "position.y",
            "position.z",
            "orientation.x",
            "orientation.y",
            "orientation.z",
            "orientation.w"
        ])]
        super().__init__(ros_node, name, inputs=inputs, outputs=outputs)
        self.from_frame = from_frame
        self.to_frame = to_frame

    def process(self):
        try:
            transform = self.ros_node.tf_buffer.lookup_transform(self.from_frame, self.to_frame, rclpy.time.Time())
        except Exception as e:
            self.ros_node.log(f"no transform from {self.from_frame} to {self.to_frame}")
            return

        in_pose = self.get_input(0)
        out_pose = tf2_geometry_msgs.do_transform_pose(in_pose, transform)
        self.set_output(0, out_pose)
        self.is_updated = True


class TransformPoseNoLookUp(Node):
    def __init__(self, ros_node, name):
        inputs = [("geometry_msgs/Pose", [
            "position.x",
            "position.y",
            "position.z",
            "orientation.x",
            "orientation.y",
            "orientation.z",
            "orientation.w"
        ]), ("geometry_msgs/TransformStamped", [
            "header.stamp",
            "transform.translation.x",
            "transform.translation.y",
            "transform.translation.z",
            "transform.rotation.x",
            "transform.rotation.y",
            "transform.rotation.z",
            "transform.rotation.w"
        ])]
        outputs = [("geometry_msgs/Pose", [
            "position.x",
            "position.y",
            "position.z",
            "orientation.x",
            "orientation.y",
            "orientation.z",
            "orientation.w"
        ])]
        super().__init__(ros_node, name, inputs=inputs, outputs=outputs)

    def process(self):
        transform = self.get_input(1)
        in_pose = self.get_input(0)
        out_pose = tf2_geometry_msgs.do_transform_pose(in_pose, transform)
        self.set_output(0, out_pose)
        self.is_updated = True


class AddVector(Node):
    def __init__(self, ros_node, name):
        inputs = [
            ('Vector3d', ['x', 'y', 'z']),
            ('Vector3d', ['x', 'y', 'z']),
        ]

        outputs = [
            ('Vector3d', ['x', 'y', 'z'])
        ]
        super().__init__(ros_node, name, inputs=inputs, outputs=outputs)

    def process(self):
        v1 = self.get_input(0)
        v2 = self.get_input(1)

        v = Vector3d(v1[0] + v2[0], v1[1] + v2[1], v1[2] + v2[2])

        self.set_output(0, v)
        self.is_updated = True


class RotateVector(Node):
    def __init__(self, ros_node, name, ang_unit='rad', cw=False):
        inputs = [
            ('Vector2d', ['x', 'y']),
            ('Float', ['data'])
        ]

        outputs = [
            ('Vector2d', ['x', 'y'])
        ]
        super().__init__(ros_node, name, inputs=inputs, outputs=outputs)
        self.ang_unit = ang_unit
        self.cw = cw

    def process(self):
        v = self.get_input(0)
        angle = self.get_input(1, 0)
        if self.ang_unit != 'rad':
            angle = math.radians(angle)

        c, s = math.cos(angle), math.sin(angle)
        if self.cw:
            x = v.x * c + v.y * s
            y = -v.x * s + v.y * c
        else:
            x = v.x * c - v.y * s
            y = v.x * s + v.y * c

        v_out = Vector2d(x, y)

        self.set_output(0, v_out)
        self.is_updated = True


class TimeDerivative(Node):
    def __init__(self, ros_node, name, ang_unit=None):
        inputs = [
            ('Float', ['data']),
            ('builtin_interfaces/Time', [])
        ]
        outputs = [
            ('Float', ['data'])
        ]
        super().__init__(ros_node, name, inputs=inputs, outputs=outputs)

        self.last_t = None
        self.last_x = None
        self.ang_unit = ang_unit

    def process(self):
        x = self.get_input(0, 0)
        ros_ts = self.get_input(1)
        ts = ros_ts.sec + ros_ts.nanosec * 1e-9

        if self.last_t is None:
            self.last_t = ts
            self.last_x = x
            return

        dt = ts - self.last_t

        if dt < 1e-5:
            return

        dx = (x - self.last_x)
        if self.ang_unit == 'rad':
            dx = math.remainder(dx, 2 * math.pi)
        elif self.ang_unit == 'deg':
            dx = math.remainder(dx, 360.0)

        derivative = dx / dt

        self.last_t = ts
        self.last_x = x
        self.set_output(0, derivative, 0)

        self.is_updated = True


class LinearTransform(Node):
    def __init__(self, ros_node, name, raw_1, result_1, raw_2, result_2):
        inputs = [
            ('Float', ['data'])
        ]

        outputs = [
            ('Float', ['data'])
        ]
        super().__init__(ros_node, name, inputs=inputs, outputs=outputs)
        if raw_1 == raw_2:
            raise ValueError("raw_1 и raw_2 не должны совпадать (деление на ноль).")

        # Коэффициенты линейной функции y = m*x + b, проходящей через (raw_1, result_1) и (raw_2, result_2)
        self.m = (float(result_2) - float(result_1)) / (float(raw_2) - float(raw_1))
        self.b = float(result_1) - self.m * float(raw_1)


    def process(self):
        x = self.get_input(0, 0)
        y = self.m * float(x) + self.b
        self.set_output(0, y, 0)
        self.is_updated = True

class InRange(Node):
    def __init__(self, ros_node, name, min_val=None, max_val=None):
        inputs = [
            ('Float', ['data'])
        ]
        outputs = [
            ('Bool', ['data'])
        ]
        super().__init__(ros_node, name, inputs=inputs, outputs=outputs)

        if min_val is None and max_val is None:
            raise ValueError("Должен быть задан хотя бы один из параметров: min или max.")

        self.min_val = -math.inf if min_val is None else float(min_val)
        self.max_val =  math.inf if max_val is None else float(max_val)

        if self.min_val > self.max_val:
            raise ValueError("min не может быть больше max.")

    def process(self):
        x = self.get_input(0, 0)

        in_range = (self.min_val <= x <= self.max_val)
        self.set_output(0, in_range, 0)
        self.is_updated = True

class Limiter(Node):
    def __init__(self, ros_node, name, min_val=None, max_val=None):
        inputs = [
            ('Float', ['data'])
        ]
        outputs = [
            ('Float', ['data'])
        ]
        super().__init__(ros_node, name, inputs=inputs, outputs=outputs)

        if min_val is None and max_val is None:
            raise ValueError("Должен быть задан хотя бы один из параметров: min или max.")

        self.min_val = -math.inf if min_val is None else float(min_val)
        self.max_val =  math.inf if max_val is None else float(max_val)

        if self.min_val > self.max_val:
            raise ValueError("min не может быть больше max.")

    def process(self):
        x = self.get_input(0, 0)

        y = max(self.min_val, min(self.max_val, x))
        self.set_output(0, y, 0)
        self.is_updated = True

