{"enters": [{"module": "io", "class": "Subscriber", "params": {"name": "body_inc_sub", "topic_name": "/board_inclinometer", "queue_size": 10, "process_rate": 50, "outputs": [["drill_msgs/Vector2d", ["header.stamp", "x", "y"]]]}, "position": {"x": 84, "y": 143}}, {"module": "io", "class": "Subscriber", "params": {"name": "front_imu_sub", "topic_name": "/front_imu", "queue_size": 10, "process_rate": 50, "outputs": [["drill_msgs/IMU", ["header.stamp", "roll", "pitch"]]]}, "position": {"x": 80, "y": 704}}, {"module": "io", "class": "Subscriber", "params": {"name": "rear_imu_sub", "topic_name": "/rear_imu", "queue_size": 10, "process_rate": 50, "outputs": [["drill_msgs/IMU", ["header.stamp", "roll", "pitch"]]]}, "position": {"x": 82, "y": 1020}}, {"module": "io", "class": "Subscriber", "params": {"name": "gnss_sub", "topic_name": "/gnss_position", "queue_size": 10, "process_rate": 30, "outputs": [["drill_msgs/GNSS", ["header.stamp", "latitude", "longitude", "altitude", "heading", "quality", "sat_num"]]]}, "position": {"x": 40, "y": 2508}}, {"module": "io", "class": "Subscriber", "params": {"name": "drill_state_raw_sub", "topic_name": "/drill_state_raw", "queue_size": 10, "process_rate": 40, "outputs": [["drill_msgs/DrillStateRaw", ["header.stamp", "head_pos", "head_angular_pos", "drill_rpm", "feed_pressure", "rot_pressure", "air_pressure"]]]}, "position": {"x": 229, "y": 4258}}], "nodes": [{"module": "io", "class": "Publisher", "params": {"name": "level_pub", "topic_name": "/level", "queue_size": 10, "inputs": [["drill_msgs/Level", ["header.stamp", "roll", "pitch", "is_reliable"]]]}, "position": {"x": 2056, "y": 365}}, {"module": "basic", "class": "LPF", "params": {"name": "inc_roll_lpf", "cutoff_freq": "$cutoff_freq", "ang_unit": "deg"}, "position": {"x": 519, "y": 90}}, {"module": "basic", "class": "LPF", "params": {"name": "inc_pitch_lpf", "cutoff_freq": "$cutoff_freq", "ang_unit": "deg"}, "position": {"x": 516, "y": 333}}, {"module": "dedicated", "class": "RPAlign", "params": {"name": "body_rp_align", "swap_rp": "$swap_rp", "roll_offset": "$roll_offset", "pitch_offset": "$pitch_offset", "invert_roll": "$invert_roll", "invert_pitch": "$invert_pitch"}, "position": {"x": 882, "y": 79}}, {"module": "basic", "class": "LPF", "params": {"name": "front_imu_pitch_lpf", "cutoff_freq": "$cutoff_freq", "ang_unit": "deg"}, "position": {"x": 515, "y": 769}}, {"module": "basic", "class": "LPF", "params": {"name": "front_imu_roll_lpf", "cutoff_freq": "$cutoff_freq", "ang_unit": "deg"}, "position": {"x": 516, "y": 556}}, {"module": "dedicated", "class": "RPAlign", "params": {"name": "front_imu_rp_align", "swap_rp": "$swap_rp", "roll_offset": "$roll_offset", "pitch_offset": "$pitch_offset", "invert_roll": "$invert_roll", "invert_pitch": "$invert_pitch"}, "position": {"x": 872, "y": 574}}, {"module": "basic", "class": "LPF", "params": {"name": "rear_imu_pitch_lpf", "cutoff_freq": "$cutoff_freq", "ang_unit": "deg"}, "position": {"x": 515, "y": 1186}}, {"module": "basic", "class": "LPF", "params": {"name": "rear_imu_roll_lpf", "cutoff_freq": "$cutoff_freq", "ang_unit": "deg"}, "position": {"x": 515, "y": 973}}, {"module": "dedicated", "class": "RPAlign", "params": {"name": "rear_imu_rp_align", "swap_rp": "$swap_rp", "roll_offset": "$roll_offset", "pitch_offset": "$pitch_offset", "invert_roll": "$invert_roll", "invert_pitch": "$invert_pitch"}, "position": {"x": 870, "y": 966}}, {"module": "basic", "class": "CheckDeviation", "params": {"name": "front_imu_roll_check", "allowed_deviation": "$allowed_deviation", "ang_unit": "deg"}, "position": {"x": 1358, "y": 324}}, {"module": "basic", "class": "CheckDeviation", "params": {"name": "front_imu_pitch_check", "allowed_deviation": "$allowed_deviation", "ang_unit": "deg"}, "position": {"x": 1353, "y": 555}}, {"module": "basic", "class": "CheckDeviation", "params": {"name": "rear_imu_roll_check", "allowed_deviation": "$allowed_deviation", "ang_unit": "deg"}, "position": {"x": 1346, "y": 811}}, {"module": "basic", "class": "CheckDeviation", "params": {"name": "rear_imu_pitch_check", "allowed_deviation": "$allowed_deviation", "ang_unit": "deg"}, "position": {"x": 1350, "y": 1057}}, {"module": "basic", "class": "LogicalAnd", "params": {"name": "all_level_check", "inputs": [["Bool", ["data"]], ["Bool", ["data"]]]}, "position": {"x": 1754, "y": 650}}, {"module": "dedicated", "class": "RPAlign", "params": {"name": "front_imu_to_lidar_rp_align", "swap_rp": "$swap_rp", "roll_offset": "$roll_offset", "pitch_offset": "$pitch_offset", "invert_roll": "$invert_roll", "invert_pitch": "$invert_pitch"}, "position": {"x": 508, "y": 1406}}, {"module": "dedicated", "class": "RPAlign", "params": {"name": "rear_imu_to_lidar_rp_align", "swap_rp": "$swap_rp", "roll_offset": "$roll_offset", "pitch_offset": "$pitch_offset", "invert_roll": "$invert_roll", "invert_pitch": "$invert_pitch"}, "position": {"x": 501, "y": 1700}}, {"module": "basic", "class": "EulerToQuaternion", "params": {"name": "front_imu_q", "input_is_radian": 0}, "position": {"x": 872, "y": 1409}}, {"module": "basic", "class": "EulerToQuaternion", "params": {"name": "rear_imu_q", "input_is_radian": 0}, "position": {"x": 871, "y": 1704}}, {"module": "io", "class": "TransformPublisher", "params": {"name": "front_lidar_inc_tf", "from_frame": "front_lidar_noinc", "to_frame": "velodyne_front", "reverse": 0}, "position": {"x": 1288, "y": 1286}}, {"module": "io", "class": "TransformPublisher", "params": {"name": "rear_lidar_inc_tf", "from_frame": "rear_lidar_noinc", "to_frame": "velodyne_rear", "reverse": 0}, "position": {"x": 1281, "y": 1583}}, {"module": "basic", "class": "EulerToQuaternion", "params": {"name": "body_inc_q", "input_is_radian": 0}, "position": {"x": 1330, "y": 61}}, {"module": "io", "class": "TransformPublisher", "params": {"name": "body_inc_tf", "from_frame": "main_antenna_noinc", "to_frame": "main_antenna", "reverse": 0}, "position": {"x": 1766, "y": 58}}, {"module": "dedicated", "class": "LatLonToCartesian", "params": {"name": "gnss_to_xy", "utm_zone": "$Global/enterprise/location/utm_zone", "ref_lat": "$Global/enterprise/location/base_point/lat", "ref_lon": "$Global/enterprise/location/base_point/lon"}, "position": {"x": 469, "y": 2130}}, {"module": "dedicated", "class": "ProcessRtkHeading", "params": {"name": "rtk_heading_proc", "main_antenna_x": "$Vehicle/devices/position_antenna/local_x", "main_antenna_y": "$Vehicle/devices/position_antenna/local_y", "vect_antenna_x": "$Vehicle/devices/vector_antenna/local_x", "vect_antenna_y": "$Vehicle/devices/vector_antenna/local_y"}, "position": {"x": 423, "y": 3045}}, {"module": "basic", "class": "EulerToQuaternion", "params": {"name": "heading_q", "input_is_radian": 1}, "position": {"x": 1201, "y": 2901}}, {"module": "io", "class": "TransformPublisher", "params": {"name": "main_antenna_noinc_tf", "from_frame": "map", "to_frame": "main_antenna_noinc", "reverse": 0}, "position": {"x": 1706, "y": 2047}}, {"module": "io", "class": "TransformPublisher", "params": {"name": "front_lidar_noinc_tf", "from_frame": "front_lidar_mount", "to_frame": "front_lidar_noinc", "reverse": 1}, "position": {"x": 2104, "y": 58}}, {"module": "io", "class": "TransformPublisher", "params": {"name": "rear_lidar_noinc_tf", "from_frame": "rear_lidar_mount", "to_frame": "rear_lidar_noinc", "reverse": 0}, "position": {"x": 2455, "y": 57}}, {"module": "basic", "class": "LPF", "params": {"name": "yaw_lpf", "cutoff_freq": "$cutoff_freq", "ang_unit": "rad"}, "position": {"x": 818, "y": 3029}}, {"module": "basic", "class": "TransformPose", "params": {"name": "main_pose_est_1", "from_frame": "main_antenna_noinc", "to_frame": "base_link"}, "position": {"x": 822, "y": 2572}}, {"module": "io", "class": "Publisher", "params": {"name": "debug_pose_pub", "topic_name": "/debug/main_pose", "queue_size": 10, "inputs": [["geometry_msgs/PoseStamped", ["header.stamp", "pose.position.x", "pose.position.y", "pose.position.z", "pose.orientation.x", "pose.orientation.y", "pose.orientation.z", "pose.orientation.w"]]]}, "position": {"x": 2182, "y": 2030}}, {"module": "basic", "class": "TransformPoseNoLookUp", "params": {"name": "main_pose_est_2"}, "position": {"x": 1705, "y": 2406}}, {"module": "io", "class": "Publisher", "params": {"name": "position_pub", "topic_name": "/position", "queue_size": 10, "inputs": [["drill_msgs/Position", ["header.stamp", "x", "y", "z", "yaw", "is_reliable"]]]}, "position": {"x": 2203, "y": 2648}}, {"module": "basic", "class": "EqualTo", "params": {"name": "quality_check", "value": 4}, "position": {"x": 1209, "y": 3213}}, {"module": "basic", "class": "LPF", "params": {"name": "x_lpf_for_speed", "cutoff_freq": "$cutoff_freq"}, "position": {"x": 2208, "y": 3021}}, {"module": "basic", "class": "LPF", "params": {"name": "y_lpf_for_speed", "cutoff_freq": "$cutoff_freq"}, "position": {"x": 2207, "y": 3251}}, {"module": "basic", "class": "LPF", "params": {"name": "yaw_lpf_for_speed", "cutoff_freq": "$cutoff_freq", "ang_unit": "rad"}, "position": {"x": 2211, "y": 3489}}, {"module": "basic", "class": "TimeDerivative", "params": {"name": "dxdt"}, "position": {"x": 2684, "y": 3013}}, {"module": "basic", "class": "TimeDerivative", "params": {"name": "dydt"}, "position": {"x": 2682, "y": 3251}}, {"module": "basic", "class": "TimeDerivative", "params": {"name": "dyawdt", "ang_unit": "rad"}, "position": {"x": 2687, "y": 3479}}, {"module": "basic", "class": "RotateVector", "params": {"name": "rotate_vel", "ang_unit": "rad", "cw": 1}, "position": {"x": 3579, "y": 3053}}, {"module": "basic", "class": "LPF", "params": {"name": "vx_lpf", "cutoff_freq": "$cutoff_freq"}, "position": {"x": 3102, "y": 3005}}, {"module": "basic", "class": "LPF", "params": {"name": "vy_lpf", "cutoff_freq": "$cutoff_freq"}, "position": {"x": 3101, "y": 3248}}, {"module": "basic", "class": "LPF", "params": {"name": "w_lpf", "cutoff_freq": "$cutoff_freq", "ang_unit": "rad"}, "position": {"x": 3102, "y": 3483}}, {"module": "dedicated", "class": "CalcWheelsSpeed", "params": {"name": "calc_tracks_speed", "width": "$Vehicle/geometry/tracks_dist"}, "position": {"x": 4099, "y": 3210}}, {"module": "io", "class": "Publisher", "params": {"name": "speed_state_pub", "topic_name": "/speed_state", "queue_size": 10, "inputs": [["drill_msgs/SpeedState", ["header.stamp", "forward", "lateral", "angular", "is_reliable"]]]}, "position": {"x": 4083, "y": 2739}}, {"module": "io", "class": "Publisher", "params": {"name": "tracks_state_pub", "topic_name": "/tracks_state", "queue_size": 10, "inputs": [["drill_msgs/TracksState", ["header.stamp", "left", "right", "is_reliable"]]]}, "position": {"x": 4532, "y": 3132}}, {"module": "io", "class": "Publisher", "params": {"name": "drill_pos_pub", "topic_name": "/drill_position", "queue_size": 10, "inputs": [["drill_msgs/Position", ["header.stamp", "x", "y", "z", "is_reliable", "yaw"]]]}, "position": {"x": 3232, "y": 1755}}, {"module": "io", "class": "Publisher", "params": {"name": "debug_drill_pub", "topic_name": "/debug/drill_pose", "queue_size": 10, "inputs": [["geometry_msgs/PoseStamped", ["header.stamp", "pose.position.x", "pose.position.y", "pose.position.z", "pose.orientation.x", "pose.orientation.y", "pose.orientation.z", "pose.orientation.w"]]]}, "position": {"x": 3230, "y": 2207}}, {"module": "basic", "class": "TransformPoseNoLookUp", "params": {"name": "drill_pose_est"}, "position": {"x": 2688, "y": 1937}}, {"module": "basic", "class": "TransformPose", "params": {"name": "drill2bl", "from_frame": "base_link", "to_frame": "drill"}, "position": {"x": 2129, "y": 1529}}, {"module": "basic", "class": "LinearTransform", "params": {"name": "head_pos_transform", "raw_1": "$raw_1", "result_1": "$result_1", "raw_2": "$raw_2", "result_2": "$result_2"}, "position": {"x": 787, "y": 4264}}, {"module": "basic", "class": "InRange", "params": {"name": "head_pos_raw_check", "min_val": "$min_val", "max_val": "$max_val"}, "position": {"x": 802, "y": 4047}}, {"module": "io", "class": "Publisher", "params": {"name": "drill_state_pub", "topic_name": "/drill_state", "queue_size": 10, "inputs": [["drill_msgs/DrillState", ["header.stamp", "head_pos", "head_speed", "head_angular_pos", "drill_rpm", "feed_pressure", "rot_pressure", "air_pressure", "head_pos_is_reliable"]]]}, "position": {"x": 2407, "y": 4387}}, {"module": "basic", "class": "LPF", "params": {"name": "head_pos_lpf", "cutoff_freq": "$cutoff_freq"}, "position": {"x": 1167, "y": 4257}}, {"module": "basic", "class": "TimeDerivative", "params": {"name": "head_speed_calc"}, "position": {"x": 1535, "y": 4257}}, {"module": "basic", "class": "LPF", "params": {"name": "head_speed_lpf", "cutoff_freq": "$cutoff_freq"}, "position": {"x": 1921, "y": 4253}}, {"module": "basic", "class": "LinearTransform", "params": {"name": "head_ang_pos_transform", "raw_1": "$raw_1", "result_1": "$result_1", "raw_2": "$raw_2", "result_2": "$result_2"}, "position": {"x": 778, "y": 4499}}, {"module": "basic", "class": "LinearTransform", "params": {"name": "head_rpm_transform", "raw_1": "$raw_1", "result_1": "$result_1", "raw_2": "$raw_2", "result_2": "$result_2"}, "position": {"x": 775, "y": 4754}}, {"module": "basic", "class": "LinearTransform", "params": {"name": "feed_pressure_transform", "raw_1": "$raw_1", "result_1": "$result_1", "raw_2": "$raw_2", "result_2": "$result_2"}, "position": {"x": 772, "y": 5000}}, {"module": "basic", "class": "LinearTransform", "params": {"name": "rot_pressure_transform", "raw_1": "$raw_1", "result_1": "$result_1", "raw_2": "$raw_2", "result_2": "$result_2"}, "position": {"x": 771, "y": 5238}}, {"module": "basic", "class": "LinearTransform", "params": {"name": "air_pressure_transform", "raw_1": "$raw_1", "result_1": "$result_1", "raw_2": "$raw_2", "result_2": "$result_2"}, "position": {"x": 769, "y": 5487}}, {"module": "basic", "class": "Limiter", "params": {"name": "ap_lim", "min_val": 0}, "position": {"x": 1147, "y": 5507}}, {"module": "basic", "class": "Limiter", "params": {"name": "rp_lim", "min_val": 0}, "position": {"x": 1172, "y": 5255}}, {"module": "basic", "class": "Limiter", "params": {"name": "fp_lim", "min_val": 0}, "position": {"x": 1193, "y": 5015}}, {"module": "basic", "class": "Limiter", "params": {"name": "rpm_lim", "min_val": 0}, "position": {"x": 1199, "y": 4770}}], "links": {"level_pub": [], "inc_roll_lpf": [{"name": "body_rp_align", "links": [[[0, 0], [0, 0]]]}], "inc_pitch_lpf": [{"name": "body_rp_align", "links": [[[0, 0], [0, 1]]]}], "body_rp_align": [{"name": "level_pub", "links": [[[0, 0], [0, 1]], [[0, 1], [0, 2]]]}, {"name": "front_imu_roll_check", "links": [[[0, 0], [0, 0]]]}, {"name": "rear_imu_roll_check", "links": [[[0, 0], [0, 0]]]}, {"name": "body_inc_q", "links": [[[0, 0], [0, 0]], [[0, 1], [0, 1]]]}, {"name": "front_imu_pitch_check", "links": [[[0, 1], [0, 0]]]}, {"name": "rear_imu_pitch_check", "links": [[[0, 1], [0, 0]]]}], "front_imu_pitch_lpf": [{"name": "front_imu_rp_align", "links": [[[0, 0], [0, 1]]]}], "front_imu_roll_lpf": [{"name": "front_imu_rp_align", "links": [[[0, 0], [0, 0]]]}], "front_imu_rp_align": [{"name": "front_imu_roll_check", "links": [[[0, 0], [1, 0]]]}, {"name": "front_imu_pitch_check", "links": [[[0, 1], [1, 0]]]}], "rear_imu_pitch_lpf": [{"name": "rear_imu_rp_align", "links": [[[0, 0], [0, 1]]]}], "rear_imu_roll_lpf": [{"name": "rear_imu_rp_align", "links": [[[0, 0], [0, 0]]]}], "rear_imu_rp_align": [{"name": "rear_imu_roll_check", "links": [[[0, 0], [1, 0]]]}, {"name": "rear_imu_pitch_check", "links": [[[0, 1], [1, 0]]]}], "front_imu_roll_check": [{"name": "all_level_check", "links": [[[0, -1], [0, -1]]]}], "front_imu_pitch_check": [{"name": "all_level_check", "links": [[[0, -1], [1, -1]]]}], "rear_imu_roll_check": [{"name": "all_level_check", "links": [[[0, -1], [2, -1]]]}], "rear_imu_pitch_check": [{"name": "all_level_check", "links": [[[0, -1], [3, -1]]]}], "all_level_check": [{"name": "level_pub", "links": [[[0, 0], [0, 3]]]}], "front_imu_to_lidar_rp_align": [{"name": "front_imu_q", "links": [[[0, 0], [0, 0]], [[0, 1], [0, 1]]]}], "rear_imu_to_lidar_rp_align": [{"name": "rear_imu_q", "links": [[[0, 0], [0, 0]], [[0, 1], [0, 1]]]}], "front_imu_q": [{"name": "front_lidar_inc_tf", "links": [[[0, 0], [0, 4]], [[0, 1], [0, 5]], [[0, 2], [0, 6]], [[0, 3], [0, 7]]]}], "rear_imu_q": [{"name": "rear_lidar_inc_tf", "links": [[[0, 0], [0, 4]], [[0, 1], [0, 5]], [[0, 2], [0, 6]], [[0, 3], [0, 7]]]}], "front_lidar_inc_tf": [], "rear_lidar_inc_tf": [], "body_inc_q": [{"name": "body_inc_tf", "links": [[[0, 0], [0, 4]], [[0, 1], [0, 5]], [[0, 2], [0, 6]], [[0, 3], [0, 7]]]}, {"name": "front_lidar_noinc_tf", "links": [[[0, 0], [0, 4]], [[0, 1], [0, 5]], [[0, 2], [0, 6]], [[0, 3], [0, 7]]]}, {"name": "rear_lidar_noinc_tf", "links": [[[0, 0], [0, 4]], [[0, 1], [0, 5]], [[0, 2], [0, 6]], [[0, 3], [0, 7]]]}], "body_inc_tf": [], "gnss_to_xy": [{"name": "main_antenna_noinc_tf", "links": [[[0, 0], [0, 1]], [[0, 1], [0, 2]]]}, {"name": "main_pose_est_2", "links": [[[0, 0], [1, 1]], [[0, 1], [1, 2]]]}], "rtk_heading_proc": [{"name": "yaw_lpf", "links": [[[0, -1], [0, -1]]]}], "heading_q": [{"name": "main_antenna_noinc_tf", "links": [[[0, 0], [0, 4]], [[0, 1], [0, 5]], [[0, 2], [0, 6]], [[0, 3], [0, 7]]]}, {"name": "main_pose_est_2", "links": [[[0, 0], [1, 4]], [[0, 1], [1, 5]], [[0, 2], [1, 6]], [[0, 3], [1, 7]]]}], "main_antenna_noinc_tf": [], "front_lidar_noinc_tf": [], "rear_lidar_noinc_tf": [], "yaw_lpf": [{"name": "yaw_lpf_for_speed", "links": [[[0, -1], [0, -1]]]}, {"name": "rotate_vel", "links": [[[0, -1], [1, -1]]]}, {"name": "heading_q", "links": [[[0, 0], [0, 2]]]}, {"name": "position_pub", "links": [[[0, 0], [0, 4]]]}, {"name": "drill_pos_pub", "links": [[[0, 0], [0, 5]]]}], "main_pose_est_1": [{"name": "main_pose_est_2", "links": [[[0, -1], [0, -1]]]}], "debug_pose_pub": [], "main_pose_est_2": [{"name": "debug_pose_pub", "links": [[[0, 0], [0, 1]], [[0, 1], [0, 2]], [[0, 2], [0, 3]], [[0, 3], [0, 4]], [[0, 4], [0, 5]], [[0, 5], [0, 6]], [[0, 6], [0, 7]]]}, {"name": "position_pub", "links": [[[0, 0], [0, 1]], [[0, 1], [0, 2]], [[0, 2], [0, 3]]]}, {"name": "x_lpf_for_speed", "links": [[[0, 0], [0, 0]]]}, {"name": "drill_pose_est", "links": [[[0, 0], [1, 1]], [[0, 1], [1, 2]], [[0, 2], [1, 3]], [[0, 3], [1, 4]], [[0, 4], [1, 5]], [[0, 5], [1, 6]], [[0, 6], [1, 7]]]}, {"name": "y_lpf_for_speed", "links": [[[0, 1], [0, 0]]]}], "position_pub": [], "quality_check": [{"name": "position_pub", "links": [[[0, 0], [0, 5]]]}, {"name": "tracks_state_pub", "links": [[[0, 0], [0, 3]]]}, {"name": "speed_state_pub", "links": [[[0, 0], [0, 4]]]}, {"name": "drill_pos_pub", "links": [[[0, 0], [0, 4]]]}], "x_lpf_for_speed": [{"name": "dxdt", "links": [[[0, -1], [0, -1]]]}], "y_lpf_for_speed": [{"name": "dydt", "links": [[[0, -1], [0, -1]]]}], "yaw_lpf_for_speed": [{"name": "dyawdt", "links": [[[0, -1], [0, -1]]]}], "dxdt": [{"name": "vx_lpf", "links": [[[0, -1], [0, -1]]]}], "dydt": [{"name": "vy_lpf", "links": [[[0, -1], [0, -1]]]}], "dyawdt": [{"name": "w_lpf", "links": [[[0, -1], [0, -1]]]}], "rotate_vel": [{"name": "calc_tracks_speed", "links": [[[0, 0], [0, 0]]]}, {"name": "speed_state_pub", "links": [[[0, 0], [0, 1]], [[0, 1], [0, 2]]]}], "vx_lpf": [{"name": "rotate_vel", "links": [[[0, 0], [0, 0]]]}], "vy_lpf": [{"name": "rotate_vel", "links": [[[0, 0], [0, 1]]]}], "w_lpf": [{"name": "calc_tracks_speed", "links": [[[0, -1], [1, -1]]]}, {"name": "speed_state_pub", "links": [[[0, 0], [0, 3]]]}], "calc_tracks_speed": [{"name": "tracks_state_pub", "links": [[[0, 0], [0, 1]], [[1, 0], [0, 2]]]}], "speed_state_pub": [], "tracks_state_pub": [], "drill_pos_pub": [], "debug_drill_pub": [], "drill_pose_est": [{"name": "drill_pos_pub", "links": [[[0, 0], [0, 1]], [[0, 1], [0, 2]], [[0, 2], [0, 3]]]}, {"name": "debug_drill_pub", "links": [[[0, 0], [0, 1]], [[0, 1], [0, 2]], [[0, 2], [0, 3]], [[0, 3], [0, 4]], [[0, 4], [0, 5]], [[0, 5], [0, 6]], [[0, 6], [0, 7]]]}], "drill2bl": [{"name": "drill_pose_est", "links": [[[0, -1], [0, -1]]]}], "head_pos_transform": [{"name": "head_pos_lpf", "links": [[[0, -1], [0, -1]]]}], "head_pos_raw_check": [{"name": "drill_state_pub", "links": [[[0, 0], [0, 8]]]}], "drill_state_pub": [], "head_pos_lpf": [{"name": "head_speed_calc", "links": [[[0, -1], [0, -1]]]}, {"name": "drill_state_pub", "links": [[[0, 0], [0, 1]]]}], "head_speed_calc": [{"name": "head_speed_lpf", "links": [[[0, -1], [0, -1]]]}], "head_speed_lpf": [{"name": "drill_state_pub", "links": [[[0, 0], [0, 2]]]}], "head_ang_pos_transform": [{"name": "drill_state_pub", "links": [[[0, 0], [0, 3]]]}], "head_rpm_transform": [{"name": "rpm_lim", "links": [[[0, 0], [0, 0]]]}], "feed_pressure_transform": [{"name": "fp_lim", "links": [[[0, 0], [0, 0]]]}], "rot_pressure_transform": [{"name": "rp_lim", "links": [[[0, 0], [0, 0]]]}], "air_pressure_transform": [{"name": "ap_lim", "links": [[[0, 0], [0, 0]]]}], "body_inc_sub": [{"name": "inc_roll_lpf", "links": [[[0, 0], [1, -1]], [[0, 1], [0, 0]]]}, {"name": "inc_pitch_lpf", "links": [[[0, 0], [1, -1]], [[0, 2], [0, 0]]]}, {"name": "level_pub", "links": [[[0, 0], [0, 0]]]}, {"name": "body_inc_tf", "links": [[[0, 0], [0, 0]]]}, {"name": "front_lidar_noinc_tf", "links": [[[0, 0], [0, 0]]]}, {"name": "rear_lidar_noinc_tf", "links": [[[0, 0], [0, 0]]]}], "front_imu_sub": [{"name": "front_imu_roll_lpf", "links": [[[0, 0], [1, -1]], [[0, 1], [0, 0]]]}, {"name": "front_imu_pitch_lpf", "links": [[[0, 0], [1, -1]], [[0, 2], [0, 0]]]}, {"name": "front_lidar_inc_tf", "links": [[[0, 0], [0, 0]]]}, {"name": "front_imu_to_lidar_rp_align", "links": [[[0, 1], [0, 0]], [[0, 2], [0, 1]]]}], "rear_imu_sub": [{"name": "rear_imu_pitch_lpf", "links": [[[0, 0], [1, -1]], [[0, 2], [0, 0]]]}, {"name": "rear_imu_roll_lpf", "links": [[[0, 0], [1, -1]], [[0, 1], [0, 0]]]}, {"name": "rear_lidar_inc_tf", "links": [[[0, 0], [0, 0]]]}, {"name": "rear_imu_to_lidar_rp_align", "links": [[[0, 1], [0, 0]], [[0, 2], [0, 1]]]}], "gnss_sub": [{"name": "main_antenna_noinc_tf", "links": [[[0, 0], [0, 0]], [[0, 3], [0, 3]]]}, {"name": "yaw_lpf", "links": [[[0, 0], [1, -1]]]}, {"name": "main_pose_est_2", "links": [[[0, 0], [1, 0]], [[0, 3], [1, 3]]]}, {"name": "debug_pose_pub", "links": [[[0, 0], [0, 0]]]}, {"name": "main_pose_est_1", "links": [[[0, 0], [1, -1]]]}, {"name": "position_pub", "links": [[[0, 0], [0, 0]]]}, {"name": "x_lpf_for_speed", "links": [[[0, 0], [1, -1]]]}, {"name": "y_lpf_for_speed", "links": [[[0, 0], [1, -1]]]}, {"name": "yaw_lpf_for_speed", "links": [[[0, 0], [1, -1]]]}, {"name": "dxdt", "links": [[[0, 0], [1, -1]]]}, {"name": "dydt", "links": [[[0, 0], [1, -1]]]}, {"name": "dyawdt", "links": [[[0, 0], [1, -1]]]}, {"name": "vx_lpf", "links": [[[0, 0], [1, -1]]]}, {"name": "vy_lpf", "links": [[[0, 0], [1, -1]]]}, {"name": "w_lpf", "links": [[[0, 0], [1, -1]]]}, {"name": "speed_state_pub", "links": [[[0, 0], [0, 0]]]}, {"name": "tracks_state_pub", "links": [[[0, 0], [0, 0]]]}, {"name": "drill_pos_pub", "links": [[[0, 0], [0, 0]]]}, {"name": "debug_drill_pub", "links": [[[0, 0], [0, 0]]]}, {"name": "drill_pose_est", "links": [[[0, 0], [1, 0]]]}, {"name": "drill2bl", "links": [[[0, 0], [1, -1]]]}, {"name": "gnss_to_xy", "links": [[[0, 1], [0, 0]], [[0, 2], [0, 1]]]}, {"name": "rtk_heading_proc", "links": [[[0, 4], [0, 0]]]}, {"name": "quality_check", "links": [[[0, 5], [0, 0]]]}], "drill_state_raw_sub": [{"name": "drill_state_pub", "links": [[[0, 0], [0, 0]]]}, {"name": "head_pos_lpf", "links": [[[0, 0], [1, -1]]]}, {"name": "head_speed_calc", "links": [[[0, 0], [1, -1]]]}, {"name": "head_speed_lpf", "links": [[[0, 0], [1, -1]]]}, {"name": "head_pos_transform", "links": [[[0, 1], [0, 0]]]}, {"name": "head_pos_raw_check", "links": [[[0, 1], [0, 0]]]}, {"name": "head_ang_pos_transform", "links": [[[0, 2], [0, 0]]]}, {"name": "head_rpm_transform", "links": [[[0, 3], [0, 0]]]}, {"name": "feed_pressure_transform", "links": [[[0, 4], [0, 0]]]}, {"name": "rot_pressure_transform", "links": [[[0, 5], [0, 0]]]}, {"name": "air_pressure_transform", "links": [[[0, 6], [0, 0]]]}], "ap_lim": [{"name": "drill_state_pub", "links": [[[0, 0], [0, 7]]]}], "rp_lim": [{"name": "drill_state_pub", "links": [[[0, 0], [0, 6]]]}], "fp_lim": [{"name": "drill_state_pub", "links": [[[0, 0], [0, 5]]]}], "rpm_lim": [{"name": "drill_state_pub", "links": [[[0, 0], [0, 4]]]}]}}