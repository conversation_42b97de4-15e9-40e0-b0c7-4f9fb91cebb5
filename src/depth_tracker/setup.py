from setuptools import setup
from glob import glob
import os

package_name = "depth_tracker"

setup(
    name=package_name,
    version="0.0.1",
    packages=[package_name],
    data_files=[
        ("share/ament_index/resource_index/packages", ["resource/"+package_name]),
        ("share/"+package_name, ["package.xml"]),
    ],
    install_requires=[
        "setuptools",
        "transforms3d>=0.4.2",
    ],
    zip_safe=True,
    maintainer="Roman Fedorenko",
    maintainer_email="<EMAIL>",
    description="Depth tracking node",
    license="proprietary",
    entry_points={
        "console_scripts": [
            "depth_tracker = depth_tracker:main",
        ],
    },
)
