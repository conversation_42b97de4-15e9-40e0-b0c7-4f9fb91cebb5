# DepthTracker (ROS2): проектирование и согласование

Документ описывает дизайн узла трекинга глубины бурения под ROS2.
Сделан на основе базовых правил проекта (наследование от `BaseFSM` — расширяет `BaseNode`, использование собственного сервера параметров, единая терминология), сообщений `drill_msgs`, геометрии машины (`Vehicle`) и с учетом legacy ROS1 реализации (/../../ZyfraDrillv1/catkin_ws/src/drill-software/drill-mover/remote_connector/src/remote_connector/main_node.py, см, depth_tracking).

## 1) Интерфейсы узла

### Входы (Subscribers)

| Топик | Тип | Назначение |
| --- | --- | --- |
| `/drill_state` | `drill_msgs/DrillState` | Позиция вращателя (шпинделя) `head_pos` (м), давление подачи `feed_pressure` (бар), валидность `head_pos_is_reliable` |
| `/tower_state` | `drill_msgs/TowerState` | Наклон мачты (отклонение относительно вертикали) `inclination` (градусы) |
| `/driller_status` | `StateMachineStatus` | (опционально) статусы бурения `TOUCHDOWN`/`OVERBURDEN_PASS` для надёжного старта |
| `/fork_state` | `ForkState` | Состояние вилки (открыта/закрыта); при открытой вилке интегрирование останавливаем |
| `/main_state_machine_status` | `StateMachineStatus` | Режимы верхнего уровня: `DRILLING`/`REMOTE`/`IDLE` |
| `/main_action` | `MainAction` | (опционально) целевая глубина от сервера. |
| `/drill_position` | `Position` | Поза фрейма `drill` от state_tracker (x,y,z,yaw). z — абсолютная высота фрейма; используем в расчётах |


### Выходы (Publishers)

| Топик | Тип | Назначение |
| --- | --- | --- |
| `/depth_info` | `drill_msgs/DepthInfo` | Консолидированная информация о глубинах/состоянии (см. структуру ниже) |

### TF (публикуется узлом)

TF иерархия:
```
base_link (машина)
└── drill
    ├── tower_top (верх хода вращателя: z = +tower_height, pitch = inclination − base_pitch)
    │   ├── head (вращатель: z = -head_pos)
    │   └── ground_level [tracking_active & wellhead_altitude] (z = −(string_len + ground_head_pos))
    │       ├── drill_bit (z = -current_bit_depth)
    │       └── hole_bottom (z = -hole_depth)
```

Примечания:
- Ось z направлена вверх; глубины задаём отрицательными смещениями по z (вниз).
- `drill` наследует ориентацию `base_link` (в `state_tracker` статический `base_link→drill`). Угол наклона мачты берём из `/tower_state.inclination` (в градусах относительно вертикали). Для корректной ориентации мачты относительно `base_link` и `drill` нужно использовать относительный угол: `tower_top.pitch = inclination − base_pitch` (`base_pitch` берем из TF `base_link`). 
- `ground_level` относительно наклонной мачты: публикуется в локальных осях мачты `tower_top`.
  Вертикальное смещение в этих осях:
  `ground_level.z = (wellhead_altitude − (drill_position.z + tower_height))`.

## 2) Параметры

- Геометрия станка: из `self.vehicle_params` (`Vehicle.geometry.*` в `src/params_server/params_server/base_config/vehicle.yaml`).
- Настройки узла: из `self.node_params` (`src/params_server/params_server/base_config/nodes.yaml`). 
- ROS-параметры не используем.

```yaml
depth_tracker:
  rate: 10.0
  max_reasonable_delta: 0.10      # м на цикл
  drilling_pressure_threshold: 0.5 # бар
  remote_pressure_threshold: 1.0   # бар
  subscription_timeout: 0.5       # с
  remote_pressure_stable_time_s: 1.0            # стабильность давления для REMOTE (сек)
  drilling_pressure_stable_time_s: 0.5          # стабильность давления для DRILLING (сек)
  remote_new_session_min_distance_m: 1.0        # мин. XY-смещение для новой скважины (м)
  max_wellhead_below_drill_m: 2.5               # допуск ниже z_drill при касании (м)
```

Примечание: В коде не используем `.get()` с значениями по умолчанию — требуем явного наличия ключей: падение на инициализации предпочтительнее скрытых дефолтов.

## 3) Сообщения и терминология

### Термины

- **head_pos**: позиция вращателя (шпинделя) от верха мачты, м. Источник: `DrillState.head_pos`.
- **current_bit_depth**: текущая глубина бура от устья, м. Интегрируется по Δ(head_pos) в режиме бурения и останавливается при наращивании (вилка открыта).
- **hole_depth (measured depth)**: максимальная достигнутая глубина от устья, м. Не уменьшается при протяжках.
- **tvd_hole_depth (TVD)**: истинная вертикальная глубина дна скважины, м. Используем угол наклона мачты `inclination` из `/tower_state` (0° = вертикально): `tvd_hole_depth = hole_depth * cos(inclination_rad)`.
- **wellhead_altitude**: абсолютная вертикальная высота устья, м. При касании фиксируем по формуле:
  `wellhead_altitude = drill_position.z + (tower_height − string_len − ground_head_pos) * cos(inclination_rad)`.
- **ground_head_pos**: значение `head_pos` на момент касания земли (опорное).
- **tower_height**: высота мачты от уровня вилки до верхней точки хода вращателя (м), из геометрии `Vehicle`.
- **string_len**: полная длина става на момент касания (когда начинаем бурение, без нарощенных штанг):
  `string_len = shaft_len + drill_bit_len + extension_len`.

### `drill_msgs/DepthInfo`

Формат сообщения в `src/drill_msgs/msg/DepthInfo.msg`:

- Основное: `hole_depth`, `current_bit_depth`, `tvd_hole_depth`, `head_pos`, `ground_head_pos`.
- Геометрия скважины/цель: `wellhead_altitude`, `wellhead_x`, `wellhead_y`, `hole_inclination` (в градусах), `target_hole_depth`.
- Статус: `tracking_active`.

## 4) Логика трекинга

Принцип — тот же, что и в ROS1-версии (`remote_connector/main_node.py:770-829`): старт трекинга по физическому контакту, интегрирование по шпинделю (`head_pos`), пауза на наращиваниях. Код — безопасный и компактный, без дублирования и лишних сущностей.

### Условия активного трекинга

```python
def can_track_depth(self) -> bool:
    if self.subs.fork_state is None or self.subs.main_state_machine_status is None:
        return False
    if self.subs.fork_state.open:  # string buildup → pause
        return False
    if not self.depth_data_valid:
        return False
    return self.subs.main_state_machine_status.current_state in ("DRILLING", "REMOTE")
```

### Старт/рестарт отсчёта устья (касание земли)

Физика одна — касание земли буром. Источники сигнала могут различаться при дистанционном управлении и автоматическом бурении и объединены в общие правила:

- DRILLING:
  - Основной триггер (при наличии входа `/driller_status`): переход `TOUCHDOWN → OVERBURDEN_PASS` при `head_pos_is_reliable`.
  - Резервный триггер: устойчивое давление `feed_pressure > drilling_pressure_threshold` в течение `drilling_pressure_stable_time_s` при `head_pos_is_reliable`.
- REMOTE:
  - Давление стабильно `feed_pressure > remote_pressure_threshold` в течение `remote_pressure_stable_time_s`.
  - И дополнительно — признак новой скважины: расстояние между текущим `/drill_position` и `wellhead_xy` ≥ `remote_new_session_min_distance_m`.

Минимальная реализация: сначала выбираем ветку условий для REMOTE или DRILLING. Учитываем достоверность `head_pos` и устойчивость давления:

```python
def should_start_tracking(self, s) -> bool:
    ms = self.subs.main_state_machine_status
    in_remote = (ms and ms.current_state == "REMOTE")

    # REMOTE: stable pressure + new-hole candidate, requires reliable head_pos
    if in_remote:
        if not s.head_pos_is_reliable:
            return False
        if self.pressure_stable(s.feed_pressure, self.remote_pressure_threshold, self.remote_pressure_stable_time_s):
            if self.is_new_remote_hole_candidate():
                self.log("Touchdown by stable pressure (REMOTE)", level=self.INFO)
                return True
        return False

    # DRILLING: check driller_status edge first, then pressure fallback
    st = self.subs.driller_status
    prev = getattr(self, '_prev_driller_status', None)
    if st and prev == 'TOUCHDOWN' and st.current_state == 'OVERBURDEN_PASS' and s.head_pos_is_reliable:
        self.log("Touchdown by driller_status edge", level=self.INFO)
        self._prev_driller_status = st.current_state
        return True

    # Update prev only when head_pos is reliable
    if st and s.head_pos_is_reliable:
        self._prev_driller_status = st.current_state

    if s.head_pos_is_reliable and self.pressure_stable(s.feed_pressure, self.drilling_pressure_threshold, self.drilling_pressure_stable_time_s):
        self.log("Touchdown by stable pressure (DRILLING)", level=self.INFO)
        return True
    return False

```

Вспомогательные функции:

```python
def pressure_stable(self, pressure: float, threshold: float, stable_time_s: float) -> bool:
    now = self.get_time()
    above = pressure > threshold
    if above:
        if getattr(self, '_pressure_above_since', None) is None:
            self._pressure_above_since = now
        return (now - self._pressure_above_since) >= stable_time_s
    else:
        self._pressure_above_since = None
        return False

def is_new_remote_hole_candidate(self) -> bool:
    # New hole if no prior wellhead or XY-offset exceeds the threshold
    if getattr(self, 'wellhead_xy', None) is None:
        return True
    pos = self.subs.drill_position
    if pos is None or not pos.is_reliable:
        return False
    dx = pos.x - self.wellhead_xy[0]
    dy = pos.y - self.wellhead_xy[1]
    return math.hypot(dx, dy) >= self.remote_new_session_min_distance_m
```

При срабатывании триггера фиксируем опорные величины по геометрической формуле (как в ROS1-версии):

```python 
def on_touchdown(self, head_pos_now: float):
    # drill_position.z — absolute altitude of the drill frame
    z_drill = self.subs.drill_position.z

    # Geometry: tower_height and string components
    tower_height = self.vehicle_params['geometry']['tower_height']
    shaft_id = self.vehicle_params['shaft_list'][0]
    shaft_len = self.vehicle_params['shaft_len_params'][shaft_id]['full_len']
    drill_bit_len = self.vehicle_params['drill_bit_len']
    extension_len = self.vehicle_params['extension_len']
    string_len = shaft_len + drill_bit_len + extension_len

    # Wellhead altitude (MSL, vertical projection)
    inc_rad = math.radians(self.subs.tower_state.inclination)
    raw_alt = z_drill + (tower_height - string_len - head_pos_now) * math.cos(inc_rad)

    # Reasonable bounds: not above z_drill, not below z_drill - max_wellhead_below_drill_m
    min_allowed = z_drill - self.node_params['max_wellhead_below_drill_m']
    clamped = max(min(raw_alt, z_drill), min_allowed)
    if abs(clamped - raw_alt) > 1e-6:
        self.log(
            f"Clamped wellhead_altitude from {raw_alt:.3f} to {clamped:.3f}",
            level=self.WARN,
            period=2.0
        )
    self.wellhead_altitude = clamped
    self.wellhead_xy = (self.subs.drill_position.x, self.subs.drill_position.y)
    self.ground_head_pos = head_pos_now
    self.current_bit_depth = 0.0
    self.hole_depth = 0.0
    self.tvd_hole_depth = 0.0
    self.tracking_active = True
    self.log(
        f"Touchdown: z_drill={z_drill:.3f}, head_pos={head_pos_now:.3f}, wellhead={self.wellhead_altitude:.3f}",
        level=self.INFO
    )
```

Замечание о сбросах: начало трекинга (touchdown) всегда сопровождается обнулением глубин (старт новой последовательности измерений). Дополнительно, при выходе из буровых режимов глубины также сбрасываются, чтобы исключить показ «устаревшей» скважины при перемещении между точками (см. «Критерии нового сеанса и сброса» ниже).

### Критерии «нового сеанса» и сброса

- Новый сеанс начинается при срабатывании триггеров раздела «Старт/рестарт отсчёта устья (касание земли)».
- Сброс без старта интегрирования (ожидание нового касания):
  - Получение нового `main_action` → немедленный сброс текущих глубин/состояния.
  - DRILLING: как только `main_state_machine_status` выходит из буровых режимов → сброс. Под «выходом» понимаем переход текущего состояния `current_state` не в (`DRILLING`, `REMOTE` или `IDLE`).
  - REMOTE: «сброс при отъезде» — когда оператор покидает текущую точку бурения. Условие: текущая дистанция от сохранённого `wellhead_xy` ≥ `remote_new_session_min_distance_m` И давление `feed_pressure <= remote_pressure_threshold`. При выполнении → немедленный сброс глубин/состояния, чтобы не отображать старую скважину при перемещении.

Поведение при сбросе:
- `tracking_active = False`, `current_bit_depth = 0.0`, `hole_depth = 0.0`, `tvd_hole_depth = 0.0`.
- Опорные `wellhead_altitude` и `ground_head_pos` очищаем (не используем) до следующего касания.
- TF публикация ограничивается только кадром `head`.

Минимальная реализация сброса:

```python
def reset_depth_tracking(self, reason: str = ""):
    self.tracking_active = False
    self.current_bit_depth = 0.0
    self.hole_depth = 0.0
    self.tvd_hole_depth = 0.0
    self.wellhead_altitude = None
    self.ground_head_pos = None
    # Keep wellhead_xy to detect REMOTE move-away; it will be refreshed on next touchdown
    self.log(f"Depth tracking reset: {reason}", level=self.INFO, period=1.0)

def update_session_resets(self, s):
    ms = self.subs.main_state_machine_status
    cur_mode = ms.current_state if ms else None

    # 1) Reset when leaving drilling contexts
    prev_mode = getattr(self, '_prev_machine_state', None)
    if prev_mode in ("DRILLING", "REMOTE") and cur_mode not in ("DRILLING", "REMOTE", "IDLE"):
        self.reset_depth_tracking("left drilling/remote mode")
        # Reset pressure stability window after mode change
        self._pressure_above_since = None

    # 2) Reset on new main_action (if header available)
    main_action = self.subs.main_action
    if main_action is not None and hasattr(main_action, 'header'):
        last_stamp = getattr(self, '_last_main_action_stamp', None)
        if last_stamp is None or main_action.header.stamp != last_stamp:
            self.reset_depth_tracking("new main_action")
            self._last_main_action_stamp = main_action.header.stamp

    # 3) Remote move-away: if moved far from saved wellhead in REMOTE
    if getattr(self, 'wellhead_xy', None) is not None and cur_mode == "REMOTE":
        pos = self.subs.drill_position
        if pos is not None and pos.is_reliable:
            dx = pos.x - self.wellhead_xy[0]
            dy = pos.y - self.wellhead_xy[1]
            if math.hypot(dx, dy) >= self.remote_new_session_min_distance_m:
                if s.feed_pressure <= self.remote_pressure_threshold:
                    self.reset_depth_tracking("remote move-away")

    self._prev_machine_state = cur_mode
```

### Интегрирование

Пример реализации с минимальными проверками:

```python
def update_depth(self, msg):
    # Initialize previous head position/time if empty
    if self.prev_head_pos is None:
        self.prev_head_pos = msg.head_pos
        self.prev_stamp = msg.header.stamp
        return

    # Pause integration when tracking hasn't started yet or fork/mode disallows it
    if (not self.tracking_active) or (not self.can_track_depth()):
        self.prev_head_pos = msg.head_pos
        self.prev_stamp = msg.header.stamp
        return

    delta = msg.head_pos - self.prev_head_pos

    # Sanity checks
    if abs(delta) > self.max_reasonable_delta:
        self.depth_anomaly_detected = True
        return

    # Integrate
    self.current_bit_depth += delta
    if self.current_bit_depth > self.hole_depth:
        self.hole_depth = self.current_bit_depth

    # TVD using tower inclination (degrees → radians)
    inc_deg = self.subs.tower_state.inclination if self.subs.tower_state else 0.0
    inc_rad = math.radians(inc_deg)
    self.tvd_hole_depth = self.hole_depth * math.cos(inc_rad) if inc_deg else self.hole_depth

    # Bookkeeping
    self.depth_anomaly_detected = False
    self.prev_head_pos = msg.head_pos
    self.prev_stamp = msg.header.stamp
```

## 5) Архитектура (BaseFSM на базе BaseNode)

- Выбор: используем `BaseFSM` с одним состоянием (расширение `BaseNode`) как базовую архитектуру. Причины: меньше кода (без ручных callback-ов), встроенные подписчики с таймаутами, стандартные проверки безопасности и единый цикл `do_work()`.
- Базовый функционал `BaseNode` (логирование `log()`, время `get_time()/get_rostime()`, жизненный цикл `run()/initialize()/do_work()`) доступен и используется через `BaseFSM`.
- Обновление настроек — через `on_params_update(updated_keys)`.

Подписчики регистрируем через `BaseFSM.add_subscriber(...)` с таймаутами: `/drill_state`, опц. `/driller_status`, `/fork_state`, `/main_state_machine_status`, `/drill_position`. Доступ к данным — как `self.subs.drill_state` и т.п. Таймауты обрабатываются `BaseFSM`, `can_track_depth()` таймауты не проверяет.

Минимальный цикл работы:

```python
def do_work(self):
    s = self.subs.drill_state
    if s is None or not self.validate_drill_state(s):
        return
    # Unified session reset logic (mode changes, new action, remote move-away)
    self.update_session_resets(s)
    if self.should_start_tracking(s):
        self.on_touchdown(s.head_pos)
    self.update_depth(s)

def do_work_finally(self):
    # Publish DepthInfo and TF at node rate when tracking is active
    if getattr(self, 'depth_info_pub', None) is None:
        return
    msg = DepthInfo()
    msg.header.stamp = self.get_clock().now().to_msg()
    msg.hole_depth = self.hole_depth
    msg.current_bit_depth = self.current_bit_depth
    msg.tvd_hole_depth = self.tvd_hole_depth
    msg.head_pos = self.subs.drill_state.head_pos if self.subs.drill_state else 0.0
    msg.ground_head_pos = self.ground_head_pos or 0.0
    msg.wellhead_altitude = self.wellhead_altitude or 0.0
    if getattr(self, 'wellhead_xy', None):
        msg.wellhead_x, msg.wellhead_y = self.wellhead_xy
    msg.hole_inclination = self.subs.drill_state.head_angular_pos if self.subs.drill_state else 0.0
    msg.target_hole_depth = getattr(self, 'target_hole_depth', 0.0)
    msg.tracking_active = self.tracking_active
    self.depth_info_pub.publish(msg)

    # TF publication sketch (frames omitted for brevity):
    # if self.tracking_active and self.wellhead_altitude is not None:
    #     self.tf_broadcaster.sendTransform(...)
```

Почему одно состояние, а не несколько (idle/paused/tracking):

- **Проще и короче код**: пауза уже корректно обрабатывается через `can_track_depth()` и обновление `prev_*`; дополнительных побочных эффектов при паузе нет.
- **Меньше пограничных случаев**: меньше логики переходов.
- **Достаточно статуса**: флаг `tracking_active` в `DepthInfo` и публикация TF полностью описывают поведение; внешним потребителям не нужны отдельные имена FSM-состояний.
- **Встроенные таймауты**: просрочка данных автоматически блокирует интегрирование без смены состояния.

Если понадобится, можно разделить на состояния позже.

Инициализация: проверяем обязательные ключи и падаем при отсутствии, например:

```python
def initialize(self):
    # required node params
    required = (
        'rate',
        'max_reasonable_delta',
        'drilling_pressure_threshold',
        'remote_pressure_threshold',
        'subscription_timeout',
        'remote_pressure_stable_time_s',
        'drilling_pressure_stable_time_s',
        'remote_new_session_min_distance_m',
        'max_wellhead_below_drill_m',
    )
    for k in required:
        _ = self.node_params[k]

    # required vehicle params
    _ = self.vehicle_params['geometry']['tower_height']
```


## 6) Диагностика

- `/depth_info` — основной канал: контролируем `hole_depth`, `current_bit_depth`, `tvd_hole_depth`, `tracking_active`.
- TF — проверяем в RViz дерево кадров и величины смещений.
- События/логи — через `BaseNode.log()` с периодическим подавлением.

---

Основано на анализе `remote_connector/main_node.py:770-829` (legacy) и актуальных файлов ROS2 проекта (`BaseNode`, `drill_msgs/DrillState.msg`, `drill_msgs/DepthInfo.msg`).