from base_node.base_fsm import BaseFSM, BaseState

from drill_msgs.msg import (
    DrillState,
    DepthInfo,
    ForkState,
    StateMachineStatus,
    Position,
    MainAction,
    TowerState,
)

import math
from geometry_msgs.msg import TransformStamped
from tf2_ros import TransformBroadcaster
from transforms3d.euler import euler2quat


class DepthTrackingState(BaseState):
    """Single-state implementation for depth tracking.

    The logic follows the system design in docs: integrates `head_pos` deltas
    while tracking is active; starts on touchdown triggers; pauses on string build.
    """

    def __init__(self, node: "DepthTrackerFSM"):
        super().__init__(
            name="run",
            node=node,
            remember_as_prev=True,
            # We manage subscriber readiness inside do_work() for required inputs
            # Optional inputs should not block the loop
            ignore_outdated_subs=True,
        )

    def do_work(self):
        s = self.subs.drill_state
        if s is None or not self.node.validate_drill_state(s):
            return

        # Unified session reset logic (mode changes, new action, remote move-away)
        self.node.update_session_resets(s)

        if self.node.should_start_tracking(s):
            self.node.on_touchdown(s.head_pos)

        self.node.update_depth(s)

    def do_work_finally(self):
        self.node.publish_depth_info()


class DepthTrackerFSM(BaseFSM):
    def __init__(self):
        super().__init__(node_name="depth_tracker")

        # Internal state (depth tracking)
        self.tracking_active = False
        self.depth_data_valid = False

        self.current_bit_depth = 0.0
        self.hole_depth = 0.0
        self.tvd_hole_depth = 0.0

        self.prev_head_pos = None
        self.prev_stamp = None

        self.wellhead_altitude = None
        self.wellhead_xy = None
        self.ground_head_pos = None

        self.depth_anomaly_detected = False

        # Pressure stability bookkeeping
        self._pressure_above_since = None
        self._prev_machine_state = None
        self._prev_driller_status = None
        self._last_main_action_stamp = None

        # Cached node params (populated in initialize())
        self.max_reasonable_delta = None
        self.drilling_pressure_threshold = None
        self.remote_pressure_threshold = None
        self.subscription_timeout = None
        self.remote_pressure_stable_time_s = None
        self.drilling_pressure_stable_time_s = None
        self.remote_new_session_min_distance_m = None
        self.max_wellhead_below_drill_m = None

        # Publisher placeholder
        self.depth_info_pub = None

    # ------------------------------------------------------------------
    # BaseFSM required overrides
    # ------------------------------------------------------------------
    def stop_control(self):
        # Nothing to actively stop in this passive tracking node
        pass

    def safety_check(self) -> bool:
        # Passive computations only
        return True

    # ------------------------------------------------------------------
    # Lifecycle
    # ------------------------------------------------------------------
    def initialize(self):
        # Validate node parameters presence (fail fast on missing keys)
        required = (
            "rate",
            "max_reasonable_delta",
            "drilling_pressure_threshold",
            "remote_pressure_threshold",
            "subscription_timeout",
            "remote_pressure_stable_time_s",
            "drilling_pressure_stable_time_s",
            "remote_new_session_min_distance_m",
            "max_wellhead_below_drill_m",
        )
        for k in required:
            _ = self.node_params[k]

        # Required vehicle param
        _ = self.vehicle_params["geometry"]["tower_height"]

        # Cache frequently used params
        self._refresh_cached_params()

        # Subscribers (timeouts are governed by config; optional inputs allowed)
        to = self.subscription_timeout
        self.add_subscriber("/drill_state", DrillState, "drill_state", timeout=to)
        self.add_subscriber("/fork_state", ForkState, "fork_state", timeout=to)
        self.add_subscriber(
            "/main_state_machine_status",
            StateMachineStatus,
            "main_state_machine_status",
            timeout=to,
        )
        # Optional helpers
        self.add_subscriber("/driller_status", StateMachineStatus, "driller_status", timeout=to)
        self.add_subscriber("/main_action", MainAction, "main_action", timeout=to)
        self.add_subscriber("/drill_position", Position, "drill_position", timeout=to)
        self.add_subscriber("/tower_state", TowerState, "tower_state", timeout=to)

        # Publisher
        self.depth_info_pub = self.create_publisher(DepthInfo, "/depth_info", qos_profile=self.qos_default)
        self.tf_broadcaster = TransformBroadcaster(self)

        # Single state
        self.add_states(DepthTrackingState(self))
        self.set_state("run")

        self.log("DepthTracker initialized")

    def on_params_update(self, updated_keys):
        if self.get_name() in updated_keys or "Vehicle" in updated_keys:
            self._refresh_cached_params()

    # ------------------------------------------------------------------
    # Helpers from design doc
    # ------------------------------------------------------------------
    def _refresh_cached_params(self):
        self.max_reasonable_delta = self.node_params["max_reasonable_delta"]  # m/cycle
        self.drilling_pressure_threshold = self.node_params["drilling_pressure_threshold"]  # bar
        self.remote_pressure_threshold = self.node_params["remote_pressure_threshold"]  # bar
        self.subscription_timeout = self.node_params["subscription_timeout"]  # s
        self.remote_pressure_stable_time_s = self.node_params["remote_pressure_stable_time_s"]  # s
        self.drilling_pressure_stable_time_s = self.node_params["drilling_pressure_stable_time_s"]  # s
        self.remote_new_session_min_distance_m = self.node_params["remote_new_session_min_distance_m"]  # m
        self.max_wellhead_below_drill_m = self.node_params["max_wellhead_below_drill_m"]  # m

    def can_track_depth(self) -> bool:
        if self.subs.fork_state is None or self.subs.main_state_machine_status is None:
            return False
        if self.subs.fork_state.open:
            return False
        if not self.depth_data_valid:
            return False
        return self.subs.main_state_machine_status.current_state in ("DRILLING", "REMOTE")

    def validate_drill_state(self, s: DrillState) -> bool:
        # Head pos validity only – extend when needed
        if not s.head_pos_is_reliable:
            self.depth_data_valid = False
            return False
        self.depth_data_valid = True
        return True

    def pressure_stable(self, pressure: float, threshold: float, stable_time_s: float) -> bool:
        now = self.get_time()
        above = pressure > threshold
        if above:
            if self._pressure_above_since is None:
                self._pressure_above_since = now
            return (now - self._pressure_above_since) >= stable_time_s
        else:
            self._pressure_above_since = None
            return False

    def is_new_remote_hole_candidate(self) -> bool:
        if self.wellhead_xy is None:
            return True
        pos = self.subs.drill_position
        if pos is None or not pos.is_reliable:
            return False
        dx = pos.x - self.wellhead_xy[0]
        dy = pos.y - self.wellhead_xy[1]
        return math.hypot(dx, dy) >= self.remote_new_session_min_distance_m

    def should_start_tracking(self, s: DrillState) -> bool:
        ms = self.subs.main_state_machine_status
        in_remote = (ms and ms.current_state == "REMOTE")

        # REMOTE: stable pressure + new-hole candidate, requires reliable head_pos
        if in_remote:
            if not s.head_pos_is_reliable:
                return False
            if self.pressure_stable(s.feed_pressure, self.remote_pressure_threshold, self.remote_pressure_stable_time_s):
                if self.is_new_remote_hole_candidate():
                    self.log("Touchdown by stable pressure (REMOTE)", level=self.INFO)
                    return True
            return False

        # DRILLING: check driller_status edge first, then pressure fallback
        st = self.subs.driller_status
        prev = getattr(self, "_prev_driller_status", None)
        if st and prev == "TOUCHDOWN" and st.current_state == "OVERBURDEN_PASS" and s.head_pos_is_reliable:
            self.log("Touchdown by driller_status edge", level=self.INFO)
            self._prev_driller_status = st.current_state
            return True

        # Update prev only when head_pos is reliable
        if st and s.head_pos_is_reliable:
            self._prev_driller_status = st.current_state

        if s.head_pos_is_reliable and self.pressure_stable(s.feed_pressure, self.drilling_pressure_threshold, self.drilling_pressure_stable_time_s):
            self.log("Touchdown by stable pressure (DRILLING)", level=self.INFO)
            return True
        return False

    def on_touchdown(self, head_pos_now: float):
        # drill_position.z — absolute altitude of the drill frame
        if self.subs.drill_position is None:
            return
        z_drill = self.subs.drill_position.z

        # Geometry: tower_height and string components
        tower_height = self.vehicle_params["geometry"]["tower_height"]
        shaft_id = str(self.vehicle_params["shaft_list"][0])
        shaft_len = self.vehicle_params["shaft_len_params"][shaft_id]["full_len"]
        drill_bit_len = self.vehicle_params["drill_bit_len"]
        extension_len = self.vehicle_params["extension_len"]
        string_len = shaft_len + drill_bit_len + extension_len

        # Wellhead altitude (MSL, vertical projection)
        st = self.subs.drill_state
        head_rad = math.radians(st.head_angular_pos)
        raw_alt = z_drill + (tower_height - string_len - head_pos_now) * math.cos(head_rad)

        # Reasonable bounds: not above z_drill, not below z_drill - max_wellhead_below_drill_m
        min_allowed = z_drill - self.max_wellhead_below_drill_m
        clamped = max(min(raw_alt, z_drill), min_allowed)
        if abs(clamped - raw_alt) > 1e-6:
            self.log(
                f"Clamped wellhead_altitude from {raw_alt:.3f} to {clamped:.3f}",
                level=self.WARN,
                period=2.0,
            )

        self.wellhead_altitude = clamped
        self.wellhead_xy = (self.subs.drill_position.x, self.subs.drill_position.y)
        self.ground_head_pos = head_pos_now
        self.current_bit_depth = 0.0
        self.hole_depth = 0.0
        self.tvd_hole_depth = 0.0
        self.tracking_active = True
        self.log(
            f"Touchdown: z_drill={z_drill:.3f}, head_pos={head_pos_now:.3f}, wellhead={self.wellhead_altitude:.3f}",
            level=self.INFO,
        )

    def reset_depth_tracking(self, reason: str = ""):
        self.tracking_active = False
        self.current_bit_depth = 0.0
        self.hole_depth = 0.0
        self.tvd_hole_depth = 0.0
        self.wellhead_altitude = None
        self.ground_head_pos = None
        # Keep wellhead_xy to detect REMOTE move-away; it will be refreshed on next touchdown
        self.log(f"Depth tracking reset: {reason}", level=self.INFO, period=1.0)

    def update_session_resets(self, s: DrillState):
        ms = self.subs.main_state_machine_status
        cur_mode = ms.current_state if ms else None

        # 1) Reset when leaving drilling contexts
        prev_mode = getattr(self, "_prev_machine_state", None)
        if prev_mode in ("DRILLING", "REMOTE") and cur_mode not in ("DRILLING", "REMOTE", "IDLE"):
            self.reset_depth_tracking("left drilling/remote mode")
            # Reset pressure stability window after mode change
            self._pressure_above_since = None

        # 2) Reset on new main_action (if header available)
        main_action = self.subs.main_action
        if main_action is not None and hasattr(main_action, "header"):
            last_stamp = getattr(self, "_last_main_action_stamp", None)
            if last_stamp is None or main_action.header.stamp != last_stamp:
                self.reset_depth_tracking("new main_action")
                self._last_main_action_stamp = main_action.header.stamp

        # 3) Remote move-away: if moved far from saved wellhead in REMOTE
        if self.wellhead_xy is not None and cur_mode == "REMOTE":
            pos = self.subs.drill_position
            if pos is not None and pos.is_reliable:
                dx = pos.x - self.wellhead_xy[0]
                dy = pos.y - self.wellhead_xy[1]
                if math.hypot(dx, dy) >= self.remote_new_session_min_distance_m:
                    # Require low pressure to avoid reset during drilling at a new spot
                    ds = self.subs.drill_state
                    if ds is not None and ds.feed_pressure <= self.remote_pressure_threshold:
                        self.reset_depth_tracking("remote move-away")
                    self.reset_depth_tracking("remote move-away")

        self._prev_machine_state = cur_mode

    def update_depth(self, msg: DrillState):
        # Initialize previous head position/time if empty
        if self.prev_head_pos is None:
            self.prev_head_pos = msg.head_pos
            self.prev_stamp = msg.header.stamp
            return

        # Pause integration when tracking hasn't started or mode disallows it
        if (not self.tracking_active) or (not self.can_track_depth()):
            self.prev_head_pos = msg.head_pos
            self.prev_stamp = msg.header.stamp
            return

        delta = msg.head_pos - self.prev_head_pos

        # Sanity checks
        if abs(delta) > self.max_reasonable_delta:
            self.depth_anomaly_detected = True
            return

        # Integrate
        self.current_bit_depth += delta
        if self.current_bit_depth > self.hole_depth:
            self.hole_depth = self.current_bit_depth

        # TVD using current inclination (degrees → radians)
        # Use tower inclination (degrees) from /tower_state
        inc_deg = self.subs.tower_state.inclination if self.subs.tower_state else 0.0
        inc_rad = math.radians(inc_deg)
        self.tvd_hole_depth = self.hole_depth * math.cos(inc_rad) if inc_deg else self.hole_depth

        # Bookkeeping
        self.depth_anomaly_detected = False
        self.prev_head_pos = msg.head_pos
        self.prev_stamp = msg.header.stamp

    # ------------------------------------------------------------------
    # Publication
    # ------------------------------------------------------------------
    def publish_depth_info(self):
        if self.depth_info_pub is None:
            return
        msg = DepthInfo()
        msg.header.stamp = self.get_clock().now().to_msg()
        msg.hole_depth = self.hole_depth
        msg.current_bit_depth = self.current_bit_depth
        msg.tvd_hole_depth = self.tvd_hole_depth
        msg.head_pos = self.subs.drill_state.head_pos if self.subs.drill_state else 0.0
        msg.ground_head_pos = self.ground_head_pos if self.ground_head_pos is not None else 0.0
        msg.wellhead_altitude = self.wellhead_altitude if self.wellhead_altitude is not None else 0.0
        if self.wellhead_xy is not None:
            msg.wellhead_x = self.wellhead_xy[0]
            msg.wellhead_y = self.wellhead_xy[1]
        msg.hole_inclination = self.subs.tower_state.inclination if self.subs.tower_state else 0.0
        # Target hole depth from MainAction if available
        mad = self.subs.main_action
        msg.target_hole_depth = mad.depth if mad is not None else 0.0
        msg.tracking_active = bool(self.tracking_active)
        self.depth_info_pub.publish(msg)

        # TF publication
        self._publish_tf_frames()

    # ------------------------------------------------------------------
    # TF helpers
    # ------------------------------------------------------------------
    def _publish_tf_frames(self):
        if self.tf_broadcaster is None:
            return
        s = self.subs.drill_state
        if s is None:
            return
        # Publish only when tracking is active and we have wellhead_altitude
        if not self.tracking_active or self.wellhead_altitude is None:
            return

        now = self.get_clock().now().to_msg()

        # tower_top relative to drill: pitch = head_angular_pos (approx; base_pitch unavailable here)
        t1 = TransformStamped()
        t1.header.stamp = now
        t1.header.frame_id = "drill"
        t1.child_frame_id = "tower_top"
        t1.transform.translation.x = 0.0
        t1.transform.translation.y = 0.0
        t1.transform.translation.z = self.vehicle_params["geometry"]["tower_height"]  # z = +tower_height
        # Convert pitch to quaternion (yaw=roll=0) using standard function
        pitch_rad = math.radians(s.head_angular_pos)
        # transforms3d returns quaternion as (w, x, y, z)
        qw, qx, qy, qz = euler2quat(0.0, pitch_rad, 0.0, axes='sxyz')
        t1.transform.rotation.x = qx
        t1.transform.rotation.y = qy
        t1.transform.rotation.z = qz
        t1.transform.rotation.w = qw

        # head relative to tower_top: z = -head_pos
        t2 = TransformStamped()
        t2.header.stamp = now
        t2.header.frame_id = "tower_top"
        t2.child_frame_id = "head"
        t2.transform.translation.x = 0.0
        t2.transform.translation.y = 0.0
        t2.transform.translation.z = -s.head_pos
        self._set_identity_rotation(t2)

        # ground_level relative to tower_top: local z = −(string_len + ground_head_pos)
        if self.ground_head_pos is not None:
            tower_height = self.vehicle_params["geometry"]["tower_height"]
            shaft_id = str(self.vehicle_params["shaft_list"][0])
            shaft_len = self.vehicle_params["shaft_len_params"][shaft_id]["full_len"]
            drill_bit_len = self.vehicle_params["drill_bit_len"]
            extension_len = self.vehicle_params["extension_len"]
            string_len = shaft_len + drill_bit_len + extension_len

            t3 = TransformStamped()
            t3.header.stamp = now
            t3.header.frame_id = "tower_top"
            t3.child_frame_id = "ground_level"
            t3.transform.translation.x = 0.0
            t3.transform.translation.y = 0.0
            t3.transform.translation.z = -(string_len + self.ground_head_pos)
            self._set_identity_rotation(t3)

            # drill_bit and hole_bottom from ground_level
            t4 = TransformStamped()
            t4.header.stamp = now
            t4.header.frame_id = "ground_level"
            t4.child_frame_id = "drill_bit"
            t4.transform.translation.x = 0.0
            t4.transform.translation.y = 0.0
            t4.transform.translation.z = -self.current_bit_depth
            self._set_identity_rotation(t4)

            t5 = TransformStamped()
            t5.header.stamp = now
            t5.header.frame_id = "ground_level"
            t5.child_frame_id = "hole_bottom"
            t5.transform.translation.x = 0.0
            t5.transform.translation.y = 0.0
            t5.transform.translation.z = -self.hole_depth
            self._set_identity_rotation(t5)

            self.tf_broadcaster.sendTransform([t1, t2, t3, t4, t5])
            return

        self.tf_broadcaster.sendTransform([t1, t2])

    def _set_identity_rotation(self, t: TransformStamped):
        t.transform.rotation.w = 1.0
        t.transform.rotation.x = 0.0
        t.transform.rotation.y = 0.0
        t.transform.rotation.z = 0.0


def main(args=None):
    import rclpy
    rclpy.init(args=args)
    node = DepthTrackerFSM()
    node.run()
