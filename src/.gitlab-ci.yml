# .gitlab-ci.yml - ROS 2 Jazzy CI/CD Pipeline

stages:
  - docker_build
  - ros_build
  - lint
  - test
  - report
  - deploy

workflow:
  rules:
    - when: never  # TEMP: disable all pipelines; remove this line to re-enable
    - if: $CI_PIPELINE_SOURCE == "merge_request_event"
    - if: $CI_COMMIT_BRANCH == $CI_DEFAULT_BRANCH
    - if: $CI_COMMIT_TAG

variables:
  ROS_DISTRO: jazzy
  # Place workspace inside project directory so artifacts can be shared
  WORKSPACE_DIR: ${CI_PROJECT_DIR}/ros_ws
  CCACHE_DIR: ${CI_PROJECT_DIR}/.ccache
  DEBIAN_FRONTEND: noninteractive
  DOCKER_IMAGE: ${CI_REGISTRY_IMAGE}/ros2_jazzy:${CI_COMMIT_SHORT_SHA}
  # Stop executing job after set timeout (minutes)
  TIMEOUT: 20
  GIT_DEPTH: 1

# Docker build job
.docker_build_template: &docker_build
  image: docker:24.0
  services:
    - docker:24.0-dind
  before_script:
    - docker login -u $CI_REGISTRY_USER -p $CI_REGISTRY_PASSWORD $CI_REGISTRY || true

# ROS container jobs
.ros_container_template: &ros_container
  image: 
    name: ${DOCKER_IMAGE}
    entrypoint: [""]
  needs:
    - build_docker
  before_script:
    # Простое копирование с использованием промежуточной папки
    - mkdir -p /tmp/ros_src
    - cp -r ${CI_PROJECT_DIR}/* /tmp/ros_src/
    - mkdir -p ${WORKSPACE_DIR}/src
    - cp -r /tmp/ros_src/* ${WORKSPACE_DIR}/src/
    # Source ROS
    - source /opt/ros/$ROS_DISTRO/setup.bash

# Build Docker image
build_docker:
  <<: *docker_build
  stage: docker_build
  script:
    - docker build 
        --build-arg ROS_DISTRO=$ROS_DISTRO 
        --build-arg DEBIAN_FRONTEND=$DEBIAN_FRONTEND 
        --tag ${DOCKER_IMAGE} 
        --file ${CI_PROJECT_DIR}/docker/Dockerfile.ci 
        ${CI_PROJECT_DIR}
    - docker push ${DOCKER_IMAGE}
  cache:
    key: docker-${CI_COMMIT_REF_SLUG}
    paths:
      - .docker_cache/

# Lint code
ros_lint:
  <<: *ros_container
  stage: lint
  script:
    - cd ${WORKSPACE_DIR}
    - ament_flake8 
    - ament_cpplint 
    - ament_copyright --verbose 
    - ament_xmllint 
    - ament_lint_cmake 
  allow_failure: true

# Build workspace
build_ros_workspace:
  <<: *ros_container
  stage: ros_build
  script:
    - cd ${WORKSPACE_DIR}
    - ccache -s
    - colcon build --symlink-install --cmake-args 
        -DCMAKE_BUILD_TYPE=Release 
        -DCMAKE_CXX_FLAGS=-Wall 
        -DCMAKE_C_FLAGS=-Wall 
        -DCMAKE_EXPORT_COMPILE_COMMANDS=ON 
        -DCMAKE_CXX_COMPILER_LAUNCHER=ccache 
        -DCMAKE_C_COMPILER_LAUNCHER=ccache
    - ccache -s
  artifacts:
    paths:
      - ${WORKSPACE_DIR}/build/
      - ${WORKSPACE_DIR}/install/
      - ${WORKSPACE_DIR}/log/
    expire_in: 1 day
  cache:
    key: ccache-${CI_COMMIT_REF_SLUG}
    paths:
      - ${CCACHE_DIR}

# Run tests
run_tests:
  <<: *ros_container
  stage: test
  needs:
    - build_docker
    - build_ros_workspace
  script:
    - cd ${WORKSPACE_DIR}
    - source ${WORKSPACE_DIR}/install/setup.bash
    
    # Запускаем тесты и сохраняем результат (но продолжаем выполнение)
    - set +e
    - colcon test --packages-skip launchpack --return-code-on-test-failure
    - TEST_RESULT=$?
    - set -e
    
    # Показываем результаты тестов в логе
    - echo "=== Test Results Details ==="
    - colcon test-result --verbose || true
    - echo "==========================="
    
    # Создаем директории для артефактов
    - cd ${CI_PROJECT_DIR}
    - mkdir -p python_tests cpp_tests
    
    # Копируем результаты тестов
    - echo "=== Copying test results ==="
    # Python tests
    - |
      for PKG_XML in $(find ${WORKSPACE_DIR}/build -name "pytest.xml"); do
        if [ -f "${PKG_XML}" ]; then
          PKG_NAME=$(basename $(dirname ${PKG_XML}))
          echo "Found Python test results for ${PKG_NAME}"
          cp "${PKG_XML}" "python_tests/${PKG_NAME}_pytest.xml"
        fi
      done
    
    # C++ tests
    - |
      for PKG_DIR in $(find ${WORKSPACE_DIR}/build -path "*/test_results/*"); do
        if [ -d "${PKG_DIR}" ]; then
          PKG_NAME=$(basename ${PKG_DIR})
          echo "Found C++ test results for ${PKG_NAME}"
          mkdir -p "cpp_tests/${PKG_NAME}"
          cp ${PKG_DIR}/*.xml "cpp_tests/${PKG_NAME}/" || true
        fi
      done
    
    # Проверяем наличие файлов
    - echo "=== Test Results Summary ==="
    - echo "Python test files:"
    - ls -R python_tests/ || echo "No Python test results found"
    - echo "C++ test files:"
    - ls -R cpp_tests/ || echo "No C++ test results found"
    
    # В конце возвращаем статус тестов
    - |
      if [ "${TEST_RESULT}" -ne 0 ]; then
        echo "Tests failed with exit code ${TEST_RESULT}";
        exit ${TEST_RESULT};
      fi
    
  artifacts:
    when: always
    reports:
      junit:
        - python_tests/*.xml
        - "cpp_tests/*/*.xml"
    paths:
      - python_tests/
      - cpp_tests/
      - ros_ws/log/
    expire_in: 1 week
  allow_failure: false  # Job будет падать при ошибках в тестах

# Generate coverage report
generate_coverage:
  <<: *ros_container
  stage: report
  needs:
    - run_tests
  allow_failure: true
  script:
    - cd ${WORKSPACE_DIR}
    - mkdir -p ${CI_PROJECT_DIR}/coverage
    - gcovr --xml-pretty 
        --exclude-unreachable-branches 
        --print-summary 
        -o ${CI_PROJECT_DIR}/coverage/coverage.xml 
        --root ${WORKSPACE_DIR} 
        --exclude ".*test.*" 
        --exclude ".*build/.*" 
        --exclude ".*_vendor.*"
  artifacts:
    paths:
      - coverage/
    reports:
      coverage_report:
        coverage_format: cobertura
        path: coverage/coverage.xml
    expire_in: 1 week 